package cn.iocoder.yudao.aiBase.config;

/**
 * 通用常量
 * <AUTHOR>
 */
public class BaseConstant {

    // 占位符
    public static final String PLACE_HOLDER = "====placeholder====";

    // 空字符串
    public static final String EMPTY_STR = "";

    public static final String COMMA_STR = ",";

    public static final String UNDER_LINE_STR = "_";

    public static final String STAR_STR = "*";

    public static final String DOT_STR = ".";

    public static final String SLASH_STR = "/";

    public static final String COLON_STR = ":";

    public static final String LIMIT_STR = " limit 1 ";

    public static final Integer ZERO = 0;

    public static final String ZERO_STR = "0";

    public static final Integer ONE = 1;

    public static final Integer NEGATIVE_ONE = -1;

    public static final Integer TWO = 2;

    public static final Integer THREE = 3;

    public static final Integer FIVE = 5;

    public static final Integer SIX = 6;

    public static final Integer TEN = 10;

    public static final Integer TWELVE = 12;

    public static final Integer FIFTY = 50;

    public static final Integer KILO = 1000;

    public static final String SHI = "是";

    public static final String FOU = "否";

    public static final String END_TIME = " 23:59:59";

    public static final long MIN_MONTH_DAY_NUM = 30;

    public static final long YEAR_DAY_NUM = 365;

    public static final String PROD_STR = "prod";

    public static final String EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
    public static final String PWD_REGEX = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).+$";

    public static final String BEARER = "Bearer ";

    public static final String DEFAULT_STR = "DEFAULT";

    public static final String NULL_STR = "null";


}
