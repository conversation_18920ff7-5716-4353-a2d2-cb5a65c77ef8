package cn.iocoder.yudao.aiBase.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface BaseIService<T, E> extends IService<T> {

    default LambdaQueryWrapper<T> getLambda() {
        return getLambda(null);
    }

    /**
     * 获取lambda
     *
     * @param param
     * @return
     */
    default LambdaQueryWrapper<T> getLambda(E param) {
        LambdaQueryWrapper<T> lambda = new LambdaQueryWrapper<>();
        if (param != null) {
            // 如果参数存在，可以重写词方法
        }
        return lambda;
    }

    /**
     * 获取字段lambda
     * @param param
     * @param columns
     * @return
     */
    default LambdaQueryWrapper<T> getLambda(E param, SFunction<T, ?>... columns) {
        return getLambda(param).select(columns);
    }

    /**
     * 获取字段lambda倒序
     * @param param
     * @param columns
     * @return
     */
    default LambdaQueryWrapper<T> getLambdaDesc(E param, SFunction<T, ?> orderBy, SFunction<T, ?>... columns) {
        return getLambda(param, columns).orderByDesc(orderBy);
    }

    /**
     * 获取字段lambda正序
     * @param param
     * @param columns
     * @return
     */
    default LambdaQueryWrapper<T> getLambdaAsc(E param, SFunction<T, ?> orderBy, SFunction<T, ?>... columns) {
        return getLambda(param, columns).orderByAsc(orderBy);
    }

    /**
     * 获取list
     *
     * @param param
     * @return
     */
    default List<T> getList(E param) {
        return list(getLambda(param));
    }

    /**
     * 获取list
     *
     * @param param
     * @return
     */
    default List<T> getList(E param, SFunction<T, ?>... columns) {
        return list(getLambda(param, columns));
    }

    /**
     * 获取list倒序
     *
     * @param param
     * @return
     */
    default List<T> getListDesc(E param, SFunction<T, ?> orderBy, SFunction<T, ?>... columns) {
        return list(getLambdaDesc(param, orderBy, columns));
    }

    /**
     * 获取list正序
     *
     * @param param
     * @return
     */
    default List<T> getListAsc(E param, SFunction<T, ?> orderBy, SFunction<T, ?>... columns) {
        return list(getLambdaAsc(param, orderBy, columns));
    }

    /**
     * 判断是否存在
     *
     * @param param
     * @return
     */
    default Boolean existByCount(E param) {
        return count(getLambda(param).last(BaseConstant.LIMIT_STR)) > BaseConstant.ZERO;
    }
}

