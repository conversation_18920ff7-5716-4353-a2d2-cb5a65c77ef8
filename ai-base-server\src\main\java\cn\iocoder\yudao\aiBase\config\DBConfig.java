package cn.iocoder.yudao.aiBase.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "spring.datasource.dynamic.datasource")
@Data
public class DBConfig {

	private DBProperty master;

	private DBProperty aiBase;

	private DBProperty difyBase;

}
