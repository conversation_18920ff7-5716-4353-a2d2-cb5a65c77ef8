package cn.iocoder.yudao.aiBase.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "dify-base")
@Data
public class DifyBaseConfig {

	private String host;

	private String articleQaAppid;

	private List<String> hideAppIds;

	public void setHideAppIds(List<String> hideAppIds) {
		hideAppIds.add(articleQaAppid);
		this.hideAppIds = hideAppIds;
	}

}
