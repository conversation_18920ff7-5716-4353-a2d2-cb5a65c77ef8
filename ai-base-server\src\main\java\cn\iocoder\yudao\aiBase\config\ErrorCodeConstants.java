package cn.iocoder.yudao.aiBase.config;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;

public interface ErrorCodeConstants extends GlobalErrorCodeConstants {

    ErrorCode ERROR_5001 = new ErrorCode(5001, "用户不存在");
    ErrorCode ERROR_5002 = new ErrorCode(5002, "邮箱格式错误");
    ErrorCode ERROR_5003 = new ErrorCode(5003, "邮箱已注册");
    ErrorCode ERROR_5004 = new ErrorCode(5004, "邮箱不能为空");
    ErrorCode ERROR_5005 = new ErrorCode(5005, "密码不能为空");
    ErrorCode ERROR_5006 = new ErrorCode(5006, "邮箱验证码不能为空");
    ErrorCode ERROR_5007 = new ErrorCode(5007, "密码长度为{min}-{max}位");
    ErrorCode ERROR_5008 = new ErrorCode(5008, "密码必须包含数字、字母、特殊字符中的两种");
    ErrorCode ERROR_5009 = new ErrorCode(5009, "应用ID不能为空");
    ErrorCode ERROR_5010 = new ErrorCode(5010, "周期不能为空");
    ErrorCode ERROR_5011 = new ErrorCode(5011, "appId不能为空");
    ErrorCode ERROR_5012 = new ErrorCode(5012, "用户名不能为空");
    ErrorCode ERROR_5013 = new ErrorCode(5013, "查询不能为空");
    ErrorCode ERROR_5014 = new ErrorCode(5014, "模式不能为空");
    ErrorCode ERROR_5015 = new ErrorCode(5015, "任务Id不能为空");
    ErrorCode ERROR_5016 = new ErrorCode(5016, "消息ID不能为空");
    ErrorCode ERROR_5017 = new ErrorCode(5017, "会话ID不能为空");
    ErrorCode ERROR_5018 = new ErrorCode(5018, "会话名称不能为空");
    ErrorCode ERROR_5019 = new ErrorCode(5019, "文件不能为空");
    ErrorCode ERROR_5020 = new ErrorCode(5020, "三方用户ID不能为空");
    ErrorCode ERROR_5021 = new ErrorCode(5021, "三方用户ID不能为空");
    ErrorCode ERROR_5022 = new ErrorCode(5022, "三方用户openid不能为空");
    ErrorCode ERROR_5023 = new ErrorCode(5023, "用户名不能为空");
    ErrorCode ERROR_5024 = new ErrorCode(5024, "邮箱不能为空");
    ErrorCode ERROR_5025 = new ErrorCode(5025, "文件格式不正确");
    ErrorCode ERROR_5026 = new ErrorCode(5026, "文件大小不能超过{0}");
    ErrorCode ERROR_5027 = new ErrorCode(5027, "应用已下架，请使用其他应用");
    ErrorCode ERROR_5028 = new ErrorCode(5028, "已订阅，请直接使用");
    ErrorCode ERROR_5029 = new ErrorCode(5029, "用户不存在，请联系管理员");
    ErrorCode ERROR_5030 = new ErrorCode(5030, "已支付，请直接使用");
    ErrorCode ERROR_5031 = new ErrorCode(5031, "订阅失败，请稍后再试");
    ErrorCode ERROR_5032 = new ErrorCode(5032, "未订阅或已取消");
    ErrorCode ERROR_5033 = new ErrorCode(5033, "仅支持人民币");
    ErrorCode ERROR_5034 = new ErrorCode(5034, "创建订单失败，请稍后再试");
    ErrorCode ERROR_5035 = new ErrorCode(5035, "订单不存在");
    ErrorCode ERROR_5036 = new ErrorCode(5036, "应用未开放对外访问，请联系管理员");
    ErrorCode ERROR_5037 = new ErrorCode(5037, "订单信息不匹配，请联系管理员");
    ErrorCode ERROR_5038 = new ErrorCode(5038, "请求失败，请联系管理员");
    ErrorCode ERROR_5039 = new ErrorCode(5039, "请求返回失败，请联系管理员");
    ErrorCode ERROR_5040 = new ErrorCode(5040, "已注册Facebook，请切换使用Facebook账号登录");
    ErrorCode ERROR_5041 = new ErrorCode(5041, "已注册Google，请切换使用Google账号登录");
    ErrorCode ERROR_5042 = new ErrorCode(5042, "错误次数太多，请稍后再试");
    ErrorCode ERROR_5043 = new ErrorCode(5043, "邮箱或密码错误，1小时内还可以尝试{}次");
    ErrorCode ERROR_5044 = new ErrorCode(5044, "Stripe价格不存在，请联系管理员");
    ErrorCode ERROR_5045 = new ErrorCode(5045, "请求过于频繁，请稍后重试");
    ErrorCode ERROR_5046 = new ErrorCode(5046, "文章ID不能为空");
    ErrorCode ERROR_5047 = new ErrorCode(5047, "AI使用权限已过期！继续使用请联系购买。");
    ErrorCode ERROR_5048 = new ErrorCode(5048, "正在努力输出中，请等待或重新继续追问~");
    ErrorCode ERROR_5049 = new ErrorCode(5049, "请输入您的问题");
    ErrorCode ERROR_5050 = new ErrorCode(5050, "订阅应用ID不能为空或订阅套餐不能为空");
    ErrorCode ERROR_5051 = new ErrorCode(5051, "已退订，需要最后一期完成才能再次订阅");
    ErrorCode ERROR_5052 = new ErrorCode(5052, "试用次数已用完，请购买后使用");
    ErrorCode ERROR_5053 = new ErrorCode(5053, "验证码错误");
    ErrorCode ERROR_5054 = new ErrorCode(5054, "仅限邀请用户体验，请联系管理员提交申请");
    ErrorCode ERROR_5055 = new ErrorCode(5055, "体验时间已截止，请使用其他应用");

}
