package cn.iocoder.yudao.aiBase.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 用于配置服务端渲染相关设置
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // 如果需要处理根路径路由到特定页面
        registry.addViewController("/").setViewName("forward:/index.html");
    }
    
    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        // 确保支持HTML响应
        configurer.defaultContentType(MediaType.APPLICATION_JSON)
                .mediaType("json", MediaType.APPLICATION_JSON) // 支持 JSON
                .mediaType("stream", MediaType.APPLICATION_OCTET_STREAM)
                .mediaType("html", MediaType.TEXT_HTML); // 支持 HTML
    }
} 