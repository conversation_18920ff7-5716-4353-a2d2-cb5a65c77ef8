package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.config.ErrorCodeConstants;
import cn.iocoder.yudao.aiBase.dto.request.AppUsageByMonthRequest;
import cn.iocoder.yudao.aiBase.dto.request.AppUsageRequest;
import cn.iocoder.yudao.aiBase.service.AiAppUsageRecordsService;
import cn.iocoder.yudao.aiBase.service.OauthService;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static org.springframework.http.HttpStatus.UNAUTHORIZED;

/**
 * 应用使用记录 Controller
 */
@Tag(name = "应用使用记录")
@RestController
@RequestMapping("/ai-base/app-use")
public class AiAppUsageController {

    @Autowired
    private AiAppUsageRecordsService aiAppUsageRecordsService;
    
    @Autowired
    private OauthService oauthService;
    
    @PostMapping("/record")
    @Operation(summary = "记录应用使用")
    public CommonResult<Boolean> recordAppUsage(@RequestHeader(value= HttpHeaders.AUTHORIZATION) String auth,
                                              @Valid @RequestBody AppUsageRequest request) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessToken(auth);
        if (authUser == null) {
            return CommonResult.error(UNAUTHORIZED.value(), "账号未登录");
        }

        // 从authUser获取socialUserId和socialType
        Long socialUserId = authUser.getUserId();
        Integer socialType = authUser.getUserType();

        // 使用新的Service方法记录应用使用
        int result = aiAppUsageRecordsService.recordAppUsage(socialUserId, socialType, request.getAppUuid(), request.getRequestId());

        // 根据返回结果响应不同的信息
        if (result == 0) {
            return success(true);
        } else if (result == -1) {
            return CommonResult.error(ErrorCodeConstants.ERROR_5052);
        } else {
            return CommonResult.error(500, "记录应用使用失败");
        }
    }
    
    @PostMapping("/count")
    @Operation(summary = "查询应用使用次数")
    public CommonResult<Integer> countAppUsage(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth,
                                             @Valid @RequestBody AppUsageRequest request) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessToken(auth);
        if (authUser == null) {
            return CommonResult.error(UNAUTHORIZED.value(), "账号未登录");
        }

        // 从authUser获取socialUserId和socialType
        Long socialUserId = authUser.getUserId();
        Integer socialType = authUser.getUserType();

        int count = aiAppUsageRecordsService.countUserAppUsage(socialUserId, socialType, request.getAppUuid());
        return success(count);
    }
    
    @PostMapping("/count/month")
    @Operation(summary = "查询指定月份的应用使用次数")
    public CommonResult<Integer> countAppUsageByMonth(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth,
                                                  @Valid @RequestBody AppUsageByMonthRequest request) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessToken(auth);
        if (authUser == null) {
            return CommonResult.error(UNAUTHORIZED.value(), "账号未登录");
        }

        // 从authUser获取socialUserId和socialType
        Long socialUserId = authUser.getUserId();
        Integer socialType = authUser.getUserType();

        // 解析年月字符串为YearMonth对象
        YearMonth yearMonth = YearMonth.parse(request.getYearMonth(), DateTimeFormatter.ofPattern("yyyy-MM"));

        int count = aiAppUsageRecordsService.countUserAppUsageByMonth(socialUserId, socialType, request.getAppUuid(), yearMonth);
        return success(count);
    }
    
    @PostMapping("/remaining")
    @Operation(summary = "查询剩余使用次数")
    public CommonResult<Integer> getRemainingCount(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth,
                                                 @Valid @RequestBody AppUsageRequest request) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessToken(auth);
        if (authUser == null) {
            return CommonResult.error(UNAUTHORIZED.value(), "账号未登录");
        }

        // 从authUser获取socialUserId和socialType
        Long socialUserId = authUser.getUserId();
        Integer socialType = authUser.getUserType();

        int remaining = aiAppUsageRecordsService.getRemainingUsageCount(socialUserId, socialType, request.getAppUuid());
        return success(remaining);
    }

    @PostMapping("/check")
    @Operation(summary = "检查是否可以使用应用")
    public CommonResult<Boolean> checkCanUseApp(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth,
                                              @Valid @RequestBody AppUsageRequest request) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessToken(auth);
        if (authUser == null) {
            return CommonResult.error(UNAUTHORIZED.value(), "账号未登录");
        }

        // 从authUser获取socialUserId和socialType
        Long socialUserId = authUser.getUserId();
        Integer socialType = authUser.getUserType();

        boolean canUse = aiAppUsageRecordsService.checkUserCanUseApp(socialUserId, socialType, request.getAppUuid());
        return success(canUse);
    }
    
    @GetMapping("/free-limit")
    @Operation(summary = "获取免费使用次数限制")
    public CommonResult<Integer> getFreeLimit() {
        int limit = aiAppUsageRecordsService.getFreeAppUsageLimit();
        return success(limit);
    }
    
    @PostMapping("/refresh-cache")
    @Operation(summary = "刷新免费使用次数配置缓存", description = "管理员使用，当配置变更后调用此接口刷新缓存")
    public CommonResult<Boolean> refreshCache(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessToken(auth);
        if (authUser == null) {
            return CommonResult.error(UNAUTHORIZED.value(), "账号未登录");
        }
        
        // 在实际应用中，这里应该检查用户是否有管理员权限
        // 为简化实现，当前版本不做权限检查
        
        aiAppUsageRecordsService.clearFreeAppUsageLimitCache();
        return success(true);
    }
} 