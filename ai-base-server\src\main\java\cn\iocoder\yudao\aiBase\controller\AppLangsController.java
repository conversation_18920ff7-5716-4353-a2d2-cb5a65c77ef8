package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.dto.request.AppLangReqVO;
import cn.iocoder.yudao.aiBase.dto.request.HandleTaskReqVO;
import cn.iocoder.yudao.aiBase.dto.request.UpdatePriceIdReqVO;
import cn.iocoder.yudao.aiBase.dto.response.SubscriptionCountResponse;
import cn.iocoder.yudao.aiBase.service.AiAppLangsService;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "管理后台 - 应用语言版本")
@RestController
@RequestMapping("/admin-api/appLangs")
@Validated
public class AppLangsController {

    @Resource
    private AiAppLangsService aiAppLangsService;

    @PostMapping("/createProduct")
    @Operation(summary = "关联国际支付产品")
    public CommonResult<Boolean> createProduct(@Valid @RequestBody AppLangReqVO appLangReqVO) {
        aiAppLangsService.createProduct(appLangReqVO);
        return CommonResult.success(true);
    }

    @PostMapping("/initProject")
    @Operation(summary = "初始化国际支付产品及价格")
    public CommonResult<Boolean> initProject(@Valid @RequestBody AppLangReqVO appLangReqVO) {
        aiAppLangsService.initProject(appLangReqVO.getAppUuid());
        return CommonResult.success(true);
    }

    @GetMapping("/countSubscriptions")
    @Operation(summary = "查询订阅统计")
    public CommonResult<List<SubscriptionCountResponse>> countSubscriptions() {
        List<SubscriptionCountResponse> subscriptions = aiAppLangsService.countSubscriptions();
        return CommonResult.success(subscriptions);
    }

    @PostMapping("/updatePriceId")
    @Operation(summary = "更新应用priceID")
    public CommonResult<Integer> updatePriceId(@Valid @RequestBody UpdatePriceIdReqVO reqVO) {
        return CommonResult.success(aiAppLangsService.updatePriceId(reqVO));
    }

    @PostMapping("/handleTask")
    @Operation(summary = "手动更新订阅")
    public CommonResult<Boolean> handleTask(@Valid @RequestBody HandleTaskReqVO reqVO) {
        aiAppLangsService.handleTask(reqVO.getToday().atStartOfDay());
        return CommonResult.success(true);
    }


}
