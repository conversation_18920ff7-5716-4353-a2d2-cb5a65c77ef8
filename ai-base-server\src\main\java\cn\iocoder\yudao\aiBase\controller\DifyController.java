package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.ErrorCodeConstants;
import cn.iocoder.yudao.aiBase.dto.param.ApiTokensParam;
import cn.iocoder.yudao.aiBase.dto.request.dify.*;
import cn.iocoder.yudao.aiBase.dto.response.AppBaseResponse;
import cn.iocoder.yudao.aiBase.dto.response.DifyBaseResponse;
import cn.iocoder.yudao.aiBase.dto.response.MyCollectionResponse;
import cn.iocoder.yudao.aiBase.entity.ApiTokens;
import cn.iocoder.yudao.aiBase.service.*;
import cn.iocoder.yudao.aiBase.util.CommonUtil;
import cn.iocoder.yudao.aiBase.util.DifyUtil;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 测试接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ai-base/v1")
@Tag(name = "dify接口")
public class DifyController {


    @Autowired
    private ApiTokensService apiTokensService;

    @Autowired
    private MedsciUsersService medsciUsersService;

    @Autowired
    private OauthService oauthService;

    @Autowired
    private YudaoSystemService yudaoSystemService;

    @Autowired
    private AiAppUserPackageService aiAppUserPackageService;

    /**
     * 测试接口
     * @param param
     * @return
     */
    @PostMapping("/test")
    @Operation(summary = "测试接口")
    public CommonResult<?> test(@RequestBody ApiTokensParam param) {
        List<ApiTokens> res = apiTokensService.getList(param);

        return CommonResult.success(res);
    }

    @PostMapping(value = "/chat-messages")
    @Operation(summary = "创建会话消息")
    public Object chatMsg(@RequestHeader(value= HttpHeaders.AUTHORIZATION) String auth,
                          @Valid @RequestBody ChatMessagesRequest param) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

        ErrorCode errorCode = aiAppUserPackageService.preCheck(authUser, param.getAppUuid(), param.getRequestId());

        param.setUser(medsciUsersService.getUserName(authUser));
        if (DifyUtil.STREAMING_MODE.equals(param.getResponse_mode())) {
            if (errorCode == null) {
                // 使用一个线程或反应式流来订阅第三方SSE事件并转发到前端
                Flux<ServerSentEvent> flux = apiTokensService.chatMsg(param);
                SseEmitter emitter = apiTokensService.getEmitter(flux);
                return emitter;
            }
            return apiTokensService.getEmitter(null, errorCode);
        } else {
            if (errorCode == null) {
                JSONObject res = apiTokensService.chatMsg1(param);
                return CommonResult.success(res);
            }
            return CommonResult.error(errorCode);
        }
    }

    /**
     * 停止响应
     * @param param
     * @return
     */
    @PostMapping("/chat-messages/{task_id}/stop")
    @Operation(summary = "停止响应，streaming才可以调用此接口，通过 task_id")
    public CommonResult<?> stopChat(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth,
                                    @PathVariable("task_id") String taskId, @Valid @RequestBody StopChatRequest param) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

        try {
            param.setUser(medsciUsersService.getUserName(authUser));
            param.setTask_id(taskId);
            JSONObject res = apiTokensService.stopChat(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 消息反馈（点赞）
     * @param param
     * @return
     */
    @PostMapping("/messages/{message_id}/feedbacks")
    @Operation(summary = "消息反馈（点赞）")
    public CommonResult<?> feedback(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth,
                                    @PathVariable("message_id") String messageId, @Valid @RequestBody FeedbackRequest param) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

        try {
            param.setUser(medsciUsersService.getUserName(authUser));
            param.setMessage_id(messageId);
            JSONObject res = apiTokensService.feedback(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 获取下一轮建议问题列表
     * @return
     */
    @GetMapping("/messages/{message_id}/suggested")
    @Operation(summary = "获取指定消息的下一轮建议问题列表")
    public CommonResult<?> suggested(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth,
                                     @RequestParam(value="appId") String appId,
                                     @PathVariable("message_id") String messageId) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

        try {
            SuggestedRequest param = new SuggestedRequest();
            param.setMessage_id(messageId);
            param.setAppId(appId);
            param.setUser(medsciUsersService.getUserName(authUser));
            JSONObject res = apiTokensService.suggested(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }


    /**
     * 获取会话历史消息
     * @return
     */

    @GetMapping("/messages")
    @Operation(summary = "获取会话历史消息")
    public CommonResult<?> messages(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth,
                                    @RequestParam(value="appId") String appId,
                                    @RequestParam(value = "conversation_id") String conversation_id,
                                    @RequestParam(value = "first_id", required = false) String first_id,
                                    @RequestParam(value = "limit", required = false, defaultValue = "20") Integer limit) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

        try {
            MessagesRequest param = new MessagesRequest();
            param.setConversation_id(conversation_id);
            param.setFirst_id(first_id==null? BaseConstant.EMPTY_STR :first_id);
            param.setLimit(limit);
            param.setAppId(appId);
            param.setUser(medsciUsersService.getUserName(authUser));

            JSONObject res = apiTokensService.messages(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }


    /**
     * 获取会话列表
     * @return
     */
    @GetMapping("/conversations")
    @Operation(summary = "获取会话列表")
    public CommonResult<?> getConversations(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth,
                                            @RequestParam(value="appId") String appId,
                                            @RequestParam(value = "last_id", required = false) String last_id,
                                            @RequestParam(value = "limit", required = false, defaultValue = "20") Integer limit) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

        try {
            ConversationsRequest param = new ConversationsRequest();
            param.setLast_id(last_id==null? BaseConstant.EMPTY_STR :last_id);
            param.setLimit(limit);
            param.setSort_by("-updated_at");
            param.setAppId(appId);
            param.setUser(medsciUsersService.getUserName(authUser));

            JSONObject res = apiTokensService.conversations(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 删除会话
     * @return
     */
    @DeleteMapping("/conversations/{conversation_id}")
    @Operation(summary = "删除指定会话")
    public CommonResult<?> deleteConversationById(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth,
                                                  @RequestParam(value="appId") String appId,
                                                  @PathVariable("conversation_id") String conversationId) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

        try {
            DeleteConversationsRequest param = new DeleteConversationsRequest();
            param.setConversation_id(conversationId);
            param.setAppId(appId);
            param.setUser(medsciUsersService.getUserName(authUser));

            Boolean res = apiTokensService.deleteConverse(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 会话重命名
     * @param param
     * @return
     */
    @PostMapping("/conversations/{conversation_id}/name")
    @Operation(summary = "重命名指定会话")
    public CommonResult<?> renameConversationById(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth,
                                                  @PathVariable("conversation_id") String conversationId,
                                                  @Valid @RequestBody RenameRequest param) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

        try {
            param.setConversation_id(conversationId);
            JSONObject res = apiTokensService.renameConverse(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 获取应用配置信息
     * @return
     */
    @GetMapping("/parameters")
    @Operation(summary = "获取应用配置信息")
    public CommonResult<?> parameters(@RequestParam(value = "appId") String appId) {
        try {
            DifyBaseRequest param = new DifyBaseRequest();
            param.setAppId(appId);
            param.setUser("nologin");

            JSONObject res = apiTokensService.parameters(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 执行workflow
     * @param param
     * @return
     */
    @PostMapping(value = "/workflows/run")
    @Operation(summary = "执行workflow")
    public Object workflowsRun(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth, @Valid @RequestBody WorkflowsRunRequest param) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

        ErrorCode errorCode = aiAppUserPackageService.preCheck(authUser, param.getAppUuid(), param.getRequestId());

        param.setUser(medsciUsersService.getUserName(authUser));
        if (DifyUtil.STREAMING_MODE.equals(param.getResponse_mode())) {
            if (errorCode == null) {
                // 使用一个线程或反应式流来订阅第三方SSE事件并转发到前端
                Flux<ServerSentEvent> flux = apiTokensService.workflowsRun(param);
                SseEmitter emitter = apiTokensService.getEmitter(flux);
                return emitter;
            }
            return apiTokensService.getEmitter(null, errorCode);
        } else {
            if (errorCode == null) {
                JSONObject res = apiTokensService.workflowsRun1(param);
                return CommonResult.success(res);
            }
            return CommonResult.error(errorCode);
        }
    }

    /**
     * 流模式，执行文本完成
     * @param param
     * @return
     */
    @PostMapping(value = "/completion-messages")
    @Operation(summary = "执行文本完成")
    public Object completionMsg(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth, @Valid @RequestBody WorkflowsRunRequest param) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

        ErrorCode errorCode = aiAppUserPackageService.preCheck(authUser, param.getAppUuid(), param.getRequestId());

        param.setUser(medsciUsersService.getUserName(authUser));
        if (DifyUtil.STREAMING_MODE.equals(param.getResponse_mode())) {
            if (errorCode == null) {
                // 使用一个线程或反应式流来订阅第三方SSE事件并转发到前端
                Flux<ServerSentEvent> flux = apiTokensService.completionMsg(param);
                SseEmitter emitter = apiTokensService.getEmitter(flux);
                return emitter;
            }
            return apiTokensService.getEmitter(null, errorCode);
        } else {
            if (errorCode == null) {
                JSONObject res = apiTokensService.completionMsg1(param);
                return CommonResult.success(res);
            }
            return CommonResult.error(errorCode);
        }
    }

    /**
     * 文件上传
     * @param uploadReqVO
     * @return
     */
    @PostMapping("/files/upload")
    @Operation(summary = "上传文件", description = "上传文件")
    public CommonResult<?> filesUpload(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth, @Valid FilesUploadRequest uploadReqVO) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

        String ext = CommonUtil.getFileExtension(uploadReqVO.getFile().getOriginalFilename());
        if (StringUtils.isEmpty(ext)) {
            throw exception(ErrorCodeConstants.ERROR_5025);
        }

        Integer MAX_FILE_SIZE = yudaoSystemService.getUploadLimitSize(ext.toLowerCase());
        if (uploadReqVO.getFile().getSize() > MAX_FILE_SIZE*1024*1024) {
            return CommonResult.error(ErrorCodeConstants.ERROR_5026.getCode(), MAX_FILE_SIZE+"MB");
        }
        uploadReqVO.setUser(medsciUsersService.getUserName(authUser));
        return success(apiTokensService.filesUpload(uploadReqVO));
    }

    /**
     * 获取meta
     * @return
     */
    @GetMapping(value = "/meta")
    @Operation(summary = "获取meta")
    public CommonResult<?> meta(@RequestParam(value = "appId") String appId) {
        try {
            DifyBaseRequest param = new DifyBaseRequest();
            param.setAppId(appId);
            param.setUser("nologin");
            JSONObject res = apiTokensService.meta(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 停止响应
     * @param param
     * @return
     */
    @PostMapping("/workflows/tasks/{task_id}/stop")
    @Operation(summary = "停止响应，streaming才可以调用此接口，通过 task_id")
    public CommonResult<?> stopWorkflow(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth,
                                    @PathVariable("task_id") String taskId, @Valid @RequestBody StopChatRequest param) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

        try {
            param.setUser(medsciUsersService.getUserName(authUser));
            param.setTask_id(taskId);
            param.setMode("workflow");
            JSONObject res = apiTokensService.stopChat(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 停止响应
     * @param param
     * @return
     */
    @PostMapping("/completion-messages/{task_id}/stop")
    @Operation(summary = "停止响应，streaming才可以调用此接口，通过 task_id")
    public CommonResult<?> stopCompletion(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth,
                                   @PathVariable("task_id") String taskId, @Valid @RequestBody StopChatRequest param) {
        OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

        try {
            param.setUser(medsciUsersService.getUserName(authUser));
            param.setTask_id(taskId);
            param.setMode("completion");
            JSONObject res = apiTokensService.stopChat(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }






    /**
     * 获取app配置-数组
     * @param param
     * @return
     */
    @PostMapping("/appsParams1")
    @Operation(summary = "获取apps配置1")
    public CommonResult<?> appsParams1(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth, @Valid @RequestBody DifyBaseRequest param) {
        try {
            OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

            List<DifyBaseResponse> res = apiTokensService.getAppsPrePrompt(param.getAppId());
            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(ErrorCodeConstants.ERROR_5036);
        }
    }

    /**
     * 我的收藏
     * @param param
     * @return
     */
    @PostMapping("/myCollection")
    @Operation(summary = "我的收藏")
    public CommonResult<?> myCollection(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth, @Valid @RequestBody MyCollectionRequest param) {
        try {
            OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

            List<MyCollectionResponse> res = apiTokensService.myCollection(param);
            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 根据标签获取应用
     * @param tag
     * @return
     */
    @GetMapping(value = "/getAppsByTag")
    @Operation(summary = "根据标签获取应用")
    public CommonResult<?> getAppsByTag(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth, @RequestParam(value = "tag") String tag) {
        try {
            OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

            List<AppBaseResponse> res = apiTokensService.getAppsByTag(tag);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(ErrorCodeConstants.ERROR_5036);
        }
    }

    /**
     * 根据名称获取应用
     * @param param
     * @return
     */
    @PostMapping("/getAppsByName")
    @Operation(summary = "根据名称获取应用")
    public CommonResult<?> getAppsByName(@Valid @RequestBody GetAppsRequest param) {
        try {
            List<DifyBaseResponse> res = apiTokensService.getAppsByName(param.getName());
            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(ErrorCodeConstants.ERROR_5036);
        }
    }

    /**
     * 获取token
     * @param appId
     * @return
     */
    @GetMapping(value = "/getToken")
    @Operation(summary = "获取token")
    public CommonResult<?> getToken(@RequestHeader(value=HttpHeaders.AUTHORIZATION) String auth, @RequestParam(value = "appId") String appId) {
        try {
            OAuth2AccessTokenCheckRespDTO authUser = oauthService.checkAccessTokenOrError(auth);

            apiTokensService.getToken(appId);

            return CommonResult.success(true);
        } catch (Exception e) {
            return CommonResult.success(false);
        }
    }

    /**
     * 执行workflow
     * @param param
     * @return
     */
    @PostMapping(value = "/workflows_run")
    @Operation(summary = "执行workflow")
    public Object workflowsRun(@Valid @RequestBody WorkflowsRunRequest param, HttpServletResponse response) {
        if (DifyUtil.STREAMING_MODE.equals(param.getResponse_mode())) {
            param.setUser(param.getUser());
            // 使用一个线程或反应式流来订阅第三方SSE事件并转发到前端
            Flux<ServerSentEvent> flux = apiTokensService.workflowsRun(param);
            SseEmitter emitter = apiTokensService.getEmitter(flux);
            return emitter;
        } else {
            try {
                JSONObject res = apiTokensService.workflowsRun1(param);
                return CommonResult.success(res);
            } catch (Exception e) {
                return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
            }
        }
    }





}
