package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.dto.request.difyStatistics.DifyStatisticsMessageReqVo;
import cn.iocoder.yudao.aiBase.dto.response.DifyStatisticsMessageResponse;
import cn.iocoder.yudao.aiBase.service.DifyStatisticsService;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin-api/dify-statistics")
@Tag(name = "Dify统计")
public class DifyStatisticsController {
    @Autowired
    private DifyStatisticsService difyStatisticsService;

    @PostMapping("/messages")
    public CommonResult<PageResult<DifyStatisticsMessageResponse>> getMessages(
        @RequestBody DifyStatisticsMessageReqVo reqVO) {
        PageResult<DifyStatisticsMessageResponse> pageResult = difyStatisticsService.getMessages(reqVO);
        return CommonResult.success(pageResult);
    }
}
