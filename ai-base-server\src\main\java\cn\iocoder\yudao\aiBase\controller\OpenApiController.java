package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.dto.request.dify.WorkflowsRunRequest;
import cn.iocoder.yudao.aiBase.service.ApiTokensService;
import cn.iocoder.yudao.aiBase.util.DifyUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import java.net.URI;
import java.util.Enumeration;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;

/**
 * OpenApi通用转发接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/ai-base/openapi")
@Tag(name = "OpenApi通用转发接口")
@PermitAll
public class OpenApiController {

    @Value("${dify-base.openapi-host}")
    private String openapiHost;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ApiTokensService apiTokensService;
    
    @PostConstruct
    public void init() {
        // 配置RestTemplate
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        // 设置连接超时为180秒
        factory.setConnectTimeout(180000);
        // 不使用setReadTimeout，因为在当前版本不可用
        restTemplate.setRequestFactory(factory);
        restTemplate.setErrorHandler(new DefaultResponseErrorHandler());
    }
    
    /**
     * OpenApi通用转发接口
     * 
     * @param request HTTP请求
     * @return 响应结果
     */
    @RequestMapping(value = "/**", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE})
    @Operation(summary = "OpenApi通用转发接口")
    @PermitAll
    public Object proxyRequest(
                             HttpServletRequest request) {
        try {
            // 获取请求路径
            String requestUri = request.getRequestURI();
            String contextPath = "/ai-base/openapi";
            String path = requestUri.substring(requestUri.indexOf(contextPath) + contextPath.length());
            
            // 确保path以/开头
            if (!path.startsWith("/")) {
                path = "/" + path;
            }
            
            // 构建目标URL - 确保没有双斜杠
            String baseUrl = openapiHost;
            if (baseUrl.endsWith("/")) {
                baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
            }
            String targetUrl = baseUrl + path;
            
            // 添加查询参数
            String queryString = request.getQueryString();
            if (queryString != null && !queryString.isEmpty()) {
                targetUrl = targetUrl + "?" + queryString;
            }
            
            URI uri = URI.create(targetUrl);
            
            // 复制请求头
            HttpHeaders headers = new HttpHeaders();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                // 过滤掉一些不需要转发的请求头
                if (!headerName.equalsIgnoreCase("host") && !headerName.equalsIgnoreCase("content-length")) {
                    headers.set(headerName, request.getHeader(headerName));
                }
            }
            
            // 记录请求头信息
            if (log.isDebugEnabled()) {
                StringBuilder headerLog = new StringBuilder("请求头信息:\n");
                headers.forEach((name, values) -> {
                    headerLog.append(name).append(": ").append(values).append("\n");
                });
                log.debug(headerLog.toString());
            }
            
            // 获取Content-Type
            String contentType = request.getContentType();
            
            // 创建请求实体
            HttpEntity<?> httpEntity;
            
            // 根据Content-Type处理不同类型的请求
            if (contentType != null) {
                // 处理表单数据 (application/x-www-form-urlencoded)
                if (contentType.contains(MediaType.APPLICATION_FORM_URLENCODED_VALUE)) {
                    MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
                    // 获取所有表单参数
                    Map<String, String[]> parameterMap = request.getParameterMap();
                    if (parameterMap != null) {
                        parameterMap.forEach((key, values) -> {
                            for (String value : values) {
                                formData.add(key, value);
                            }
                        });
                    }
                    log.info("处理表单数据 (application/x-www-form-urlencoded): {}", formData);
                    httpEntity = new HttpEntity<>(formData, headers);
                }
                // 处理文件上传 (multipart/form-data)
                else if (contentType.contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
                    if (request instanceof MultipartHttpServletRequest) {
                        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
                        MultiValueMap<String, Object> parts = new LinkedMultiValueMap<>();
                        
                        // 添加普通表单字段
                        multipartRequest.getParameterMap().forEach((key, values) -> {
                            for (String value : values) {
                                parts.add(key, value);
                            }
                        });
                        
                        // 添加文件字段
                        multipartRequest.getFileMap().forEach((name, file) -> {
                            parts.add(name, file.getResource());
                        });
                        
                        httpEntity = new HttpEntity<>(parts, headers);
                    } else {
                        // 如果不能处理为MultipartHttpServletRequest，则使用通用方法
                        byte[] body = StreamUtils.copyToByteArray(request.getInputStream());
                        httpEntity = new HttpEntity<>(body, headers);
                    }
                }
                // 处理JSON和其他类型
                else {
                    byte[] body = StreamUtils.copyToByteArray(request.getInputStream());
                    httpEntity = new HttpEntity<>(body, headers);
                }
            } else {
                // 没有Content-Type的情况
                if (request.getContentLength() > 0) {
                    byte[] body = StreamUtils.copyToByteArray(request.getInputStream());
                    httpEntity = new HttpEntity<>(body, headers);
                } else {
                    httpEntity = new HttpEntity<>(headers);
                }
            }
            
            // 获取请求方法
            String requestMethod = request.getMethod();
            HttpMethod method;
            if ("GET".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.GET;
            } else if ("POST".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.POST;
            } else if ("PUT".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.PUT;
            } else if ("DELETE".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.DELETE;
            } else if ("HEAD".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.HEAD;
            } else if ("OPTIONS".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.OPTIONS;
            } else if ("PATCH".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.PATCH;
            } else if ("TRACE".equalsIgnoreCase(requestMethod)) {
                method = HttpMethod.TRACE;
            } else {
                method = HttpMethod.GET;
            }
            
            // 发送请求
            try {
                log.info("转发请求到: {}, 方法: {}, Content-Type: {}", targetUrl, method, contentType);
                ResponseEntity<Object> responseEntity = restTemplate.exchange(uri, method, httpEntity, Object.class);
                log.info("转发请求成功，状态码: {}", responseEntity.getStatusCode());
                return responseEntity.getBody();
            } catch (Exception e) {
                log.error("OpenApi通用转发接口失败，目标URL: {}，错误: {}", targetUrl, e.getMessage(), e);
                
                // 提取并返回更详细的错误信息
                String errorMessage = e.getMessage();
                if (e instanceof org.springframework.web.client.HttpStatusCodeException) {
                    org.springframework.web.client.HttpStatusCodeException httpError = 
                        (org.springframework.web.client.HttpStatusCodeException) e;
                    errorMessage = "目标服务器返回错误 " + httpError.getStatusCode() + ": " + 
                        httpError.getResponseBodyAsString();
                }
                
                return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), errorMessage);
            }
        } catch (Exception e) {
            log.error("处理请求时发生错误", e);
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), "处理请求时发生错误: " + e.getMessage());
        }
    }

    /**
     * 执行workflow
     * @param param
     * @return
     */
    @PostMapping(value = "/workflows_run")
    @Operation(summary = "执行workflow")
    public Object workflowsRun(@Valid @RequestBody WorkflowsRunRequest param) {
        if (DifyUtil.STREAMING_MODE.equals(param.getResponse_mode())) {
            // 使用一个线程或反应式流来订阅第三方SSE事件并转发到前端
            Flux<ServerSentEvent> flux = apiTokensService.workflowsRun(param);
            SseEmitter emitter = apiTokensService.getEmitter(flux);
            return emitter;
        } else {
            try {
                JSONObject res = apiTokensService.workflowsRun1(param);
                return CommonResult.success(res);
            } catch (Exception e) {
                return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
            }
        }
    }
} 