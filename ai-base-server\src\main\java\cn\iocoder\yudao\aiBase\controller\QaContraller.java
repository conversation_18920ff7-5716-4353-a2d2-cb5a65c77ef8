package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.dto.param.QaParam;
import cn.iocoder.yudao.aiBase.dto.param.QaSaveParam;
import cn.iocoder.yudao.aiBase.dto.response.QaResponse;
import cn.iocoder.yudao.aiBase.entity.Qa;
import cn.iocoder.yudao.aiBase.service.QaService;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;

@RestController
@RequestMapping("/ai-base/index")
public class QaContraller {
    @Autowired
    private QaService qaService;
    @PostMapping(value = "/qa-list")
    public CommonResult<?> qaList(@RequestBody @Validated QaParam qaParam){
        try {
            List<Qa> select = qaService.select(qaParam);
            return CommonResult.success(BeanUtils.toBean(select, QaResponse.class));
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping(value = "/article/qa-save")
    @Operation(summary = "主站资讯问答存储接口")
    public CommonResult<?> articleQaSave(@Valid @RequestBody QaSaveParam param) {
        try {
            qaService.ruleSave(param);
            return CommonResult.success(true);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping(value = "/article/initEncryptionId")
    @Operation(summary = "加密id")
    public CommonResult<?> initEncryptionId() {
        try {
            qaService.initEncryptionId();
            return CommonResult.success(true);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }
}
