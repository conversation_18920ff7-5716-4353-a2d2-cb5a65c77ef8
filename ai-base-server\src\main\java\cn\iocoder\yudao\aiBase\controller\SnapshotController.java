package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.dto.response.PushArticleResponse;
import cn.iocoder.yudao.aiBase.service.AiAppLangsService;
import cn.iocoder.yudao.aiBase.service.WorkflowRunsPushArticleService;
import cn.iocoder.yudao.aiBase.task.SnapshotTask;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vladsch.flexmark.html.HtmlRenderer;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.util.ast.Node;
import com.vladsch.flexmark.util.data.MutableDataSet;
import com.vladsch.flexmark.ext.tables.TablesExtension;
import com.vladsch.flexmark.ext.gfm.strikethrough.StrikethroughExtension;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.Arrays;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.*;

/**
 * 快照任务控制器
 * 提供手动触发快照生成的接口
 */
@Tag(name = "管理后台 - 快照任务")
@RestController
@RequestMapping("/ai-base/index/snapshot")
@Validated
@Slf4j
public class SnapshotController {

    @Autowired
    private SnapshotTask snapshotTask;

    @Autowired
    private WorkflowRunsPushArticleService workflowRunsPushArticleService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private AiAppLangsService aiAppLangsService;

    @Value("${article.api.url}")
    private String articleApiBaseUrl;

    /**
     * 手动触发快照生成（用于测试）
     * 
     * @return 生成结果信息
     */
    @PostMapping("/generate")
    @Operation(summary = "手动触发快照生成")
    public CommonResult<String> manualGenerateSnapshot() {
        log.info("手动触发快照生成接口被调用");
        // 异步执行快照生成任务，不阻塞当前请求
        CompletableFuture.runAsync(() -> {
            log.info("异步执行快照生成任务开始");
            snapshotTask.manualGenerateSnapshot();
            log.info("异步执行快照生成任务结束");
        });
        return CommonResult.success("快照生成任务已触发，正在后台执行");
    }

    @GetMapping("/getArticle")
    @Operation(summary = "获取主站资讯详情")
    public CommonResult<PushArticleResponse> getArticle(@RequestParam("id") UUID id) {
        PushArticleResponse res = workflowRunsPushArticleService.getArticle(id);
        return CommonResult.success(res);
    }

    /**
     * 从主站获取文章详情
     * @param articleIds 文章ID列表
     * @param recordUuids 记录UUID列表（用于前端跳转）
     * @return 文章详情数据列表
     */
    private List<Map<String, Object>> fetchArticlesFromMainSite(List<String> articleIds, List<String> recordUuids) {
        List<Map<String, Object>> results = new ArrayList<>();
        
        if (articleIds == null || articleIds.isEmpty()) {
            return results;
        }
        
        // 创建并行处理任务
        List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();
        
        for (int i = 0; i < articleIds.size(); i++) {
            String articleId = articleIds.get(i);
            String recordUuid = (recordUuids != null && i < recordUuids.size()) ? recordUuids.get(i) : null;
            
            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    // 调用主站接口
                    String url = articleApiBaseUrl + "/api/mg/information/article/getArticleById";
                    
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    
                    Map<String, Object> requestBody = new HashMap<>();
                    requestBody.put("projectId", 1);
                    requestBody.put("id", articleId);
                    
                    RestTemplate restTemplate = new RestTemplate();
                    HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
                    
                    ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
                    
                    if (response.getStatusCode().is2xxSuccessful() && 
                        response.getBody() != null && 
                        response.getBody().get("data") != null) {
                        
                        Map<String, Object> fetchedArticleData = (Map<String, Object>) response.getBody().get("data");
                        // 添加UUID标识，用于前端跳转
                        if (recordUuid != null) {
                            fetchedArticleData.put("uuid", recordUuid);
                        }
                        return fetchedArticleData;
                    }
                } catch (Exception e) {
                    log.error("获取主站文章详情失败, article_id={}", articleId, e);
                }
                return null;
            });
            
            futures.add(future);
        }
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        // 处理结果
        for (CompletableFuture<Map<String, Object>> future : futures) {
            try {
                Map<String, Object> article = future.get();
                if (article != null) {
                    results.add(article);
                }
            } catch (Exception e) {
                log.error("获取文章详情结果失败", e);
            }
        }
        
        return results;
    }

    /**
     * 根据ID获取workflow_runs_push_article,把拿到的articleId去调主站后，合并拿表和拿接口的数据
     */
    @GetMapping("/getArticleWithSEO")
    @Operation(summary = "获取文章SEO数据")
    public CommonResult<Map<String, Object>> getArticleWithSEO(@RequestParam("id") String id) {
        try {
            long startTime = System.currentTimeMillis();
            // 验证UUID格式
            if (StringUtils.isBlank(id)) {
                return CommonResult.error(BAD_REQUEST.getCode(), "id参数不能为空");
            }
            
            UUID uuid;
            try {
                uuid = UUID.fromString(id);
            } catch (IllegalArgumentException e) {
                return CommonResult.error(BAD_REQUEST.getCode(), "无效的UUID格式");
            }
            
            // 1. 直接查询workflow_runs_push_article表数据
            String sql = "SELECT id, article_id, app_uuid, app_name, dify_app_id, dify_name, " +
                    "seo_title, seo_description, app_type, created_at, updated_at " +
                    "FROM ai_base.workflow_runs_push_article WHERE id = ?";
            
            Map<String, Object> seoData;
            String mainArticleId;
            try {
                // 使用JdbcTemplate直接查询数据
                seoData = jdbcTemplate.queryForMap(sql, uuid);
                if (seoData == null || seoData.isEmpty()) {
                    return CommonResult.error(NOT_FOUND.getCode(), "未找到对应的文章数据");
                }
                
                mainArticleId = (String) seoData.get("article_id");
                if (StringUtils.isBlank(mainArticleId)) {
                    return CommonResult.error(NOT_FOUND.getCode(), "文章ID为空");
                }
            } catch (Exception e) {
                log.error("查询workflow_runs_push_article表失败, id={}", id, e);
                return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), "查询SEO数据失败: " + e.getMessage());
            }
            
            // 2. 获取主站文章数据，以及最近和相关文章ID
            List<String> recentIds = new ArrayList<>();
            List<String> recentUuids = new ArrayList<>();
            List<String> relatedIds = new ArrayList<>();
            List<String> relatedUuids = new ArrayList<>();
            
            // 2a. 获取最近文章ID
            try {
                String recentSql = "SELECT id, article_id " +
                        "FROM ai_base.workflow_runs_push_article " +
                        "WHERE article_id IS NOT NULL AND article_id != '' " +
                        "ORDER BY created_at DESC LIMIT 3";
                
                List<Map<String, Object>> recentRecords = jdbcTemplate.queryForList(recentSql);
                
                for (Map<String, Object> record : recentRecords) {
                    String recentId = (String) record.get("article_id");
                    String recordId = record.get("id").toString();
                    
                    if (StringUtils.isNotBlank(recentId)) {
                        recentIds.add(recentId);
                        recentUuids.add(recordId);
                    }
                }
            } catch (Exception e) {
                log.error("查询最近文章失败", e);
            }
            
            // 2b. 获取相关文章ID
            try {
                String relatedSql = "SELECT id, article_id " +
                        "FROM ai_base.workflow_runs_push_article " +
                        "WHERE id != ? AND article_id IS NOT NULL AND article_id != '' " +
                        "ORDER BY RANDOM() LIMIT 3";
                
                List<Map<String, Object>> relatedRecords = jdbcTemplate.queryForList(relatedSql, uuid);
                
                for (Map<String, Object> record : relatedRecords) {
                    String relatedId = (String) record.get("article_id");
                    String recordId = record.get("id").toString();
                    
                    if (StringUtils.isNotBlank(relatedId)) {
                        relatedIds.add(relatedId);
                        relatedUuids.add(recordId);
                    }
                }
            } catch (Exception e) {
                log.error("查询相关文章失败", e);
            }
            
            // 3. 并行获取所有文章数据
            CompletableFuture<Map<String, Object>> mainArticleFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    // 调用主站接口
                    String url = articleApiBaseUrl + "/api/mg/information/article/getArticleById";
                    
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    
                    Map<String, Object> requestBody = new HashMap<>();
                    requestBody.put("projectId", 1);
                    requestBody.put("id", mainArticleId);
                    
                    RestTemplate restTemplate = new RestTemplate();
                    HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
                    
                    ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
                    
                    if (response.getStatusCode().is2xxSuccessful() && 
                        response.getBody() != null && 
                        response.getBody().get("data") != null) {
                        
                        return (Map<String, Object>) response.getBody().get("data");
                    }
                } catch (Exception e) {
                    log.error("获取主文章详情失败, article_id={}", mainArticleId, e);
                }
                return null;
            });
            
            CompletableFuture<List<Map<String, Object>>> recentArticlesFuture = CompletableFuture.supplyAsync(() -> 
                fetchArticlesFromMainSite(recentIds, recentUuids)
            );
            
            CompletableFuture<List<Map<String, Object>>> relatedArticlesFuture = CompletableFuture.supplyAsync(() -> 
                fetchArticlesFromMainSite(relatedIds, relatedUuids)
            );
            
            // 4. 获取工具图标和英文名称
            CompletableFuture<Map<String, String>> appInfoFuture = CompletableFuture.supplyAsync(() -> {
                Map<String, String> appInfo = new HashMap<>();
                if (seoData != null && seoData.containsKey("app_uuid")) {
                    String appUuid = (String) seoData.get("app_uuid");
                    if (StringUtils.isNotBlank(appUuid)) {
                        try {
                            String appInfoSql = "SELECT app_lang, app_icon, app_name_en FROM ai_base.ai_app_langs WHERE app_uuid = ? LIMIT 1";
                            List<Map<String, Object>> appInfoResult = jdbcTemplate.queryForList(appInfoSql, appUuid);

                            if (!appInfoResult.isEmpty()) {
                                Map<String, Object> result = appInfoResult.get(0);
                                if (result.containsKey("app_lang")) {
                                    appInfo.put("app_lang", (String) result.get("app_lang"));
                                }
                                if (result.containsKey("app_icon")) {
                                    appInfo.put("app_icon", (String) result.get("app_icon"));
                                }
                                if (result.containsKey("app_name_en")) {
                                    appInfo.put("app_name_en", (String) result.get("app_name_en"));
                                }
                            }
                        } catch (Exception e) {
                            log.error("获取应用信息失败, app_uuid={}", appUuid, e);
                        }
                    }
                }
                return appInfo;
            });
            
            // 5. 等待所有异步任务完成
            CompletableFuture.allOf(
                mainArticleFuture, 
                recentArticlesFuture, 
                relatedArticlesFuture,
                appInfoFuture
            ).join();
            
            // 6. 获取结果
            Map<String, Object> mainArticleData = mainArticleFuture.get();
            if (mainArticleData == null) {
                return CommonResult.error(NOT_FOUND.getCode(), "无法获取主站文章内容");
            }
            
            List<Map<String, Object>> recentArticles = recentArticlesFuture.get();
            List<Map<String, Object>> relatedArticles = relatedArticlesFuture.get();
            Map<String, String> appInfo = appInfoFuture.get();
            
            // 设置应用信息
            if (appInfo != null) {
                if (appInfo.containsKey("app_lang")) {
                    seoData.put("app_lang", appInfo.get("app_lang"));
                }
                if (appInfo.containsKey("app_icon")) {
                    seoData.put("app_icon", appInfo.get("app_icon"));
                }
                if (appInfo.containsKey("app_name_en")) {
                    seoData.put("app_name_en", appInfo.get("app_name_en"));
                }
            }
            
            // 7. 合并数据
            Map<String, Object> result = new HashMap<>();
            result.put("seo", seoData);
            result.put("article", mainArticleData);
            result.put("recentArticles", recentArticles);
            result.put("relatedArticles", relatedArticles);
            
            long endTime = System.currentTimeMillis();
            log.info("getArticleWithSEO处理时间: {}ms", (endTime - startTime));
            
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("获取文章数据失败, id={}", id, e);
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), "获取文章数据失败: " + e.getMessage());
        }
    }

    @GetMapping("/article/{id}")
    @ResponseBody
    public Object renderArticlePage(@PathVariable("id") String id, HttpServletResponse response) throws IOException {
        try {
            long startTime = System.currentTimeMillis();
            
            // 验证UUID格式
            if (StringUtils.isBlank(id)) {
                return renderErrorPage(response, "ID参数不能为空");
            }
            
            UUID uuid;
            try {
                uuid = UUID.fromString(id);
            } catch (IllegalArgumentException e) {
                return renderErrorPage(response, "无效的UUID格式");
            }
            
            // 1. 查询数据库获取SEO信息
            String sql = "SELECT id, article_id, app_uuid, app_name, dify_app_id, dify_name, " +
                    "seo_title, seo_description, app_type, created_at, updated_at " +
                    "FROM ai_base.workflow_runs_push_article WHERE id = ?";
            
            Map<String, Object> seoData;
            String mainArticleId;
            try {
                seoData = jdbcTemplate.queryForMap(sql, uuid);
                if (seoData == null || seoData.isEmpty()) {
                    return renderErrorPage(response, "未找到对应的文章数据");
                }
                
                mainArticleId = (String) seoData.get("article_id");
                if (StringUtils.isBlank(mainArticleId)) {
                    return renderErrorPage(response, "文章ID为空");
                }
            } catch (Exception e) {
                log.error("查询workflow_runs_push_article表失败, id={}", id, e);
                return renderErrorPage(response, "查询SEO数据失败: " + e.getMessage());
            }
            
            // 2. 获取最近文章和相关文章IDs
            List<String> recentIds = new ArrayList<>();
            List<String> recentUuids = new ArrayList<>();
            List<String> relatedIds = new ArrayList<>();
            List<String> relatedUuids = new ArrayList<>();
            
            // 获取最近文章IDs
            try {
                String recentSql = "SELECT id, article_id FROM ai_base.workflow_runs_push_article " +
                        "WHERE article_id IS NOT NULL AND article_id != '' " +
                        "ORDER BY created_at DESC LIMIT 3";
                
                List<Map<String, Object>> recentRecords = jdbcTemplate.queryForList(recentSql);
                for (Map<String, Object> record : recentRecords) {
                    String recentId = (String) record.get("article_id");
                    String recordId = record.get("id").toString();
                    
                    if (StringUtils.isNotBlank(recentId)) {
                        recentIds.add(recentId);
                        recentUuids.add(recordId);
                    }
                }
            } catch (Exception e) {
                log.error("查询最近文章失败", e);
            }
            
            // 获取相关文章IDs
            try {
                String relatedSql = "SELECT id, article_id FROM ai_base.workflow_runs_push_article " +
                        "WHERE id != ? AND article_id IS NOT NULL AND article_id != '' " +
                        "ORDER BY RANDOM() LIMIT 3";
                
                List<Map<String, Object>> relatedRecords = jdbcTemplate.queryForList(relatedSql, uuid);
                for (Map<String, Object> record : relatedRecords) {
                    String relatedId = (String) record.get("article_id");
                    String recordId = record.get("id").toString();
                    
                    if (StringUtils.isNotBlank(relatedId)) {
                        relatedIds.add(relatedId);
                        relatedUuids.add(recordId);
                    }
                }
            } catch (Exception e) {
                log.error("查询相关文章失败", e);
            }
            
            // 3. 获取主文章内容
            Map<String, Object> mainArticleData = fetchArticleData(mainArticleId);
            if (mainArticleData == null) {
                return renderErrorPage(response, "无法获取主站文章内容");
            }
            
            // 4. 获取最近文章和相关文章内容
            List<Map<String, Object>> recentArticles = fetchArticleList(recentIds, recentUuids);
            List<Map<String, Object>> relatedArticles = fetchArticleList(relatedIds, relatedUuids);
            
            // 5. 获取应用图标和英文名称
            Map<String, String> appInfo = fetchAppInfo((String) seoData.get("app_uuid"));
            if (appInfo != null) {
                if (appInfo.containsKey("app_lang")) {
                    seoData.put("app_lang", appInfo.get("app_lang"));
                }
                if (appInfo.containsKey("app_icon")) {
                    seoData.put("app_icon", appInfo.get("app_icon"));
                }
                if (appInfo.containsKey("app_name_en")) {
                    seoData.put("app_name_en", appInfo.get("app_name_en"));
                }
            }
            
            // 6. 处理SEO关键词
            List<String> keywords = parseKeywords(seoData.get("seo_description"));
            
            // 7. 渲染完整HTML页面
            String html = renderArticleHtml(seoData, mainArticleData, recentArticles, 
                                           relatedArticles, keywords);
            
            long endTime = System.currentTimeMillis();
            log.info("文章页面渲染时间: {}ms", (endTime - startTime));
            
            // 设置响应类型并输出HTML
            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().write(html);
            return null;
            
        } catch (IOException e) {
            log.error("渲染文章页面时发生IO异常, id={}", id, e);
            throw e; // 直接重新抛出IOException
        } catch (Exception e) {
            log.error("渲染文章页面失败, id={}", id, e);
            try {
                return renderErrorPage(response, "页面渲染失败: " + e.getMessage());
            } catch (IOException ioe) {
                log.error("渲染错误页面时发生IO异常", ioe);
                throw ioe; // 重新抛出IOException
            }
        }
    }

    // 辅助方法：获取单篇文章数据
    private Map<String, Object> fetchArticleData(String articleId) {
        try {
            String url = articleApiBaseUrl + "/api/mg/information/article/getArticleById";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("projectId", 1);
            requestBody.put("id", articleId);
            
            RestTemplate restTemplate = new RestTemplate();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && 
                response.getBody() != null && 
                response.getBody().get("data") != null) {
                
                return (Map<String, Object>) response.getBody().get("data");
            }
        } catch (Exception e) {
            log.error("获取文章详情失败, article_id={}", articleId, e);
        }
        return null;
    }

    // 辅助方法：批量获取文章数据
    private List<Map<String, Object>> fetchArticleList(List<String> articleIds, List<String> recordUuids) {
        List<Map<String, Object>> results = new ArrayList<>();
        
        if (articleIds == null || articleIds.isEmpty()) {
            return results;
        }
        
        for (int i = 0; i < articleIds.size(); i++) {
            String articleId = articleIds.get(i);
            String recordUuid = (i < recordUuids.size()) ? recordUuids.get(i) : null;
            
            Map<String, Object> article = fetchArticleData(articleId);
            if (article != null) {
                if (recordUuid != null) {
                    article.put("uuid", recordUuid);
                }
                results.add(article);
            }
        }
        
        return results;
    }

    // 辅助方法：获取应用信息
    private Map<String, String> fetchAppInfo(String appUuid) {
        Map<String, String> appInfo = new HashMap<>();
        if (StringUtils.isNotBlank(appUuid)) {
            try {
                String appInfoSql = "SELECT app_lang, app_icon, app_name_en FROM ai_base.ai_app_langs WHERE app_uuid = ? LIMIT 1";
                List<Map<String, Object>> appInfoResult = jdbcTemplate.queryForList(appInfoSql, appUuid);

                if (!appInfoResult.isEmpty()) {
                    Map<String, Object> result = appInfoResult.get(0);
                    if (result.containsKey("app_lang")) {
                        appInfo.put("app_lang", (String) result.get("app_lang"));
                    }
                    if (result.containsKey("app_icon")) {
                        appInfo.put("app_icon", (String) result.get("app_icon"));
                    }
                    if (result.containsKey("app_name_en")) {
                        appInfo.put("app_name_en", (String) result.get("app_name_en"));
                    }
                }
            } catch (Exception e) {
                log.error("获取应用信息失败, app_uuid={}", appUuid, e);
            }
        }
        return appInfo;
    }

    // 辅助方法：获取语言前缀
    private String getLanguages(String lang) {
        return aiAppLangsService.getLanguages(lang);
    }

    // 辅助方法：解析SEO关键词
    private List<String> parseKeywords(Object seoDescription) {
        if (seoDescription == null) {
            return new ArrayList<>();
        }
        
        try {
            String jsonStr = seoDescription.toString();
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonStr);
            JsonNode keywordsNode = rootNode.path("keywords");
            
            List<String> keywords = new ArrayList<>();
            if (keywordsNode.isArray()) {
                for (JsonNode keywordNode : keywordsNode) {
                    String keyword = keywordNode.asText();
                    if (!"梅斯医学".equals(keyword)) {
                        keywords.add(keyword);
                    }
                }
            }
            return keywords;
        } catch (Exception e) {
            log.error("解析SEO关键词失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    // 渲染错误页面
    private Object renderErrorPage(HttpServletResponse response, String errorMessage) throws IOException {
        response.setContentType("text/html;charset=UTF-8");
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html><html lang=\"zh-CN\"><head>");
        html.append("<meta charset=\"UTF-8\">");
        html.append("<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">");
        html.append("<title>加载失败 - 梅斯医学</title>");
        html.append("<style>");
        html.append("body{font-family:system-ui,-apple-system,sans-serif;color:#333;line-height:1.6;margin:0;padding:0;display:flex;justify-content:center;align-items:center;height:100vh;background:#f5f5f5;}");
        html.append(".error-container{text-align:center;padding:2rem;background:white;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);max-width:500px;}");
        html.append("h1{color:#e74c3c;margin-bottom:1rem;}");
        html.append("p{margin-bottom:1.5rem;}");
        html.append("a{color:#3498db;text-decoration:none;}");
        html.append("a:hover{text-decoration:underline;}");
        html.append("</style>");
        html.append("</head><body>");
        html.append("<div class=\"error-container\">");
        html.append("<h1>加载失败</h1>");
        html.append("<p>").append(errorMessage).append("</p>");
        html.append("<p><a href=\"/\">返回首页</a></p>");
        html.append("</div>");
        html.append("</body></html>");
        
        response.getWriter().write(html.toString());
        return null;
    }

    private String renderArticleHtml(Map<String, Object> seoData, Map<String, Object> article, 
                                   List<Map<String, Object>> recentArticles, 
                                   List<Map<String, Object>> relatedArticles,
                                   List<String> keywords) {
        
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("  <meta charset=\"UTF-8\">\n");
        html.append("  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        
        // 添加favicon链接，防止浏览器自动请求默认路径
        html.append("  <link rel=\"icon\" href=\"https://static.medsci.cn/product/medsci-site/portal/favicon.ico\" type=\"image/x-icon\">\n");
        html.append("  <link rel=\"shortcut icon\" href=\"https://static.medsci.cn/product/medsci-site/portal/favicon.ico\" type=\"image/x-icon\">\n");
        
        // 标题和SEO
        String title = article.get("title") != null ? article.get("title").toString() : "文章";
        String description = article.get("summary") != null ? article.get("summary").toString() : "";
        String canonicalUrl = article.get("linkOutUrl") != null ? article.get("linkOutUrl").toString() : "/article/" + seoData.get("id");
        
        html.append("  <title>").append(title).append(" - 梅斯医学</title>\n");
        html.append("  <meta name=\"description\" content=\"").append(escapeHtml(description)).append("\">\n");
        
        if (keywords != null && !keywords.isEmpty()) {
            html.append("  <meta name=\"keywords\" content=\"").append(String.join(",", keywords)).append("\">\n");
        }
        
        // SEO优化的元标签
        html.append("  <link rel=\"canonical\" href=\"").append(canonicalUrl).append("\">\n");
        html.append("  <meta name=\"robots\" content=\"index, follow\">\n");
        html.append("  <meta name=\"author\" content=\"梅斯医学\">\n");
        html.append("  <meta name=\"application-name\" content=\"梅斯医学\">\n");
        
        // 添加发布时间和修改时间用于SEO
        if (article.get("publishedTime") != null) {
            String publishDate = article.get("publishedTimeString") != null ? 
                article.get("publishedTimeString").toString() : article.get("publishedTime").toString();
            html.append("  <meta property=\"article:published_time\" content=\"").append(publishDate).append("\">\n");
        }
        
        if (article.get("updatedTime") != null) {
            String updateDate = article.get("updatedTimeString") != null ? 
                article.get("updatedTimeString").toString() : article.get("updatedTime").toString();
            html.append("  <meta property=\"article:modified_time\" content=\"").append(updateDate).append("\">\n");
        }
        
        // Open Graph 元数据
        html.append("  <meta property=\"og:title\" content=\"").append(escapeHtml(title)).append("\">\n");
        html.append("  <meta property=\"og:description\" content=\"").append(escapeHtml(description)).append("\">\n");
        html.append("  <meta property=\"og:url\" content=\"").append(canonicalUrl).append("\">\n");
        html.append("  <meta property=\"og:site_name\" content=\"梅斯医学\">\n");
        if (article.get("cover") != null) {
            html.append("  <meta property=\"og:image\" content=\"").append(article.get("cover")).append("\">\n");
        }
        html.append("  <meta property=\"og:type\" content=\"article\">\n");
        
        // Twitter卡片
        html.append("  <meta name=\"twitter:card\" content=\"summary_large_image\">\n");
        html.append("  <meta name=\"twitter:title\" content=\"").append(escapeHtml(title)).append("\">\n");
        html.append("  <meta name=\"twitter:description\" content=\"").append(escapeHtml(description)).append("\">\n");
        if (article.get("cover") != null) {
            html.append("  <meta name=\"twitter:image\" content=\"").append(article.get("cover")).append("\">\n");
        }
        
        // JSON-LD 结构化数据
        html.append("  <script type=\"application/ld+json\">\n");
        html.append("  {\n");
        html.append("    \"@context\": \"https://schema.org\",\n");
        html.append("    \"@type\": \"Article\",\n");
        html.append("    \"headline\": \"").append(escapeHtml(title)).append("\",\n");
        html.append("    \"description\": \"").append(escapeHtml(description)).append("\",\n");
        
        if (article.get("publishedTime") != null) {
            html.append("    \"datePublished\": \"").append(article.get("publishedTimeString") != null ? 
                article.get("publishedTimeString") : article.get("publishedTime")).append("\",\n");
        }
        
        if (article.get("updatedTime") != null) {
            html.append("    \"dateModified\": \"").append(article.get("updatedTimeString") != null ? 
                article.get("updatedTimeString") : article.get("updatedTime")).append("\",\n");
        }
        
        if (article.get("cover") != null) {
            html.append("    \"image\": \"").append(article.get("cover")).append("\",\n");
        }
        
        html.append("    \"author\": {\n");
        html.append("      \"@type\": \"Organization\",\n");
        html.append("      \"name\": \"梅斯医学\"\n");
        html.append("    },\n");
        html.append("    \"publisher\": {\n");
        html.append("      \"@type\": \"Organization\",\n");
        html.append("      \"name\": \"梅斯医学\",\n");
        html.append("      \"logo\": {\n");
        html.append("        \"@type\": \"ImageObject\",\n");
        html.append("        \"url\": \"https://www.medsci.cn/images/logo.png\"\n");
        html.append("      }\n");
        html.append("    },\n");
        html.append("    \"mainEntityOfPage\": {\n");
        html.append("      \"@type\": \"WebPage\",\n");
        html.append("      \"@id\": \"").append(canonicalUrl).append("\"\n");
        html.append("    }\n");
        html.append("  }\n");
        html.append("  </script>\n");
        
        // CSS样式
        html.append("  <style>\n");
        html.append("    /* 页面整体布局 */\n");
        html.append("    * { margin: 0; padding: 0; box-sizing: border-box; }\n");
        html.append("    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; color: #333; line-height: 1.6; margin: 0; padding: 0; min-height: 100vh; }\n");
        html.append("    .page-wrapper { display: flex; flex-direction: column; min-height: 100vh; }\n");

        // 头部导航栏样式
        html.append("    /* 头部导航栏样式 */\n");
        html.append("    .header { background-color: #fff; border-bottom: 1px solid #e8e8e8; box-shadow: 0 2px 8px rgba(0,0,0,0.06); position: sticky; top: 0; z-index: 1000; }\n");
        html.append("    .header-container { max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; align-items: center; height: 64px; }\n");
        html.append("    .header-logo { display: flex; align-items: center; text-decoration: none; -webkit-tap-highlight-color: transparent; }\n");
        html.append("    .logo-icon { width: 139px; height: 25px; object-fit: contain; transition: opacity 0.2s; }\n");
        html.append("    /* 只在非触摸设备上启用hover效果 */\n");
        html.append("    @media (hover: hover) and (pointer: fine) {\n");
        html.append("      .header-logo:hover .logo-icon { opacity: 0.8; }\n");
        html.append("    }\n");
        html.append("    /* 移动端点击时防止颜色变化 */\n");
        html.append("    .header-logo:active .logo-icon { opacity: 1; }\n");

        html.append("    .main-container { display: flex; flex: 1; max-width: 1200px; margin: 0 auto; padding: 20px; width: 100%; }\n");
        html.append("    .article-container { flex: 3; margin-right: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; color: #333; line-height: 1.6; }\n");
        
        // 添加剩余样式，保持与Vue文件中完全一致
        html.append("    .sidebar { flex: 1; min-width: 280px; max-width: 320px; }\n");
        html.append("    .sidebar-section { background-color: #f8f8f8; border-radius: 8px; margin-bottom: 20px; padding: 15px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }\n");
        html.append("    .section-title { font-size: 18px; font-weight: bold; color: #333; margin-bottom: 15px; padding-bottom: 8px; border-bottom: 1px solid #eee; }\n");
        html.append("    .tool-section { background-color: #f0f7ff; }\n");
        html.append("    .tool-card { display: flex; align-items: center; padding: 10px; background: white; border-radius: 6px; }\n");
        html.append("    .tool-icon { width: 50px; height: 50px; margin-right: 12px; }\n");
        html.append("    .tool-icon img { width: 100%; height: 100%; object-fit: contain; border-radius: 6px; }\n");
        html.append("    .tool-info { flex: 1; display: flex; flex-direction: column; }\n");
        html.append("    .tool-name { font-weight: bold; margin-bottom: 8px; }\n");
        html.append("    .tool-name a { color: #333; text-decoration: none; }\n");
        html.append("    .tool-name a:hover { color: #1890ff; text-decoration: underline; }\n");
        html.append("    .use-button { background-color: #1890ff; color: white; border: none; padding: 5px 15px; border-radius: 4px; cursor: pointer; font-size: 14px; align-self: flex-start; text-decoration: none; }\n");
        html.append("    .use-button:hover { background-color: #40a9ff; }\n");
        
        // 文章相关样式
        html.append("    .article-item { padding: 10px; background: white; border-radius: 6px; margin-bottom: 10px; transition: all 0.2s; display: block; text-decoration: none; color: inherit; }\n");
        html.append("    .article-item:hover { background-color: #f0f7ff; transform: translateY(-2px); box-shadow: 0 2px 6px rgba(0,0,0,0.1); }\n");
        html.append("    .question-title, .related-article-title { font-size: 14px; font-weight: 500; margin-bottom: 5px; color: #333; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; }\n");
        html.append("    .question-meta, .related-article-meta { font-size: 12px; color: #999; }\n");
        html.append("    .loading-side { text-align: center; color: #999; padding: 15px 0; }\n");
        html.append("    .empty-data { text-align: center; color: #999; padding: 15px 0; font-size: 14px; }\n");
        html.append("    .article-header { margin-bottom: 20px; }\n");
        html.append("    .article-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; color: #333; }\n");
        html.append("    .meta-info { font-size: 14px; color: #666; margin-bottom: 10px; }\n");
        html.append("    .publish-time, .author, .location { margin-right: 15px; }\n");
        html.append("    .author a { color: #1890ff; text-decoration: none; }\n");
        html.append("    .author a:hover { text-decoration: underline; }\n");
        html.append("    .categories { display: flex; flex-wrap: wrap; margin-top: 10px; }\n");
        html.append("    .category { display: inline-block; background-color: #f0f0f0; padding: 4px 8px; margin-right: 8px; margin-bottom: 8px; border-radius: 4px; font-size: 12px; color: #666; text-decoration: none; transition: all 0.2s; }\n");
        html.append("    .category:hover { background-color: #e0e0e0; color: #333; }\n");
        html.append("    .article-summary { font-size: 16px; background-color: #f5f5f5; padding: 15px; border-left: 4px solid #eee; margin-bottom: 20px; color: #555; }\n");
        html.append("    .article-content { font-size: 16px; margin-bottom: 30px; }\n");
        html.append("    .article-content a { color: #3498db; text-decoration: none; }\n");
        html.append("    .article-content a:hover { text-decoration: underline; }\n");
        html.append("    .article-content p { margin-bottom: 16px; }\n");
        html.append("    .article-content img { max-width: 100%; height: auto; margin: 10px 0; }\n");

        // AI对话框样式
        html.append("    /* AI对话框样式 */\n");
        html.append("    .ai-chat-container { margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 12px; border: 1px solid #e9ecef; }\n");
        html.append("    .ai-chat-title { font-size: 18px; font-weight: 600; color: #333; margin-bottom: 15px; display: flex; align-items: center; }\n");
        html.append("    .ai-chat-icon { width: 24px; height: 24px; margin-right: 8px; border-radius: 50%; background: linear-gradient(135deg, #1890ff, #40a9ff); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; font-weight: bold; }\n");
        html.append("    .ai-chat-form { display: flex; gap: 10px; align-items: stretch; }\n");
        html.append("    .ai-chat-input { flex: 1; padding: 12px 16px; border: 1px solid #d9d9d9; border-radius: 8px; font-size: 14px; outline: none; transition: border-color 0.2s; background-color: white; }\n");
        html.append("    .ai-chat-input:focus { border-color: #1890ff; box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1); }\n");
        html.append("    .ai-chat-input::placeholder { color: #999; }\n");
        html.append("    .ai-chat-button { padding: 12px 20px; background-color: #1890ff; color: white; border: none; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; transition: background-color 0.2s; white-space: nowrap; }\n");
        html.append("    .ai-chat-button:hover { background-color: #40a9ff; }\n");
        html.append("    .ai-chat-button:active { background-color: #096dd9; }\n");

        // 添加Markdown样式
        html.append("    /* Markdown样式 */\n");
        html.append("    .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin-top: 1.5em; margin-bottom: 0.8em; font-weight: 600; line-height: 1.25; }\n");
        html.append("    .article-content h1 { font-size: 2em; border-bottom: 1px solid #eee; padding-bottom: 0.3em; }\n");
        html.append("    .article-content h2 { font-size: 1.5em; border-bottom: 1px solid #eee; padding-bottom: 0.3em; }\n");
        html.append("    .article-content h3 { font-size: 1.25em; }\n");
        html.append("    .article-content h4 { font-size: 1em; }\n");
        html.append("    .article-content h5 { font-size: 0.875em; }\n");
        html.append("    .article-content h6 { font-size: 0.85em; color: #6a737d; }\n");
        html.append("    .article-content blockquote { padding: 0 1em; color: #6a737d; border-left: 0.25em solid #dfe2e5; margin: 0 0 16px 0; }\n");
        html.append("    .article-content ul, .article-content ol { padding-left: 2em; margin-bottom: 16px; }\n");
        html.append("    .article-content ul ul, .article-content ul ol, .article-content ol ul, .article-content ol ol { margin-top: 0; margin-bottom: 0; }\n");
        html.append("    .article-content li { margin-bottom: 0.3em; }\n");
        html.append("    .article-content code { padding: 0.2em 0.4em; margin: 0; font-size: 85%; background-color: rgba(27,31,35,0.05); border-radius: 3px; font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace; }\n");
        html.append("    .article-content pre { margin-bottom: 16px; padding: 16px; overflow: auto; font-size: 85%; line-height: 1.45; background-color: #f6f8fa; border-radius: 3px; }\n");
        html.append("    .article-content pre code { padding: 0; margin: 0; background-color: transparent; border: 0; }\n");
        html.append("    .article-content table { border-collapse: collapse; width: 100%; margin-bottom: 16px; overflow: auto; }\n");
        html.append("    .article-content table th, .article-content table td { padding: 6px 13px; border: 1px solid #dfe2e5; }\n");
        html.append("    .article-content table tr { background-color: #fff; border-top: 1px solid #c6cbd1; }\n");
        html.append("    .article-content table tr:nth-child(2n) { background-color: #f6f8fa; }\n");
        html.append("    .article-content hr { height: 0.25em; padding: 0; margin: 24px 0; background-color: #e1e4e8; border: 0; }\n");
        
        // 修改页脚样式，确保全包边没有边距
        html.append("    .article-footer { margin: 0; padding: 20px 0 0; background-color: #333; color: #ccc; width: 100%; }\n");
        html.append("    .footer-bottom { padding: 20px; text-align: center; }\n");
        html.append("    .copyright { margin-bottom: 10px; font-weight: 600; color: #ddd; }\n");
        html.append("    .license-info, .footer-info { margin-bottom: 10px; line-height: 1.5; font-size: 12px; color: #aaa; }\n");
        
        // 添加响应式设计，自适应移动端
        html.append("    /* 响应式设计 */\n");
        html.append("    @media screen and (max-width: 768px) {\n");
        html.append("      .header-container { padding: 0 15px; height: 56px; }\n");
        html.append("      .logo-icon { width: 80px; height: 14px; object-fit: contain; }\n");
        html.append("      .main-container { flex-direction: column; padding: 10px; }\n");
        html.append("      .article-container { margin-right: 0; margin-bottom: 20px; }\n");
        html.append("      .sidebar { min-width: 100%; max-width: 100%; }\n");
        html.append("      .article-title { font-size: 20px; }\n");
        html.append("      .meta-info { flex-wrap: wrap; }\n");
        html.append("      .meta-info span { margin-bottom: 5px; }\n");
        html.append("      .ai-chat-container { margin-top: 20px; padding: 15px; }\n");
        html.append("      .ai-chat-title { font-size: 16px; }\n");
        html.append("      .ai-chat-form { flex-direction: column; gap: 12px; }\n");
        html.append("      .ai-chat-input { padding: 10px 14px; }\n");
        html.append("      .ai-chat-button { padding: 10px 16px; }\n");
        html.append("    }\n");
        
        // 针对超小屏幕的额外优化
        html.append("    @media screen and (max-width: 480px) {\n");
        html.append("      .header-container { padding: 0 10px; height: 52px; }\n");
        html.append("      .logo-icon { width: 80px; height: 14px; object-fit: contain; }\n");
        html.append("      .main-container { padding: 8px; }\n");
        html.append("      .article-title { font-size: 18px; }\n");
        html.append("      .article-summary { font-size: 14px; padding: 10px; }\n");
        html.append("      .article-content { font-size: 14px; }\n");
        html.append("      .article-content h1 { font-size: 1.8em; }\n");
        html.append("      .article-content h2 { font-size: 1.3em; }\n");
        html.append("      .article-content img { margin: 5px 0; }\n");
        html.append("      .ai-chat-container { margin-top: 15px; padding: 12px; }\n");
        html.append("      .ai-chat-title { font-size: 15px; }\n");
        html.append("      .ai-chat-input { padding: 8px 12px; font-size: 13px; }\n");
        html.append("      .ai-chat-button { padding: 8px 14px; font-size: 13px; }\n");
        html.append("      .footer-bottom { padding: 15px 10px; }\n");
        html.append("    }\n");
        
        // 确保在内容短时footer始终在底部
        html.append("    /* 确保footer始终在底部 */\n");
        html.append("    html, body { height: 100%; }\n");
        html.append("    .page-wrapper { min-height: 100%; display: flex; flex-direction: column; }\n");
        html.append("    .main-container { flex: 1 0 auto; }\n");
        html.append("    .article-footer { flex-shrink: 0; }\n");
        
        html.append("  </style>\n");
        
        html.append("</head>\n");
        html.append("<body>\n");

        // 页面主体
        html.append("<div class=\"page-wrapper\">\n");

        // 头部导航栏
        html.append("  <header class=\"header\">\n");
        html.append("    <div class=\"header-container\">\n");
        html.append("      <a href=\"https://ai.medsci.cn/\" class=\"header-logo\" title=\"梅斯小智\">\n");
        html.append("        <img src=\"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png\" alt=\"MedSci AI\" class=\"logo-icon\" />\n");
        html.append("      </a>\n");
        html.append("    </div>\n");
        html.append("  </header>\n");

        html.append("  <div class=\"main-container\">\n");
        html.append("    <div class=\"article-container\">\n");
        
        // 文章头部
        html.append("      <div class=\"article-header\">\n");
        html.append("        <h1 class=\"article-title\">").append(title).append("</h1>\n");
        html.append("        <div class=\"meta-info\">\n");
        if (article.get("publishedTimeString") != null) {
            html.append("          <span class=\"publish-time\">").append(article.get("publishedTimeString")).append("</span>\n");
        }
        if (article.get("createdName") != null) {
            html.append("          <span class=\"author\"><a href=\"https://ai.medsci.cn/\" target=\"_blank\" rel=\"noopener\" title=\"梅斯小智-梅斯医学AI智能体\">").append(article.get("createdName")).append("</a></span>\n");
        }
        if (article.get("ipAttribution") != null) {
            html.append("          <span class=\"location\">发表于").append(article.get("ipAttribution")).append("</span>\n");
        }
        html.append("        </div>\n");
        html.append("      </div>\n");
        
        // 标签关键词改为a标签
        if (keywords != null && !keywords.isEmpty()) {
            html.append("      <div class=\"categories\">\n");
            for (String keyword : keywords) {
                html.append("        <a href=\"https://www.medsci.cn/search?q=").append(escapeHtml(keyword)).append("\" class=\"category\" target=\"_blank\" rel=\"noopener\" title=\"").append(escapeHtml(keyword)).append("\">").append(escapeHtml(keyword)).append("</a>\n");
            }
            html.append("      </div>\n");
        }
        
        // 文章摘要
        if (article.get("summary") != null) {
            html.append("      <div class=\"article-summary\">\n");
            html.append("        ").append(escapeHtml(article.get("summary").toString())).append("\n");
            html.append("      </div>\n");
        }
        
        // 文章内容
        if (article.get("content") != null) {
            String content = article.get("content").toString();
            content = decodeHtmlEntities(content); // 解码HTML实体
            
            // 检测并处理Markdown内容
            if (isMarkdown(content)) {
                content = renderMarkdown(content);
            }
            
            html.append("      <div class=\"article-content\">\n");
            html.append("        ").append(content).append("\n");
            html.append("      </div>\n");
        } else {
            html.append("      <pre>");
            try {
                html.append(escapeHtml(new ObjectMapper().writeValueAsString(article)));
            } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
                log.error("JSON序列化文章数据失败", e);
                html.append("文章内容解析失败");
            }
            html.append("</pre>\n");
        }

        // AI对话框区域
        if (seoData != null) {
            // 获取工具URL构建逻辑（与侧边栏工具使用区域相同）
            String appType = (String) seoData.get("app_type");
            String appName = (String) (seoData.get("app_name_en") != null ? seoData.get("app_name_en") : seoData.get("app_name"));
            String appLang = (String) seoData.get("app_lang");
            String languagePrefix = getLanguages(appLang);
            String chatUrl = "#";

            if ("工具".equals(appType)) {
                chatUrl = "/" + languagePrefix + "tool/" + appName;
            } else if ("问答".equals(appType)) {
                chatUrl = "/" + languagePrefix + "chat/" + appName;
            } else if ("写作".equals(appType)) {
                chatUrl = "/" + languagePrefix + "write/" + appName;
            }

            html.append("      <div class=\"ai-chat-container\">\n");
            html.append("        <div class=\"ai-chat-title\">\n");
            html.append("          <div class=\"ai-chat-icon\">AI</div>\n");
            html.append("          与梅斯小智对话\n");
            html.append("        </div>\n");
            html.append("        <div class=\"ai-chat-form\">\n");
            html.append("          <input type=\"text\" class=\"ai-chat-input\" placeholder=\"输入您的问题，与AI助手开始对话...\" onclick=\"window.open('").append(chatUrl).append("', '_blank')\" readonly />\n");
            html.append("          <button class=\"ai-chat-button\" onclick=\"window.open('").append(chatUrl).append("', '_blank')\" title=\"开始AI对话\">发送</button>\n");
            html.append("        </div>\n");
            html.append("      </div>\n");
        }

        html.append("    </div>\n");

        // 侧边栏
        html.append("    <div class=\"sidebar\">\n");
        
        // 工具使用区域 - 改为a标签
        if (seoData != null) {
            html.append("      <div class=\"sidebar-section tool-section\">\n");
            html.append("        <div class=\"section-title\">工具使用</div>\n");
            html.append("        <div class=\"tool-card\">\n");
            html.append("          <div class=\"tool-icon\">\n");
            String iconUrl = seoData.get("app_icon") != null ? seoData.get("app_icon").toString() : "https://www.medsci.cn/images/logo.png";
            String toolName = seoData.get("app_name") != null ? seoData.get("app_name").toString() : "工具名称";
            html.append("            <img src=\"").append(iconUrl).append("\" alt=\"").append(toolName).append("图标\" />\n");
            html.append("          </div>\n");
            html.append("          <div class=\"tool-info\">\n");

            // 根据app_type构建不同的链接
            String appType = (String) seoData.get("app_type");
            String appName = (String) (seoData.get("app_name_en") != null ? seoData.get("app_name_en") : seoData.get("app_name"));
            String appLang = (String) seoData.get("app_lang");
            String languagePrefix = getLanguages(appLang);
            String toolUrl = "#";
            String toolTypeDesc = "工具";

            if ("工具".equals(appType)) {
                toolUrl = "/" + languagePrefix + "tool/" + appName;
                toolTypeDesc = "AI工具";
            } else if ("问答".equals(appType)) {
                toolUrl = "/" + languagePrefix + "chat/" + appName;
                toolTypeDesc = "AI问答";
            } else if ("写作".equals(appType)) {
                toolUrl = "/" + languagePrefix + "write/" + appName;
                toolTypeDesc = "AI写作";
            }

            html.append("            <div class=\"tool-name\"><a href=\"").append(toolUrl).append("\" target=\"_blank\" rel=\"noopener\" title=\"").append(toolName).append("-").append(toolTypeDesc).append("\">").append(toolName).append("</a></div>\n");
            html.append("            <button class=\"use-button\" onclick=\"window.open('").append(toolUrl).append("', '_blank')\" title=\"").append(toolName).append("-").append(toolTypeDesc).append("\">使用</button>\n");
            html.append("          </div>\n");
            html.append("        </div>\n");
            html.append("      </div>\n");
        }
        
        // 最新文章 - 改为a标签
        html.append("      <div class=\"sidebar-section\">\n");
        html.append("        <div class=\"section-title\">最新问答</div>\n");
        if (recentArticles != null && !recentArticles.isEmpty()) {
            for (int i = 0; i < Math.min(3, recentArticles.size()); i++) {
                Map<String, Object> item = recentArticles.get(i);
                String linkUrl = item.get("linkOutUrl") != null ? item.get("linkOutUrl").toString() : "";
                String itemTitle = item.get("title") != null ? item.get("title").toString() : "";
                String date = formatDate(item.get("publishedTime") != null ? item.get("publishedTime") : 
                                        item.get("createdTime"));
                
                html.append("        <a href=\"").append(linkUrl).append("\" class=\"article-item\" target=\"_blank\" rel=\"noopener\" title=\"").append(escapeHtml(itemTitle)).append("\">\n");
                html.append("          <div class=\"question-title\">").append(escapeHtml(itemTitle)).append("</div>\n");
                html.append("          <div class=\"question-meta\">").append(date).append("</div>\n");
                html.append("        </a>\n");
            }
        } else {
            html.append("        <div class=\"empty-data\">暂无数据</div>\n");
        }
        html.append("      </div>\n");
        
        // 相关文章 - 改为a标签
        html.append("      <div class=\"sidebar-section\">\n");
        html.append("        <div class=\"section-title\">随机问答</div>\n");
        if (relatedArticles != null && !relatedArticles.isEmpty()) {
            for (int i = 0; i < Math.min(3, relatedArticles.size()); i++) {
                Map<String, Object> item = relatedArticles.get(i);
                String linkUrl = item.get("linkOutUrl") != null ? item.get("linkOutUrl").toString() : "";
                String itemTitle = item.get("title") != null ? item.get("title").toString() : "";
                String date = formatDate(item.get("publishedTime") != null ? item.get("publishedTime") : 
                                        item.get("createdTime"));
                
                html.append("        <a href=\"").append(linkUrl).append("\" class=\"article-item\" target=\"_blank\" rel=\"noopener\" title=\"").append(escapeHtml(itemTitle)).append("\">\n");
                html.append("          <div class=\"related-article-title\">").append(escapeHtml(itemTitle)).append("</div>\n");
                html.append("          <div class=\"related-article-meta\">").append(date).append("</div>\n");
                html.append("        </a>\n");
            }
        } else {
            html.append("        <div class=\"empty-data\">暂无数据</div>\n");
        }
        html.append("      </div>\n");
        
        html.append("    </div>\n");
        
        // 页脚
        html.append("  </div>\n");
        html.append("  <div class=\"article-footer\">\n");
        html.append("    <div class=\"footer-bottom\">\n");
        html.append("      <div class=\"copyright\">\n");
        html.append("        &copy;Copyright 2012-至今 梅斯（MedSci）\n");
        html.append("      </div>\n");
        html.append("      <div class=\"license-info\">\n");
        html.append("        增值电信业务经营许可证 | 备案号 沪ICP备14018916号-1 | 互联网药品信息服务资格证书((沪)-非经营性-2020-0033) | 出版物经营许可证\n");
        html.append("      </div>\n");
        html.append("      <div class=\"license-info\">\n");
        html.append("        上海工商 | 上海网警网络110 | 网络社会征信网 | 违法和不良信息举报中心 | 信息举报中心 |违法举报：021-54485309 | 沪公网安备 31010402000380\n");
        html.append("      </div>\n");
        html.append("      <div class=\"footer-info\">\n");
        html.append("        本站旨在介绍医药健康研究进展和信息，不作为诊疗方案推荐。如需获得诊断或治疗方面指导，请前往正规医院就诊。\n");
        html.append("      </div>\n");
        html.append("      <div class=\"footer-info\">\n");
        html.append("        用户应遵守著作权法，尊重著作权人合法权益，不违法上传、存储并分享他人作品。投诉、举报、维权邮箱：<EMAIL>，或在此留言\n");
        html.append("      </div>\n");
        html.append("    </div>\n");
        html.append("  </div>\n");
        html.append("</div>\n");
        html.append("</body>\n");
        html.append("</html>");
        
        return html.toString();
    }

    // 辅助方法：格式化日期
    private String formatDate(Object dateObj) {
        if (dateObj == null) return "";
        
        try {
            String dateStr = dateObj.toString();
            // 简单处理，实际应该根据日期格式进行转换
            if (dateStr.length() > 10) {
                return dateStr.substring(0, 10);
            }
            return dateStr;
        } catch (Exception e) {
            return "";
        }
    }

    // 辅助方法：转义HTML
    private String escapeHtml(String text) {
        if (text == null) return "";
        return text.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#39;");
    }

    // 辅助方法：HTML实体解码
    private String decodeHtmlEntities(String content) {
        if (content == null) return "";
        return content.replace("&lt;", "<")
                     .replace("&gt;", ">")
                     .replace("&amp;", "&")
                     .replace("&ldquo;", "\u201C") // 左双引号Unicode
                     .replace("&rdquo;", "\u201D") // 右双引号Unicode
                     .replace("&hellip;", "\u2026") // 省略号Unicode
                     .replace("&nbsp;", " ")
                     .replace("&mdash;", "\u2014") // 破折号Unicode
                     .replace("&ndash;", "\u2013") // 连字符Unicode
                     .replace("&lsquo;", "\u2018") // 左单引号Unicode
                     .replace("&rsquo;", "\u2019") // 右单引号Unicode
                     .replace("&bull;", "\u2022") // 项目符号Unicode
                     .replace("&middot;", "\u00B7") // 中点Unicode
                     .replace("&quot;", "\""); // 双引号
    }

    // Markdown转HTML
    private String renderMarkdown(String markdown) {
        if (StringUtils.isBlank(markdown)) {
            return "";
        }
        
        try {
            // 设置Markdown解析器选项
            MutableDataSet options = new MutableDataSet();
            options.set(Parser.EXTENSIONS, Arrays.asList(
                TablesExtension.create(),
                StrikethroughExtension.create()
            ));
            
            // 创建解析器和渲染器
            Parser parser = Parser.builder(options).build();
            HtmlRenderer renderer = HtmlRenderer.builder(options).build();
            
            // 解析Markdown为AST
            Node document = parser.parse(markdown);
            
            // 渲染为HTML
            return renderer.render(document);
        } catch (Exception e) {
            log.error("Markdown渲染失败: {}", e.getMessage());
            // 发生错误时返回原始文本，但进行HTML转义
            return escapeHtml(markdown);
        }
    }
    
    // 检测内容是否为Markdown
    private boolean isMarkdown(String content) {
        if (StringUtils.isBlank(content)) {
            return false;
        }
        
        // 检查常见的Markdown标记
        return content.contains("#") || // 标题
               content.contains("```") || // 代码块
               content.contains("- ") || // 无序列表
               content.contains("1. ") || // 有序列表
               content.contains("*") || // 强调
               content.contains("_") || // 强调
               content.contains("[") && content.contains("](") || // 链接
               content.contains("![") && content.contains("](") || // 图片
               content.contains("|") && content.contains("-|-"); // 表格
    }

}
