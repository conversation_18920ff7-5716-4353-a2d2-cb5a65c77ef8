package cn.iocoder.yudao.aiBase.controller;

import cn.iocoder.yudao.aiBase.dto.request.dify.ChatMessagesRequest;
import cn.iocoder.yudao.aiBase.dto.request.dify.MessagesRequest;
import cn.iocoder.yudao.aiBase.dto.request.dify.WorkflowsRunRequest;
import cn.iocoder.yudao.aiBase.service.AliyunService;
import cn.iocoder.yudao.aiBase.service.ApiTokensService;
import cn.iocoder.yudao.aiBase.util.DifyUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.ratelimiter.core.annotation.RateLimiter;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;


/**
 * 测试接口
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/ai-base/yps-chat")
@Tag(name = "益普生dify-执行chat接口")
public class YpsChatController {

    @Autowired
    private ApiTokensService apiTokensService;

    @Autowired
    private AliyunService aliyunService;


    @RateLimiter(count = 1, message = "ERROR_5045")
    @GetMapping("/test")
    @Operation(summary = "测试接口")
    @ResponseBody
    public CommonResult<String> test() {
        return CommonResult.success("测试接口");
    }

    /**
     * 创建会话消息
     * @param param
     * @return
     */
    @PostMapping(value = "/chat-messages", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "流模式，创建会话消息")
    public SseEmitter chatMsg(@Valid @RequestBody ChatMessagesRequest param) {
        List<String> list = new ArrayList<>();
        list.add("acee0214-9eca-41ac-928d-987863849a1e");
        list.add("54b9062b-b835-4408-ac06-9f85eac0e33e");
        list.add("713e4fff-4c73-441f-acf0-f049106a541d");
        list.add("91cacef4-e9ed-44b5-aa82-323c7ee8e38f");
        if (list.contains(param.getAppId())) {
            Flux<ServerSentEvent> flux = apiTokensService.chatMsg(param);
            SseEmitter emitter = apiTokensService.getEmitter(flux);
            return emitter;
        } else {
            return null;
        }
    }

    /**
     * 获取会话历史消息
     * @param param
     * @return
     */
    @PostMapping("/messages")
    @Operation(summary = "获取会话历史消息")
    public CommonResult<?> messages(@Valid @RequestBody MessagesRequest param) {
        try {
            JSONObject res = apiTokensService.messages(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    @PostMapping(value = "/workflows/run1")
    @Operation(summary = "阻塞模式，执行workflow")
    @ResponseBody
    public CommonResult<?> workflowsRun1(@Valid @RequestBody WorkflowsRunRequest param) {
        try {
            JSONObject res = apiTokensService.workflowsRun1(param);

            return CommonResult.success(res);
        } catch (Exception e) {
            return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 执行workflow
     * @param param
     * @return
     */
    @PostMapping(value = "/workflows_run")
    @Operation(summary = "执行workflow")
    public Object workflowsRun(@Valid @RequestBody WorkflowsRunRequest param) {
        if (DifyUtil.STREAMING_MODE.equals(param.getResponse_mode())) {
            // 使用一个线程或反应式流来订阅第三方SSE事件并转发到前端
            Flux<ServerSentEvent> flux = apiTokensService.workflowsRun(param);
            SseEmitter emitter = apiTokensService.getEmitter(flux);
            return emitter;
        } else {
            try {
                JSONObject res = apiTokensService.workflowsRun1(param);
                return CommonResult.success(res);
            } catch (Exception e) {
                return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
            }
        }
    }


    @PostMapping(value = "/completion-messages", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "流模式，执行文本完成")
    public SseEmitter completionMessages(@Valid @RequestBody WorkflowsRunRequest param) {
        // 使用一个线程或反应式流来订阅第三方SSE事件并转发到前端
        param.setAppId("e69add1c-c6a1-409d-81ce-eeff39f18196");
        Flux<ServerSentEvent> flux = apiTokensService.completionMsg(param);
        SseEmitter emitter = apiTokensService.getEmitter(flux);
        return emitter;
    }

    @GetMapping("/getAsrToken")
    @Operation(summary = "获取asr token")
    @ResponseBody
    public CommonResult<String> getAsrToken() {
        return CommonResult.success(aliyunService.getAsrToken());
    }



}
