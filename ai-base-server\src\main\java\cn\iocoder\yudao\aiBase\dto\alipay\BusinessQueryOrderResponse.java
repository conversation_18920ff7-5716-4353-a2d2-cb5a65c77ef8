package cn.iocoder.yudao.aiBase.dto.alipay;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BusinessQueryOrderResponse {
    /**
     * 业务订单id
     */
    private String appOrderId;
    /**
     * 支付金额
     */
    private String payAmount;
    /**
     * 订单的支付状态
     */
    private PayStatus payStatus;
    /**
     * 订单创建时间
     */
    private String createdTime;
    private BigDecimal itemOrigAmt;
    private Long itemId;
    private String mobile;
}
