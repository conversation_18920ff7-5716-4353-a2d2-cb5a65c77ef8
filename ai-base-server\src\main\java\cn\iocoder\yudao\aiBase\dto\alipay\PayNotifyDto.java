package cn.iocoder.yudao.aiBase.dto.alipay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 统一的支付通知
 *
 * <AUTHOR>
 * @date 2021/4/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PayNotifyDto {

    /**
     * 支付流水订单号
     */
    private String payOrderLogId;

    /**
     * 业务系统的订单id
     */
    private String appOrderId;

    /**
     * 支付渠道
     */
    private String payChannel = "ALI";

    /**
     * 支付来源
     */
    private String paySource;

    /**
     * 支付方式
     */
    private String payType;

    /**
     * 支付金额(单位:分)
     */
    private Integer amount;

    /**
     * 支付平台交易号
     */
    private String payNumber;

    /**
     * 通知时间
     */
    private String notifyTime;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 收款账号
     */
    private String payAccount;

    /**
     * 交易状态
     */
    private PayStatus payStatus;

    /**
     * 收款银行
     */
    private String payWay;

    /**
     * 结算方法: 微信->201, 支付宝202. code由ERP定义
     */
    private String settlementMethod;

    /**
     * 收款银行
     */
    private String collectionBank;

    /**
     * 支付系统的订单id
     */
    private String payOrderId;

}
