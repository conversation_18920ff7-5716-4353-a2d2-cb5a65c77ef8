package cn.iocoder.yudao.aiBase.dto.alipay;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

@Getter
@JsonFormat(shape = JsonFormat.Shape.STRING)
public enum PayStatus {


    /**
     * 支付中
     */
    PAYING,

    /**
     * 已支付
     */
    PAID,

    /**
     * 部分付款
     */
    PARTIAL,

    /**
     * 支付/退款失败
     */
    FAILED,

    /**
     * 已关单
     */
    CLOSED,

    /**
     * 未找到订单
     */
    NOT_FOUND;

}
