package cn.iocoder.yudao.aiBase.dto.alipay;

import lombok.Data;

import java.io.Serializable;

@Data
public class Result<T> implements Serializable {
	private static final String SUCCESS = "SUCCESS";
	private static final String HTTP_STATUS_CODE_500 = "500";
	public static final Result<Object> OK = success((Object)null);
	public static final Result<Object> FAIL = error("操作失败");
	private Integer status = 200;
	private String code;
	private String msg;
	private T data;

	// 默认构造函数
	public Result() {
	}

	public Result(String code, String msg) {
		this.code = code;
		this.msg = msg;
	}

	public static <T> Result<T> success(T data) {
		return new Result(SUCCESS, "成功", data);
	}

	public static <T> Result<T> success(String msg, T data) {
		return new Result(SUCCESS, msg, data);
	}

//	public static Result<Object> error(ResultStatus statusResult, String msg) {
//		return error(statusResult.getStatusCode(), (String)StringUtils.defaultIfBlank(msg, statusResult.getStatusMsg()));
//	}

	public static Result<Object> error(String msg) {
		return error("500", msg);
	}

	public static Result<Object> error(String code, String msg) {
		return new Result(code, msg, (Object)null);
	}

//	public static <T> Result<ArrayContent<T>> array(List<T> data) {
//		return success(ArrayContent.arrayOf(data));
//	}
//
//	public static <T> Result<PageContent<T>> page(IPage<T> iPage) {
//		return success(PageContent.pageOf(iPage.getRecords(), iPage.getCurrent(), iPage.getPages(), iPage.getSize(), iPage.getTotal()));
//	}

	public boolean isSuccess() {
		return SUCCESS.equals(this.code);
	}

	public String getCode() {
		return this.code;
	}

	public String getMsg() {
		return this.msg;
	}

	public T getData() {
		return this.data;
	}

	public void setCode(final String code) {
		this.code = code;
	}

	public void setMsg(final String msg) {
		this.msg = msg;
	}

	public void setData(final T data) {
		this.data = data;
	}

	public boolean equals(final Object o) {
		if (o == this) {
			return true;
		} else if (!(o instanceof Result)) {
			return false;
		} else {
			Result<?> other = (Result)o;
			if (!other.canEqual(this)) {
				return false;
			} else {
				label47: {
					Object this$code = this.getCode();
					Object other$code = other.getCode();
					if (this$code == null) {
						if (other$code == null) {
							break label47;
						}
					} else if (this$code.equals(other$code)) {
						break label47;
					}

					return false;
				}

				Object this$msg = this.getMsg();
				Object other$msg = other.getMsg();
				if (this$msg == null) {
					if (other$msg != null) {
						return false;
					}
				} else if (!this$msg.equals(other$msg)) {
					return false;
				}

				Object this$data = this.getData();
				Object other$data = other.getData();
				if (this$data == null) {
					if (other$data != null) {
						return false;
					}
				} else if (!this$data.equals(other$data)) {
					return false;
				}

				return true;
			}
		}
	}

	protected boolean canEqual(final Object other) {
		return other instanceof Result;
	}

	public int hashCode() {
		int result = 1;
		Object $code = this.getCode();
		result = result * 59 + ($code == null ? 43 : $code.hashCode());
		Object $msg = this.getMsg();
		result = result * 59 + ($msg == null ? 43 : $msg.hashCode());
		Object $data = this.getData();
		result = result * 59 + ($data == null ? 43 : $data.hashCode());
		return result;
	}

	public String toString() {
		String var10000 = this.getCode();
		return "Result(code=" + var10000 + ", msg=" + this.getMsg() + ", data=" + String.valueOf(this.getData()) + ")";
	}

	public Result(final String code, final String msg, final T data) {
		this.code = code;
		this.msg = msg;
		this.data = data;
	}
}

