package cn.iocoder.yudao.aiBase.dto.asr;

import cn.iocoder.yudao.aiBase.mq.AsrDataProducer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ASRParam {
	@Schema(description = "Port on which to listen.")
	private String port = "10095";

	@Schema(description = "the IP address of server.")
	private String host = "127.0.0.1";

	@Schema(description = "wav path for decoding")
	private String audioIn = "asr_example.wav";

	@Schema(description = "num of threads for test.")
	private Integer numThreads = 1;

	@Schema(description = "chunk size for asr.")
	private String chunkSize = "5, 10, 5";

	@Schema(description = "chunk for asr.")
	private Integer chunkInterval = 10;

	@Schema(description = "mode for asr")
	private String mode = "offline";

	@Schema(description = "hotwords, splited by space, hello 30 nihao 4")
	private String hotwords = "";

	private Integer sendChunkSize;
	private String wavName;
	private AsrDataProducer producer;
}
