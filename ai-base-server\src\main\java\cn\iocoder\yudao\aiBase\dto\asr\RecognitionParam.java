package cn.iocoder.yudao.aiBase.dto.asr;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RecognitionParam {

	@Schema(description = "model")
	private String mode = "offline";

	@Schema(description = "{\"时光\":20,\"谢谢\":20,\"设置\":30}")
	private String hotWords = "{}";

	@Schema(description = "文件路径")
	private String fileUrl = "/Users/<USER>/Documents/java/ai-base-main/ai-base-server/src/main/resources/temp/";

	@Schema(description = "服务端")
	private String serverIpPort = "ws://127.0.0.1:10095";

}
