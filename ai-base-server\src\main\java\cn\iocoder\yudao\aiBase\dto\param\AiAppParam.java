package cn.iocoder.yudao.aiBase.dto.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiAppParam {
    @Schema(description = "主站用户ID，登录时必传")
    private Long socialUserId;

    @Schema(description =  "三方用户ID")
    private String openid;

    @NotBlank(message = "请选择语言")
    @Schema(description = "语言")
    private String appLang;

    @Schema(description = "唯一英文名")
    private String appNameEn;
    private List<String> appNameEns;

    @Schema(description = "状态")
    private String appStatus;

    @Schema(description = "类型")
    private String appType;

    @Schema(description = "appuser状态 1订阅中 2已过期")
    private Integer status;

    @Schema(description = "1使用频率/订阅热度，2订阅到期/点击热度")
    private Integer order = 1;

    @Schema(description = "我的， 1是 2否")
    private Integer isMine = 2;

    @Schema(description = "过期时间")
    private LocalDateTime[] expiredAt;

    @Schema(description = "app uuid")
    private String appUuid;

    @Schema(description = "difyAppUuid")
    private String difyAppUuid;

    @Schema(description = "0外部应用 1内部应用 2活动应用")
    private Integer isInternalUser;
    private List<Integer> isInternalUsers;

    private Integer lastLimit;

}
