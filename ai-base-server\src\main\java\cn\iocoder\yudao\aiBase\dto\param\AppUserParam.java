package cn.iocoder.yudao.aiBase.dto.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppUserParam {
    @Schema(description = "主站用户ID，登录时必传")
    private Long socialUserId;

    @Schema(description =  "三方类型")
    private Integer socialType;

    @Schema(description =  "语言")
    private String appUuid;

    @Schema(description =  "状态 1订阅中 2已过期")
    private Integer status;

    @Schema(description = "过期时间")
    private LocalDateTime[] expiredAt;
}
