package cn.iocoder.yudao.aiBase.dto.param;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileAsrParam  extends PageParam {
    @Schema(description =  "uuid")
    private String uuid;

    @Schema(description =  "fileName")
    private String fileName;

    @Schema(description =  "fileMd5")
    private String fileMd5;

    @Schema(description =  "status")
    private Integer status;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdAt;
}
