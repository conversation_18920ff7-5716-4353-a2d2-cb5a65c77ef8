package cn.iocoder.yudao.aiBase.dto.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IndexParam {
    @Schema(description =  "姓名")
    private String name;

    @Schema(description =  "年龄")
    private Integer age;

    @Schema(description =  "性别")
    private String sex;
}
