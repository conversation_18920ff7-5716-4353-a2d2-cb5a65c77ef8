package cn.iocoder.yudao.aiBase.dto.param;

import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MsUserParam {
    @Schema(description =  "三方ID")
    private String openid;

    /**
     * 枚举 {@link SocialTypeEnum}
     */
    @Schema(description =  "三方平台类型")
    private Integer socialType;


    @Schema(description = "主站用户ID", example = "4708")
    private Long socialUserId;

    @Schema(description = "用户名", example = "王五")
    private String userName;

    @Schema(description = "邮箱", example = "王五")
    private String email;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "真实姓名", example = "赵六")
    private String realName;

    @Schema(description = "内部用户", example = "1")
    private Integer isInternalUser;

    @Schema(description = "1启用，2禁用", example = "2")
    private Integer status;
}
