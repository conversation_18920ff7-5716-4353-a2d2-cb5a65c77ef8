package cn.iocoder.yudao.aiBase.dto.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QaParam {
    @Schema(description = "QAId")
    private Long id;
    @Schema(description =  "资源id")
    @NotNull(message = "资源id不能为空")
    private String articleId;
    @Schema(description = "用户名")
    private String userName;
    @Schema(description = "查询数量")
    private Integer count;
    @Schema(description =  "加密id")
    private String encryptionId;
}
