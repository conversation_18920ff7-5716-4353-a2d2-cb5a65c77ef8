package cn.iocoder.yudao.aiBase.dto.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class QaSaveParam {
    @Schema(description = "用户名")
    @NotNull(message = "用户名不能为空")
    private String userName;

    @NotBlank(message="{ERROR_5046}")
    @Schema(description =  "加密文章ID")
    private String articleId;

    @Schema(description =  "生成数量 默认3")
    private Integer num = 3;

    @Schema(description =  "是否重新生成 Y N 默认N")
    private String regenerate = "N";
}
