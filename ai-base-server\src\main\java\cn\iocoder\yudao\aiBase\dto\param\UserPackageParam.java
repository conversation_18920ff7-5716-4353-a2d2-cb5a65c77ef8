package cn.iocoder.yudao.aiBase.dto.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserPackageParam {
    @Schema(description = "主站用户ID，登录时必传")
    private Long socialUserId;

    @Schema(description =  "三方类型")
    private Integer socialType;

    @Schema(description =  "套餐键名")
    private String packageKey;

    @Schema(description =  "套餐类型")
    private String packageType;

    @Schema(description =  "sessionId")
    private String checkoutSessionId;

    @Schema(description =  "subStatus")
    private Integer subStatus;

    @Schema(description =  "过期时间")
    private LocalDateTime[] expiredAt;
}
