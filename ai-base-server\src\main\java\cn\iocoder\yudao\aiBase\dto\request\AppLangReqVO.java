package cn.iocoder.yudao.aiBase.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AppLangReqVO {

    @Schema(description =  "uuid")
    private String appUuid;

    @Schema(description =  "修改前包月费用")
    private BigDecimal feePrice;

    @Schema(description =  "修改前包季费用")
    private BigDecimal feePrice2;

    @Schema(description =  "修改前包年费用")
    private BigDecimal feePrice3;

}
