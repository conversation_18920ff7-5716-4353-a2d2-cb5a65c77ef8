package cn.iocoder.yudao.aiBase.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 应用使用按月查询请求参数
 */
@Data
public class AppUsageByMonthRequest {

    @Schema(description = "应用UUID", required = true)
    @NotBlank(message = "应用UUID不能为空")
    private String appUuid;
    
    @Schema(description = "年月，格式：yyyy-MM，例如：2023-05", required = true)
    @NotBlank(message = "年月不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "年月格式不正确，应为yyyy-MM")
    private String yearMonth;
} 