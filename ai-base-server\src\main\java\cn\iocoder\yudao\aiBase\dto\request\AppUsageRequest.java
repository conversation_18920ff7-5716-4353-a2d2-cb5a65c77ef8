package cn.iocoder.yudao.aiBase.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 应用使用请求参数
 */
@Data
public class AppUsageRequest {

    @Schema(description = "应用UUID", required = true)
    @NotBlank(message = "应用UUID不能为空")
    private String appUuid;

    @Schema(description = "请求ID")
    private String requestId;
} 