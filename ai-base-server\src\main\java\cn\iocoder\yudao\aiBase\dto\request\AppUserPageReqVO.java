package cn.iocoder.yudao.aiBase.dto.request;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppUserPageReqVO extends PageParam {

    @Schema(description =  "uuid")
    private String appUuid;

    @Schema(description = "三方ID")
    private Long socialUserId;

    @Schema(description = "三方类型")
    private Integer socialType;

    @Schema(description = "价格")
    private String priceId;

    @Schema(description = "sessionId")
    private String checkoutSessionId;

    @Schema(description = "subId")
    private String subId;

    @Schema(description = "piId")
    private String piId;

    @Schema(description =  "支付状态")
    private String paymentStatus;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdAt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] expiredAt;

}
