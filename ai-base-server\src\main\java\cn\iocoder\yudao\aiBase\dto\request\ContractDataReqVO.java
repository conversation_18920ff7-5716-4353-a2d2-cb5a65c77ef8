package cn.iocoder.yudao.aiBase.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ContractDataReqVO {


    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "用户不能为空")
    private String userId;

    @Schema(description = "签约类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "签约类型不能为空")
    private String contractType = "ALI_WITHHOLD";

    @Schema(description = "订单id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "订单id不能为空")
    private String orderId;

    @NotEmpty(message = "扣款金额不可为空")
    @Schema(description = "扣款金额")
    private String singleAmount;

    @NotNull(message = "周期数不可为空")
    @Schema(description = "周期数")
    private Long period;

    @NotNull(message = "周期类型不可为空")
    @Schema(description = "周期类型")
    private String periodType = "MONTH";

    @NotEmpty(message = "签约场景不可为空")
    @Schema(description = "签约场景")
    private String signScene;

    @Schema(description = "接入应用的appId(支付系统提供)等")
    @NotBlank(message = "accessAppId不可为空")
    private String accessAppId;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "商品id")
    private Long itemId;
}
