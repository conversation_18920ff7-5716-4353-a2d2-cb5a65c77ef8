package cn.iocoder.yudao.aiBase.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CreateMedsciMemberSignDto {

    @Schema(description = "周期类型枚举值为DAY和MONTH")
    private String periodType;

    @Schema(description = "周期数")
    private Integer period;

    @Schema(description = "商户发起首次扣款的时间")
    private String executeTime;

    @Schema(description = "每次发起扣款时限制的最大金额(元)")
    private BigDecimal singleAmount;

    @Schema(description = "签约协议号")
    private String agreementNo;

    @Schema(description = "签约场景码")
    private String signScene;

    @Schema(description = "签约状态(0-已解约，1-已签约)")
    private Integer status;

    @Schema(description = "原始订单id")
    private String sourceOrderId;

    @Schema(description = "创建人id")
    private String createdBy;

    @Schema(description = "签约渠道 -ALI")
    private String channel;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "商品id")
    private Long itemId;
}
