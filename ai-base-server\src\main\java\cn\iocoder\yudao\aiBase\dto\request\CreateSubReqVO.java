package cn.iocoder.yudao.aiBase.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateSubReqVO{

    @NotNull(message = "{ERROR_5009}")
    @Schema(description =  "uuid")
    private String appUuid;

    @Schema(description = "价格")
    private String priceId;

    @Min(value = 1, message = "{ERROR_5010}")
    @Max(value = 12, message = "{ERROR_5010}")
    @Schema(description = "时长")
    private Integer monthNum = 1;

    @Schema(description =  "packageKey")
    private String packageKey;

    @Schema(description =  "packageType")
    private String packageType;

}
