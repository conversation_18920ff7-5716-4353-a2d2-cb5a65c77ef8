package cn.iocoder.yudao.aiBase.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 主站用户新增/修改 Request VO")
@Data
public class MedsciUsersSaveReqVO {

    @Schema(description = "id", example = "4708")
    private Integer id;

    @Schema(description = "三方id", example = "4708")
    private Long socialUserId;

    @Schema(description = "三方id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7001")
    @NotEmpty(message = "{ERROR_5020}")
    private String openid;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "{ERROR_5022}")
    private String userName;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mobile;

    @Schema(description = "真实姓名", example = "赵六")
    private String realName;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "1启用，2禁用", example = "2")
    private Integer status = 1;

    @Schema(description = "过期时间")
    private LocalDateTime expireAt;

    @Schema(description = "是否是内部用户 0否 1是")
    private Integer isInternalUser;
}