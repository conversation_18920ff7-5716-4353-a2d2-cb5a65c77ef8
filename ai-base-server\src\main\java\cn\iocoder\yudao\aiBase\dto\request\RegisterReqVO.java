package cn.iocoder.yudao.aiBase.dto.request;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegisterReqVO {

    @NotBlank(message = "{ERROR_5004}")
    @Schema(description =  "email")
    @Pattern(regexp = BaseConstant.EMAIL_REGEX, message = "{ERROR_5002}")
    private String email;

    @NotBlank(message = "{ERROR_5005}")
    @Schema(description = "password")
    @Length(min = 8, max = 20, message = "{ERROR_5007}")
    @Pattern(regexp = BaseConstant.PWD_REGEX, message = "{ERROR_5008}")
    private String password;

    @NotBlank(message = "{ERROR_5006}")
    @Schema(description = "emailCode")
    private String emailCode;

    @Schema(description = "userName")
    private String userName;

}
