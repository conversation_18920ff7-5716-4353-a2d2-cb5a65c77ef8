package cn.iocoder.yudao.aiBase.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UpdatePriceIdReqVO {

    @Schema(description =  "id")
    private Integer id;

    @Schema(description =  "包月费用id")
    private String fee_price_id;

    @Schema(description =  "包季费用id")
    private String fee_price_id2;

    @Schema(description =  "包年费用id")
    private String fee_price_id3;

}
