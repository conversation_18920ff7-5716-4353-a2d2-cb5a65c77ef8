package cn.iocoder.yudao.aiBase.dto.request.difyStatistics;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import lombok.Data;

@Data
public class DifyStatisticsMessageReqVo extends PageParam {
    private String appId;
    private String appName;
    private String[] tagIds;
    private String[] excludeTagIds;
    private String conversationName;
    private String conversationStatus;
    private String sessionId;
    private String messageCount;
    private String totalTokens;
    private String avgLatency;
    private String totalLatency;
    private String createTime;
    private String createTimeStart;
    private String createTimeEnd;
    // 时间戳
    private Long startTime;
    private Long endTime;
    private Integer limit;
    private Integer offset;
    
    // 新增字段
    private Integer minTokens;
    private Integer maxTokens;
}
