package cn.iocoder.yudao.aiBase.dto.request.dify;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class ChatMessagesRequest extends DifyBaseRequest {

    @Schema(description = "请求ID")
    private String requestId;

    @NotBlank(message="{ERROR_5013}")
    @Schema(description =  "query")
    private String query;

    @NotBlank(message="{ERROR_5014}")
    @Schema(description =  "streaming:流式模式 blocking:阻塞模式")
    private String response_mode = "streaming";

    @Schema(description =  "会话id")
    private String conversation_id;

    @Schema(description =  "json字符串，inputs 参数包含了多组键值对（Key/Value pairs）")
    private JSONObject inputs;

    @Schema(description =  "json数组，附件")
    private JSONArray files;

    @Schema(description = "应用的uuid")
    private String appUuid;

}
