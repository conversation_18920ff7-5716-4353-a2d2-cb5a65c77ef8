package cn.iocoder.yudao.aiBase.dto.request.dify;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ConversationsRequest extends DifyBaseRequest {

    @Schema(description =  "当前页最后面一条记录的 ID，默认 null")
    private String last_id;

    @Schema(description =  "一次请求返回多少条记录，默认 20")
    private Integer limit = 20;

    @Schema(description =  "排序字段，默认-updated_at，选项created_at, -created_at, updated_at, -updated_at")
    private String sort_by;

}
