package cn.iocoder.yudao.aiBase.dto.request.dify;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class FeedbackRequest extends DifyBaseRequest{

    @Schema(description =  "消息 ID")
    private String message_id;

    @Schema(description =  "点赞 like, 点踩 dislike, 撤销点赞 null")
    private String rating;

    @Schema(description =  "消息反馈的具体信息。")
    private String content;

}
