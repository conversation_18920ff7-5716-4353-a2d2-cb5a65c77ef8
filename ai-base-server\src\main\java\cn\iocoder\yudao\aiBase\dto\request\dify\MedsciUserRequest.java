package cn.iocoder.yudao.aiBase.dto.request.dify;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MedsciUserRequest {

    @Schema(description =  "三方用户类型，0主站，35谷歌，36Facebook，详见SocialTypeEnum")
    private Integer socialType = 0;

    @Schema(description =  "三方用户ID，主站的取plaintextUserId")
    @JsonProperty("plaintextUserId")
    private Long socialUserId;

    @JsonProperty("userId")
    @Schema(description =  "三方用户openid，主站取userId")
    private String openid;

    @Schema(description =  "用户名")
    private String userName;

    @Schema(description =  "头像")
    private String avatar;

    @Schema(description =  "手机号")
    private String mobile;

    @Schema(description =  "真实名")
    private String realName;

    @Schema(description =  "邮箱")
    private String email;

    @Schema(description =  "应用id")
    private String appUuid;

    @Schema(description =  "来源")
    private String fromPlatform;


}
