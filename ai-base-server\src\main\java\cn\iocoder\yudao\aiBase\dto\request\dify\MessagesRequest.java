package cn.iocoder.yudao.aiBase.dto.request.dify;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class MessagesRequest extends DifyBaseRequest {

    @NotBlank(message="{ERROR_5017}")
    @Schema(description =  "会话 ID")
    private String conversation_id;

    @Schema(description =  "当前页第一条聊天记录的 ID，默认 null")
    private String first_id;

    @Schema(description =  "一次请求返回多少条聊天记录，默认 20 条")
    private Integer limit = 20;

}
