package cn.iocoder.yudao.aiBase.dto.request.dify;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class MyCollectionRequest {

    @NotNull(message="{ERROR_5009}")
    @Schema(description =  "appIds")
    private List<String> appIds;

    @NotBlank(message="{ERROR_5012}")
    @Schema(description =  "用户名")
    private String user;

    @Schema(description =  "状态：传 正常 或 不传")
    private String status;

    @Schema(description =  "每页数量")
    private Integer pageSize = 50;

    @Schema(description =  "页码")
    private Integer pageIndex = 1;
}
