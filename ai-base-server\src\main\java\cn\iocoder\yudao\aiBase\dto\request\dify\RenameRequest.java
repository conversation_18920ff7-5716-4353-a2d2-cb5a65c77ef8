package cn.iocoder.yudao.aiBase.dto.request.dify;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Data
public class RenameRequest extends DifyBaseRequest {

    @Schema(description =  "会话Id")
    private String conversation_id;

    @NotNull(message="{ERROR_5018}")
    @Schema(description =  "名称")
    private String name;

    @Schema(description =  "自动生成标题，默认 false。")
    private Boolean autoGenerate = false;



}
