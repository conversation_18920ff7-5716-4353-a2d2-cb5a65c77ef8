package cn.iocoder.yudao.aiBase.dto.response;

import com.alibaba.excel.util.ListUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class AiAppResponse {

    @Schema(description =  "主键")
    private Integer id;

    @Schema(description =  "uuid")
    private String appUuid;

    @Schema(description =  "语言")
    private String appLang;

    @Schema(description =  "状态")
    private String appStatus;

    @Schema(description =  "didy应用uuid")
    @JsonProperty("dAppUuid")
    private String difyAppUuid;

    @Schema(description =  "应用名")
    private String appName;

    @Schema(description =  "应用名")
    private String appNameEn;

    @Schema(description =  "应用类型")
    private String appType;

    @Schema(description =  "图标")
    private String appIcon;

    @Schema(description =  "描述")
    private String appDescription;

    @Schema(description =  "应用结构")
    private String directoryMd;

    @Schema(description =  "当前登录人信息")
    private AiAppUserResponse appUser;

    @Schema(description = "links")
    private List<FeeTypeVo> feeTypes = ListUtils.newArrayList();

    @Schema(description = "是否是内部用户 0否 1是")
    private Integer isInternalUser;

    @Data
    public static class FeeTypeVo {

        @Schema(description = "1开启")
        private Integer online;

        @Schema(description = "订阅套餐")
        private String packageKey;

        @Schema(description = "订阅方式")
        private String type;

        @Schema(description = "周期类型，默认月")
        private String periodType = "MONTH";

        @Schema(description = "时长")
        private Integer monthNum;

        @Schema(description = "原价格")
        private BigDecimal oldPrice;

        @Schema(description = "价格")
        private BigDecimal feePrice;

        @Schema(description = "币种")
        private String coinType;

        @Schema(description = "价格ID，币种是人民币的是支付宝场景值，美元的是stripe")
        private String priceId;

        @Schema(description = "可使用次数，0不限制")
        private Integer num;
    }

}
