package cn.iocoder.yudao.aiBase.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AiAppUserResponse {
    @Schema(description =  "主键")
    private Integer id;

    @Schema(description =  "主站用户ID")
    private Long socialUserId;

    @Schema(description =  "三方类型")
    private Integer socialType;

    @Schema(description =  "语言")
    private String appUuid;

    @Schema(description =  "状态")
    private String status;

    @Schema(description =  "售出数量")
    private String useNum;

    @Schema(description =  "过期时间")
    private String expireAt;
}
