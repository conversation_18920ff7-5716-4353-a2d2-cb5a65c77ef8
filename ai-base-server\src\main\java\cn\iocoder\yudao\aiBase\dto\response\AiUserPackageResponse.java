package cn.iocoder.yudao.aiBase.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class AiUserPackageResponse {
    @Schema(description =  "主键")
    private Integer id;

    @Schema(description =  "三方id")
    private Integer socialUserId;

    @Schema(description =  "三方类型")
    private Integer socialType;

    @Schema(description =  "套餐")
    private String packageKey;

    @Schema(description =  "套餐类型")
    private String packageType;

    @Schema(description =  "状态")
    private Integer subStatus;

    @Schema(description =  "订购时间")
    private String subAt;

    @Schema(description =  "退订时间")
    private String unSubAt;

    @Schema(description =  "开始时间")
    private String startAt;

    @Schema(description =  "过期时间")
    private String expireAt;

    @Schema(description =  "上线的套餐")
    private List<AiAppResponse.FeeTypeVo> feeTypes;
}
