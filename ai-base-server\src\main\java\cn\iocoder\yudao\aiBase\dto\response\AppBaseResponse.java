package cn.iocoder.yudao.aiBase.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AppBaseResponse {

    @Schema(description =  "appId")
    private String appId;

    @Schema(description =  "name")
    private String name;

    @Schema(description =  "iconUrl")
    private String iconUrl;

    @Schema(description =  "mode")
    private String mode;

    @Schema(description =  "description")
    private String description;
}
