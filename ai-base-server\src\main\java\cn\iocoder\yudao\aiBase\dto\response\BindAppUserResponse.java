package cn.iocoder.yudao.aiBase.dto.response;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class BindAppUserResponse {

    @Schema(description = "三方用户id")
    private Long socialUserId;

    @Schema(description = "三方用户id")
    private Integer socialType;

    @Schema(description = "应用")
    private String appUuid;

    @Schema(description = "免费数量")
    private Integer freeNum = BaseConstant.THREE;

    @Schema(description = "使用数量")
    private Integer useNum = BaseConstant.THREE;

    @Schema(description = "剩余数量")
    private Integer remainNum = BaseConstant.ZERO;
}
