package cn.iocoder.yudao.aiBase.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DifyBaseResponse {

    @Schema(description =  "appId")
    private String appId;

    @Schema(description =  "name")
    private String name;

    @Schema(description =  "iconUrl")
    private String iconUrl;

    @Schema(description =  "prePrompt")
    private String prePrompt;

    @Schema(description =  "mode")
    private String mode;

    @Schema(description =  "suggestedQuestions")
    private String suggestedQuestions;
}
