package cn.iocoder.yudao.aiBase.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MyCollectionResponse {

    @Schema(description =  "appId")
    private String appId;

    @Schema(description =  "conversationId")
    private String conversationId;

    @Schema(description =  "name")
    private String name;

    @Schema(description =  "createdAt")
    private String createdAt;

    @Schema(description =  "collectionId")
    private String collectionId;

    @Schema(description =  "status")
    private String status;
}
