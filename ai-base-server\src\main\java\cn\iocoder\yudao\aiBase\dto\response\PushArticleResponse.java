package cn.iocoder.yudao.aiBase.dto.response;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PushArticleResponse {
    @Schema(description =  "主键")
    private  Integer id;

    @Schema(description =  "应用id")
    private String appUuid;

    @Schema(description =  "应用名")
    private String appName;

    @Schema(description =  "应用类型")
    private String appType;

    @Schema(description =  "资源id")
    private String articleId;

    @Schema(description =  "资源id")
    private JSONObject msArticle;


}
