package cn.iocoder.yudao.aiBase.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
@Data
public class QaResponse {
    @Schema(description =  "主键")
    private  Integer id;
    @Schema(description =  "加密id")
    private String encryptionId;
    @Schema(description =  "资源id")
    private String articleId;
    @Schema(description =  "用户名")
    private String userName;
    @Schema(description =  "问题")
    private String question;
    @Schema(description =  "答案")
    private String answer;
    @Schema(description =  "点击数量")
    private Integer clickNum;
    @Schema(description =  "类型")
    private String type;
    @Schema(description =  "创建时间")
    private Date createdAt;
    @Schema(description =  "修改时间")
    private Date updatedAt;
}
