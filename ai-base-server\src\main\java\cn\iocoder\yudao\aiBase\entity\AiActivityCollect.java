package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@TableName("ai_activity_collect")
@KeySequence("ai_activity_collect_id_seq")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiActivityCollect extends Model<AiActivityCollect> {

    @Schema(description = "主键")
    @TableId
    private Integer id;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "单位")
    private String company;

    @Schema(description = "职称")
    private String professional;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "应用名称(英文)")
    private String appNameEn;

    @Schema(description =  "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createdAt;

    @Schema(description =  "更新时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime updatedAt;

    @Schema(description =  "0正常 1删除")
    @TableLogic
    private Integer deleted;
}
