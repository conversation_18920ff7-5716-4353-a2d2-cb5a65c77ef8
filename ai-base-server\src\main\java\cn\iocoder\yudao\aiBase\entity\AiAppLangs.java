package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@TableName("ai_app_langs")
@KeySequence("ai_app_langs_id_seq")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiAppLangs extends Model<AiAppLangs> {

    @Schema(description =  "主键")
    @TableId
    private Integer id;

    @Schema(description =  "父id")
    private Integer pid;

    @Schema(description =  "uuid")
    private String appUuid;

    @Schema(description =  "stripe productId")
    private String productId;

    @Schema(description =  "语言")
    private String appLang;

    @Schema(description =  "状态")
    private String appStatus;

    @Schema(description =  "didy应用uuid")
    private String difyAppUuid;

    @Schema(description =  "应用名")
    private String appName;

    @Schema(description =  "唯一英文名")
    private String appNameEn;

    @Schema(description =  "应用类型")
    private String appType;

    @Schema(description =  "图标")
    private String appIcon;

    @Schema(description =  "描述")
    private String appDescription;

    @Schema(description =  "币种")
    private String coinType;

    @Schema(description =  "包月，1启用")
    private Integer feeType;

    @Schema(description =  "包月费用")
    private BigDecimal feePrice;

    @Schema(description =  "stripe包月价格ID")
    private String feePriceId;

    @Schema(description =  "包季，1启用")
    private Integer feeType2;

    @Schema(description =  "包季费用")
    private BigDecimal feePrice2;

    @Schema(description =  "stripe包季价格ID")
    private String feePriceId2;

    @Schema(description =  "包年")
    private Integer feeType3;

    @Schema(description =  "包年费用")
    private BigDecimal feePrice3;

    @Schema(description =  "stripe包年价格ID")
    private String feePriceId3;

    @Schema(description =  "应用结构")
    private String directoryMd;

    @Schema(description =  "上线的语言")
    private String onlineLangs;

    @Schema(description =  "售出数量")
    private Integer useNum;

    @Schema(description =  "点击数量")
    private Integer clickNum;

    @Schema(description =  "创建时间")
    private LocalDateTime creationTime;

    @Schema(description =  "更新时间")
    private LocalDateTime modificationTime;

    @Schema(description = "0外部应用 1内部应用 2活动应用")
    private Integer isInternalUser;
    
    @Schema(description = "自定义JS")
    private String customJs;

    @Schema(description = "自定义CSS")
    private String customCss;
}
