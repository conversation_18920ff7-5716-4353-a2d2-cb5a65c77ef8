package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 应用使用记录表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_base.ai_app_usage_records")
public class AiAppUsageRecords implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 社交用户ID
     */
    private Long socialUserId;

    /**
     * 社交类型
     */
    private Integer socialType;

    /**
     * 应用UUID
     */
    private String appUuid;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 是否删除 0=未删除 1=已删除
     */
    @TableLogic
    private Integer deleted;
} 