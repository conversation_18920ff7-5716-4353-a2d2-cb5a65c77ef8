package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@TableName("ai_app_user_package")
@KeySequence("ai_app_user_package_id_seq")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiAppUserPackage extends Model<AiAppUserPackage> {

    @Schema(description =  "主键")
    @TableId
    private Integer id;

    @Schema(description =  "主站用户ID")
    private Long socialUserId;

    @Schema(description =  "三方类型")
    private Integer socialType;

    @Schema(description =  "套餐")
    private String packageKey;

    @Schema(description =  "套餐类型")
    private String packageType;

    @Schema(description =  "套餐类型")
    private String checkoutSessionId;

    // 免费的只有0 和 1
    // 收费的有 0 1 3
    @Schema(description =  "状态 0已退订 1订阅中 2已过期 3退订中")
    private Integer subStatus;

    @Schema(description =  "订购时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime subAt;

    @Schema(description =  "退订时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime unSubAt;

    @Schema(description =  "开始时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime startAt;

    @Schema(description =  "过期时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime expireAt;

    @Schema(description =  "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createdAt;

    @Schema(description =  "更新时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime updatedAt;

    @Schema(description =  "0正常 1删除")
    private Integer deleted;
}
