package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@TableName("ai_file_asr")
@KeySequence("ai_file_asr_id_seq")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiFileAsr extends Model<AiFileAsr> {

    @Schema(description =  "主键")
    @TableId
    private Integer id;
    @Schema(description =  "uuid")
    private String uuid;

    @Schema(description =  "fileName")
    private String fileName;

    @Schema(description =  "文件md5")
    private String fileMd5;

    @Schema(description =  "解析出的文本")
    private String asrText;

    @Schema(description =  "备注信息")
    private String mate;

    @Schema(description =  "回调地址")
    private String callback;

    @Schema(description =  "0接收 1转译中 2转译完成 3转译失败 4转译超时")
    private Integer status;

    @Schema(description =  "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createdAt;

    @Schema(description =  "更新时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime updatedAt;

    @Schema(description =  "0正常，1删除")
    private Integer deleted;

}
