package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@TableName("ai_stripe_event_log")
@KeySequence("ai_stripe_event_log_id_seq")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiStripeEventLog extends Model<AiStripeEventLog> {

    @Schema(description =  "主键")
    @TableId
    private Integer id;

    @Schema(description =  "事件ID")
    private String eventId;

    @Schema(description =  "事件类型")
    private String eventType;

    @Schema(description =  "事件详情")
    private String stripeObject;

    @Schema(description =  "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createdAt;

    @Schema(description =  "更新时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime updatedAt;

    private Integer deleted;


}
