package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@TableName("ai_sub_orders")
@KeySequence("ai_sub_orders_id_seq")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiSubOrders extends Model<AiSubOrders> {

    @Schema(description =  "主键")
    @TableId
    private Integer id;

    @Schema(description =  "主站用户ID")
    private Long socialUserId;

    @Schema(description =  "三方类型")
    private Integer socialType;

    @Schema(description =  "关联订阅表checkoutSessionId")
    private String checkoutSessionId;

    @Schema(description =  "订单ID")
    private String piId;

    @Schema(description =  "支付状态")
    private String paymentStatus;

    @Schema(description =  "支付金额")
    private BigDecimal payAmount;

    @Schema(description =  "0免费 1付费")
    private Integer needPay;

    @Schema(description =  "本期开始时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime startAt;

    @Schema(description =  "本期截止时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime expireAt;

    @Schema(description =  "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createdAt;

    @Schema(description =  "更新时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime updatedAt;

    @Schema(description =  "0正常，1删除")
    private Integer deleted;

}
