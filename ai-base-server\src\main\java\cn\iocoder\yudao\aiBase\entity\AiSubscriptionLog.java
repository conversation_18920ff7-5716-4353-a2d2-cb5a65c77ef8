package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@TableName("ai_subscription_log")
@KeySequence("ai_subscription_log_id_seq")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiSubscriptionLog extends Model<AiSubscriptionLog> {

    @Schema(description =  "主键")
    @TableId
    private Integer id;

    @Schema(description =  "主站用户ID")
    private Long socialUserId;

    @Schema(description =  "三方类型")
    private Integer socialType;

    @Schema(description =  "医院uuid")
    private String appUuid;

    @Schema(description =  "客户id")
    private String stripeCustomerId;

    @Schema(description =  "价格ID")
    private String priceId;

    @Schema(description =  "价格")
    private BigDecimal payAmount;

    @Schema(description =  "订阅链接ID")
    private String checkoutSessionId;

    @Schema(description =  "支付状态")
    private String paymentStatus;

    @Schema(description =  "支付链接")
    private String url;

    @Schema(description =  "subId")
    private String subId;

    @Schema(description =  "订阅时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime subAt;

    @Schema(description =  "piId")
    private String piId;

    @Schema(description =  "月数")
    private Integer monthNum;

    @Schema(description =  "支付成功的月数")
    private Integer paymentNum;

    @Schema(description =  "取消订阅的时间id")
    private String unsubEventId;

    @Schema(description =  "过期时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime expiredAt;

    @Schema(description =  "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createdAt;

    @Schema(description =  "更新时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime updatedAt;

    private Integer deleted;
    private Integer packageId;


}
