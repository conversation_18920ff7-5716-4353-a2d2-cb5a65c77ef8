package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@TableName("api_tokens")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiTokens extends Model<ApiTokens> {

    @Schema(description =  "主键")
    @TableId
    private UUID id;

    @Schema(description =  "应用id")
    private String appId;

    @Schema(description =  "类型")
    private String type;

    @Schema(description =  "token")
    private String token;

    @Schema(description =  "最后使用时间")
    private LocalDateTime lastUsedAt;

    @Schema(description =  "创建时间")
    private LocalDateTime createdAt;

    @Schema(description =  "租户id")
    private UUID tenantId;

    @TableField(value = "count(1)", select = false, insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Integer num;


}
