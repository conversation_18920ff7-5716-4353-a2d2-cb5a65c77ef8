package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@TableName("messages")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Messages extends Model<Messages> {
    @Schema(description =  "主键")
    @TableId
    private UUID id;

    @Schema(description = "应用程序的唯一标识符")
    private UUID appId;

    @Schema(description = "模型提供者")
    private String modelProvider;

    @Schema(description = "模型的唯一标识符")
    private String modelId;

    @Schema(description = "覆盖模型配置的文本")
    private String overrideModelConfigs;

    @Schema(description = "会话的唯一标识符")
    private UUID conversationId;

    @Schema(description = "输入的JSON数据")
    private String inputs;

    @Schema(description = "查询文本")
    private String query;

    @Schema(description = "消息的JSON数据")
    private String message;

    @Schema(description = "消息的令牌数量")
    private int messageTokens;

    @Schema(description = "消息的单价")
    private BigDecimal messageUnitPrice;

    @Schema(description = "回答文本")
    private String answer;

    @Schema(description = "回答的令牌数量")
    private int answerTokens;

    @Schema(description = "回答的单价")
    private BigDecimal answerUnitPrice;

    @Schema(description = "提供者响应的延迟时间")
    private double providerResponseLatency;

    @Schema(description = "总价格")
    private BigDecimal totalPrice;

    @Schema(description = "货币类型")
    private String currency;

    @Schema(description = "来源")
    private String fromSource;

    @Schema(description = "终端用户的唯一标识符")
    private UUID fromEndUserId;

    @Schema(description = "账户的唯一标识符")
    private UUID fromAccountId;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "是否基于代理")
    private boolean agentBased;

    @Schema(description = "消息价格单位")
    private BigDecimal messagePriceUnit;

    @Schema(description = "回答价格单位")
    private BigDecimal answerPriceUnit;

    @Schema(description = "工作流运行的唯一标识符")
    private UUID workflowRunId;

    @Schema(description = "消息状态")
    private String status;

    @Schema(description = "错误信息")
    private String error;

    @Schema(description = "消息元数据")
    private String messageMetadata;

    @Schema(description = "调用来源")
    private String invokeFrom;

    @Schema(description = "父消息的唯一标识符")
    private UUID parentMessageId;
}
