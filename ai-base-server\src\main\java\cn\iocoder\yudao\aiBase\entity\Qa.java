package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@TableName("medsci_article_qa")
@KeySequence("medsci_article_qa_id_seq")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public  class Qa extends Model<Qa> {
    @Schema(description =  "主键")
    @TableId
    private  Integer id;
    @Schema(description =  "资源id")
    private String articleId;
    @Schema(description =  "用户名")
    private String userName;
    @Schema(description =  "问题")
    private String question;
    @Schema(description =  "答案")
    private String answer;
    @Schema(description =  "点击数量")
    private Integer clickNum;
    @Schema(description =  "类型")
    private String type;
    @Schema(description =  "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createdAt;
    @Schema(description =  "更新时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime updatedAt;
    @Schema(description = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer deleted;

    @Schema(description =  "加密id")
    private String encryptionId;
}
