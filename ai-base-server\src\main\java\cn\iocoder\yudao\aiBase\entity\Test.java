package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023-09-15
 */
@TableName("test")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Test extends Model<Test> {

    @Schema(description =  "主键")
    private Integer id;

    @Schema(description =  "科室名称")
    private String name;

    @TableField(value = "count(1)", select = false, insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Integer num;

}
