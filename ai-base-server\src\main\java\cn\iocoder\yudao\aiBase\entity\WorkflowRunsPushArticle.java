package cn.iocoder.yudao.aiBase.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

@TableName("workflow_runs_push_article")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowRunsPushArticle {

    @Schema(description =  "主键")
    @TableId
    private UUID id;

    @Schema(description =  "资源id")
    private String articleId;

    @Schema(description =  "应用id")
    private String appUuid;

    @Schema(description =  "应用名")
    private String appName;

    @Schema(description =  "应用类型")
    private String appType;

    @Schema(description =  "dify id")
    private String difyAppId;

    @Schema(description =  "dify应用名")
    private String difyName;

    @Schema(description =  "文字标题")
    private String seoTitle;

    @Schema(description =  "文字简介")
    private String seoDescription;

    @Schema(description =  "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime createdAt;

    @Schema(description =  "更新时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
    private LocalDateTime updatedAt;

}
