package cn.iocoder.yudao.aiBase.mapper;

import cn.iocoder.yudao.aiBase.dto.param.AiAppParam;
import cn.iocoder.yudao.aiBase.dto.response.SubscriptionCountResponse;
import cn.iocoder.yudao.aiBase.entity.AiAppLangs;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 操作记录
 */
@Mapper
public interface AiAppLangsMapper extends BaseMapperX<AiAppLangs> {

    default List<AiAppLangs> selectList(AiAppParam reqVO) {
        LambdaQueryWrapperX<AiAppLangs> wrapper = new LambdaQueryWrapperX<>();
        wrapper
                .eqIfPresent(AiAppLangs::getAppUuid, reqVO.getAppUuid())
                .eqIfPresent(AiAppLangs::getAppNameEn, reqVO.getAppNameEn())
                .inIfPresent(AiAppLangs::getAppNameEn, reqVO.getAppNameEns())
                .eqIfPresent(AiAppLangs::getAppLang, reqVO.getAppLang())
                .eqIfPresent(AiAppLangs::getAppType, reqVO.getAppType())
                .eqIfPresent(AiAppLangs::getDifyAppUuid, reqVO.getDifyAppUuid())
                .eqIfPresent(AiAppLangs::getAppStatus, reqVO.getAppStatus())
                .inIfPresent(AiAppLangs::getIsInternalUser, reqVO.getIsInternalUsers())
                .orderByDesc(reqVO.getOrder()!=null && reqVO.getOrder() ==1, AiAppLangs::getUseNum)
                .orderByDesc(reqVO.getOrder()!=null && reqVO.getOrder() ==2, AiAppLangs::getClickNum)
                .ne(AiAppLangs::getAppStatus, "删除")
//                .orderByDesc(AiAppLangs::getIsInternalUser)
                .orderByDesc(AiAppLangs::getId)
                .last(reqVO.getLastLimit()!=null && reqVO.getLastLimit() > 0, "limit "+reqVO.getLastLimit());

        return selectList(wrapper);
    }
    default List<AiAppLangs> getSiteMapList() {
        LambdaQueryWrapperX<AiAppLangs> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(AiAppLangs::getAppStatus, "上架");
        return selectList(wrapper);
    }


    List<SubscriptionCountResponse> countSubscriptions();

}
