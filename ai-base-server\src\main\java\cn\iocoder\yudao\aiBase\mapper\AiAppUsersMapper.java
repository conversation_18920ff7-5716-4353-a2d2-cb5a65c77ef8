package cn.iocoder.yudao.aiBase.mapper;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.dto.param.AppUserParam;
import cn.iocoder.yudao.aiBase.entity.AiAppUsers;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 操作记录
 */
@Mapper
public interface AiAppUsersMapper extends BaseMapperX<AiAppUsers> {

    default List<AiAppUsers> selectList(AppUserParam reqVO) {
        return selectList(new LambdaQueryWrapperX<AiAppUsers>()
            .eqIfPresent(AiAppUsers::getSocialUserId, reqVO.getSocialUserId())
            .eqIfPresent(AiAppUsers::getSocialType, reqVO.getSocialType())
            .eqIfPresent(AiAppUsers::getAppUuid, reqVO.getAppUuid())
            .eqIfPresent(AiAppUsers::getStatus, reqVO.getStatus())
            .betweenIfPresent(AiAppUsers::getExpireAt, reqVO.getExpiredAt())
            .eq(AiAppUsers::getDeleted, BaseConstant.ZERO)
            .orderByDesc(AiAppUsers::getId));
    }

}
