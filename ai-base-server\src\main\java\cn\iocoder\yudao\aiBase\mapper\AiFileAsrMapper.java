package cn.iocoder.yudao.aiBase.mapper;

import cn.iocoder.yudao.aiBase.dto.param.FileAsrParam;
import cn.iocoder.yudao.aiBase.dto.request.AppUserPageReqVO;
import cn.iocoder.yudao.aiBase.entity.AiFileAsr;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 操作记录
 */
@Mapper
public interface AiFileAsrMapper extends BaseMapperX<AiFileAsr> {

    default PageResult<AiFileAsr> selectPage(AppUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiFileAsr>()
            .betweenIfPresent(AiFileAsr::getCreatedAt, reqVO.getCreatedAt())
            .orderByDesc(AiFileAsr::getId));
    }

    default List<AiFileAsr> selectList(FileAsrParam reqVO) {
        return selectList(new LambdaQueryWrapperX<AiFileAsr>()
                .eqIfPresent(AiFileAsr::getUuid, reqVO.getUuid())
                .eqIfPresent(AiFileAsr::getFileMd5, reqVO.getFileMd5())
                .eqIfPresent(AiFileAsr::getStatus, reqVO.getStatus())
                .likeIfPresent(AiFileAsr::getFileName, reqVO.getFileName())
                .betweenIfPresent(AiFileAsr::getCreatedAt, reqVO.getCreatedAt())
                .orderByDesc(AiFileAsr::getId));
    }

}
