package cn.iocoder.yudao.aiBase.mapper;


import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.dto.request.EventPageReqVO;
import cn.iocoder.yudao.aiBase.entity.AiStripeEventLog;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;


/**
 * 操作记录
 */
@Mapper
public interface AiStripeEventLogMapper extends BaseMapperX<AiStripeEventLog> {

    default PageResult<AiStripeEventLog> selectPage(EventPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiStripeEventLog>()
            .and(reqVO.getKeyword()!=null, query -> {
                query.like(AiStripeEventLog::getEventId, reqVO.getKeyword())
                    .or()
                    .like(AiStripeEventLog::getEventType, reqVO.getKeyword())
                    .or()
                    .like(AiStripeEventLog::getStripeObject, reqVO.getKeyword());
            })
            .eq(AiStripeEventLog::getDeleted, BaseConstant.ZERO)
            .orderByDesc(AiStripeEventLog::getId));
    }
}
