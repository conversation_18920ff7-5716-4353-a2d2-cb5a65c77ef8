package cn.iocoder.yudao.aiBase.mapper;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.dto.request.AppUserPageReqVO;
import cn.iocoder.yudao.aiBase.entity.AiSubOrders;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 操作记录
 */
@Mapper
public interface AiSubOrdersMapper extends BaseMapperX<AiSubOrders> {

    default PageResult<AiSubOrders> selectPage(AppUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiSubOrders>()
            .eqIfPresent(AiSubOrders::getSocialUserId, reqVO.getSocialUserId())
            .eqIfPresent(AiSubOrders::getSocialType, reqVO.getSocialType())
            .eqIfPresent(AiSubOrders::getCheckoutSessionId, reqVO.getCheckoutSessionId())
            .eqIfPresent(AiSubOrders::getPiId, reqVO.getPiId())
            .betweenIfPresent(AiSubOrders::getCreatedAt, reqVO.getCreatedAt())
            .betweenIfPresent(AiSubOrders::getExpireAt, reqVO.getExpiredAt())
            .eq(AiSubOrders::getDeleted, BaseConstant.ZERO)
            .orderByDesc(AiSubOrders::getId));
    }

    default List<AiSubOrders> selectList(AppUserPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AiSubOrders>()
            .eqIfPresent(AiSubOrders::getSocialUserId, reqVO.getSocialUserId())
            .eqIfPresent(AiSubOrders::getSocialType, reqVO.getSocialType())
            .eqIfPresent(AiSubOrders::getCheckoutSessionId, reqVO.getCheckoutSessionId())
            .eqIfPresent(AiSubOrders::getPiId, reqVO.getPiId())
            .eqIfPresent(AiSubOrders::getPaymentStatus, reqVO.getPaymentStatus())
            .betweenIfPresent(AiSubOrders::getCreatedAt, reqVO.getCreatedAt())
            .betweenIfPresent(AiSubOrders::getExpireAt, reqVO.getExpiredAt())
            .eq(AiSubOrders::getDeleted, BaseConstant.ZERO)
            .orderByDesc(AiSubOrders::getId));
    }

}
