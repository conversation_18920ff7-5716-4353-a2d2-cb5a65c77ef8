package cn.iocoder.yudao.aiBase.mapper;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.dto.request.AppUserPageReqVO;
import cn.iocoder.yudao.aiBase.entity.AiSubscriptionLog;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 操作记录
 */
@Mapper
public interface AiSubscriptionLogMapper extends BaseMapperX<AiSubscriptionLog> {

    default PageResult<AiSubscriptionLog> selectPage(AppUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AiSubscriptionLog>()
            .eqIfPresent(AiSubscriptionLog::getSocialUserId, reqVO.getSocialUserId())
            .eqIfPresent(AiSubscriptionLog::getSocialType, reqVO.getSocialType())
            .eqIfPresent(AiSubscriptionLog::getAppUuid, reqVO.getAppUuid())
            .eqIfPresent(AiSubscriptionLog::getPriceId, reqVO.getPriceId())
            .eqIfPresent(AiSubscriptionLog::getCheckoutSessionId, reqVO.getCheckoutSessionId())
            .eqIfPresent(AiSubscriptionLog::getSubId, reqVO.getSubId())
            .eqIfPresent(AiSubscriptionLog::getPiId, reqVO.getPiId())
            .betweenIfPresent(AiSubscriptionLog::getCreatedAt, reqVO.getCreatedAt())
            .eq(AiSubscriptionLog::getDeleted, BaseConstant.ZERO)
            .orderByDesc(AiSubscriptionLog::getId));
    }

    default List<AiSubscriptionLog> selectList(AppUserPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AiSubscriptionLog>()
            .eqIfPresent(AiSubscriptionLog::getSocialUserId, reqVO.getSocialUserId())
            .eqIfPresent(AiSubscriptionLog::getSocialType, reqVO.getSocialType())
            .eqIfPresent(AiSubscriptionLog::getAppUuid, reqVO.getAppUuid())
            .eqIfPresent(AiSubscriptionLog::getPriceId, reqVO.getPriceId())
            .eqIfPresent(AiSubscriptionLog::getCheckoutSessionId, reqVO.getCheckoutSessionId())
            .eqIfPresent(AiSubscriptionLog::getSubId, reqVO.getSubId())
            .eqIfPresent(AiSubscriptionLog::getPiId, reqVO.getPiId())
            .betweenIfPresent(AiSubscriptionLog::getCreatedAt, reqVO.getCreatedAt())
            .eq(AiSubscriptionLog::getDeleted, BaseConstant.ZERO)
            .orderByDesc(AiSubscriptionLog::getId));
    }

}
