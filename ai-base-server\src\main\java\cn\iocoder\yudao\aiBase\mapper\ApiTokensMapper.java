package cn.iocoder.yudao.aiBase.mapper;


import cn.iocoder.yudao.aiBase.dto.response.AppBaseResponse;
import cn.iocoder.yudao.aiBase.dto.response.DifyBaseResponse;
import cn.iocoder.yudao.aiBase.dto.response.MyCollectionResponse;
import cn.iocoder.yudao.aiBase.entity.ApiTokens;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 操作记录
 */
@Mapper
public interface ApiTokensMapper extends BaseMapperX<ApiTokens> {

    DifyBaseResponse getAppPrePrompt(@Param("appId") String appId);

    List<DifyBaseResponse> getAppsPrePrompt(@Param("appIds") List<String> appIds);

    List<MyCollectionResponse> myCollection(
        @Param("appIds") List<String> appIds,
        @Param("user") String user,
        @Param("status") String status,
        @Param("limit") Integer limit,
        @Param("offset") Integer offset
    );

    List<AppBaseResponse> getAppsByTag(@Param("tag") String tag);

    List<DifyBaseResponse> getAppsByName(@Param("name") String name);

    String getDifyAccount(@Param("email") String email);

    Integer saveDifyAccount(@Param("email") String email, @Param("name") String name);

    Integer saveTenantAccount(@Param("id") String id);

    Integer activeDifyAccount(@Param("email") String email);

}
