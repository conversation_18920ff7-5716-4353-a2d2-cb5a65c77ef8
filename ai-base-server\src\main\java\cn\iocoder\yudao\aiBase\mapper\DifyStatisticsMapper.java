package cn.iocoder.yudao.aiBase.mapper;

import cn.iocoder.yudao.aiBase.dto.request.difyStatistics.DifyStatisticsMessageReqVo;
import cn.iocoder.yudao.aiBase.dto.response.DifyStatisticsMessageResponse;
import cn.iocoder.yudao.aiBase.entity.Messages;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DifyStatisticsMapper extends BaseMapperX<Messages> {
    List<DifyStatisticsMessageResponse> selectMessages(DifyStatisticsMessageReqVo reqVO);

    long countMessages(DifyStatisticsMessageReqVo reqVO);
    
}
