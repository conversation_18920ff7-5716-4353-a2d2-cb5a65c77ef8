package cn.iocoder.yudao.aiBase.mapper;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.dto.param.MsUserParam;
import cn.iocoder.yudao.aiBase.dto.request.MedsciUsersPageReqVO;
import cn.iocoder.yudao.aiBase.entity.MedsciUsers;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 操作记录
 */
@Mapper
public interface MedsciUsersMapper extends BaseMapperX<MedsciUsers> {

    default PageResult<MedsciUsers> selectPage(MedsciUsersPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MedsciUsers>()
            .eqIfPresent(MedsciUsers::getSocialUserId, reqVO.getSocialUserId())
            .eqIfPresent(MedsciUsers::getOpenid, reqVO.getOpenid())
            .eqIfPresent(MedsciUsers::getSocialType, reqVO.getSocialType())
            .likeIfPresent(MedsciUsers::getEmail, reqVO.getEmail())
            .likeIfPresent(MedsciUsers::getUserName, reqVO.getUserName())
            .eqIfPresent(MedsciUsers::getMobile, reqVO.getMobile())
            .likeIfPresent(MedsciUsers::getRealName, reqVO.getRealName())
            .betweenIfPresent(MedsciUsers::getCreatedAt, reqVO.getCreatedAt())
            .eqIfPresent(MedsciUsers::getStatus, reqVO.getStatus())
            .betweenIfPresent(MedsciUsers::getExpireAt, reqVO.getExpireAt())
            .ltIfPresent(MedsciUsers::getExpireAt, reqVO.getExpireAt1())
            .eq(MedsciUsers::getDeleted, BaseConstant.ZERO)
            .orderByDesc(MedsciUsers::getId));
    }

    default List<MedsciUsers> selectList(MsUserParam reqVO) {
        return selectList(new LambdaQueryWrapperX<MedsciUsers>()
            .eqIfPresent(MedsciUsers::getSocialUserId, reqVO.getSocialUserId())
            .eqIfPresent(MedsciUsers::getOpenid, reqVO.getOpenid())
            .eqIfPresent(MedsciUsers::getSocialType, reqVO.getSocialType())
            .eqIfPresent(MedsciUsers::getEmail, reqVO.getEmail())
            .eqIfPresent(MedsciUsers::getUserName, reqVO.getUserName())
            .eqIfPresent(MedsciUsers::getMobile, reqVO.getMobile())
            .likeIfPresent(MedsciUsers::getRealName, reqVO.getRealName())
            .eqIfPresent(MedsciUsers::getStatus, reqVO.getStatus())
            .eqIfPresent(MedsciUsers::getIsInternalUser, reqVO.getIsInternalUser())
            .eq(MedsciUsers::getDeleted, BaseConstant.ZERO)
            .orderByDesc(MedsciUsers::getId));
    }

    default List<MedsciUsers> selectList(List<Long> socialUserIds, List<Integer> socialTypes) {
        return selectList(new LambdaQueryWrapperX<MedsciUsers>()
                .inIfPresent(MedsciUsers::getSocialType, socialTypes)
                .inIfPresent(MedsciUsers::getSocialUserId, socialUserIds)
                .eq(MedsciUsers::getDeleted, BaseConstant.ZERO)
                .orderByDesc(MedsciUsers::getId));
    }

}
