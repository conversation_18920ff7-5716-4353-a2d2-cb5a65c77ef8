package cn.iocoder.yudao.aiBase.mapper;

import cn.iocoder.yudao.aiBase.dto.param.AiAppParam;
import cn.iocoder.yudao.aiBase.dto.param.QaParam;
import cn.iocoder.yudao.aiBase.entity.AiAppLangs;
import cn.iocoder.yudao.aiBase.entity.Qa;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

import static com.baomidou.mybatisplus.extension.toolkit.Db.remove;

@Mapper
public interface QaMapper extends BaseMapperX<Qa> {
    default List<Qa> selectList(QaParam reqVO) {
        LambdaQueryWrapperX<Qa> wrapper = new LambdaQueryWrapperX<>();
        wrapper
                .eqIfPresent(Qa::getId, reqVO.getId())
                .eqIfPresent(Qa::getArticleId, reqVO.getArticleId())
                .eqIfPresent(Qa::getEncryptionId, reqVO.getEncryptionId())
                .last("ORDER BY RANDOM() LIMIT " + reqVO.getCount());

        return selectList(wrapper);
    }

    // 根据文章ID删除历史数据
    default void deleteByArticleId(String articleId) {
        if (StringUtils.isEmpty(articleId)) {
            return;
        }

        delete(new LambdaQueryWrapper<Qa>().eq(Qa::getArticleId, articleId));
    }

    default List<Qa> selectInitList(QaParam reqVO) {
        LambdaQueryWrapperX<Qa> wrapper = new LambdaQueryWrapperX<>();
        wrapper
                .eq(Qa::getEncryptionId, reqVO.getEncryptionId())
                .ltIfPresent(Qa::getId, reqVO.getId())
                .orderByDesc(Qa::getId)
                .last("LIMIT 1000");

        return selectList(wrapper);
    }

}
