package cn.iocoder.yudao.aiBase.mapper;


import cn.iocoder.yudao.aiBase.entity.Test;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;

import org.apache.ibatis.annotations.Mapper;


/**
 * 操作记录
 */
@Mapper
public interface TestMapper extends BaseMapperX<Test> {

//    @Cacheable(value = DBConstant.CACHE_NAME, key = "'hash0-' + #p0.hashCode()")
//    List<DoctorEchartsResponse> getAllDoctorEcharts(@Param("request") BaseParam request);
}
