package cn.iocoder.yudao.aiBase.mapper;

import cn.iocoder.yudao.aiBase.entity.WorkflowRunsPushArticle;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.UUID;

@Mapper
public interface WorkflowRunsPushArticleMapper extends BaseMapperX<WorkflowRunsPushArticle> {

    default List<WorkflowRunsPushArticle> getList(WorkflowRunsPushArticle reqVO) {
        LambdaQueryWrapperX<WorkflowRunsPushArticle> wrapper = new LambdaQueryWrapperX<>();
        wrapper
                .eqIfPresent(WorkflowRunsPushArticle::getId, reqVO.getId())
                .eqIfPresent(WorkflowRunsPushArticle::getArticleId, reqVO.getArticleId())
                .orderByDesc(WorkflowRunsPushArticle::getCreatedAt);

        return selectList(wrapper);
    }

    default WorkflowRunsPushArticle getArticle(UUID id) {
        WorkflowRunsPushArticle reqVO = new WorkflowRunsPushArticle();
        reqVO.setId(id);
        List<WorkflowRunsPushArticle> list = getList(reqVO);

        return list.isEmpty() ? null : list.get(0);
    }

}
