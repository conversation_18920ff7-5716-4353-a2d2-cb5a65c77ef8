<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.aiBase.mapper.AiAppLangsMapper">

    <select id="countSubscriptions" resultType="cn.iocoder.yudao.aiBase.dto.response.SubscriptionCountResponse">
        SELECT
            ai_app_langs.app_uuid AS appUuid,
            ai_app_langs.app_lang AS lang,
            ai_app_langs.app_name AS appName,
            COUNT(ai_app_users.id) AS count
        FROM
            ai_app_users
        JOIN
            ai_app_langs
            ON ai_app_users.app_uuid::uuid = ai_app_langs.app_uuid
        WHERE
            ai_app_users.status = 1
            AND ai_app_users.expire_at > CURRENT_TIMESTAMP
        GROUP BY
            ai_app_langs.app_uuid, ai_app_langs.app_lang, ai_app_langs.app_name
        ORDER BY
            count DESC;
    </select>

</mapper>
