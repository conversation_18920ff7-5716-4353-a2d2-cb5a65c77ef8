<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.aiBase.mapper.ApiTokensMapper">

    <select id="getAppPrePrompt" resultType="cn.iocoder.yudao.aiBase.dto.response.DifyBaseResponse">
        select a.id as appId, a.name, a.icon as iconUrl, b.pre_prompt as prePrompt, a.mode from apps a left join app_model_configs b
        on a.app_model_config_id = b.id
        where a.id = cast(#{appId} as uuid)
    </select>

    <select id="getAppsPrePrompt" resultType="cn.iocoder.yudao.aiBase.dto.response.DifyBaseResponse">
        select a.id as appId, a.name, a.icon as iconUrl, b.pre_prompt, a.mode, b.suggested_questions from apps a left join app_model_configs b
        on a.app_model_config_id = b.id
        where a.id in
        <foreach collection="appIds" item="appId" open="(" close=")" separator=",">
            cast(#{appId} as uuid)
        </foreach>
    </select>

    <select id="myCollection" resultType="cn.iocoder.yudao.aiBase.dto.response.MyCollectionResponse">
        select
        a.app_id,
        a.id as conversation_id,
        a.name,
        a.created_at,
        c.id as collection_id ,
        c.status
        from
        conversations a
        inner join end_users b on
        a.from_end_user_id = b.id
        left join collections c on
        a.id = c.conversation_id
        where
        b.session_id = #{user}
        and a.app_id in
        <foreach collection="appIds" item="appId" open="(" close=")" separator=",">
            cast(#{appId} as uuid)
        </foreach>
        <if test="status != null and status != ''">
            and c.status = #{status}
        </if>
        order by a.created_at desc
        limit #{limit} offset #{offset}

    </select>

    <select id="getAppsByTag" resultType="cn.iocoder.yudao.aiBase.dto.response.AppBaseResponse">
        select a.id as appId, a.name, a.icon as iconUrl, a.mode, a.description
        from apps a
        left join tag_bindings b on a.id = b.target_id
        inner join tags c on b.tag_id = c.id
        where
        c."name" = #{tag}
        and c."type" = 'app'

    </select>

    <select id="getAppsByName" resultType="cn.iocoder.yudao.aiBase.dto.response.DifyBaseResponse">
        select a.id as appId, a.name, a.icon as iconUrl, b.pre_prompt as prePrompt, a.mode from apps a left join app_model_configs b
        on a.app_model_config_id = b.id
        where a.name like CONCAT('%', #{name}, '%')
    </select>

    <select id="getDifyAccount" resultType="java.lang.String">
        select id from accounts where email = #{email}
    </select>

    <insert id="saveDifyAccount">
        insert into accounts (email, name, password, password_salt, status, interface_language, interface_theme, timezone, initialized_at)
        values (#{email}, #{name}, 'OWFjM2IzMjAzNjFlMjNjOWYwZDQ1MWNmNDI4MmQyMDcyMjVkZmJmNzNmZmNjMGQyZTk3ZTM0NGM2MThlNzk4Yw==', 'A9pdzgXXZEUPG4rJhM6EQQ==', 'active',
        'zh-Hans', 'light', 'Asia/Shanghai', now())
    </insert>

    <insert id="saveTenantAccount">
        insert into tenant_account_joins (account_id, tenant_id, role)
        values (#{id}::uuid, 'defe0924-dc2c-4a42-a49b-d580ff67bf67', 'editor')
    </insert>

    <update id="activeDifyAccount" >
        update accounts set
        password = 'OWFjM2IzMjAzNjFlMjNjOWYwZDQ1MWNmNDI4MmQyMDcyMjVkZmJmNzNmZmNjMGQyZTk3ZTM0NGM2MThlNzk4Yw==',
        password_salt = 'A9pdzgXXZEUPG4rJhM6EQQ==',
        status = 'active'
        where email = #{email}
    </update>

</mapper>