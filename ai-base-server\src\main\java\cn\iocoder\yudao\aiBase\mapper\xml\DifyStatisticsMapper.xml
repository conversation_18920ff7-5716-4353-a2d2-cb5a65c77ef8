<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.iocoder.yudao.aiBase.mapper.DifyStatisticsMapper">

    <select id="selectMessages" resultType="cn.iocoder.yudao.aiBase.dto.response.DifyStatisticsMessageResponse">
        WITH m_combined AS (
        SELECT
        m.conversation_id,
        COUNT(m.id) AS message_count,
        SUM(m.message_tokens + m.answer_tokens) AS total_tokens,
        AVG(m.provider_response_latency) AS avg_latency,
        SUM(m.provider_response_latency) AS total_latency,
        MAX(CASE WHEN m.status = 'error' THEN 1 ELSE 0 END) AS has_error
        FROM public.messages m
        INNER JOIN public.conversations c ON m.conversation_id = c.id
        WHERE 1=1
        <if test="appId != null and appId != ''">
            AND c.app_id = CAST(#{appId}::text AS UUID)
        </if>
        <if test="startTime != null and endTime != null">
            AND c.created_at BETWEEN TO_TIMESTAMP(#{startTime} / 1000) AND TO_TIMESTAMP(#{endTime} / 1000)
        </if>
        GROUP BY m.conversation_id
        ),
        wne_stats AS (
        SELECT
        wne.workflow_run_id,
        COUNT(wne.id) AS execution_count,
        SUM(wne.elapsed_time) AS total_elapsed_time,
        SUM(CAST((wne.execution_metadata::json->>'total_tokens') AS INTEGER)) AS total_tokens,
        MAX(CASE WHEN wne.status = 'failed' THEN 1 ELSE 0 END) AS has_error
        FROM public.workflow_node_executions wne
        INNER JOIN public.workflow_app_logs wal ON wne.workflow_run_id = wal.workflow_run_id
        WHERE 1=1
        <if test="appId != null and appId != ''">
            AND wal.app_id = CAST(#{appId}::text AS UUID)
        </if>
        <if test="startTime != null and endTime != null">
            AND wal.created_at BETWEEN TO_TIMESTAMP(#{startTime} / 1000) AND TO_TIMESTAMP(#{endTime} / 1000)
        </if>
        GROUP BY wne.workflow_run_id
        ),
        app_data AS (
        SELECT
        a.id,
        a.name AS app_name,
        COALESCE(c.app_id, wal.app_id) AS app_id,
        c.id AS conversation_id,
        c.name AS conversation_name,
        c.status,
        wal.workflow_run_id,
        COALESCE(c.created_at, wal.created_at) AS created_at,
        COALESCE(wal_eu.session_id, eu.session_id, 'medsciUser') AS session_id
        FROM
        public.apps a
        LEFT JOIN public.conversations c ON a.id = c.app_id
        LEFT JOIN public.workflow_app_logs wal ON a.id = wal.app_id
        LEFT JOIN public.end_users eu ON c.from_end_user_id = eu.id
        LEFT JOIN public.end_users wal_eu ON wal.created_by = wal_eu.id
        WHERE
        1=1
        AND COALESCE(c.app_id, wal.app_id) IS NOT NULL
        <if test="appId != null and appId != ''">
            AND COALESCE(c.app_id, wal.app_id) = CAST(#{appId}::text AS UUID)
        </if>
        <if test="appName != null and appName != ''">
            AND a.name LIKE CONCAT('%', #{appName}, '%')
        </if>
        <if test="conversationName != null and conversationName != ''">
            AND c.name LIKE CONCAT('%', #{conversationName}, '%')
        </if>
        <if test="conversationStatus != null and conversationStatus != ''">
            AND (
            CASE
            WHEN m_combined.has_error = 1 THEN 'error'
            WHEN wne_stats.has_error = 1 THEN 'error'
            WHEN c.status IS NOT NULL THEN c.status
            WHEN wne_stats.workflow_run_id IS NOT NULL THEN
            CASE
            WHEN wne_stats.has_error = 0 THEN 'normal'
            ELSE 'unknown'
            END
            ELSE NULL
            END
            ) = #{conversationStatus}
        </if>
        <if test="sessionId != null and sessionId != ''">
            AND COALESCE(wal_eu.session_id, eu.session_id, 'medsciUser') = #{sessionId}
        </if>
        <if test="startTime != null and endTime != null">
            AND COALESCE(c.created_at, wal.created_at) BETWEEN TO_TIMESTAMP(#{startTime} / 1000) AND TO_TIMESTAMP(#{endTime} / 1000)
        </if>
        <if test="minTokens != null or maxTokens != null">
            AND (
            <if test="minTokens != null">
                (COALESCE(m_combined.total_tokens, 0) + COALESCE(wne_stats.total_tokens, 0)) <![CDATA[ >= #{minTokens} ]]>
            </if>
            <if test="minTokens != null and maxTokens != null">
                AND
            </if>
            <if test="maxTokens != null">
                (COALESCE(m_combined.total_tokens, 0) + COALESCE(wne_stats.total_tokens, 0)) <![CDATA[ <= #{maxTokens} ]]>
            </if>
            )
        </if>
        <if test="tagIds != null and tagIds.length > 0">
            AND EXISTS (
            SELECT 1
            FROM public.tag_bindings tb
            WHERE tb.target_id = a.id
            AND tb.tag_id IN
            <foreach item="tagId" collection="tagIds" open="(" separator="," close=")">
                CAST(#{tagId}::text AS UUID)
            </foreach>
            )
        </if>
        <if test="excludeTagIds != null and excludeTagIds.length > 0">
            AND NOT EXISTS (
            SELECT 1
            FROM public.tag_bindings tb_exclude
            WHERE tb_exclude.target_id = a.id
            AND tb_exclude.tag_id IN
            <foreach item="tagId" collection="excludeTagIds" open="(" separator="," close=")">
                CAST(#{tagId}::text AS UUID)
            </foreach>
            )
        </if>
        )
        SELECT DISTINCT
        ad.app_id,
        ad.app_name,
        ad.conversation_id,
        ad.conversation_name,
        CASE
        WHEN m_combined.has_error = 1 THEN 'error'
        WHEN wne_stats.has_error = 1 THEN 'error'
        WHEN ad.status IS NOT NULL THEN ad.status
        WHEN wne_stats.workflow_run_id IS NOT NULL THEN
        CASE
        WHEN wne_stats.has_error = 0 THEN 'normal'
        ELSE 'unknown'
        END
        ELSE NULL
        END AS conversation_status,
        ad.session_id,
        m_combined.message_count,
        COALESCE(m_combined.total_tokens, 0) + COALESCE(wne_stats.total_tokens, 0) AS total_tokens,
        CASE
        WHEN (COALESCE(m_combined.message_count, 0) + COALESCE(wne_stats.execution_count, 0)) > 0
        THEN (COALESCE(m_combined.total_latency, 0) + COALESCE(wne_stats.total_elapsed_time, 0)) /
        (COALESCE(m_combined.message_count, 0) + COALESCE(wne_stats.execution_count, 0))
        ELSE 0
        END AS avg_latency,
        COALESCE(m_combined.total_latency, 0) + COALESCE(wne_stats.total_elapsed_time, 0) AS total_latency,
        (ad.created_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') AS created_time,
        ad.created_at
        FROM
        app_data ad
        LEFT JOIN m_combined ON ad.conversation_id = m_combined.conversation_id
        LEFT JOIN public.workflow_node_executions wne ON ad.workflow_run_id = wne.workflow_run_id
        LEFT JOIN wne_stats ON ad.workflow_run_id = wne_stats.workflow_run_id
        LEFT JOIN public.tag_bindings tb ON ad.id = tb.target_id
        ORDER BY
        ad.created_at DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="countMessages" resultType="long">
        WITH m_combined AS (
        SELECT
        m.conversation_id,
        SUM(m.message_tokens + m.answer_tokens) AS total_tokens,
        MAX(CASE WHEN m.status = 'error' THEN 1 ELSE 0 END) AS has_error
        FROM public.messages m
        INNER JOIN public.conversations c ON m.conversation_id = c.id
        WHERE 1=1
        <if test="appId != null and appId != ''">
            AND c.app_id = CAST(#{appId}::text AS UUID)
        </if>
        <if test="startTime != null and endTime != null">
            AND c.created_at BETWEEN TO_TIMESTAMP(#{startTime} / 1000) AND TO_TIMESTAMP(#{endTime} / 1000)
        </if>
        GROUP BY m.conversation_id
        ),
        wne_stats AS (
        SELECT
        wne.workflow_run_id,
        SUM(CAST((wne.execution_metadata::json->>'total_tokens') AS INTEGER)) AS total_tokens,
        MAX(CASE WHEN wne.status = 'failed' THEN 1 ELSE 0 END) AS has_error
        FROM public.workflow_node_executions wne
        INNER JOIN public.workflow_app_logs wal ON wne.workflow_run_id = wal.workflow_run_id
        WHERE 1=1
        <if test="appId != null and appId != ''">
            AND wal.app_id = CAST(#{appId}::text AS UUID)
        </if>
        <if test="startTime != null and endTime != null">
            AND wal.created_at BETWEEN TO_TIMESTAMP(#{startTime} / 1000) AND TO_TIMESTAMP(#{endTime} / 1000)
        </if>
        GROUP BY wne.workflow_run_id
        )
        SELECT COUNT(DISTINCT COALESCE(c.id, wal.id)) AS total_count
        FROM
        public.apps a
        LEFT JOIN public.conversations c ON a.id = c.app_id
        LEFT JOIN public.workflow_app_logs wal ON a.id = wal.app_id
        LEFT JOIN public.end_users eu ON c.from_end_user_id = eu.id
        LEFT JOIN public.end_users wal_eu ON wal.created_by = wal_eu.id
        LEFT JOIN m_combined ON c.id = m_combined.conversation_id
        LEFT JOIN public.workflow_node_executions wne ON wal.workflow_run_id = wne.workflow_run_id
        LEFT JOIN wne_stats ON wal.workflow_run_id = wne_stats.workflow_run_id
        LEFT JOIN public.tag_bindings tb ON a.id = tb.target_id
        WHERE
        1=1
        AND COALESCE(c.app_id, wal.app_id) IS NOT NULL
        <if test="appId != null and appId != ''">
            AND COALESCE(c.app_id, wal.app_id) = CAST(#{appId}::text AS UUID)
        </if>
        <if test="appName != null and appName != ''">
            AND a.name LIKE CONCAT('%', #{appName}, '%')
        </if>
        <if test="conversationName != null and conversationName != ''">
            AND c.name LIKE CONCAT('%', #{conversationName}, '%')
        </if>
        <if test="conversationStatus != null and conversationStatus != ''">
            AND (
            CASE
            WHEN m_combined.has_error = 1 THEN 'error'
            WHEN wne_stats.has_error = 1 THEN 'error'
            WHEN c.status IS NOT NULL THEN c.status
            WHEN wne_stats.workflow_run_id IS NOT NULL THEN
            CASE
            WHEN wne_stats.has_error = 0 THEN 'normal'
            ELSE 'unknown'
            END
            ELSE NULL
            END
            ) = #{conversationStatus}
        </if>
        <if test="sessionId != null and sessionId != ''">
            AND COALESCE(wal_eu.session_id, eu.session_id, 'medsciUser') = #{sessionId}
        </if>
        <if test="startTime != null and endTime != null">
            AND COALESCE(c.created_at, wal.created_at) BETWEEN TO_TIMESTAMP(#{startTime} / 1000) AND TO_TIMESTAMP(#{endTime} / 1000)
        </if>
        <if test="minTokens != null or maxTokens != null">
            AND (
            <if test="minTokens != null">
                (COALESCE(m_combined.total_tokens, 0) + COALESCE(wne_stats.total_tokens, 0)) <![CDATA[ >= #{minTokens} ]]>
            </if>
            <if test="minTokens != null and maxTokens != null">
                AND
            </if>
            <if test="maxTokens != null">
                (COALESCE(m_combined.total_tokens, 0) + COALESCE(wne_stats.total_tokens, 0)) <![CDATA[ <= #{maxTokens} ]]>
            </if>
            )
        </if>
        <if test="tagIds != null and tagIds.length > 0">
            AND EXISTS (
            SELECT 1
            FROM public.tag_bindings tb2
            WHERE tb2.target_id = a.id
            AND tb2.tag_id IN
            <foreach item="tagId" collection="tagIds" open="(" separator="," close=")">
                CAST(#{tagId}::text AS UUID)
            </foreach>
            )
        </if>
        <if test="excludeTagIds != null and excludeTagIds.length > 0">
            AND NOT EXISTS (
            SELECT 1
            FROM public.tag_bindings tb_exclude
            WHERE tb_exclude.target_id = a.id
            AND tb_exclude.tag_id IN
            <foreach item="tagId" collection="excludeTagIds" open="(" separator="," close=")">
                CAST(#{tagId}::text AS UUID)
            </foreach>
            )
        </if>
    </select>

</mapper>