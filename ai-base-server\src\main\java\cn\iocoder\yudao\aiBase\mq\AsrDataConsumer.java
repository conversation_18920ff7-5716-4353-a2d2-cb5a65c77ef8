package cn.iocoder.yudao.aiBase.mq;

import cn.iocoder.yudao.aiBase.service.AiFileAsrService;
import cn.iocoder.yudao.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AsrDataConsumer extends AbstractRedisStreamMessageListener<AsrDataMsg> {

    @Autowired
    private AiFileAsrService aiFileAsrService;

    @Override
    public void onMessage(AsrDataMsg msg) {
        log.info("开始处理消息AsrDataMsg{}", msg);
        try {
            // todo 处理ASR接收消息和ASR发送消息
            if (AsrDataMsg.ASR_RECEIVED.equals(msg.getSigHeader())) {
                aiFileAsrService.handleReceived(msg.getPayload());
            } else {
                aiFileAsrService.handleSend(msg.getPayload());
            }
        } catch (Exception e) {
            log.error("处理结束消息报错");
            e.printStackTrace();
        }

        log.info("处理结束消息AsrDataMsg");
    }
}
