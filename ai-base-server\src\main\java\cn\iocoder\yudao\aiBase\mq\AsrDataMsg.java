package cn.iocoder.yudao.aiBase.mq;

import cn.iocoder.yudao.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import com.alibaba.fastjson.JSONObject;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AsrDataMsg extends AbstractRedisStreamMessage {

	public static final String ASR_RECEIVED = "ASR:received";
	public static final String ASR_SEND = "ASR:send";

	private String sigHeader;

	private JSONObject payload;

}
