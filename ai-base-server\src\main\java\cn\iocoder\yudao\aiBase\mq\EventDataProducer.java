package cn.iocoder.yudao.aiBase.mq;

import cn.iocoder.yudao.framework.mq.redis.core.RedisMQTemplate;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EventDataProducer {
    @Resource
    private RedisMQTemplate redisMQTemplate;

    public void send(EventDataMsg msg) {
        log.info("开始发送消息EventDataMsg");
        redisMQTemplate.send(msg);
    }

}
