package cn.iocoder.yudao.aiBase.mq;

import cn.iocoder.yudao.aiBase.service.QaService;
import cn.iocoder.yudao.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class QaSaveConsumer extends AbstractRedisStreamMessageListener<QaSaveMsg> {

    @Autowired
    private QaService qaService;

    @Override
    public void onMessage(QaSaveMsg msg) {
        log.info("开始处理消息：{}", msg);

        try {
            // 处理生成QA的逻辑
            qaService.handleQaRequest(msg);
        } catch (Exception e) {
            log.error("处理QA请求消息失败", e);
        }

        log.info("处理结束消息：{}", msg);
    }
}
