package cn.iocoder.yudao.aiBase.mq;

import cn.iocoder.yudao.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class QaSaveMsg extends AbstractRedisStreamMessage {

	private String articleId; // 文章ID
	private String regenerate; // 是否需要重新生成（Y/N）
	private Integer num; // 生成数量
	private String userName; // 用户名
}
