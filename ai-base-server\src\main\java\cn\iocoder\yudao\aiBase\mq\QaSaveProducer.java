package cn.iocoder.yudao.aiBase.mq;

import cn.iocoder.yudao.framework.mq.redis.core.RedisMQTemplate;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class QaSaveProducer {
    @Resource
    private RedisMQTemplate redisMQTemplate;

    // 发送消息到 Redis 队列
    public void sendQaSaveRequest(QaSaveMsg msg) {
        log.info("将QA请求消息发送到队列，articleId: {}", msg.getArticleId());
        redisMQTemplate.send(msg);
    }

}
