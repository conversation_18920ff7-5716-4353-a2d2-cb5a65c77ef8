package cn.iocoder.yudao.aiBase.mq;

import cn.iocoder.yudao.aiBase.service.AiAppLangsService;
import cn.iocoder.yudao.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SyncUserAppConsumer extends AbstractRedisStreamMessageListener<SyncUserAppMsg> {

    @Autowired
    private AiAppLangsService aiAppLangsService;

    @Override
    public void onMessage(SyncUserAppMsg msg) {
        log.info("开始处理消息SyncUserAppMsg{}", msg);
        try {
            aiAppLangsService.handleSyncUserApp(msg);
        } catch (Exception e) {
            log.error("处理结束消息报错");
            e.printStackTrace();
        }

        log.info("处理结束消息SyncUserAppMsg");
    }
}
