package cn.iocoder.yudao.aiBase.mq;

import cn.iocoder.yudao.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SyncUserAppMsg extends AbstractRedisStreamMessage {

	private Long socialUserId;
	private Integer socialType;
	private String appUuid1;
	private String appUuid;


}
