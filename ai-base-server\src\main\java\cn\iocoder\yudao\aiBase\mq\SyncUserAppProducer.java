package cn.iocoder.yudao.aiBase.mq;

import cn.iocoder.yudao.framework.mq.redis.core.RedisMQTemplate;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SyncUserAppProducer {
    @Resource
    private RedisMQTemplate redisMQTemplate;

    public void send(SyncUserAppMsg msg) {
        log.info("开始发送消息SyncUserAppMsg");
        redisMQTemplate.send(msg);
        log.info("结束发送消息SyncUserAppMsg");
    }

}
