package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseIService;
import cn.iocoder.yudao.aiBase.dto.param.AiAppParam;
import cn.iocoder.yudao.aiBase.dto.request.SaveActivityCollectReqVO;
import cn.iocoder.yudao.aiBase.entity.AiActivityCollect;

public interface AiActivityCollectService extends BaseIService<AiActivityCollect, AiAppParam> {
    // 新增
    Boolean saveCollect(SaveActivityCollectReqVO param);
}