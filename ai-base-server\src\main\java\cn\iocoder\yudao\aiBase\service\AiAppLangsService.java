package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseIService;
import cn.iocoder.yudao.aiBase.dto.alipay.BusinessQueryOrderResponse;
import cn.iocoder.yudao.aiBase.dto.param.AiAppParam;
import cn.iocoder.yudao.aiBase.dto.request.AppLangReqVO;
import cn.iocoder.yudao.aiBase.dto.request.CreateSubReqVO;
import cn.iocoder.yudao.aiBase.dto.request.UpdatePriceIdReqVO;
import cn.iocoder.yudao.aiBase.dto.response.AiAppResponse;
import cn.iocoder.yudao.aiBase.dto.response.BindAppUserResponse;
import cn.iocoder.yudao.aiBase.dto.response.SubscriptionCountResponse;
import cn.iocoder.yudao.aiBase.entity.AiAppLangs;
import cn.iocoder.yudao.aiBase.entity.AiAppUsers;
import cn.iocoder.yudao.aiBase.entity.AiSubOrders;
import cn.iocoder.yudao.aiBase.entity.MedsciUsers;
import cn.iocoder.yudao.aiBase.mq.SyncUserAppMsg;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface AiAppLangsService extends BaseIService<AiAppLangs, AiAppParam> {
    public static final String ZH_CN = "中文";

    /**
     * 根据请求参数查询应用语言列表
     * @param reqVO 请求参数
     * @return 应用语言列表
     */
    List<AiAppLangs> selectList(AiAppParam reqVO);

    List<AiAppLangs> selectListOnline(AiAppParam reqVO);

    /**
     * 获取站点地图所需的应用语言列表
     * @return 站点地图数据
     */
    List<AiAppLangs> getSiteMapList();

    /**
     * 通过UUID获取应用语言信息
     * @param uuid 应用唯一标识
     * @return 应用语言信息
     */
    AiAppLangs getByUuid(String uuid);

    /**
     * 通过英文名称获取应用语言信息
     * @param nameEn 应用英文名
     * @return 应用语言信息
     */
    AiAppLangs getByNameEn(String nameEn);

    /**
     * 获取用户可见的应用列表
     *
     * @param reqVO 请求参数
     * @param auth  认证用户信息
     * @return 应用响应列表
     */
    List<AiAppResponse> getAppList(AiAppParam reqVO, String auth);

    /**
     * 通过配置键获取应用列表
     *
     * @param configKey 配置键
     * @param auth      认证用户信息
     * @return 应用响应列表
     */
    List<AiAppResponse> getAppByConfigKey(String configKey, String auth);

    /**
     * 将应用语言实体转换为响应对象
     * @param item 应用语言实体
     * @return 响应对象
     */
    AiAppResponse toBean(AiAppLangs item);

    /**
     * 获取应用的点击次数
     * @param appUuid 应用唯一标识
     * @param openid 用户三方ID
     * @return 点击次数
     */
    Integer getAppClickNum(String appUuid, String openid);

    /**
     * 更新应用使用次数
     * @param appUuid 应用唯一标识
     * @return 受影响行数
     */
    Integer updateUseNum(String appUuid);

    /**
     * 创建产品信息
     * @param oldApp 应用语言请求对象
     */
    void createProduct(AppLangReqVO oldApp);

    /**
     * 处理同步用户应用信息
     * @param msg
     */
    void handleSyncUserApp(SyncUserAppMsg msg);

    /**
     * 初始化项目配置
     * @param appUuid 应用唯一标识
     */
    void initProject(String appUuid);

    /**
     * 创建订阅
     *
     * @param auth  认证用户信息
     * @param reqVO 创建订阅请求对象
     * @return 订阅ID
     */
    String createSubscription(String auth, CreateSubReqVO reqVO);

    /**
     * 创建自动订阅
     * @param user 用户信息
     * @param reqVO 创建订阅请求对象
     */
    void createAutoSubscription(MedsciUsers user, CreateSubReqVO reqVO);

    /**
     * 取消订阅
     *
     * @param auth       认证用户信息
     * @param appUuid    应用唯一标识
     * @param packageKey
     * @return 取消结果
     */
    String cancelSubscription(String auth, String appUuid, String packageKey);

    /**
     * 处理Webhook回调
     * @param sigHeader 签名头
     * @param payload 回调内容
     */
    void webhook(String sigHeader, String payload);

    /**
     * 获取订阅日志
     * @param orderId 业务订单ID
     * @return 订单响应信息
     */
    BusinessQueryOrderResponse getSubLog(String orderId);

    /**
     * 处理过期任务
     * @param expired 过期时间
     */
    void handleTask(LocalDateTime expired);

    /**
     * 创建支付宝订阅地址
     * @param piId 支付ID
     * @param sessionId 会话ID
     * @param socialUserId 主站用户ID
     * @param openid 用户三方ID
     * @return 订阅ID
     */
    String createAliSub(String piId, String sessionId, Long socialUserId, String openid);

    /**
     * 统计所有订阅数量
     * @return 订阅统计信息
     */
    List<SubscriptionCountResponse> countSubscriptions();

    /**
     * 通过UUID获取应用响应信息
     *
     * @param uuid 应用唯一标识
     * @param auth 认证用户信息
     * @return 应用响应信息
     */
    AiAppResponse getByUuid(String uuid, String auth);

    /**
     * 更新价格ID
     * @param reqVO 更新价格ID请求对象
     * @return 受影响行数
     */
    Integer updatePriceId(UpdatePriceIdReqVO reqVO);

    /**
     * 获取应用英文名称，从缓存中获取
     * @param appUuid 应用唯一标识
     * @return 应用英文名称
     */
    String getUserAppPackage(Long socialUserId, Integer socialType, String appUuid);

    /**
     * 获取应用英文名称，从缓存中获取
     * @param appUuid 应用唯一标识
     * @return 应用英文名称
     */
    Boolean checkIsUXOFromCache(Long socialUserId, Integer socialType, String appUuid);

    /**
     * 通过应用UUID获取应用英文名称，从缓存中获取
     * @param appUuid 应用唯一标识
     * @return 应用英文名称
     */
    String getAppNameEn(String appUuid);

    /**
     * 获取免费次数，从缓存中获取
     * @param appUuid 应用唯一标识
     * @return 免费次数
     */
    Integer getFreeNum(String appUuid);

    /**
     * 创建应用用户
     *
     * @param subOrder  @return 应用用户对象
     * @param packageId
     */
    AiAppUsers createAppUsersByOrder(AiSubOrders subOrder, Integer packageId);

    /**
     * 绑定应用用户
     * @param auth
     * @param appUuid
     * @param nameEn
     * @return
     */
    BindAppUserResponse bindAppUser(String auth, String appUuid, String nameEn);

    /**
     * 获取语言前缀
     * @param lang 语言名称
     * @return 语言前缀
     */
    String getLanguages(String lang);

}
