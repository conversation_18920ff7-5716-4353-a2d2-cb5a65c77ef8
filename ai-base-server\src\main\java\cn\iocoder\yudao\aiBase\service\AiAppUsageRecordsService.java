package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.entity.AiAppUsageRecords;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.YearMonth;

/**
 * 应用使用记录 Service 接口
 */
public interface AiAppUsageRecordsService extends IService<AiAppUsageRecords> {

    /**
     * 添加应用使用记录
     *
     * @param socialUserId 社交用户ID
     * @param socialType 社交类型
     * @param appUuid 应用UUID
     * @param requestId 请求ID (可选)
     * @return 操作结果
     */
    boolean addUsageRecord(Long socialUserId, Integer socialType, String appUuid, String requestId);

    /**
     * 记录应用使用并判断是否成功（适用于内部调用）
     *
     * @param socialUserId 社交用户ID
     * @param socialType 社交类型
     * @param appUuid 应用UUID
     * @param requestId 请求ID (可选)
     * @return 0=记录成功 -1=剩余次数不足
     */
    int recordAppUsage(Long socialUserId, Integer socialType, String appUuid, String requestId);
    
    /**
     * 获取免费应用可使用次数
     * 
     * @return 免费使用次数
     */
    int getFreeAppUsageLimit();
    
    /**
     * 清除Redis中的免费使用次数配置缓存
     * 当配置变更时调用此方法，以便下次获取最新配置
     */
    void clearFreeAppUsageLimitCache();
    
    /**
     * 查询用户对指定应用的使用次数（当前月）
     *
     * @param socialUserId 社交用户ID
     * @param socialType 社交类型
     * @param appUuid 应用UUID
     * @return 使用次数
     */
    int countUserAppUsage(Long socialUserId, Integer socialType, String appUuid);

    /**
     * 查询用户对指定应用在指定月份的使用次数
     *
     * @param socialUserId 社交用户ID
     * @param socialType 社交类型
     * @param appUuid 应用UUID
     * @param yearMonth 年月，格式如：2023-05
     * @return 使用次数
     */
    int countUserAppUsageByMonth(Long socialUserId, Integer socialType, String appUuid, YearMonth yearMonth);

    /**
     * 检查用户是否可以使用应用
     *
     * @param socialUserId 社交用户ID
     * @param socialType 社交类型
     * @param appUuid 应用UUID
     * @return 是否可以使用
     */
    boolean checkUserCanUseApp(Long socialUserId, Integer socialType, String appUuid);

    /**
     * 获取用户所有应用使用次数
     *
     * @param socialUserId 社交用户ID
     * @param socialType 社交类型
     * @param appUuid 应用UUID
     * @return 所有应用使用次数
     */
    int countAll(Long socialUserId, Integer socialType, String appUuid);

    /**
     * 获取用户剩余使用次数
     *
     * @param socialUserId 社交用户ID
     * @param socialType 社交类型
     * @param appUuid 应用UUID
     * @return 剩余使用次数，-1表示无限制
     */
    int getRemainingUsageCount(Long socialUserId, Integer socialType, String appUuid);
} 