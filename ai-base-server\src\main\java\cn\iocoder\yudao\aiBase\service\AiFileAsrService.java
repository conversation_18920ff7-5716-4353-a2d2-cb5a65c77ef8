package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseIService;
import cn.iocoder.yudao.aiBase.dto.param.FileAsrParam;
import cn.iocoder.yudao.aiBase.dto.param.IndexParam;
import cn.iocoder.yudao.aiBase.entity.AiFileAsr;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface AiFileAsrService extends BaseIService<AiFileAsr, IndexParam> {

	List<AiFileAsr> selectList(FileAsrParam param);

	AiFileAsr getByUuid(String uuid);

	AiFileAsr getByMd5(String md5);

	void handleSend(JSONObject jsonObject);

	void handleReceived(JSONObject jsonObject);


}
