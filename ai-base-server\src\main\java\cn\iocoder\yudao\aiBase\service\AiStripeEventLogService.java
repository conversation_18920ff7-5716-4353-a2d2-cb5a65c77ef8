package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseIService;
import cn.iocoder.yudao.aiBase.dto.param.IndexParam;
import cn.iocoder.yudao.aiBase.dto.request.EventPageReqVO;
import cn.iocoder.yudao.aiBase.entity.AiStripeEventLog;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

public interface AiStripeEventLogService extends BaseIService<AiStripeEventLog, IndexParam> {

    PageResult<AiStripeEventLog> selectPage(EventPageReqVO reqVO);

    Integer insert(String eventId, String eventType, String stripeObject);


}
