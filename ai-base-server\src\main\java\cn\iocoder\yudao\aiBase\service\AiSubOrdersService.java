package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseIService;
import cn.iocoder.yudao.aiBase.dto.param.IndexParam;
import cn.iocoder.yudao.aiBase.dto.request.AppUserPageReqVO;
import cn.iocoder.yudao.aiBase.entity.AiSubOrders;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import java.time.LocalDateTime;
import java.util.List;

public interface AiSubOrdersService extends BaseIService<AiSubOrders, IndexParam> {

    PageResult<AiSubOrders> selectPage(AppUserPageReqVO reqVO);

    List<AiSubOrders> selectList(AppUserPageReqVO reqVO);

    Integer createOrder(AiSubOrders subOrders);

    AiSubOrders getByPiId(String piId);

    void updatePaid(Integer id);

    List<AiSubOrders> getExpiredOrders(LocalDateTime expired);




}
