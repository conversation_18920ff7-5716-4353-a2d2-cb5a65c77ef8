package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseIService;
import cn.iocoder.yudao.aiBase.dto.request.AppUserPageReqVO;
import cn.iocoder.yudao.aiBase.entity.AiSubscriptionLog;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import java.util.List;

public interface AiSubscriptionLogService extends BaseIService<AiSubscriptionLog, AppUserPageReqVO> {

    /**
     * 根据请求参数查询订阅日志列表
     *
     * @param reqVO 请求参数
     * @return 订阅日志列表
     */
    List<AiSubscriptionLog> selectList(AppUserPageReqVO reqVO);

    /**
     * 分页查询订阅日志
     *
     * @param reqVO 请求参数
     * @return 分页结果，包含当前页的订阅日志列表和总数量
     */
    PageResult<AiSubscriptionLog> selectPage(AppUserPageReqVO reqVO);

    /**
     * 根据用户信息和医院UUID获取订阅日志
     *
     * @param socialUserId 用户ID
     * @param socialType 三方类型
     * @param appUuid 医院UUID
     * @return 匹配的订阅日志
     */
    AiSubscriptionLog getByAppUser(Long socialUserId, Integer socialType, String appUuid);

    /**
     * 获取最后一次有效的订阅记录
     *
     * @param socialUserId 用户ID
     * @param socialType 三方类型
     * @param appUuid 医院UUID
     * @param priceId 价格ID
     * @return 最后一次有效的订阅记录
     */
    AiSubscriptionLog getLastValid(Long socialUserId, Integer socialType, String appUuid, String priceId);

    /**
     * 创建订阅日志
     *
     * @param subLog 订阅日志对象
     * @return 创建结果，成功返回1，失败返回0
     */
    Integer createSubLog(AiSubscriptionLog subLog);

    /**
     * 根据支付会话ID获取订阅日志
     *
     * @param checkoutSessionId 支付会话ID
     * @return 匹配的订阅日志
     */
    AiSubscriptionLog getByCheckoutSessionId(String checkoutSessionId);

    /**
     * 更新订阅日志信息
     *
     * @param subLog 订阅日志对象
     * @return 更新结果，成功返回1，失败返回0
     */
    Integer updateSubLog(AiSubscriptionLog subLog);

    /**
     * 根据订阅ID获取订阅日志
     *
     * @param subId 订阅ID
     * @return 匹配的订阅日志
     */
    AiSubscriptionLog getBySubId(String subId);

    /**
     * 更新订阅ID
     *
     * @param id 主键ID
     * @param subId 新的订阅ID
     * @return 更新结果，成功返回1，失败返回0
     */
    Integer updateSubId(Integer id, String subId);

    /**
     * 根据付款ID获取订阅日志
     *
     * @param piId 付款ID
     * @return 匹配的订阅日志
     */
    AiSubscriptionLog getByPiId(String piId);

}
