package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.dto.asr.ASRToken;
import cn.iocoder.yudao.aiBase.util.AliyunUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AliyunService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public String getAsrToken() {
        String key = "Aliyun:AsrToken";
        String value = stringRedisTemplate.opsForValue().get(key);
        if (value == null) {
            ASRToken asrToken = AliyunUtil.getAsrToken();
            if (asrToken != null) {
                value = asrToken.getToken();
                long timeout = asrToken.getExpireTime() - System.currentTimeMillis() / 1000;
                stringRedisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
            }
        }
        return value;
    }


}
