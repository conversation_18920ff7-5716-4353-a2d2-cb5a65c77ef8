package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseIService;
import cn.iocoder.yudao.aiBase.dto.param.ApiTokensParam;
import cn.iocoder.yudao.aiBase.dto.request.dify.*;
import cn.iocoder.yudao.aiBase.dto.response.AppBaseResponse;
import cn.iocoder.yudao.aiBase.dto.response.DifyBaseResponse;
import cn.iocoder.yudao.aiBase.dto.response.MyCollectionResponse;
import cn.iocoder.yudao.aiBase.entity.ApiTokens;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import com.alibaba.fastjson.JSONObject;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import java.util.List;

public interface ApiTokensService extends BaseIService<ApiTokens, ApiTokensParam> {

    /**
     * 刷新API令牌，用于重新获取或更新当前用户的访问令牌。
     */
    void refreshToken();

    /**
     * 获取指定应用的API令牌。
     *
     * @param appId 应用的唯一标识符
     * @return 返回对应的API令牌字符串
     */
    String getToken(String appId);

    /**
     * 发送聊天消息并接收流式响应（Server-Sent Events）。
     *
     * @param param 包含聊天请求参数的对象
     * @return Flux<ServerSentEvent> 流式返回服务器发送事件
     */
    Flux<ServerSentEvent> chatMsg(ChatMessagesRequest param);

    /**
     * 发送聊天消息并同步获取JSON格式响应。
     *
     * @param param 包含聊天请求参数的对象
     * @return JSONObject 同步返回的JSON响应结果
     */
    JSONObject chatMsg1(ChatMessagesRequest param);

    /**
     * 停止正在进行的聊天任务。
     *
     * @param param 包含停止聊天请求参数的对象
     * @return JSONObject 返回停止操作的结果
     */
    JSONObject stopChat(StopChatRequest param);

    /**
     * 提交反馈信息，例如对某条消息进行点赞或点踩。
     *
     * @param param 包含反馈请求参数的对象
     * @return JSONObject 返回反馈操作的结果
     */
    JSONObject feedback(FeedbackRequest param);

    /**
     * 获取建议的问题列表。
     *
     * @param param 包含获取建议请求参数的对象
     * @return JSONObject 返回建议问题的数据
     */
    JSONObject suggested(SuggestedRequest param);

    /**
     * 获取指定会话的消息记录。
     *
     * @param param 包含查询消息请求参数的对象
     * @return JSONObject 返回消息记录数据
     */
    JSONObject messages(MessagesRequest param);

    /**
     * 获取会话列表。
     *
     * @param param 包含查询会话请求参数的对象
     * @return JSONObject 返回会话数据列表
     */
    JSONObject conversations(ConversationsRequest param);

    /**
     * 删除指定的会话。
     *
     * @param param 包含删除会话请求参数的对象
     * @return Boolean 返回是否删除成功
     */
    Boolean deleteConverse(DeleteConversationsRequest param);

    /**
     * 重命名指定的会话。
     *
     * @param param 包含重命名会话请求参数的对象
     * @return JSONObject 返回重命名操作的结果
     */
    JSONObject renameConverse(RenameRequest param);

    /**
     * 获取应用的基础参数信息。
     *
     * @param param 包含基础请求参数的对象
     * @return JSONObject 返回应用参数数据
     */
    JSONObject parameters(DifyBaseRequest param);

    /**
     * 获取指定应用的预提示信息。
     *
     * @param appId 应用的唯一标识符
     * @return DifyBaseResponse 返回预提示信息对象
     */
    DifyBaseResponse getAppPrePrompt(String appId);

    /**
     * 批量获取多个应用的预提示信息。
     *
     * @param appIds 应用ID的列表
     * @return List<DifyBaseResponse> 返回预提示信息对象列表
     */
    List<DifyBaseResponse> getAppsPrePrompt(List<String> appIds);

    /**
     * 根据逗号分隔的字符串形式的appIds获取预提示信息。
     *
     * @param appIds 字符串形式的应用ID集合
     * @return List<DifyBaseResponse> 返回预提示信息对象列表
     */
    List<DifyBaseResponse> getAppsPrePrompt(String appIds);

    /**
     * 获取用户收藏的应用列表。
     *
     * @param param 包含收藏查询请求参数的对象
     * @return List<MyCollectionResponse> 返回收藏的应用列表
     */
    List<MyCollectionResponse> myCollection(MyCollectionRequest param);

    /**
     * 运行工作流并接收流式响应（Server-Sent Events）。
     *
     * @param param 包含工作流请求参数的对象
     * @return Flux<ServerSentEvent> 流式返回服务器发送事件
     */
    Flux<ServerSentEvent> workflowsRun(WorkflowsRunRequest param);

    /**
     * 运行工作流并同步获取JSON格式响应。
     *
     * @param param 包含工作流请求参数的对象
     * @return JSONObject 返回工作流执行结果
     */
    JSONObject workflowsRun1(WorkflowsRunRequest param);

    /**
     * 获取元数据信息。
     *
     * @param param 包含基础请求参数的对象
     * @return JSONObject 返回元数据信息
     */
    JSONObject meta(DifyBaseRequest param);

    /**
     * 根据标签获取相关的应用列表。
     *
     * @param tag 标签名称
     * @return List<AppBaseResponse> 返回匹配该标签的应用列表
     */
    List<AppBaseResponse> getAppsByTag(String tag);

    /**
     * 根据应用名称模糊搜索获取应用列表。
     *
     * @param name 应用名称关键字
     * @return List<DifyBaseResponse> 返回匹配名称的应用列表
     */
    List<DifyBaseResponse> getAppsByName(String name);

    /**
     * 完成消息处理并接收流式响应（Server-Sent Events）。
     *
     * @param param 包含完成消息请求参数的对象
     * @return Flux<ServerSentEvent> 流式返回服务器发送事件
     */
    Flux<ServerSentEvent> completionMsg(WorkflowsRunRequest param);

    /**
     * 完成消息处理并同步获取JSON格式响应。
     *
     * @param param 包含完成消息请求参数的对象
     * @return JSONObject 返回处理结果
     */
    JSONObject completionMsg1(WorkflowsRunRequest param);

    /**
     * 上传文件到服务器。
     *
     * @param uploadReqVO 文件上传请求参数
     * @return JSONObject 返回上传操作的结果
     */
    JSONObject filesUpload(FilesUploadRequest uploadReqVO);

    /**
     * 创建SSE发射器以支持流式传输。
     *
     * @param flux 包含流式数据的Flux对象
     * @return SseEmitter 返回配置好的SSE发射器
     */
    SseEmitter getEmitter(Flux<ServerSentEvent> flux);

    /**
     * 关闭SSE发射器，并发送错误码信息。
     *
     * @param emitter 当前的SSE发射器
     * @param errorCode 错误码信息
     * @return SseEmitter 返回关闭后的SSE发射器
     */
    SseEmitter getEmitter(SseEmitter emitter, ErrorCode errorCode);

    /**
     * 关闭SSE发射器，并发送指定事件和错误码信息。
     *
     * @param emitter 当前的SSE发射器
     * @param event 事件名称
     * @param errorCode 错误码信息
     * @return SseEmitter 返回关闭后的SSE发射器
     */
    SseEmitter getEmitter(SseEmitter emitter, String event, ErrorCode errorCode);

    /**
     * 激活Dify账户。
     *
     * @param email 用户邮箱
     * @param name 用户名称
     * @return Integer 返回激活状态码（成功/失败）
     */
    Integer activeDifyAccount(String email, String name);


}
