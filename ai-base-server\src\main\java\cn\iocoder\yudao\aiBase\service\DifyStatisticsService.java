package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseIService;
import cn.iocoder.yudao.aiBase.dto.request.difyStatistics.DifyStatisticsMessageReqVo;
import cn.iocoder.yudao.aiBase.dto.response.DifyStatisticsMessageResponse;
import cn.iocoder.yudao.aiBase.entity.Messages;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

public interface DifyStatisticsService extends BaseIService<Messages, DifyStatisticsMessageReqVo> {
    PageResult<DifyStatisticsMessageResponse> getMessages(DifyStatisticsMessageReqVo reqVO);
}
