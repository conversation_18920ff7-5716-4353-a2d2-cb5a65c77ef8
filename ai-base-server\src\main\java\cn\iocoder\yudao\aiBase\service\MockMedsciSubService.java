package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.dto.alipay.PayNotifyDto;
import cn.iocoder.yudao.aiBase.dto.alipay.PayStatus;
import cn.iocoder.yudao.aiBase.dto.request.ContractDataReqVO;
import cn.iocoder.yudao.aiBase.dto.request.CreateMedsciMemberSignDto;
import cn.iocoder.yudao.aiBase.mq.EventDataMsg;
import cn.iocoder.yudao.aiBase.mq.EventDataProducer;
import cn.iocoder.yudao.aiBase.util.CommonUtil;
import cn.iocoder.yudao.aiBase.util.MedsciSubUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
public class MockMedsciSubService {

	@Autowired
	private EventDataProducer eventDataProducer;

	/**
	 * mock生成扫描二维码跳转地址
	 * @param param
	 * @param addLog
	 * @return
	 */
	public String createSub(ContractDataReqVO param, Boolean addLog) {
		log.info("mock生成扫描二维码跳转地址:{}", param);
		CreateMedsciMemberSignDto subDto = new CreateMedsciMemberSignDto();
		subDto.setSourceOrderId(param.getOrderId());
		subDto.setAgreementNo("mock_sub_"+ LocalDateTime.now().format(CommonUtil.DateTimeFormat3));
		eventDataProducer.send(EventDataMsg.builder().sigHeader(MedsciSubUtil.AlipaySubEvent).payload(JsonUtils.toJsonString(subDto)).build());

		return "https://ai.medon.com.cn/dev-api/ai-base/index/mockAlipay";
	}

	/**
	 * mock支付成功
	 * @param appOrderId
	 * @param agreementNo
	 * @param addLog
	 * @return
	 */
	public String payBuild(String appOrderId, String agreementNo, Boolean addLog) {
		log.info("mock支付成功:{} : {}", appOrderId, agreementNo);
		PayNotifyDto payDto = new PayNotifyDto();
		payDto.setAppOrderId(appOrderId);
		payDto.setPayOrderLogId(appOrderId);
		payDto.setPayStatus(PayStatus.PAID);
		eventDataProducer.send(EventDataMsg.builder().sigHeader(MedsciSubUtil.AlipaySubEvent).payload(JsonUtils.toJsonString(payDto)).build());

		String payOrderId = "mock_pay_"+ LocalDateTime.now().format(CommonUtil.DateTimeFormat3);
		return payOrderId;
	}

	/**
	 * mock取消订阅
	 * @param agreementNo
	 * @param addLog
	 * @return
	 */
	public String cancelSub(String agreementNo, Boolean addLog) {
		log.info("mock取消订阅:{}", agreementNo);
		CreateMedsciMemberSignDto subDto = new CreateMedsciMemberSignDto();
		subDto.setAgreementNo(agreementNo);
		eventDataProducer.send(EventDataMsg.builder().sigHeader(MedsciSubUtil.AlipaySubEvent).payload(JsonUtils.toJsonString(subDto)).build());

		return agreementNo;
	}


}
