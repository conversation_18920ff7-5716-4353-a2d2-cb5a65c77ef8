package cn.iocoder.yudao.aiBase.service;

import cn.hutool.core.net.url.UrlQuery;
import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.entity.MedsciUsers;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthSocialLoginReqVO;
import cn.iocoder.yudao.module.member.service.auth.MemberAuthService;
import cn.iocoder.yudao.module.system.api.oauth2.OAuth2TokenApi;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import cn.iocoder.yudao.module.system.dal.dataobject.social.SocialUserDO;
import cn.iocoder.yudao.module.system.dal.mysql.social.SocialUserMapper;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.xingyuv.jushauth.model.AuthResponse;
import com.xingyuv.jushauth.model.AuthUser;
import jakarta.annotation.Resource;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.Charset;
import java.util.Base64;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static cn.iocoder.yudao.framework.common.util.json.JsonUtils.toJsonString;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.SOCIAL_USER_AUTH_FAILURE;

@Slf4j
@Service
@DS(DBConstant.YuDao)
public class OauthService {

    @Autowired
    private OAuth2TokenApi oauth2TokenApi;

    @Autowired
    private RedisService redisService;

    @Autowired
    private MemberAuthService authService;

    @Resource
    private SocialUserMapper socialUserMapper;

    @Value("${spring.profiles.active}")
    private String env;

    public static final Integer CodeExpire = BaseConstant.TWO;

    /**
     * 创建token
     * @param reqDTO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public OAuth2AccessTokenRespDTO createAccessToken(OAuth2AccessTokenCreateReqDTO reqDTO) {
        return oauth2TokenApi.createAccessToken(reqDTO);
    }

    /**
     * 删除token
     * @param token
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void removeAccessToken(String token) {
        if (StringUtils.isBlank(token)) {
            return ;
        }
        oauth2TokenApi.removeAccessToken(token.replace(BaseConstant.BEARER, BaseConstant.EMPTY_STR));
    }

    /**
     * 验证token 或报错
     * @param token
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public OAuth2AccessTokenCheckRespDTO checkAccessTokenOrError(String token) {
        if (StringUtils.isBlank(token)) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌不存在");
        }
        token = token.replace(BaseConstant.BEARER, BaseConstant.EMPTY_STR);
        if (StringUtils.isBlank(token)) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌不存在");
        }
        OAuth2AccessTokenCheckRespDTO authUser = oauth2TokenApi.checkAccessToken(token);
        return authUser;
    }

    /**
     * 验证token，获取用户
     * @param token
     * @return
     */
    public OAuth2AccessTokenCheckRespDTO checkAccessToken(String token) {
        if (StringUtils.isBlank(token) || token.toLowerCase().equals(BaseConstant.NULL_STR)) {
            log.info("[checkAccessToken][token({}) 验证失败]token=", token);
            return null;
        }
        try {
            OAuth2AccessTokenCheckRespDTO authUser = checkAccessTokenOrError(token);
            return authUser;
        } catch (Exception e) {
            log.error("[checkAccessToken][token({}) 验证失败]", token, e.getMessage());
            return null;
        }
    }

    /**
     * 获取三方授权url
     * @param socialType
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getSocialAuthorizeUrl(Integer socialType) {
        String res = authService.getSocialAuthorizeUrl(socialType, null);

        UrlQuery urlQuery = UrlQuery.of(res, Charset.defaultCharset());
        String state = urlQuery.get("state").toString();
        redisService.setRedisKey(socialType+state, state);

        return res;
    }

    /**
     * 获取三方授权openid
     * @param socialType
     * @param state
     * @param code
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getOauthOpenid(Integer socialType, String state, String code) {
        String code1 = redisService.getRedisKey(socialType + code);
        if (BaseConstant.PROD_STR.equals(env) && StringUtils.isBlank(code1)) {
            throw exception(SOCIAL_USER_AUTH_FAILURE, "code过期");
        }
        SocialUserDO socialUser = socialUserMapper.selectByTypeAndCodeAnState(socialType, code, state);
        if (socialUser == null) {
            throw exception(SOCIAL_USER_AUTH_FAILURE, "未查找到授权信息，请重新登录");
        }

        return socialUser.getOpenid();
    }

    /**
     * 获取 getRawUserInfo
     * @param socialType
     * @param state
     * @param code
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getRawUserInfo(Integer socialType, String state, String code) {
        AppAuthSocialLoginReqVO reqVO = new AppAuthSocialLoginReqVO();
        reqVO.setType(socialType);
        reqVO.setCode(code);
        reqVO.setState(state);
        String userInfo = authService.getRawUserInfo(reqVO);
        return userInfo;
    }

    /**
     * 三方授权回调信息存入数据库
     * @param socialType
     * @param state
     * @param code
     * @param authResponse
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getRawUserInfo(Integer socialType, String state, String code, AuthResponse authResponse) {
        log.info("[getAuthUser][请求社交平台 type-state-code:{}-{}-{} response({})]", socialType, state, code, toJsonString(authResponse));

        String state1 = redisService.getRedisKey(socialType + state);
        if (StringUtils.isBlank(state1)) {
            throw exception(SOCIAL_USER_AUTH_FAILURE, "授权链接过期");
        }

        if (!authResponse.ok()) {
            throw exception(SOCIAL_USER_AUTH_FAILURE, authResponse.getMsg());
        }
        AuthUser authUser = JsonUtils.parseObject(toJsonString(authResponse.getData()), AuthUser.class);

        SocialUserDO socialUser = socialUserMapper.selectByTypeAndOpenid(socialType, authUser.getUuid());
        if (socialUser == null) {
            socialUser = new SocialUserDO();
        }

        // 缓存 code
        redisService.setRedisKey(socialType + code, code, 5*60L);

        socialUser.setType(socialType).setCode(code).setState(state) // 需要保存 code + state 字段，保证后续可查询
            .setOpenid(authUser.getUuid()).setToken(authUser.getToken().getAccessToken()).setRawTokenInfo((toJsonString(authUser.getToken())))
            .setNickname(authUser.getNickname()).setAvatar(authUser.getAvatar()).setRawUserInfo(toJsonString(authUser.getRawUserInfo()));
        if (socialUser.getId() == null) {
            socialUserMapper.insert(socialUser);
        } else {
            socialUserMapper.updateById(socialUser);
        }
        JSONObject userInfo = JSONObject.parseObject(socialUser.getRawUserInfo());
        userInfo.put("socialUserId", socialUser.getId());
        return userInfo.toJSONString();
    }

    /**
     * 获取三方ID
     * @param socialType
     * @param openid
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Long getSocialUserId(Integer socialType, String openid) {
        SocialUserDO socialUser = socialUserMapper.selectByTypeAndOpenid(socialType, openid);
        if (socialUser == null) {
            socialUser = new SocialUserDO();
            socialUser.setType(socialType).setOpenid(openid).setRawUserInfo(BaseConstant.EMPTY_STR).setNickname(BaseConstant.EMPTY_STR)
                .setRawTokenInfo(BaseConstant.EMPTY_STR).setCode(BaseConstant.EMPTY_STR);
        }

        if (socialUser.getId() == null) {
            socialUserMapper.insert(socialUser);
        } else {
            socialUserMapper.updateById(socialUser);
        }
        return socialUser.getId();
    }

    /**
     * 设置cookie 没起效，暂时无用
     * @param user
     * @param seconds
     * @return
     */
    public Cookie setCookie(MedsciUsers user, Integer seconds) {
        JSONObject userJson = new JSONObject();
        userJson.put("socialType", user.getSocialType());
        userJson.put("avatar", user.getAvatar());
        userJson.put("email", user.getEmail());
        userJson.put("plaintextUserId", user.getSocialUserId());
        userJson.put("userId", user.getOpenid());
        userJson.put("realName", user.getRealName());
        userJson.put("userName", user.getUserName());
        String encodedJson = Base64.getEncoder().encodeToString(userJson.toJSONString().getBytes());
        // 创建一个Cookie对象
        Cookie cookie = new Cookie("userInfo", encodedJson);
        cookie.setMaxAge(seconds);
        cookie.setSecure(false); // 设置Secure属性
        cookie.setHttpOnly(false); // 设置HttpOnly属性
//        cookie.setDomain("localhost");
        return cookie;
    }

    //删除cookie
    public void removeCookie(HttpServletResponse response,HttpServletRequest  request, String name) {
        Cookie target = getCookieByName(request, name);
        log.info("cookie删除前{}-{}", target.getDomain(), target.getValue());
        if (target != null) {
            Cookie cookie = new Cookie(name, null);
            cookie.setPath(target.getPath());
            cookie.setDomain(target.getDomain());
            cookie.setMaxAge(0);
            cookie.setHttpOnly(target.isHttpOnly());
            cookie.setSecure(target.getSecure());
            response.addCookie(cookie);
        }
    }

    public Cookie getCookieByName(HttpServletRequest request, String name) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (name.equals(cookie.getName())) {
                    return cookie;
                }
            }
        }
        return null;
    }

    public String listCookies(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        StringBuilder result = new StringBuilder();

        if (cookies != null) {
            for (Cookie cookie : cookies) {
                result.append("Name: ").append(cookie.getName()).append("<br>");
                result.append("Value: ").append(cookie.getValue()).append("<br>");
                result.append("Domain: ").append(cookie.getDomain()).append("<br>");
                result.append("Path: ").append(cookie.getPath()).append("<br>");
                result.append("MaxAge: ").append(cookie.getMaxAge()).append("<br>");
                result.append("Secure: ").append(cookie.getSecure()).append("<br>");
                result.append("HttpOnly: ").append(cookie.isHttpOnly() ? "Yes" : "No").append("<br>");
                result.append("-----------------------------<br>");
            }
        } else {
            result.append("No cookies found.");
        }

        return result.toString();
    }

}
