package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.dto.asr.RecognitionParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.concurrent.ExecutionException;


/**
 *
 * <AUTHOR>
 * @since 2024/04/24
 *
 */

public interface RecognitionService {

	Object recognition(MultipartFile file, RecognitionParam param) throws IOException, ExecutionException, InterruptedException;

	Object recognition2(MultipartFile file, RecognitionParam param) throws IOException, ExecutionException, InterruptedException;

}
