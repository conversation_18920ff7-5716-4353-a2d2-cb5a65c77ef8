package cn.iocoder.yudao.aiBase.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

@Slf4j
@Service
public class RedisManage {
    @Autowired
    private RedisService redisService;
    

    /**
     * 设置内部用户标识（仅当键不存在时设置）
     * @param socialType 社交类型
     * @param socialUserId 社交用户ID
     * @param isInternalUser 是否为内部用户(1:是,0:否)
     */
    public void setIsInternalUser(Integer socialType, Long socialUserId, Integer isInternalUser) {
        String key = String.format("user:isInternal:%s:%s", socialType, socialUserId);
        redisService.setIfNull(key, isInternalUser.toString());
    }

    /**
     * 更新内部用户标识
     * @param socialType 社交类型
     * @param socialUserId 社交用户ID
     * @param isInternalUser 是否为内部用户(1:是,0:否)
     */
    public void updateIsInternalUser(Integer socialType, Long socialUserId, Integer isInternalUser) {
        String key = String.format("user:isInternal:%s:%s", socialType, socialUserId);
        redisService.setRedisKey(key, isInternalUser.toString());
    }

    /**
     * 获取内部用户标识
     * @param socialType 社交类型
     * @param socialUserId 社交用户ID
     * @return String 返回内部用户标识字符串
     */
    public String getIsInternalUser(Integer socialType, Long socialUserId) {
        String key = String.format("user:isInternal:%s:%s", socialType, socialUserId);
        return redisService.getRedisKey(key);
    }

    /**
     * 设置用户名（仅当键不存在时设置）
     * @param socialType 社交类型
     * @param socialUserId 社交用户ID
     * @param userName 用户名
     */
    public void setUserName(Integer socialType, Long socialUserId, String userName) {
        String key = String.format("user:userName:%s:%s", socialType, socialUserId);
        redisService.setIfNull(key, userName);
    }

    /**
     * 更新用户名
     * @param socialType 社交类型
     * @param socialUserId 社交用户ID
     * @param userName 用户名
     */
    public void updateUserName(Integer socialType, Long socialUserId, String userName) {
        String key = String.format("user:userName:%s:%s", socialType, socialUserId);
        redisService.setRedisKey(key, userName);
    }

    /**
     * 获取用户名
     * @param socialType 社交类型
     * @param socialUserId 社交用户ID
     * @return String 获取用户名
     */
    public String getUserName(Integer socialType, Long socialUserId) {
        String key = String.format("user:userName:%s:%s", socialType, socialUserId);
        return redisService.getRedisKey(key);
    }

    /**
     * 更新错误登录次数
     * @param email 用户邮箱
     * @param num 错误次数
     */
    public void updateErrorNum(String email, Integer num) {
        String key = String.format("user:errorNum:%s", email);
        redisService.setRedisKey(key, num.toString());
    }

    /**
     * 获取错误登录次数
     * @param email 用户邮箱
     * @return String 返回错误次数字符串
     */
    public String getErrorNum(String email) {
        String key = String.format("user:errorNum:%s", email);
        return redisService.getRedisKey(key);
    }

    /**
     * 设置应用英文名称
     * @param appUuid 应用UUID
     * @param appNameEn 应用英文名称
     */
    public void setAppNameEn(String appUuid, String appNameEn) {
        String key = String.format("app:nameEn:%s", appUuid);
        redisService.setIfNull(key, appNameEn);
    }

    /**
     * 更新应用英文名称
     * @param appUuid 应用UUID
     * @param appNameEn 应用英文名称
     */
    public void updateAppNameEn(String appUuid, String appNameEn) {
        String key = String.format("app:nameEn:%s", appUuid);
        redisService.setRedisKey(key, appNameEn);
    }

    /**
     * 获取应用英文名称
     * @param appUuid 应用UUID
     * @return String 返回应用英文名称
     */
    public String getAppNameEn(String appUuid) {
        String key = String.format("app:nameEn:%s", appUuid);
        return redisService.getRedisKey(key);
    }

    /**
     * 设置应用付费类型
     * @param configKey 配置键/非中文应用的nameEn
     * @param value 配置值
     */
    public void setFeeTypes(String configKey, String value) {
        String key = String.format("%s%s:%s", YudaoSystemService.ConfigCache, AiAppUserPackageService.PACKAGE_NAME, configKey);
        redisService.setIfNull(key, value);
    }

    /**
     * 获取应用付费类型
     * @param configKey 配置键/非中文应用的nameEn
     * @return String 配置值
     */
    public String getFeeTypes(String configKey) {
        String key = String.format("%s%s:%s", YudaoSystemService.ConfigCache, AiAppUserPackageService.PACKAGE_NAME, configKey);
        return redisService.getRedisKey(key);
    }

    /**
     * 设置应用免费次数
     * @param appUuid 应用UUID
     * @param num 配置值
     */
    public void setFreeNum(String appUuid, String num) {
        String key = String.format("%s%s:%s", YudaoSystemService.ConfigCache, AiAppUserPackageService.ACTIVITY_FREE_NUM, appUuid);
        redisService.setIfNull(key, num);
    }

    /**
     * 获取应用免费次数
     * @param appUuid 应用UUID
     * @return String 配置值
     */
    public String getFreeNum(String appUuid) {
        String key = String.format("%s%s:%s", YudaoSystemService.ConfigCache, AiAppUserPackageService.ACTIVITY_FREE_NUM, appUuid);
        return redisService.getRedisKey(key);
    }

    /**
     * 设置用户订阅标识
     * @param socialType 社交类型
     * @param socialUserId 社交用户ID
     * @param packageKeyAndType 订阅标识
     */
    public void setUserAppPackage(Integer socialType, Long socialUserId, String appUuid, String packageKeyAndType) {
        String key = String.format("package:user:%s:%s:%s", socialType, socialUserId, appUuid);
        redisService.setIfNull(key, packageKeyAndType);
    }

    /**
     * 获取用户订阅标识
     * @param socialType 社交类型
     * @param socialUserId 社交用户ID
     * @return String 订阅标识
     */
    public String getUserAppPackage(Integer socialType, Long socialUserId, String appUuid) {
        String key = String.format("package:user:%s:%s:%s", socialType, socialUserId, appUuid);
        return redisService.getRedisKey(key);
    }

    /**
     * 订阅时清掉 所有 免费订阅标识
     * @param socialType 社交类型
     * @param socialUserId 社交用户ID
     */
    public void deleteAllUserPackage(Integer socialType, Long socialUserId) {
        String key = String.format("package:user:%s:%s*", socialType, socialUserId);
        Set<String> keys = redisService.getRedisKeys(key);
        redisService.deleteRedisKeys(keys);
    }

    /**
     * 获取请求ID
     * @param requestId 请求ID
     * @return String 请求ID
     */
    public String getRequestId(String requestId) {
        String key = String.format("chat:requestId:%s", requestId);
        return redisService.setIfNull(key, requestId, 5*60L);
    }

    /**
     * 获取随机订购
     * @return
     */
    public String getPurchaseRecords() {
        String key = String.format("package:PurchaseRecord");
        return redisService.getRedisKey(key);
    }

    /**
     * 设置随机订购
     * @param data
     * @return
     */
    public String setPurchaseRecords(String data) {
        String key = String.format("package:PurchaseRecord");
        return redisService.setIfNull(key, data, 5*60L);
    }


}
