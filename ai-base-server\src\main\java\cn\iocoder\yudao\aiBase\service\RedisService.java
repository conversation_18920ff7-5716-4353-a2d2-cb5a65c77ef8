package cn.iocoder.yudao.aiBase.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RedisService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 设置redis key，默认1小时
     * @param key
     * @param value
     */
    public void setRedisKey(String key, String value) {
        setRedisKey(key, value, 60*60L);
    }

    /**
     * 设置redis key
     * @param key
     * @param value
     * @param timeout
     */
    public void setRedisKey(String key, String value, Long timeout) {
        stringRedisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
    }

    /**
     * 获取redis key
     * @param key
     * @return
     */
    public String getRedisKey(String key) {
        String value = stringRedisTemplate.opsForValue().get(key);
        return value;
    }

    /**
     * 设置redis key,返回获取到的值
     * @param key
     * @param value
     * @return
     */
    public String setIfNull(String key, String value) {
        return setIfNull(key, value, 60*60L);
    }

    /**
     * 设置redis key,返回获取到的值
     * @param key
     * @param value
     * @param timeout
     * @return
     */
    public String setIfNull(String key, String value, Long timeout) {
        String res = getRedisKey(key);
        if (res == null) {
            setRedisKey(key, value, timeout);
        }
        return res;
    }

    /**
     * 删除redis key
     * @param key
     * @return
     */
    public Boolean deleteRedisKey(String key) {
        return stringRedisTemplate.delete(key);
    }

    /**
     * 获取redis key
     * @param prefix 前缀
     * @return
     */
    public Set<String> getRedisKeys(String prefix) {
        Set<String> keys = stringRedisTemplate.keys(prefix);
        return keys;
    }

    /**
     * 批量删除redis key
     * @param keys
     */
    public void deleteRedisKeys(Set<String> keys) {
        stringRedisTemplate.delete(keys);
    }


}
