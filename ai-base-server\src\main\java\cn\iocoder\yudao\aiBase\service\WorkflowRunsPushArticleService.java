package cn.iocoder.yudao.aiBase.service;

import cn.iocoder.yudao.aiBase.config.BaseIService;
import cn.iocoder.yudao.aiBase.dto.param.QaParam;
import cn.iocoder.yudao.aiBase.dto.response.PushArticleResponse;
import cn.iocoder.yudao.aiBase.entity.WorkflowRunsPushArticle;

import java.util.UUID;

public interface WorkflowRunsPushArticleService extends BaseIService<WorkflowRunsPushArticle,QaParam> {

    PushArticleResponse getArticle(UUID id);

}
