package cn.iocoder.yudao.aiBase.service;

import cn.hutool.core.util.RandomUtil;
import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.infra.controller.admin.config.vo.ConfigPageReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.config.vo.ConfigRespVO;
import cn.iocoder.yudao.module.infra.convert.config.ConfigConvert;
import cn.iocoder.yudao.module.infra.dal.dataobject.config.ConfigDO;
import cn.iocoder.yudao.module.infra.service.config.ConfigService;
import cn.iocoder.yudao.module.system.controller.admin.mail.vo.log.MailLogPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO;
import cn.iocoder.yudao.module.system.dal.dataobject.mail.MailLogDO;
import cn.iocoder.yudao.module.system.enums.mail.MailSendStatusEnum;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import cn.iocoder.yudao.module.system.service.dict.DictDataService;
import cn.iocoder.yudao.module.system.service.mail.MailLogService;
import cn.iocoder.yudao.module.system.service.mail.MailSendService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
@DS(DBConstant.YuDao)
public class YudaoSystemService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private DictDataService dictDataService;

    @Autowired
    private ConfigService configService;

    @Resource
    private MailSendService mailSendService;

    @Resource
    private MailLogService mailLogService;

    public static final Integer CodeExpire = BaseConstant.TWO;

    public static final String ConfigCache = "configCache:";

    public static final String DictCache = "DictCache:";

    /**
     * 获取字典数据
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<DictDataDO> getDictDataListByDictType(String dictType) {
        return dictDataService.getDictDataListByDictType(dictType);
    }

    /**
     * 获取配置列表
     * @param pageReqVO
     * @return
     */
    public PageResult<ConfigRespVO> getConfigPage(ConfigPageReqVO pageReqVO) {
        PageResult<ConfigDO> page = configService.getConfigPage(pageReqVO);
        return ConfigConvert.INSTANCE.convertPage(page);
    }

    /**
     * 发送验证码
     * @param email
     * @return
     */
    public Integer sendEmail(String email) {
        Integer b = RandomUtil.randomInt(100000, 999999);
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("a", "MedSci xAI");
        templateParams.put("b", b);
        templateParams.put("c", CodeExpire);
        mailSendService.sendSingleMail(email, 0L, SocialTypeEnum.MEDSCI_AI.getType(), "RegisterCode", templateParams);
        return b;
    }

    /**
     * 校验验证码
     * @param email
     * @param code
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Boolean checkCode(String email, String code) {
        MailLogPageReqVO pageVO = new MailLogPageReqVO();
        pageVO.setToMail(email);
        pageVO.setSendStatus(MailSendStatusEnum.SUCCESS.getStatus());
        pageVO.setUserType(SocialTypeEnum.MEDSCI_AI.getType());
        pageVO.setSendTime(new LocalDateTime[]{LocalDateTime.now().minusHours(CodeExpire), LocalDateTime.now()});
        PageResult<MailLogDO> page = mailLogService.getMailLogPage(pageVO);

        Boolean res = false;
        for (MailLogDO mailLogDO : page.getList()) {
            if (code.equals(mailLogDO.getTemplateParams().get("b").toString())) {
                res = true;
                break;
            }
        }
        return res;
    }

    /**
     * 获取缓存中配置
     * @param pageReqVO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<ConfigRespVO> getConfigCache(ConfigPageReqVO pageReqVO) {
        pageReqVO.setPageSize(1000);
        List<ConfigRespVO> configPage = null;
        String key = ConfigCache+pageReqVO.getName()+":"+pageReqVO.getKey();
        String value = redisService.getRedisKey(key);
        if (value == null) {
            PageResult<ConfigRespVO> configPageRes = getConfigPage(pageReqVO);
            value = JSON.toJSONString(configPageRes.getList());
            redisService.setRedisKey(key, value);
        }
        if (configPage == null) {
            configPage = JSONObject.parseArray(value, ConfigRespVO.class);
        }
        return configPage;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ConfigDO getConfigByKey(String configKey) {
        return configService.getConfigByKey(configKey);
    }

    /**
     * 删除指定前缀的key
     * @param name
     */
    public void clearConfigCache(String name) {
        StringBuilder sb = new StringBuilder(ConfigCache);
        if (StringUtils.isNotBlank(name)) {
            sb = sb.append(name).append(BaseConstant.STAR_STR);
        }
        log.info("======删除前缀为{}的缓存", sb.toString());
        // 获取所有匹配前缀的键
        Set<String> keys = redisService.getRedisKeys(sb.toString());
        if (keys != null && !keys.isEmpty()) {
            redisService.deleteRedisKeys(keys);
        }
    }

    /**
     * 根据后缀获取限制上传大小
     * @param suffix
     * @return
     */
    public Integer getUploadLimitSize(String suffix) {
        Integer limit = 2; // 默认限制2M
        String configKey = "uploadType";
        String key = ConfigCache+configKey+":"+configKey;
        String value = redisService.getRedisKey(key);
        if (StringUtils.isBlank(value)) {
            ConfigDO configDO = getConfigByKey(configKey);
            if (configDO != null) {
                value = configDO.getValue();
                redisService.setRedisKey(key, value);
            }
        }
        JSONObject fileTypes = JSONObject.parseObject(value);
        JSONObject limitJson = fileTypes.getJSONObject("limit");
        for (String type : fileTypes.keySet()) {
            if (!"limit".equals(type)) {
                List<String> exts = fileTypes.getJSONArray(type).toJavaList(String.class);
                if (exts.contains(suffix)) {
                    limit = limitJson.getIntValue(type);
                    break;
                }
            }
        }

        return limit;
    }

    /**
     * 从缓存检查是否是 体验官
     * @param userId
     * @param userType
     * @param nameEn
     * @return
     */
    public Boolean checkIsUXOFromCache(Integer userType, Long userId, String nameEn) {
        String key = "app:isUXO:"+userType+BaseConstant.COLON_STR+userId+BaseConstant.COLON_STR+nameEn;
        String value = redisService.getRedisKey(key);
        if (value == null) {
            value = checkIsUXO(userType, userId, nameEn) ? BaseConstant.ONE.toString() : BaseConstant.TWO.toString();
            redisService.setRedisKey(key, value);
        }
        return BaseConstant.ONE.toString().equals(value);
    }

    /**
     * 检查是否是 体验官
     * @param userId
     * @param userType
     * @param nameEn
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Boolean checkIsUXO(Integer userType, Long userId, String nameEn) {
        Boolean res = getIsFreeTime(nameEn);
        if (!res) {
            return res;
        }

        JSONArray users = getFreeUsers(nameEn);
        if (users == null) {
            // 不是体验应用
            return res;
        }
        res = getFreeUsers(nameEn).contains(userType+BaseConstant.COLON_STR+userId);
        return res;
    }

    /**
     * 获取活动应用的免费体验时间
     * @param packageKey
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Boolean getIsFreeTime(String packageKey) {
        String date = "2025-01-01"; // 默认体验截止时间
        String freeTimeKey = "free_times";
        String key = ConfigCache+"activity_apps"+BaseConstant.COLON_STR+freeTimeKey;
        String value = redisService.getRedisKey(key);
        if (StringUtils.isBlank(value)) {
            ConfigDO configDO = getConfigByKey(freeTimeKey);
            if (configDO != null) {
                value = configDO.getValue();
                redisService.setRedisKey(key, value);
            }
        }
        if (StringUtils.isNotBlank(value)) {
            JSONObject jsonObject = JSONObject.parseObject(value);
            date = jsonObject.getString(packageKey);
            if (date == null) {
                return false;
            }
        }
        if (LocalDate.now().isAfter(LocalDate.parse(date))) {
            // 体验时间已过期
            return false;
        }
        return true;
    }

    /**
     * 获取活动应用的免费体验用户
     * @param packageKay
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JSONArray getFreeUsers(String packageKay) {
        String freeUserKey = "free_users";
        String key = ConfigCache+"activity_apps"+BaseConstant.COLON_STR+freeUserKey;
        String value2 = redisService.getRedisKey(key);
        if (StringUtils.isBlank(value2)) {
            ConfigDO configDO = getConfigByKey(freeUserKey);
            if (configDO != null) {
                value2 = configDO.getValue();
                redisService.setRedisKey(key, value2);
            }
        }
        if (StringUtils.isNotBlank(value2)) {
            JSONObject jsonObject = JSONObject.parseObject(value2);
            JSONArray res = jsonObject.getJSONArray(packageKay);
            return res;
        }
        return null;
    }

    /**
     * 根据key获取配置项
     * @param configKey
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JSONObject getConfigByKeyFromCache(String configKey) {
        return getConfigByKeyFromCache(configKey, "zh");
    }

    /**
     * 根据key获取配置项（支持语言过滤）
     * @param configKey 配置键
     * @param locale 语言标识，默认为"zh"
     * @return 过滤后的配置数据
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JSONObject getConfigByKeyFromCache(String configKey, String locale) {
        String key = ConfigCache+"activity_apps"+BaseConstant.COLON_STR+configKey;
        String value = redisService.getRedisKey(key);
        if (StringUtils.isBlank(value)) {
            ConfigDO configDO = getConfigByKey(configKey);
            if (configDO != null) {
                value = configDO.getValue();
                redisService.setRedisKey(key, value);
            }
        }
        if (StringUtils.isNotBlank(value)) {
            JSONObject configData = JSONObject.parseObject(value);
            return filterConfigByLocale(configData, locale);
        }
        return null;
    }

    /**
     * 根据语言标识过滤配置数据
     * @param configData 原始配置数据
     * @param locale 语言标识
     * @return 过滤后的配置数据
     */
    private JSONObject filterConfigByLocale(JSONObject configData, String locale) {
        if (configData == null || StringUtils.isBlank(locale)) {
            return configData;
        }

        // 标准化locale参数，处理无效值
        String normalizedLocale = normalizeLocale(locale);

        JSONObject result = new JSONObject();

        // 遍历配置数据的每个键
        for (String key : configData.keySet()) {
            Object value = configData.get(key);

            if (value instanceof JSONArray) {
                // 如果值是数组，进行语言过滤
                JSONArray filteredArray = filterArrayByLocale((JSONArray) value, normalizedLocale);
                result.put(key, filteredArray);
            } else if (value instanceof JSONObject) {
                // 如果值是对象，检查是否有lang属性
                JSONObject objValue = (JSONObject) value;
                if (objValue.containsKey("lang")) {
                    String itemLang = objValue.getString("lang");
                    if (normalizedLocale.equals(itemLang)) {
                        result.put(key, value);
                    } else if ("zh".equals(normalizedLocale)) {
                        // 如果请求中文但当前项不是中文，跳过
                        continue;
                    } else {
                        // 如果请求非中文但当前项不匹配，尝试查找中文版本
                        // 这种情况下保留原数据，让调用方处理
                        result.put(key, value);
                    }
                } else {
                    // 没有lang属性，直接保留
                    result.put(key, value);
                }
            } else {
                // 其他类型数据，直接保留
                result.put(key, value);
            }
        }

        return result;
    }

    /**
     * 标准化locale参数
     * @param locale 原始locale参数
     * @return 标准化后的locale
     */
    private String normalizeLocale(String locale) {
        if (StringUtils.isBlank(locale)) {
            return "zh";
        }

        // 处理常见的locale格式
        String normalized = locale.toLowerCase().trim();
        switch (normalized) {
            case "zh-cn":
            case "zh_cn":
            case "chinese":
            case "中文":
                return "zh";
            case "en-us":
            case "en_us":
            case "english":
            case "英文":
                return "en";
            default:
                // 如果是其他值，检查是否是有效的语言代码
                if (normalized.matches("^[a-z]{2}(-[a-z]{2})?$")) {
                    return normalized.split("-")[0]; // 取语言代码部分
                }
                return "zh"; // 默认返回中文
        }
    }

    /**
     * 根据语言标识过滤数组数据
     * @param array 原始数组数据
     * @param locale 语言标识
     * @return 过滤后的数组数据
     */
    private JSONArray filterArrayByLocale(JSONArray array, String locale) {
        if (array == null || array.isEmpty()) {
            return array;
        }

        JSONArray filteredArray = new JSONArray();
        JSONArray fallbackArray = new JSONArray(); // 用于存储中文版本作为回退

        // 第一遍遍历：查找匹配的语言项
        for (Object item : array) {
            if (item instanceof JSONObject) {
                JSONObject itemObj = (JSONObject) item;
                if (itemObj.containsKey("lang")) {
                    String itemLang = itemObj.getString("lang");
                    if (locale.equals(itemLang)) {
                        filteredArray.add(item);
                    } else if ("zh".equals(itemLang)) {
                        // 收集中文版本作为回退选项
                        fallbackArray.add(item);
                    }
                } else {
                    // 如果没有lang属性，保留该项（认为是通用项）
                    filteredArray.add(item);
                }
            } else {
                // 非对象类型，直接保留
                filteredArray.add(item);
            }
        }

        // 如果没有找到匹配的语言项且不是请求中文，使用中文作为回退
        if (filteredArray.isEmpty() && !"zh".equals(locale) && !fallbackArray.isEmpty()) {
            log.info("未找到语言 {} 的配置项，回退到中文版本", locale);
            return fallbackArray;
        }

        // 如果请求中文但没有找到中文项，返回第一个可用项作为回退
        if (filteredArray.isEmpty() && "zh".equals(locale) && !array.isEmpty()) {
            log.warn("未找到中文配置项，返回第一个可用项作为回退");
            Object firstItem = array.get(0);
            filteredArray.add(firstItem);
        }

        return filteredArray;
    }

    /**
     * 验证locale参数是否有效
     * @param locale 语言标识
     * @return 是否有效
     */
    private boolean isValidLocale(String locale) {
        if (StringUtils.isBlank(locale)) {
            return false;
        }

        // 定义支持的语言列表
        String[] supportedLocales = {"zh", "en", "pt", "ar", "zh-tw", "id", "ja", "ko", "vi", "ms", "es"};
        String normalizedLocale = normalizeLocale(locale);

        for (String supported : supportedLocales) {
            if (supported.equals(normalizedLocale)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取语言字典
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JSONObject getLangDict() {
        String dictType = "ai_base_langs";
        String key = DictCache+dictType;
        String value = redisService.getRedisKey(key);
        if (StringUtils.isBlank(value)) {
            List<DictDataDO> dictDataList = getDictDataListByDictType(dictType);
            if (!dictDataList.isEmpty()) {
                JSONObject jsonObject = new JSONObject();
                for (DictDataDO dictDataDO : dictDataList) {
                    jsonObject.put(dictDataDO.getLabel(), dictDataDO.getRemark()+BaseConstant.SLASH_STR); // 路由片段
                    if ("中文".equals(dictDataDO.getLabel())) {
                        jsonObject.put(dictDataDO.getValue(), BaseConstant.EMPTY_STR);
                    }
                    jsonObject.put(dictDataDO.getRemark(), dictDataDO.getValue());
                }
                value = jsonObject.toJSONString();
                redisService.setRedisKey(key, value);
            }
        }
        if (StringUtils.isNotBlank(value)) {
            return JSONObject.parseObject(value);
        }
        return null;
    }


}
