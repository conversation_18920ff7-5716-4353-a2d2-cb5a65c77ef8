package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.dto.request.SaveActivityCollectReqVO;
import cn.iocoder.yudao.aiBase.entity.AiActivityCollect;
import cn.iocoder.yudao.aiBase.mapper.AiActivityCollectMapper;
import cn.iocoder.yudao.aiBase.service.AiActivityCollectService;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@DS(DBConstant.AiBase)
public class AiActivityCollectServiceImpl extends ServiceImpl<AiActivityCollectMapper, AiActivityCollect> implements AiActivityCollectService {

    @Override
    public Boolean saveCollect(SaveActivityCollectReqVO param) {
        AiActivityCollect record = BeanUtils.toBean(param, AiActivityCollect.class);
        record.setCreatedAt(LocalDateTime.now());
        record.setUpdatedAt(LocalDateTime.now());
        return save(record);
    }
}