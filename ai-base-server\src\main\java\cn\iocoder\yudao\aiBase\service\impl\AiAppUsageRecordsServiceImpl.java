package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.entity.AiAppUsageRecords;
import cn.iocoder.yudao.aiBase.mapper.AiAppUsageRecordsMapper;
import cn.iocoder.yudao.aiBase.service.AiAppUsageRecordsService;
import cn.iocoder.yudao.aiBase.service.AiAppUserPackageService;
import cn.iocoder.yudao.aiBase.service.RedisService;
import cn.iocoder.yudao.module.infra.dal.dataobject.config.ConfigDO;
import cn.iocoder.yudao.module.infra.service.config.ConfigService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;

/**
 * 应用使用记录 Service 实现类
 */
@Service
@Slf4j
public class AiAppUsageRecordsServiceImpl extends ServiceImpl<AiAppUsageRecordsMapper, AiAppUsageRecords> implements AiAppUsageRecordsService {

    @Resource
    private ConfigService configService;
    
    @Resource
    private RedisService redisService;
    
    /**
     * Redis缓存相关常量
     */
    private static final String REDIS_CONFIG_KEY_PREFIX = "infra-config:all_apps:free_limit";
    private static final long REDIS_CACHE_EXPIRE = 43200; // 缓存过期时间，12小时
    private static final int DEFAULT_FREE_LIMIT = 10; // 默认免费次数限制
    
    @Override
    public boolean addUsageRecord(Long socialUserId, Integer socialType, String appUuid, String requestId) {
        // 有剩余次数，添加使用记录
        AiAppUsageRecords record = new AiAppUsageRecords();
        record.setSocialUserId(socialUserId);
        record.setSocialType(socialType);
        record.setAppUuid(appUuid);
        record.setRequestId(requestId);
        record.setCreatedAt(LocalDateTime.now());
        record.setUpdatedAt(LocalDateTime.now());
        record.setDeleted(0); // 未删除

        return this.save(record);
    }
    
    @Override
    public int recordAppUsage(Long socialUserId, Integer socialType, String appUuid, String requestId) {
        // 检查用户是否还有剩余使用次数
        if (!checkUserCanUseApp(socialUserId, socialType, appUuid)) {
            log.info("用户[{}]类型[{}]使用应用[{}]已达上限，无法继续使用", socialUserId, socialType, appUuid);
            return -1; // -1表示没有剩余次数
        }

        // 有剩余次数，添加使用记录
        AiAppUsageRecords record = new AiAppUsageRecords();
        record.setSocialUserId(socialUserId);
        record.setSocialType(socialType);
        record.setAppUuid(appUuid);
        record.setRequestId(requestId);
        record.setCreatedAt(LocalDateTime.now());
        record.setUpdatedAt(LocalDateTime.now());
        record.setDeleted(0); // 未删除

        boolean success = this.save(record);
        return success ? 0 : -2; // 0=记录成功，-2=数据库操作失败
    }
    
    @Override
    public int getFreeAppUsageLimit() {
        // 先从Redis缓存获取
        String cacheValue = redisService.getRedisKey(REDIS_CONFIG_KEY_PREFIX);
        if (StringUtils.isNotBlank(cacheValue)) {
            try {
                return Integer.parseInt(cacheValue);
            } catch (NumberFormatException e) {
                log.error("Redis中的免费使用次数配置格式错误: {}", cacheValue, e);
            }
        }
        
        // 缓存中没有，从数据库获取
        int freeLimit = DEFAULT_FREE_LIMIT;
        try {
            ConfigDO configDO = configService.getConfigByKey("all_apps");
            if (configDO != null) {
                String configValue = configDO.getValue();
                JSONArray appConfigs = JSON.parseArray(configValue);
                for (int i = 0; i < appConfigs.size(); i++) {
                    JSONObject appConfig = appConfigs.getJSONObject(i);
                    if (AiAppUserPackageService.FREE_TYPE.equals(appConfig.getString("type"))) {
                        freeLimit = appConfig.getIntValue("num");
                        break;
                    }
                }
            } else {
                log.warn("获取配置失败：all_apps配置不存在，使用默认值：{}", DEFAULT_FREE_LIMIT);
            }
        } catch (Exception e) {
            log.error("解析all_apps配置失败，使用默认值：{}", DEFAULT_FREE_LIMIT, e);
        }
        
        // 将获取到的配置存入Redis缓存
        redisService.setRedisKey(REDIS_CONFIG_KEY_PREFIX, String.valueOf(freeLimit));
        log.info("已将免费使用次数配置缓存到Redis，值为：{}", freeLimit);
        
        return freeLimit;
    }
    
    /**
     * 清除Redis中的配置缓存
     */
    @Override
    public void clearFreeAppUsageLimitCache() {
        redisService.deleteRedisKey(REDIS_CONFIG_KEY_PREFIX);
        log.info("已清除免费使用次数配置缓存");
    }
    
    @Override
    public int countUserAppUsage(Long socialUserId, Integer socialType, String appUuid) {
        // 使用当前月份调用通用方法
        return countUserAppUsageByMonth(socialUserId, socialType, appUuid, YearMonth.now());
    }
    
    @Override
    public int countUserAppUsageByMonth(Long socialUserId, Integer socialType, String appUuid, YearMonth yearMonth) {
        // 获取指定月份的第一天和最后一天
        LocalDate firstDayOfMonth = yearMonth.atDay(1);
        LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

        // 设置时间范围为指定月份的开始和结束
        LocalDateTime startOfMonth = LocalDateTime.of(firstDayOfMonth, LocalTime.MIN); // 当月第一天 00:00:00
        LocalDateTime endOfMonth = LocalDateTime.of(lastDayOfMonth, LocalTime.MAX);   // 当月最后一天 23:59:59

        // 构建查询条件
        LambdaQueryWrapper<AiAppUsageRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiAppUsageRecords::getSocialUserId, socialUserId)
                .eq(AiAppUsageRecords::getSocialType, socialType)
                .eq(AiAppUsageRecords::getAppUuid, appUuid)
                .ge(AiAppUsageRecords::getCreatedAt, startOfMonth)
                .le(AiAppUsageRecords::getCreatedAt, endOfMonth);

        return (int) this.count(queryWrapper);
    }

    @Override
    public int countAll(Long socialUserId, Integer socialType, String appUuid) {
        // 构建查询条件
        LambdaQueryWrapper<AiAppUsageRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiAppUsageRecords::getSocialUserId, socialUserId)
                .eq(AiAppUsageRecords::getSocialType, socialType)
                .eq(AiAppUsageRecords::getAppUuid, appUuid);

        return (int) this.count(queryWrapper);
    }
    
    @Override
    public boolean checkUserCanUseApp(Long socialUserId, Integer socialType, String appUuid) {
        int usageCount = countUserAppUsage(socialUserId, socialType, appUuid);
        int freeLimit = getFreeAppUsageLimit();

        return usageCount < freeLimit || freeLimit == -1; // -1表示无限制
    }

    @Override
    public int getRemainingUsageCount(Long socialUserId, Integer socialType, String appUuid) {
        int usageCount = countUserAppUsage(socialUserId, socialType, appUuid);
        int freeLimit = getFreeAppUsageLimit();

        // 无限制的情况
        if (freeLimit == -1) {
            return -1;
        }

        // 计算剩余次数
        int remaining = freeLimit - usageCount;
        return Math.max(0, remaining);  // 确保不返回负数
    }
} 