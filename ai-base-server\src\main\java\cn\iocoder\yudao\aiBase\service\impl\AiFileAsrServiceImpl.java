package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.dto.param.FileAsrParam;
import cn.iocoder.yudao.aiBase.entity.AiFileAsr;
import cn.iocoder.yudao.aiBase.mapper.AiFileAsrMapper;
import cn.iocoder.yudao.aiBase.service.AiFileAsrService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ConcurrentModificationException;
import java.util.List;

@Slf4j
@Service
@DS(DBConstant.AiBase)
public class AiFileAsrServiceImpl extends ServiceImpl<AiFileAsrMapper, AiFileAsr> implements AiFileAsrService {

	@Override
	public List<AiFileAsr> selectList(FileAsrParam param) {
		return baseMapper.selectList(param);
	}

	@Override
	public AiFileAsr getByUuid(String uuid) {
		List<AiFileAsr> list = selectList(FileAsrParam.builder().uuid(uuid).build());
		return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
	}

	@Override
	public AiFileAsr getByMd5(String md5) {
		AiFileAsr res = null;
		List<AiFileAsr> list = selectList(FileAsrParam.builder().fileMd5(md5).status(BaseConstant.TWO).build());
		for (AiFileAsr fileAsr : list) {
			if (StringUtils.isNotBlank(fileAsr.getAsrText())) {
				res = fileAsr;
				break;
			}
		}

		return res;
	}

	@Override
	public void handleSend(JSONObject jsonObject) {

	}

	@Override
	public void handleReceived(JSONObject jsonObject) {

	}

}
