package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.dto.request.EventPageReqVO;
import cn.iocoder.yudao.aiBase.entity.AiStripeEventLog;
import cn.iocoder.yudao.aiBase.mapper.AiStripeEventLogMapper;
import cn.iocoder.yudao.aiBase.service.AiStripeEventLogService;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
@DS(DBConstant.AiBase)
public class AiStripeEventLogServiceImpl extends ServiceImpl<AiStripeEventLogMapper, AiStripeEventLog> implements AiStripeEventLogService {

    @Override
    public PageResult<AiStripeEventLog> selectPage(EventPageReqVO reqVO) {
        return baseMapper.selectPage(reqVO);
    }

    @Override
    public Integer insert(String eventId, String eventType, String stripeObject) {
        AiStripeEventLog eventLog = AiStripeEventLog.builder()
            .eventId(eventId)
            .eventType(eventType)
            .stripeObject(stripeObject)
            .createdAt(LocalDateTime.now())
            .build();
        return baseMapper.insert(eventLog);
    }

}
