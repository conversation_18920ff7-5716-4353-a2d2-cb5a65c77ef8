package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.dto.request.AppUserPageReqVO;
import cn.iocoder.yudao.aiBase.entity.AiSubOrders;
import cn.iocoder.yudao.aiBase.mapper.AiSubOrdersMapper;
import cn.iocoder.yudao.aiBase.mq.EventDataMsg;
import cn.iocoder.yudao.aiBase.mq.EventDataProducer;
import cn.iocoder.yudao.aiBase.service.AiSubOrdersService;
import cn.iocoder.yudao.aiBase.util.MedsciSubUtil;
import cn.iocoder.yudao.aiBase.util.StripeUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@DS(DBConstant.AiBase)
public class AiSubOrdersServiceImpl extends ServiceImpl<AiSubOrdersMapper, AiSubOrders> implements AiSubOrdersService {

    @Autowired
    private EventDataProducer eventDataProducer;

    @Override
    public PageResult<AiSubOrders> selectPage(AppUserPageReqVO reqVO) {
        return baseMapper.selectPage(reqVO);
    }

    @Override
    public List<AiSubOrders> selectList(AppUserPageReqVO reqVO) {
        return baseMapper.selectList(reqVO);
    }

    /**
     * 创建订单，并模拟支付
     *
     * @param subOrders
     * @return
     */
    @Override
    public Integer createOrder(AiSubOrders subOrders) {
        Integer res = baseMapper.insert(subOrders);
        if (!BaseConstant.ONE.equals(subOrders.getNeedPay())) {
            // 免费的 模拟支付成功
            eventDataProducer.send(EventDataMsg.builder().sigHeader(MedsciSubUtil.FreeEvent).payload(subOrders.getPiId()).build());
        }

        return res;
    }

    /**
     * 根据订单ID查询订单
     * @param piId
     * @return
     */
    @Override
    public AiSubOrders getByPiId(String piId) {
        AppUserPageReqVO reqVO = AppUserPageReqVO.builder().piId(piId).build();
        List<AiSubOrders> list = selectList(reqVO);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    /**
     * 更新支付
     * @param id
     */
    @Override
    public void updatePaid(Integer id) {
        AiSubOrders aiSubOrders = new AiSubOrders();
        aiSubOrders.setId(id);
        aiSubOrders.setPaymentStatus(StripeUtil.PAYMENT_PAID);
        aiSubOrders.setUpdatedAt(LocalDateTime.now());
        baseMapper.updateById(aiSubOrders);
    }

    @Override
    public List<AiSubOrders> getExpiredOrders(LocalDateTime expired) {
        AppUserPageReqVO reqVO = AppUserPageReqVO.builder()
                .expiredAt(new LocalDateTime[]{expired, expired.plusDays(BaseConstant.ONE)})
                .paymentStatus(StripeUtil.PAYMENT_PAID)
                .build();
        return selectList(reqVO);
    }



}
