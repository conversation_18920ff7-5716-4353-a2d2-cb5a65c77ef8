package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.dto.request.AppUserPageReqVO;
import cn.iocoder.yudao.aiBase.entity.AiSubscriptionLog;
import cn.iocoder.yudao.aiBase.mapper.AiSubscriptionLogMapper;
import cn.iocoder.yudao.aiBase.service.AiSubscriptionLogService;
import cn.iocoder.yudao.aiBase.util.StripeUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@DS(DBConstant.AiBase)
public class AiSubscriptionLogServiceImpl extends ServiceImpl<AiSubscriptionLogMapper, AiSubscriptionLog> implements AiSubscriptionLogService {

    @Override
    public List<AiSubscriptionLog> selectList(AppUserPageReqVO reqVO) {
        return baseMapper.selectList(reqVO);
    }

    @Override
    public PageResult<AiSubscriptionLog> selectPage(AppUserPageReqVO reqVO) {
        return baseMapper.selectPage(reqVO);
    }

    /**
     * 获取已订阅但未取消订阅的记录
     * @param socialUserId
     * @param socialType
     * @param appUuid
     * @return
     */
    @Override
    public AiSubscriptionLog getByAppUser(Long socialUserId, Integer socialType, String appUuid) {
        AppUserPageReqVO reqVO = AppUserPageReqVO.builder().socialUserId(socialUserId).socialType(socialType).appUuid(appUuid).build();
        List<AiSubscriptionLog> list = selectList(reqVO).stream()
            .filter(item -> StripeUtil.PAYMENT_PAID.equals(item.getPaymentStatus()) && item.getSubId()!=null && item.getUnsubEventId()==null)
            .toList();

        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    /**
     * 获取有效的订阅
     * @param socialUserId
     * @param socialType
     * @param appUuid
     * @param priceId
     * @return
     */
    @Override
    public AiSubscriptionLog getLastValid(Long socialUserId, Integer socialType, String appUuid, String priceId) {
        AppUserPageReqVO reqVO = AppUserPageReqVO.builder().socialUserId(socialUserId).socialType(socialType).appUuid(appUuid)
            .priceId(priceId)
            .createdAt(new LocalDateTime[]{LocalDateTime.now().minusDays(1L), LocalDateTime.now()}).build();
        List<AiSubscriptionLog> list = selectList(reqVO);
        if (!list.isEmpty()) {
            if (LocalDateTime.now().plusMinutes(15L).isBefore(list.get(BaseConstant.ZERO).getExpiredAt())) {
                // 如果当前时间+15分钟，小于过期时间，则认为有效，返回
                return list.get(BaseConstant.ZERO);
            }
        }
        return null;
    }

    /**
     * 创建订阅
     * @param subLog
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createSubLog(AiSubscriptionLog subLog) {
        return baseMapper.insert(subLog);
    }

    /**
     * 根据checkSessionId查询
     * @param checkoutSessionId
     * @return
     */
    @Override
    public AiSubscriptionLog getByCheckoutSessionId(String checkoutSessionId) {
        AppUserPageReqVO reqVO = AppUserPageReqVO.builder().checkoutSessionId(checkoutSessionId).build();
        List<AiSubscriptionLog> list = selectList(reqVO);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    /**
     * 更新订阅
     * @param subLog
     * @return
     */
    @Override
    public Integer updateSubLog(AiSubscriptionLog subLog) {
        return baseMapper.updateById(subLog);
    }

    /**
     * 根据订阅id查询
     * @param subId
     * @return
     */
    @Override
    public AiSubscriptionLog getBySubId(String subId) {
        AppUserPageReqVO reqVO = AppUserPageReqVO.builder().subId(subId).build();
        List<AiSubscriptionLog> list = selectList(reqVO);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    /**
     * 更新订阅id
     * @param id
     * @param subId
     * @return
     */
    @Override
    public  Integer updateSubId(Integer id, String subId) {
        AiSubscriptionLog subLog = new AiSubscriptionLog();
        subLog.setId(id);
        subLog.setSubId(subId);
        subLog.setSubAt(LocalDateTime.now());
        subLog.setUpdatedAt(LocalDateTime.now());
        return updateSubLog(subLog);
    }

    /**
     * 根据订单ID查询
     * @param piId
     * @return
     */
    @Override
    public AiSubscriptionLog getByPiId(String piId) {
        AppUserPageReqVO reqVO = AppUserPageReqVO.builder().piId(piId).build();
        List<AiSubscriptionLog> list = selectList(reqVO);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }



}
