package cn.iocoder.yudao.aiBase.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.dto.request.difyStatistics.DifyStatisticsMessageReqVo;
import cn.iocoder.yudao.aiBase.dto.response.DifyStatisticsMessageResponse;
import cn.iocoder.yudao.aiBase.entity.Messages;
import cn.iocoder.yudao.aiBase.mapper.DifyStatisticsMapper;
import cn.iocoder.yudao.aiBase.service.DifyStatisticsService;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@DS(DBConstant.DifyBase)
public class DifyStatisticsServiceImpl extends ServiceImpl<DifyStatisticsMapper, Messages> implements DifyStatisticsService {

    @Autowired
    private DifyStatisticsMapper difyStatisticsMapper;

    @Override
    public PageResult<DifyStatisticsMessageResponse> getMessages(DifyStatisticsMessageReqVo reqVO) {
        // 开始结束时间转时间戳 结束时间+1天
        reqVO.setStartTime(DateUtil.parse(reqVO.getCreateTimeStart()).getTime());
        reqVO.setEndTime(DateUtil.parse(reqVO.getCreateTimeEnd()).getTime() + 24 * 60 * 60 * 1000);

        // 计算 offset 和 limit
        int offset = (reqVO.getPageNo() - 1) * reqVO.getPageSize();
        reqVO.setOffset(offset);
        reqVO.setLimit(reqVO.getPageSize());

        // 调用 Mapper 方法获取消息列表
        List<DifyStatisticsMessageResponse> messages = difyStatisticsMapper.selectMessages(reqVO);

        // 获取总记录数
        long total = difyStatisticsMapper.countMessages(reqVO);

        // 创建分页结果
        PageResult<DifyStatisticsMessageResponse> pageResult = new PageResult<>();
        pageResult.setList(messages);
        pageResult.setTotal(total);

        return pageResult;
    }
}
