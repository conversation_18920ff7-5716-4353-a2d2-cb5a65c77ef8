package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.config.ErrorCodeConstants;
import cn.iocoder.yudao.aiBase.dto.param.MsUserParam;
import cn.iocoder.yudao.aiBase.dto.request.*;
import cn.iocoder.yudao.aiBase.dto.request.dify.MedsciUserRequest;
import cn.iocoder.yudao.aiBase.dto.response.FacebookUserInfo;
import cn.iocoder.yudao.aiBase.dto.response.GoogleUserInfo;
import cn.iocoder.yudao.aiBase.dto.response.PurchaseRecord;
import cn.iocoder.yudao.aiBase.dto.response.TokenResponse;
import cn.iocoder.yudao.aiBase.entity.AiAppLangs;
import cn.iocoder.yudao.aiBase.entity.AiAppUsers;
import cn.iocoder.yudao.aiBase.entity.MedsciUsers;
import cn.iocoder.yudao.aiBase.mapper.MedsciUsersMapper;
import cn.iocoder.yudao.aiBase.service.*;
import cn.iocoder.yudao.aiBase.util.CommonUtil;
import cn.iocoder.yudao.aiBase.util.JwtTokenUtil;
import cn.iocoder.yudao.aiBase.util.StripeUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stripe.model.Customer;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

@Slf4j
@Service
@DS(DBConstant.AiBase)
public class MedsciUsersServiceImpl extends ServiceImpl<MedsciUsersMapper, MedsciUsers> implements MedsciUsersService {

    public static final Integer TRIAL_DAY = BaseConstant.ZERO; // 试用期
    public static final Integer ENABLE = BaseConstant.ONE;
    public static final Integer DISABLE = BaseConstant.TWO;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private AiAppLangsService aiAppLangsService;

    @Autowired
    private AiAppUsersService aiAppUsersService;

    @Autowired
    private RedisManage redisManage;

    @Autowired
    private OauthService oauthService;

    @Autowired
    private YudaoSystemService yudaoSystemService;

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public MedsciUsers getUser(MedsciUserRequest param) {
        if (StringUtils.isBlank(param.getAvatar()) || !param.getAvatar().startsWith("http")) {
            param.setAvatar("https://img.medsci.cn/web/img/user_icon.png");
        }
        MedsciUsers user = getBySocialUserIdAndOpenid(param.getSocialUserId(), param.getOpenid());
        if (user == null) {
            user = BeanUtils.toBean(param, MedsciUsers.class);
            user.setStatus(TRIAL_DAY>0 ? ENABLE : DISABLE);
            user.setExpireAt(LocalDateTime.now().plusDays(TRIAL_DAY));
            user.setPassword(passwordEncoder.encode(LocalDateTime.now().format(CommonUtil.DateTimeFormat4)+"@aiBase"));
            this.save(user);
            user = getBySocialUserIdAndOpenid(param.getSocialUserId(), param.getOpenid());
        } else {
            if (StringUtils.isNotBlank(param.getRealName())) {
                user.setRealName(param.getRealName());
            }
            if (StringUtils.isNotBlank(param.getUserName())) {
                user.setUserName(param.getUserName());
            }
            if (StringUtils.isNotBlank(param.getMobile())) {
                user.setMobile(param.getMobile());
            }
            if (StringUtils.isNotBlank(param.getEmail())) {
                user.setEmail(param.getEmail());
            }
            if (StringUtils.isNotBlank(param.getAvatar())) {
                user.setAvatar(param.getAvatar());
            }
            baseMapper.updateById(user);
            updateUserStatus(user);
        }

        // 如果来源是主站，则直接订阅传入应用的试用版
        if ("medsci".equals(param.getFromPlatform()) && StringUtils.isNotBlank(param.getAppUuid())) {
            CreateSubReqVO reqVO = new CreateSubReqVO();
            reqVO.setAppUuid(param.getAppUuid());
            aiAppLangsService.createAutoSubscription(user, reqVO);
        }

        // 更新缓存
        redisManage.setIsInternalUser(user.getSocialType(), user.getSocialUserId(), user.getIsInternalUser());
        redisManage.setUserName(user.getSocialType(), user.getSocialUserId(), user.getUserName());
        return user;
    }

    @Override
    public Long createUsers(MedsciUsersSaveReqVO createReqVO) {
        // 插入
        MedsciUsers users = BeanUtils.toBean(createReqVO, MedsciUsers.class);
        baseMapper.insert(users);
        updateUserStatus(users);
        // 返回
        return users.getSocialUserId();
    }

    @Override
    public void updateUsers(MedsciUsersSaveReqVO updateReqVO) {
        // 校验存在
        validateUsersExists(updateReqVO.getId());
        // 更新
        MedsciUsers updateObj = BeanUtils.toBean(updateReqVO, MedsciUsers.class);
        MedsciUsers other = getByEmail(updateObj.getEmail());
        if(other!=null && !updateObj.getId().equals(other.getId())){
            throw exception(ErrorCodeConstants.ERROR_5003);
        }

        baseMapper.updateById(updateObj);
        updateUserStatus(updateObj);
        redisManage.updateIsInternalUser(updateObj.getSocialType(), updateObj.getSocialUserId(), updateObj.getIsInternalUser());
    }

    @Override
    public void deleteUsers(Integer id) {
        MedsciUsers user = getUsers(id);
        if (user == null) {
            throw exception(ErrorCodeConstants.ERROR_5001);
        }
        user.setDeleted(BaseConstant.ONE);
        baseMapper.updateById(user);
    }

    private void validateUsersExists(Integer id) {
        if (baseMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.ERROR_5001);
        }
    }

    @Override
    public MedsciUsers getUsers(Integer id) {
        return baseMapper.selectById(id);
    }

    @Override
    public PageResult<MedsciUsers> selectPage(MedsciUsersPageReqVO pageReqVO) {
        return baseMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MedsciUsers> selectList(MsUserParam param) {
        return baseMapper.selectList(param);
    }

    @Override
    public MedsciUsers getBySocialUserId(Integer socialType, Long socialUserId) {
        MsUserParam userParam = MsUserParam.builder()
                .socialType(socialType)
                .socialUserId(socialUserId)
                .build();
        List<MedsciUsers> list = selectList(userParam);
        MedsciUsers user = list.isEmpty() ? null : list.get(BaseConstant.ZERO);
        if (user != null) {
            redisManage.setIsInternalUser(user.getSocialType(), user.getSocialUserId(), user.getIsInternalUser());
            redisManage.setUserName(user.getSocialType(), user.getSocialUserId(), user.getUserName());
        }

        return user;
    }

    @Override
    public MedsciUsers getBySocialOpenid(Integer socialType, String openid) {
        MsUserParam userParam = MsUserParam.builder()
                .socialType(socialType)
                .openid(openid)
                .build();
        List<MedsciUsers> list = selectList(userParam);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    @Override
    public MedsciUsers getBySocialUserIdAndOpenid(Long socialUserId, String openid) {
        MsUserParam userParam = MsUserParam.builder()
                .socialUserId(socialUserId)
                .openid(openid)
                .build();
        List<MedsciUsers> list = selectList(userParam);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    @Override
    public MedsciUsers getByUserName(String userName) {
        MsUserParam userParam = MsUserParam.builder()
                .userName(userName)
                .build();
        List<MedsciUsers> list = selectList(userParam);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    @Override
    public MedsciUsers getByEmail(String email) {
        MsUserParam userParam = MsUserParam.builder()
                .email(email)
                .build();
        List<MedsciUsers> list = selectList(userParam);
        return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
    }

    /**
     * 更新用户过期状态
     * @param user
     * @return
     */
    @Override
    public Boolean updateUserStatus(MedsciUsers user) {
        if (user.getStatus().equals(ENABLE)) {
            if (user.getExpireAt()==null || LocalDate.now().isAfter(user.getExpireAt().toLocalDate())) {
                user.setStatus(DISABLE);
                baseMapper.updateById(user);
            }
        }

        return true;
    }

    @Override
    public Boolean checkStatusByAuthUser(OAuth2AccessTokenCheckRespDTO authUser) {
        MedsciUsers user = getUserByAuthUser(authUser);
        if (user == null) {
            return false;
        }

        return ENABLE.equals(user.getStatus());
    }

    @Override
    public MedsciUsers getUserByAuthUser(OAuth2AccessTokenCheckRespDTO authUser) {
        if (authUser == null) {
            return null;
        }
        return getBySocialUserId(authUser.getUserType(), authUser.getUserId());
    }

    /**
     * 谷歌和facebook注册及登录
     * @param socialType
     * @param userInfo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MedsciUsers getUser(Integer socialType, String userInfo) {
        MedsciUsers user = null;
        if (SocialTypeEnum.GOOGLE.getType().equals(socialType)) {
            GoogleUserInfo googleUserInfo = JsonUtils.parseObject(userInfo, GoogleUserInfo.class);
            user = getBySocialOpenid(socialType, googleUserInfo.getSub());
            if (user == null) {
                user = getByEmail(googleUserInfo.getEmail());
                if (user != null) {
                    throw exception(ErrorCodeConstants.ERROR_5040);
                }
                user = new MedsciUsers();
                user.setSocialUserId(googleUserInfo.getSocialUserId());
                user.setOpenid(googleUserInfo.getSub());
                user.setSocialType(socialType);
                user.setFirstName(googleUserInfo.getGiven_name());
                user.setLastName(googleUserInfo.getFamily_name());
                user.setEmail(googleUserInfo.getEmail());
                user.setUserName(getUserName(googleUserInfo.getName()));
                user.setRealName(googleUserInfo.getName());
                user.setAvatar(googleUserInfo.getPicture());
                user.setMobile(BaseConstant.EMPTY_STR);
                user.setStatus(TRIAL_DAY>0 ? ENABLE : DISABLE);
                user.setExpireAt(LocalDateTime.now().plusDays(TRIAL_DAY));
                user.setPassword(passwordEncoder.encode(LocalDateTime.now().format(CommonUtil.DateTimeFormat4)+"@aiBase"));
                baseMapper.insert(user);
            }
        }
        if (SocialTypeEnum.FACEBOOK.getType().equals(socialType)) {
            FacebookUserInfo facebookUserInfo = JsonUtils.parseObject(userInfo, FacebookUserInfo.class);
            user = getBySocialOpenid(socialType, facebookUserInfo.getId());
            if (user == null) {
                user = getByEmail(facebookUserInfo.getEmail());
                if (user != null) {
                    throw exception(ErrorCodeConstants.ERROR_5041);
                }
                user = new MedsciUsers();
                user.setSocialUserId(facebookUserInfo.getSocialUserId());
                user.setOpenid(facebookUserInfo.getId());
                user.setSocialType(socialType);
                user.setEmail(facebookUserInfo.getEmail());
                user.setUserName(getUserName(facebookUserInfo.getName()));
                user.setRealName(facebookUserInfo.getName());
                user.setAvatar(facebookUserInfo.getPicture().getData().getUrl());
                user.setMobile(BaseConstant.EMPTY_STR);
                user.setStatus(TRIAL_DAY>0 ? ENABLE : DISABLE);
                user.setExpireAt(LocalDateTime.now().plusDays(TRIAL_DAY));
                user.setPassword(passwordEncoder.encode(LocalDateTime.now().format(CommonUtil.DateTimeFormat4)+"@aiBase"));
                baseMapper.insert(user);
            }
        }

        if (user != null) {
            updateUserStatus(user);
            if (user.getSocialUserId() == null) {
                updateStripeCustomerId(user.getId(), user.getEmail(), user.getRealName());
            }
        }

        return user;
    }

    /**
     * 为了dify能识别唯一用户
     * @param userName
     * @return
     */
    public String getUserName(String userName) {
        if (getByUserName(userName) == null) {
            return userName;
        }
        String[] strs = userName.split(BaseConstant.UNDER_LINE_STR);
        if (BaseConstant.ONE.equals(strs.length)) {
            return getUserName(userName+"_001");
        }
        try {
            String l = strs[strs.length - BaseConstant.ONE];
            Integer num = Integer.parseInt(l) + BaseConstant.ONE;
            userName = userName.replace(l, BaseConstant.EMPTY_STR) + String.format("%03d", num);
            return getUserName(userName);
        } catch (Exception e) {
            return getUserName(userName+"_001");
        }
    }

    /**
     * 设置国际支付用户ID
     * @param id
     * @param email
     * @param realName
     * @return
     */
    @Override
    public String updateStripeCustomerId(Long id, String email, String realName) {
        try {
            Customer customer = StripeUtil.getCustomerByEmail(active, email);
            if (customer == null) {
                customer = StripeUtil.createCustomer(active, email, realName);
            }

            MedsciUsers user = new MedsciUsers();
            user.setId(id);
            user.setStripeCustomerId(customer.getId());
            updateById(user);
            return customer.getId();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 支付成功后更新用户 过期信息
     * @param socialUserId
     * @param socialType
     * @param expireAt
     * @return
     */
    @Override
    public Boolean updateExpireAt(Long socialUserId, Integer socialType, LocalDateTime expireAt) {
        MedsciUsers user = getBySocialUserId(socialType, socialUserId);
        if (expireAt.isAfter(user.getExpireAt())) {
            user.setStatus(ENABLE);
            user.setExpireAt(expireAt);
            user.setUpdatedAt(LocalDateTime.now());
            return updateById(user);
        }
        return true;
    }

    /**
     * 注册
     *
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean register(RegisterReqVO param) {
        if (!yudaoSystemService.checkCode(param.getEmail(), param.getEmailCode())) {
            throw exception(ErrorCodeConstants.ERROR_5053);
        }
        Long socialUserId = oauthService.getSocialUserId(SocialTypeEnum.MEDSCI_AI.getType(), param.getEmail());

        MedsciUsers user = getByEmail(param.getEmail());
        if (user != null) {
            throw exception(ErrorCodeConstants.ERROR_5003);
        }
        String userName = param.getUserName();
        if (StringUtils.isBlank(userName)) {
            String[] strs = param.getEmail().split("@");
            userName = strs[0];
        }

        user = new MedsciUsers();
        user.setSocialUserId(socialUserId); // 自注册
        user.setOpenid(param.getEmail());
        user.setSocialType(SocialTypeEnum.MEDSCI_AI.getType());
        user.setEmail(param.getEmail());
        user.setUserName(getUserName(userName));
        user.setRealName(userName);
        user.setMobile(BaseConstant.EMPTY_STR);
        user.setPassword(passwordEncoder.encode(param.getPassword()));
        user.setStatus(TRIAL_DAY>0 ? ENABLE : DISABLE);
        user.setExpireAt(LocalDateTime.now().plusDays(TRIAL_DAY));
        Boolean res = baseMapper.insert(user) > BaseConstant.ZERO;
        if (res) {
            updateStripeCustomerId(user.getId(), user.getEmail(), user.getRealName());
        }
        return res;
    }

    /**
     * 登录
     *
     * @param param
     * @return
     */
    @Override
    public TokenResponse login(LoginReqVO param) {
        MedsciUsers user = getByEmail(param.getEmail());
        if (user == null) {
            throw exception(ErrorCodeConstants.ERROR_5001);
        }

        String numStr = redisManage.getErrorNum(param.getEmail());
        Integer num = numStr == null ? BaseConstant.ZERO : Integer.parseInt(numStr);
        if (num >= 5) {
            throw exception(ErrorCodeConstants.ERROR_5042);
        }

        if (!passwordEncoder.matches(param.getPassword(), user.getPassword())) {
            num ++;
            redisManage.updateErrorNum(param.getEmail(), num);
            throw exception(ErrorCodeConstants.ERROR_5043, 5-num);
        }

        // 登录成功删除错误次数
        redisManage.updateErrorNum(param.getEmail(), BaseConstant.ZERO);

        // 更新用户过期状态
        updateUserStatus(user);

        return getToken(user);
    }

    @Override
    public void logout(HttpServletResponse response, HttpServletRequest request, String auth) {
        oauthService.removeAccessToken(auth);
        oauthService.removeCookie(response, request,"yudaoToken");
    }

    @Override
    public Map<String, String> selectList(List<Integer> socialTypes, List<Long> socialUserIds) {
        Map<String, String> map = new HashMap<>();
        if (!socialUserIds.isEmpty() && !socialTypes.isEmpty()) {
            map = baseMapper.selectList(socialUserIds, socialTypes).stream()
                    .collect(Collectors.toMap(
                            item -> item.getSocialType()+BaseConstant.COLON_STR+item.getSocialUserId(),
                            MedsciUsers::getUserName,
                            (k1, k2) -> k1
                    ));
        }

        return map;
    }

    @Override
    public TokenResponse socialLogin(Integer socialType, String code, String state) {
        log.info("socialLogin {} ：{} ：{}", socialType, code, state);
        String openid = oauthService.getOauthOpenid(socialType, state, code);
        MedsciUsers user = getBySocialOpenid(socialType, openid);
        if (user == null) {
            throw exception(ErrorCodeConstants.ERROR_5001);
        }
        return getToken(user);
    }

    @Override
    public TokenResponse getAiWriteToken(MedsciUserRequest param) {
        MedsciUsers user = getUser(param);
        return getToken(user);
    }

    @Override
    public TokenResponse getToken(MedsciUsers user) {
        TokenResponse res = new TokenResponse();
        List<String> roles = Arrays.asList("tenant_admin");
        Integer days = 7;
        if (!"NoLoginUser".equals(user.getOpenid())) {
            user.setPassword(null);
            user.setMobile(null);

            OAuth2AccessTokenCreateReqDTO reqDTO = new OAuth2AccessTokenCreateReqDTO();
            reqDTO.setUserId(user.getSocialUserId());
            reqDTO.setUserType(user.getSocialType()); // WebProperties
            reqDTO.setClientId("ai-base");
            reqDTO.setScopes(roles);
            OAuth2AccessTokenRespDTO respDTO = oauthService.createAccessToken(reqDTO);
            res.setUserId(user.getOpenid());
            res.setOpenid(user.getOpenid());
            res.setSocialUserId(user.getSocialUserId());
            res.setToken(respDTO.getAccessToken());
            res.setSocialType(user.getSocialType());
            res.setUserInfo(user);

            days = 30;
        }

        String hToken = JwtTokenUtil.hasuraTwt(roles, user.getOpenid(), days);
        res.setHToken(hToken);

        return res;
    }

    @Override
    public String getUserName(OAuth2AccessTokenCheckRespDTO authUser) {
        String userName = redisManage.getUserName(authUser.getUserType(), authUser.getUserId());
        if (userName == null) {
            getBySocialUserId(authUser.getUserType(), authUser.getUserId());
        }
        userName = redisManage.getUserName(authUser.getUserType(), authUser.getUserId());
        return userName;
    }

    @Override
    public String getIsInternalUser(Long socialUserId, Integer socialType) {
        String res = redisManage.getIsInternalUser(socialType, socialUserId);
        if (res == null) {
            getBySocialUserId(socialType, socialUserId);
        }
        res = redisManage.getIsInternalUser(socialType, socialUserId);
        return res;
    }

    @Override
    public List<PurchaseRecord> getPurchaseRecords() {
        String data = redisManage.getPurchaseRecords();
        if (data == null) {
            // 生成5-10个随机字母
            int length = (int) (Math.random() * 6) + 5; // 生成5-10之间的随机数
            List<PurchaseRecord> records = new ArrayList<>();

            // 定义两种会员类型
            String[] membershipTypes = {"Novax Base", "Elavax Base"};

            for (int i = 0; i < length; i++) {
                // 随机生成大写或小写字母
                char randomLetter = (char) ((Math.random() * 26) + (Math.random() > 0.5 ? 65 : 97));

                // 随机选择会员类型
                String selectedMembership = membershipTypes[(int) (Math.random() * 2)];

                // 创建PurchaseRecord对象
                PurchaseRecord record = new PurchaseRecord();
                record.setUserName(String.valueOf(randomLetter) + "****");
                record.setAppName(selectedMembership);

                records.add(record);
            }

            data = JSON.toJSONString(records);
            redisManage.setPurchaseRecords(data);
        }

        return JSON.parseArray(data, PurchaseRecord.class);
    }



}
