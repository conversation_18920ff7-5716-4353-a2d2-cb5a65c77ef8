package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.dto.param.QaParam;
import cn.iocoder.yudao.aiBase.dto.param.QaSaveParam;
import cn.iocoder.yudao.aiBase.dto.request.dify.ChatMessagesRequest;
import cn.iocoder.yudao.aiBase.entity.Qa;
import cn.iocoder.yudao.aiBase.mapper.QaMapper;
import cn.iocoder.yudao.aiBase.mq.QaSaveMsg;
import cn.iocoder.yudao.aiBase.mq.QaSaveProducer;
import cn.iocoder.yudao.aiBase.service.ApiTokensService;
import cn.iocoder.yudao.aiBase.service.QaService;
import cn.iocoder.yudao.aiBase.util.CommonUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
@Slf4j
@Service
@DS(DBConstant.AiBase)
public class QaServiceImpl extends ServiceImpl<QaMapper, Qa> implements QaService {
    @Autowired
    private QaSaveProducer qaRequestProducer;

    @Value("${dify-base.article-qa-appid}")
    private String articleQaAppId;

    @Autowired
    private ApiTokensService apiTokensService;

    @Override
    public List<Qa> select(QaParam param) {
        return baseMapper.selectList(param);
    }

    @Override
    public void ruleSave(QaSaveParam param) {
        // 将QaSaveParam转换为QaSaveMsg并发送到队列
        QaSaveMsg msg = new QaSaveMsg();
        msg.setArticleId(param.getArticleId());
        msg.setRegenerate(param.getRegenerate());
        msg.setNum(param.getNum());
        msg.setUserName(param.getUserName());

        // 调用生产者，将消息发送到队列
        qaRequestProducer.sendQaSaveRequest(msg);
    }

    @Override
    public void handleQaRequest(QaSaveMsg msg) {
        JSONObject res = null;
        if ("Y".equals(msg.getRegenerate())) {
            // 调用 QaDatabaseService 删除历史数据
            deleteHistoryQa(msg.getArticleId());

            // 调用 QaApiService 获取 AI 生成的 QA 数据
            res = generateQaFromAi(msg);

            if (res == null || !res.containsKey("answer")) {
                log.warn("接口返回数据为空或格式错误，answer字段缺失，article_id: {}", msg.getArticleId());
                return;
            }

            String str = res.getString("answer").replace("```json", BaseConstant.EMPTY_STR).replace("```",BaseConstant.EMPTY_STR);
            if (!str.startsWith("[")) {
                log.warn("接口返回数据不是JSON数组格式，article_id: {}", msg.getArticleId());
                return;
            }
            JSONArray data = JSON.parseArray(str);

            // 调用 QaDatabaseService 保存生成的 QA 数据
            saveGeneratedQa(msg, data);

        } else {
            log.info("没有需要重新生成QA的数据: {}=={}", msg, res);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteHistoryQa(String articleId) {
        baseMapper.deleteByArticleId(articleId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveGeneratedQa(QaSaveMsg msg, JSONArray questionAnswerArray) {
        if (questionAnswerArray == null || questionAnswerArray.isEmpty()) {
            log.warn("接口返回数据格式错误， text字段缺失，article_id: {}", msg.getArticleId());
            return;
        }

        List<Qa> qaList = new ArrayList<>();
        questionAnswerArray.forEach(item -> {
            JSONObject questionAnswer = (JSONObject) item;
            Qa qa = new Qa();
            qa.setArticleId(msg.getArticleId());
            qa.setQuestion(questionAnswer.getString("提问"));
            qa.setAnswer(questionAnswer.getString("答案"));
            qa.setUserName(msg.getUserName());
            qa.setCreatedAt(LocalDateTime.now());
            qa.setUpdatedAt(LocalDateTime.now());
            qaList.add(qa);
        });

        if (!qaList.isEmpty()) {
            log.info("批量保存{}条数据，article_id: {}", qaList.size(), msg.getArticleId());
            saveBatch(qaList);
        }

        List<Qa> qaList1 = select(QaParam.builder().articleId(msg.getArticleId()).build());
        for (Qa qa : qaList1) {
            qa.setEncryptionId(CommonUtil.high_encryption_id(Long.valueOf(qa.getId())));
            updateById(qa);
        }
    }

    public JSONObject generateQaFromAi(QaSaveMsg msg) {
        try {
            ChatMessagesRequest param = new ChatMessagesRequest();
            param.setResponse_mode("blocking");  // 阻塞模式
            JSONObject inputs = new JSONObject();
            inputs.put("article_id", msg.getArticleId());
            inputs.put("num", msg.getNum());
            param.setInputs(inputs);
            param.setAppId(articleQaAppId);
            param.setUser("medsciArticle");
            param.setQuery("请生成提问及答案(任意提问)");

            // 调用 AI 接口获取 QA 数据
            return apiTokensService.chatMsg1(param);
        } catch (Exception e) {
            log.error("调用 AI 接口失败 article_id: {}", msg.getArticleId(), e);
            return null;
        }

    }

    @Override
    public void initEncryptionId() {
        QaParam param = QaParam.builder().encryptionId("").build();
        Integer id = null;
        for (int i = 0; i < 1000; i++) {
            if (id != null) {
                param.setId(Long.valueOf(id));
            }
            List<Qa> qaList1 = baseMapper.selectInitList(param);
            if (qaList1.isEmpty()) {
                break;
            }
            for (Qa qa : qaList1) {
                if (StringUtils.isBlank(qa.getEncryptionId())) {
                    qa.setEncryptionId(CommonUtil.high_encryption_id(Long.valueOf(qa.getId())));
                    updateById(qa);
                    id = qa.getId();
                }
            }
        }
    }

}
