package cn.iocoder.yudao.aiBase.service.impl;


import cn.iocoder.yudao.aiBase.config.WebSocketClient;
import cn.iocoder.yudao.aiBase.dto.asr.RecognitionParam;
import cn.iocoder.yudao.aiBase.service.RecognitionService;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URI;
import java.nio.ByteBuffer;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.concurrent.ExecutionException;

/**
 *
 * <AUTHOR>
 * @since 2024/04/24
 *
 */
@Service
public class RecognitionServiceImpl implements RecognitionService {

	@Override
	public Object recognition(MultipartFile file, RecognitionParam param) throws IOException, ExecutionException, InterruptedException {
//		if (file.isEmpty()) {
//			return "0"; // 文件为空，返回特殊值
//		}
//
//		String originalFilename = file.getOriginalFilename();
//		String[] parts = originalFilename.split("\\.");
//		String prefix = (parts.length > 0) ? parts[0] : originalFilename;
//		System.out.println(prefix);
//		String localFilePath = param.getFileUrl() + prefix + ".pcm";
//
//		File localFile = new File(localFilePath);
//
//		File destDir = localFile.getParentFile();
//		if (!destDir.exists() && !destDir.mkdirs()) {
//			throw new IOException("Unable to create destination directory: " + localFilePath);
//		}
//
//		file.transferTo(localFile);
		String localFilePath = "/Users/<USER>/Documents/java/ai-base-main/ai-base-server/src/main/resources/temp/test.mp4";
		String  prefix = "test";

		WebSocketClient client = new WebSocketClient();
		URI uri = URI.create(param.getServerIpPort());
		StandardWebSocketClient standardWebSocketClient = new StandardWebSocketClient();
		WebSocketSession webSocketSession = standardWebSocketClient.execute(client, null, uri).get();

		JSONObject configJson = new JSONObject();
		configJson.put("mode", param.getMode());
		configJson.put("wav_name", prefix);
		configJson.put("wav_format", "pcm"); // 文件格式为pcm
		configJson.put("is_speaking", true);
		configJson.put("hotwords", param.getHotWords());
				configJson.put("itn", true);

		// 发送配置参数与meta信息
		webSocketSession.sendMessage(new TextMessage(configJson.toString()));

		byte[] audioData;
		try {
			audioData = Files.readAllBytes(Paths.get(localFilePath));
		} catch (IOException e) {
			System.err.println("Error reading file: " + e.getMessage());
			e.printStackTrace();
			return "Error reading audio file"; // Return an appropriate error message or throw an exception
		}

		ByteBuffer audioByteBuffer = ByteBuffer.wrap(audioData);

		BinaryMessage binaryMessage = new BinaryMessage(audioByteBuffer);
		webSocketSession.sendMessage(binaryMessage);

		// 发送音频结束标志
		JSONObject endMarkerJson = new JSONObject();
		endMarkerJson.put("is_speaking", false);
		webSocketSession.sendMessage(new TextMessage(endMarkerJson.toString()));

		// TODO: 实现接收并处理服务端返回的识别结果

		return "test";

	}

	@Override
	public Object recognition2(MultipartFile file, RecognitionParam param) throws IOException, ExecutionException, InterruptedException {
//		if (file.isEmpty()) {
//			return "0"; // 文件为空，返回特殊值
//		}
//
//		String originalFilename = file.getOriginalFilename();
//		String[] parts = originalFilename.split("\\.");
//		String prefix = (parts.length > 0) ? parts[0] : originalFilename;
//		System.out.println(prefix);
//		String localFilePath = param.getFileUrl() + prefix + ".pcm";
//
//		File localFile = new File(localFilePath);
//
//		File destDir = localFile.getParentFile();
//		if (!destDir.exists() && !destDir.mkdirs()) {
//			throw new IOException("Unable to create destination directory: " + localFilePath);
//		}
//
//		file.transferTo(localFile);

		String localFilePath = "/Users/<USER>/Documents/java/ai-base-main/ai-base-server/src/main/resources/temp/test.mp3";
		String  prefix = "test";

		WebSocketClient client = new WebSocketClient();
		URI uri = URI.create(param.getServerIpPort());
		StandardWebSocketClient standardWebSocketClient = new StandardWebSocketClient();
		WebSocketSession webSocketSession = standardWebSocketClient.execute(client, null, uri).get();


		JSONObject configJson = new JSONObject();
		configJson.put("mode", param.getMode());
		configJson.put("wav_name", prefix);
		configJson.put("wav_format", "pcm"); // 文件格式为pcm
		configJson.put("is_speaking", true);
		configJson.put("hotwords", param.getHotWords());
		configJson.put("itn", true);

		// 发送配置参数与meta信息
		webSocketSession.sendMessage(new TextMessage(configJson.toString()));


		try (FileInputStream fis = new FileInputStream(localFilePath)) {
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = fis.read(buffer)) != -1) {
				baos.write(buffer, 0, bytesRead);
			}

			// 将所有读取的字节合并到一个字节数组中
			byte[] completeData = baos.toByteArray();

			// 使用字节数组创建BinaryMessage实例
			BinaryMessage binaryMessage = new BinaryMessage(completeData);
			webSocketSession.sendMessage(binaryMessage);
			// 使用或发送binaryMessage...
		} catch (IOException e) {
			System.err.println("Error reading file: " + e.getMessage());
			e.printStackTrace();
		}


		// 发送音频结束标志
		JSONObject endMarkerJson = new JSONObject();
		endMarkerJson.put("is_speaking", false);
		webSocketSession.sendMessage(new TextMessage(endMarkerJson.toString()));

		// TODO: 实现接收并处理服务端返回的识别结果

		return "test";

	}

}
