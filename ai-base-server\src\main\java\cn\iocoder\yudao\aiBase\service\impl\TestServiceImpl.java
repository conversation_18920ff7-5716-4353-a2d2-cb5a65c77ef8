package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.dto.param.IndexParam;
import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.entity.Test;
import cn.iocoder.yudao.aiBase.mapper.TestMapper;
import cn.iocoder.yudao.aiBase.service.TestService;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@DS(DBConstant.AiBase)
public class TestServiceImpl extends ServiceImpl<TestMapper, Test> implements TestService {

    // todo
    @Override
    public LambdaQueryWrapper getLambda(IndexParam param) {
        LambdaQueryWrapper<Test> lambda = new LambdaQueryWrapper<>();
        if (param == null) {
            return lambda;
        }
        lambda
                .eq(StringUtils.isNotEmpty(param.getName()), Test::getName, param.getName())
        ;

        return lambda;
    }

    @Override
    public Long test(IndexParam param) {
        return 1L;
    }

}
