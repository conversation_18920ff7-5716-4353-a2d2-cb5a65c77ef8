package cn.iocoder.yudao.aiBase.service.impl;

import cn.iocoder.yudao.aiBase.config.DBConstant;
import cn.iocoder.yudao.aiBase.dto.response.PushArticleResponse;
import cn.iocoder.yudao.aiBase.entity.WorkflowRunsPushArticle;
import cn.iocoder.yudao.aiBase.mapper.WorkflowRunsPushArticleMapper;
import cn.iocoder.yudao.aiBase.service.WorkflowRunsPushArticleService;
import cn.iocoder.yudao.aiBase.util.HttpRequestUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Slf4j
@Service
@DS(DBConstant.AiBase)
public class WorkflowRunsPushArticleServiceImpl extends ServiceImpl<WorkflowRunsPushArticleMapper, WorkflowRunsPushArticle> implements WorkflowRunsPushArticleService {

    @Value("${dify-base.openapi-host}")
    private String openapiHost;

    @Override
    public PushArticleResponse getArticle(UUID id) {
        WorkflowRunsPushArticle workflowRunsPushArticle = baseMapper.getArticle(id);
        PushArticleResponse res = new PushArticleResponse();
        if (res !=null && StringUtils.isNotBlank(workflowRunsPushArticle.getArticleId())) {
            String url = openapiHost + "get_article_by_id?id=" + workflowRunsPushArticle.getArticleId();
            JSONObject article = HttpRequestUtil.get(url, true, HttpRequestUtil.addHeader());

            if (article.getJSONObject("data") == null) {
                return null;
            }
            res.setMsArticle(article.getJSONObject("data"));
        }

        return res;
    }

}
