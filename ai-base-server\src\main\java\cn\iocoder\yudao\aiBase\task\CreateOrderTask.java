package cn.iocoder.yudao.aiBase.task;

import cn.iocoder.yudao.aiBase.service.AiAppLangsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Configuration
@Slf4j
public class CreateOrderTask {

    @Autowired
    private AiAppLangsService aiAppLangsService;

    @Scheduled(cron = "0 15 0 * * ?")
    protected void exec() {
        log.info("同步完成，dt={}", LocalDateTime.now());

        LocalDate today = LocalDate.now(); // 获取当前日期
        LocalDateTime end = today.atStartOfDay();
        try {
            // 处理今天到期的 支付宝订阅订单，发起自动扣款
            aiAppLangsService.handleTask(end);
        } catch (Exception e) {
            log.error("同步失败，dt={}", LocalDateTime.now(), e);
        }
    }


}
