package cn.iocoder.yudao.aiBase.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.aiBase.dto.request.dify.WorkflowsRunRequest;
import cn.iocoder.yudao.aiBase.service.ApiTokensService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Dify对话日志快照定时任务
 * 每3天的凌晨2点自动处理dify对话日志，生成SEO信息并推送到主站
 */
@Configuration
@Slf4j
public class SnapshotTask {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ApiTokensService apiTokensService;

    @Value("${user.dir}")
    private String projectRootPath;

    // 目标应用ID - 用于查询快照内容的dify应用ID
    @Value("${dify.snapshot.target-app-id:}")
    private String targetAppId;
    
    // SEO生成器应用ID
    @Value("${dify-base.seo-generator-app-id}")
    private String seoGeneratorAppId;
    
    // 主站文章推送应用ID
    @Value("${dify-base.article-push-app-id}")
    private String articlePushAppId;
    
    // 当前环境 - 用于判断使用哪个SEO生成器应用ID
    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    /**
     * 每7天的凌晨2点执行一次
     * 秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 0 2 */7 * ?")
    protected void generateSnapshot() {
        doGenerateSnapshot();
    }
    
    /**
     * 手动触发生成快照的公共方法（用于测试）
     * 
     * @return 生成结果信息
     */
    public String manualGenerateSnapshot() {
        log.info("手动触发处理工作流...");
        try {
            doGenerateSnapshot();
            return "工作流处理任务已完成，请查看日志获取详细信息";
        } catch (Exception e) {
            log.error("手动处理工作流失败", e);
            return "工作流处理失败: " + e.getMessage();
        }
    }
    
    /**
     * 实际执行任务的内部方法
     */
    private void doGenerateSnapshot() {
        log.info("开始处理Dify工作流，时间：{}", LocalDateTime.now());
        try {
            // 1. 查询需要处理的应用列表
            List<Map<String, Object>> appList = querySnapshotApps();
            if (CollUtil.isEmpty(appList)) {
                log.info("未找到需要处理的应用，任务结束");
                return;
            }
            
            log.info("找到{}个需要处理的应用", appList.size());

            // 2. 遍历应用列表，查询并处理工作流运行记录
            for (Map<String, Object> app : appList) {
                Object appUuidObj = app.get("app_uuid");
                String appUuid = appUuidObj instanceof UUID ? appUuidObj.toString() : String.valueOf(appUuidObj);
                
                String appName = (String) app.get("app_name");
                String appNameEn = (String) app.get("app_name_en");
                
                // 如果app_name_en为空，默认设置为"slug"
                if (appNameEn == null || appNameEn.trim().isEmpty()) {
                    appNameEn = "slug";
                    log.info("app_name_en为空，设置默认值：{}, appUuid: {}", appNameEn, appUuid);
                }
                
                // 获取app_type
                String appType = (String) app.get("app_type");
                if (appType == null) {
                    appType = "";
                }
                
                Object difyAppIdObj = app.get("dify_app_id");
                String difyAppId = difyAppIdObj instanceof UUID ? difyAppIdObj.toString() : String.valueOf(difyAppIdObj);
                
                String difyMode = (String) app.get("dify_mode");
                String difyName = (String) app.get("dify_name");
                
                log.info("处理应用：{} ({}), 英文名: {}, dify_app_id: {}, mode: {}, app_type: {}", 
                         appName, appUuid, appNameEn, difyAppId, difyMode, appType);
                
                // 只处理workflow模式的应用
                if (!"workflow".equals(difyMode)) {
                    log.info("跳过非workflow模式的应用：{}", appName);
                    continue;
                }
                
                try {
                    // 3. 查询应用的对话日志
                    List<Map<String, Object>> workflowRuns = queryWorkflowRuns(difyAppId);
                    log.info("应用[{}]找到{}条对话记录", appName, workflowRuns.size());
                    
                    if (CollUtil.isEmpty(workflowRuns)) {
                        continue;
                    }
                    
                    // 4. 处理每条对话
                    for (Map<String, Object> run : workflowRuns) {
                        processWorkflowRun(appName, appUuid, appNameEn, difyAppId, difyName, run, appType);
                    }
                } catch (Exception e) {
                    log.error("处理应用[{}]工作流时发生错误", appName, e);
                }
            }
            
            log.info("Dify工作流处理完成，时间：{}", LocalDateTime.now());
        } catch (Exception e) {
            log.error("处理Dify工作流失败", e);
        }
    }
    
    /**
     * 查询需要处理的应用列表
     */
    private List<Map<String, Object>> querySnapshotApps() {
        String sql = "SELECT \n" +
                "    a.app_uuid,\n" +
                "    a.app_name,\n" +
                "    a.app_name_en,\n" +
                "    a.app_type,\n" +
                "    p.id AS dify_app_id,\n" +
                "    p.name AS dify_name,\n" +
                "    p.mode AS dify_mode,\n" +
                "    t.name AS tag_name,\n" +
                "    t.type AS tag_type\n" +
                "FROM \n" +
                "    ai_base.ai_app_langs AS a\n" +
                "    JOIN public.apps AS p ON NULLIF(a.dify_app_uuid, '')::uuid = p.id\n" +
                "    LEFT JOIN public.tag_bindings AS tb ON p.id = tb.target_id\n" +
                "    LEFT JOIN public.tags AS t ON tb.tag_id = t.id\n" +
                "WHERE \n" +
                "    a.app_status = '上架'\n" +
                "    AND p.mode = 'workflow'\n" +
                "    AND (t.name = 'AI应用' OR t.name IS NULL)\n" +
                "    AND a.dify_app_uuid IS NOT NULL";
        
        return jdbcTemplate.queryForList(sql);
    }
    
    /**
     * 查询应用的对话日志（workflow_runs表）
     */
    private List<Map<String, Object>> queryWorkflowRuns(String difyAppId) {
        String sql = "SELECT wr.id, wr.app_id, wr.workflow_id, wr.inputs, wr.outputs, wr.status, wr.created_at " +
                "FROM public.workflow_runs wr " +
                "LEFT JOIN ai_base.workflow_runs_push_article wrpa ON wr.id = wrpa.id " +
                "WHERE wr.triggered_from = 'app-run' " +
                "AND wr.error IS NULL " +
                "AND wr.status = 'succeeded' " +
                "AND wr.type = 'workflow' " +
                "AND wr.app_id = ? " +
                "AND wrpa.id IS NULL " + // 排除已处理过的记录
                "ORDER BY wr.created_at DESC ";
        
        try {
            // 将字符串转换为UUID对象
            UUID appUuid = UUID.fromString(difyAppId);
            return jdbcTemplate.queryForList(sql, appUuid);
        } catch (IllegalArgumentException e) {
            log.error("无效的UUID格式: {}", difyAppId, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 处理单条workflow运行记录
     */
    private void processWorkflowRun(String appName, String appUuid, String appNameEn, String difyAppId, String difyName, Map<String, Object> run, String appType) {
        try {
            Object runIdObj = run.get("id");
            String runId = runIdObj instanceof UUID ? runIdObj.toString() : String.valueOf(runIdObj);
            
            // 检查该ID是否已经在workflow_runs_push_article表中存在
            try {
                Integer count = jdbcTemplate.queryForObject(
                    "SELECT COUNT(1) FROM ai_base.workflow_runs_push_article WHERE id = ?",
                    Integer.class,
                    UUID.fromString(runId)
                );
                
                if (count != null && count > 0) {
                    log.info("记录[{}]已经处理过，跳过", runId);
                    return;
                }
            } catch (Exception e) {
                log.error("检查记录是否存在时发生错误", e);
            }
            
            // 获取输出内容
            String outputsStr = (String) run.get("outputs");

            // 获取inputs
            String inputsStr = (String) run.get("inputs");

            // 如果outputs为空，跳过
            if (StrUtil.isBlank(outputsStr)) {
                log.info("跳过空输出的记录：{}", runId);
                return;
            }
            
            // 解析outputs和inputs
            JSONObject outputs = JSON.parseObject(outputsStr);
            JSONObject inputs = null;
            if (StrUtil.isNotBlank(inputsStr)) {
                inputs = JSON.parseObject(inputsStr);
            }

            if (outputs == null || outputs.isEmpty()) {
                log.info("outputs解析结果为空，跳过");
                return;
            }
            
            // 提取outputs的内容
            String outputValue = extractFirstProperty(outputs);
            
            // 检查inputs中是否包含需要的内容
            String inputValue = "";
            if (inputs != null && !inputs.isEmpty()) {
                if (inputs.containsKey("text")) {
                    inputValue = inputs.getString("text");
                } else if (inputs.containsKey("research")) {
                    inputValue = inputs.getString("research");
                } else if (inputs.containsKey("query")) {
                    inputValue = inputs.getString("query");
                } else if (inputs.containsKey("sourceText")) {
                    inputValue = inputs.getString("sourceText");
                } else {
                    log.info("inputs中不包含需要的属性(text/research/query/sourceText)，跳过处理");
                    return;
                }
            } else {
                log.info("inputs解析结果为空，跳过处理");
                return;
            }
            
            if (StrUtil.isBlank(inputValue)) {
                log.info("inputs内容为空，跳过处理");
                return;
            }
            
            // 调用SEO生成器应用
            JSONObject seoResult = callSeoGeneratorApp(outputValue, appName);
            
            if (seoResult == null) {
                log.error("SEO生成器调用失败，跳过处理");
                return;
            }
            
            // 从SEO结果中提取title
            String title = seoResult.getString("title");
            if (StrUtil.isBlank(title)) {
                log.info("SEO结果中没有title属性，尝试从inputs中获取");
                title = inputValue;
            }
            
            // 从SEO结果中提取标签和其他信息
            List<String> tags = extractTagsFromSEO(seoResult);
            String tagListStr = String.join(",", tags);
            
            // 提取SEO标题和描述（如果有的话）
            JSONObject seoTitle = seoResult.getJSONObject("seo_title");
            JSONObject seoDescription = seoResult.getJSONObject("seo_description");
            
            String description = "";
            if (seoDescription != null) {
                description = seoDescription.getString("core");
            }
            
            // 调用生成文章的Dify应用
            JSONObject articleResult = callArticlePushApp(title, outputValue, description, runId, tagListStr, appNameEn);
            
            if (articleResult == null) {
                log.error("文章推送应用调用失败，跳过处理");
                return;
            }

            // 检查status是否为200
            Integer status = articleResult.getInteger("status");
            if (status == null || status != 200) {
                log.error("文章推送应用返回状态码错误: {}", status);
            }
            
            // 从结果中获取文章ID
            String articleId = articleResult.getString("data");
            if (StrUtil.isBlank(articleId)) {
                log.error("文章推送应用返回的文章ID为空");
            }
            
            // 插入记录到数据库
            insertToPushArticleTable(runId, articleId, appUuid, appName, difyAppId, difyName, 
                                   JSON.toJSONString(seoTitle), JSON.toJSONString(seoDescription), appType);
            
            log.info("成功处理工作流记录：runId={}, articleId={}, appType={}", runId, articleId, appType);
            
        } catch (Exception e) {
            log.error("处理workflow记录时发生错误", e);
        }
    }
    
    /**
     * 从JSONObject中提取第一个属性的值
     * 如果是嵌套的JSON对象，将转换为字符串
     */
    private String extractFirstProperty(JSONObject jsonObject) {
        if (jsonObject == null || jsonObject.isEmpty()) {
            return "";
        }

        // 获取第一个键
        String firstKey = jsonObject.keySet().iterator().next();
        Object value = jsonObject.get(firstKey);

        // 如果值是JSON对象或数组，转换为字符串
        if (value instanceof JSONObject || value instanceof JSONArray) {
            return JSON.toJSONString(value);
        }

        // 否则直接返回值的字符串表示
        return value != null ? value.toString() : "";
    }
    
    /**
     * 调用SEO生成器应用
     * 
     * @param content 需要生成SEO信息的内容
     * @param appName 应用名称
     * @return SEO生成结果
     */
    private JSONObject callSeoGeneratorApp(String content, String appName) {
        try {
            log.info("调用SEO生成器应用：appId={}", seoGeneratorAppId);
            
            // 构建请求参数
            WorkflowsRunRequest request = new WorkflowsRunRequest();
            request.setAppId(seoGeneratorAppId);
            request.setResponse_mode("blocking"); 
            request.setRequestId(UUID.randomUUID().toString());
            request.setUser("medsciArticle");
            
            // 设置输入参数
            JSONObject inputs = new JSONObject();
            inputs.put("query", content);
            inputs.put("function", appName);
            request.setInputs(inputs);
            
            // 调用SEO生成器应用
            JSONObject response = apiTokensService.workflowsRun1(request);
            log.info("SEO生成器应用响应：{}", response);
            
            // 解析响应，获取输出内容
            if (response != null && response.containsKey("data")) {
                JSONObject data = response.getJSONObject("data");
                if (data != null && data.containsKey("outputs")) {
                    JSONObject outputs = data.getJSONObject("outputs");
                    if (outputs != null && outputs.containsKey("text")) {
                        String textJson = outputs.getString("text");
                        try {
                            // 处理Markdown格式的JSON字符串
                            textJson = cleanMarkdownJson(textJson);
                            // log.info("清理后的JSON: {}", textJson);
                            
                            // 尝试解析JSON字符串
                            return JSON.parseObject(textJson);
                        } catch (Exception e) {
                            log.error("解析SEO生成器返回的JSON时发生错误，返回基本JSON: {}", e.getMessage());
                            // 返回一个基本的JSON对象，包含最小必要信息
                            JSONObject fallbackResult = new JSONObject();
                            fallbackResult.put("title", "解析错误 - " + e.getMessage());
                            fallbackResult.put("tags", new JSONArray());
                            return fallbackResult;
                        }
                    }
                }
            }
            
            log.error("SEO生成器返回的数据格式不正确: {}", response);
            return null;
        } catch (Exception e) {
            log.error("调用SEO生成器应用时发生错误", e);
            return null;
        }
    }
    
    /**
     * 清理Markdown格式的JSON字符串，去除```json和```标记
     */
    private String cleanMarkdownJson(String markdownJson) {
        if (markdownJson == null) {
            return null;
        }
        
        // 去除开头的```json或```标记
        if (markdownJson.startsWith("```json")) {
            markdownJson = markdownJson.substring("```json".length());
        } else if (markdownJson.startsWith("```")) {
            markdownJson = markdownJson.substring("```".length());
        }
        
        // 去除结尾的```标记
        if (markdownJson.endsWith("```")) {
            markdownJson = markdownJson.substring(0, markdownJson.length() - "```".length());
        }
        
        // 去除可能的前后空白字符
        markdownJson = markdownJson.trim();
        
        // 尝试使用更安全的JSON解析方式
        try {
            // 先尝试标准解析，看是否合法JSON
            JSON.parseObject(markdownJson);
            return markdownJson;
        } catch (Exception e) {
            log.info("标准JSON解析失败，尝试修复JSON格式: {}", e.getMessage());
            
            // 修复常见的JSON格式问题
            try {
                // 处理字符串中未转义的双引号，通常出现在标题或描述等属性中
                markdownJson = fixJsonStringQuotes(markdownJson);
                
                // 测试修复后的JSON是否可解析
                JSON.parseObject(markdownJson);
                log.info("JSON格式修复成功");
                return markdownJson;
            } catch (Exception e2) {
                log.error("JSON格式修复失败，尝试使用备选方案解析: {}", e2.getMessage());
                
                // 如果仍然解析失败，可以考虑使用更严格的解析库或正则提取关键信息
                // 这里使用备选方案：正则表达式提取key-value对
                return handleSeverelyMalformedJson(markdownJson);
            }
        }
    }
    
    /**
     * 修复JSON字符串中未转义的双引号问题
     */
    private String fixJsonStringQuotes(String json) {
        if (json == null) {
            return null;
        }
        
        // 使用正则表达式匹配JSON字符串中的key模式
        Pattern pattern = Pattern.compile("\"([^\"]+)\"\\s*:\\s*\"([^\"]*)(\"[^\"]*\"[^\"]*)\"");
        Matcher matcher = pattern.matcher(json);
        StringBuffer sb = new StringBuffer();
        
        // 循环替换未转义的双引号
        while (matcher.find()) {
            String key = matcher.group(1);
            String valueStart = matcher.group(2);
            String valueWithQuotes = matcher.group(3);
            
            // 替换内部双引号为转义的双引号
            String fixedValue = valueWithQuotes.replace("\"", "\\\"");
            matcher.appendReplacement(sb, "\"" + key + "\":\"" + valueStart + fixedValue + "\"");
        }
        matcher.appendTail(sb);
        
        String result = sb.toString();
        if (result.equals(json)) {
            // 如果没有找到匹配项，尝试其他修复方法
            // 替换所有未转义的双引号 (位于两个非转义双引号之间的双引号)
            Pattern p = Pattern.compile("(?<=\\\":\"[^\"]*)(\")(.*?\")(?=[^\"]*\")");
            Matcher m = p.matcher(json);
            result = m.replaceAll("\\\\\"$2");
        }
        
        return result;
    }
    
    /**
     * 处理严重格式错误的JSON
     * 尝试提取主要信息，构建一个新的合法JSON
     */
    private String handleSeverelyMalformedJson(String malformedJson) {
        JSONObject result = new JSONObject();
        
        try {
            // 尝试提取title
            Pattern titlePattern = Pattern.compile("\"title\"\\s*:\\s*\"([^\"]*)\"");
            Matcher titleMatcher = titlePattern.matcher(malformedJson);
            if (titleMatcher.find()) {
                result.put("title", titleMatcher.group(1));
            }
            
            // 尝试提取tags数组
            // 这里简化处理，只获取tag labels
            Pattern tagPattern = Pattern.compile("\"label\"\\s*:\\s*\"([^\"]*)\"");
            Matcher tagMatcher = tagPattern.matcher(malformedJson);
            JSONArray tags = new JSONArray();
            while (tagMatcher.find()) {
                JSONObject tag = new JSONObject();
                tag.put("label", tagMatcher.group(1));
                tags.add(tag);
            }
            result.put("tags", tags);
            
            log.info("从格式错误的JSON中提取了基本信息: title和{}个tags", tags.size());
            return result.toJSONString();
        } catch (Exception e) {
            log.error("处理严重格式错误的JSON失败", e);
            // 返回最小合法JSON
            return "{\"title\":\"解析错误\",\"tags\":[]}";
        }
    }
    
    /**
     * 从SEO结果中提取标签
     */
    private List<String> extractTagsFromSEO(JSONObject seoResult) {
        List<String> tagList = new ArrayList<>();
        
        try {
            if (seoResult != null && seoResult.containsKey("tags")) {
                JSONArray tagsArray = seoResult.getJSONArray("tags");
                
                if (tagsArray != null) {
                    for (int i = 0; i < tagsArray.size(); i++) {
                        JSONObject tagObj = tagsArray.getJSONObject(i);
                        if (tagObj != null && tagObj.containsKey("label")) {
                            tagList.add(tagObj.getString("label"));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("提取标签时发生错误", e);
        }
        
        return tagList;
    }
    
    /**
     * 调用文章推送应用
     * 
     * @param title 标题内容（来自inputs）
     * @param content 文章内容（来自outputs）
     * @param summary 摘要内容
     * @param workRunId 工作流运行ID
     * @param tagListStr 标签列表字符串
     * @param appNameEn 应用英文名称
     * @return 文章推送结果
     */
    private JSONObject callArticlePushApp(String title, String content, String summary, 
                                        String workRunId, String tagListStr, String appNameEn) {
        try {
            log.info("调用文章推送应用：appId={}", articlePushAppId);
            
            // 构建请求参数
            WorkflowsRunRequest request = new WorkflowsRunRequest();
            request.setAppId(articlePushAppId);
            request.setResponse_mode("blocking"); 
            request.setRequestId(UUID.randomUUID().toString());
            request.setUser("medsciArticle");
            
            // 设置输入参数
            JSONObject inputs = new JSONObject();
            inputs.put("title", title);
            inputs.put("content", content);
            inputs.put("summary", summary);
            inputs.put("work_run_id", workRunId);
            inputs.put("tagListStr", tagListStr);
            inputs.put("app_name_en", appNameEn);
            request.setInputs(inputs);
            
            // 调用文章推送应用
            JSONObject response = apiTokensService.workflowsRun1(request);
            log.info("文章推送应用响应：{}", response);
            
            // 解析响应，获取输出内容
            if (response != null && response.containsKey("data")) {
                JSONObject data = response.getJSONObject("data");
                if (data != null && data.containsKey("outputs")) {
                    JSONObject outputs = data.getJSONObject("outputs");
                    if (outputs != null && outputs.containsKey("text")) {
                        String textJson = outputs.getString("text");
                        // 解析text中的JSON字符串
                        return JSON.parseObject(textJson);
                    }
                }
            }
            
            return null;
        } catch (Exception e) {
            log.error("调用文章推送应用时发生错误", e);
            return null;
        }
    }
    
    /**
     * 将不带分隔符的UUID字符串转换为标准UUID格式
     */
    private String formatUUID(String uuidStr) {
        if (uuidStr == null || uuidStr.length() != 32) {
            return uuidStr;
        }
        return uuidStr.substring(0, 8) + "-" +
               uuidStr.substring(8, 12) + "-" +
               uuidStr.substring(12, 16) + "-" +
               uuidStr.substring(16, 20) + "-" +
               uuidStr.substring(20);
    }

    private void insertToPushArticleTable(String id, String articleId, String appUuid, 
                                        String appName, String difyAppId, String difyName,
                                        String seoTitle, String seoDescription, String appType) {
        try {
            String sql = "INSERT INTO ai_base.workflow_runs_push_article " +
                         "(id, article_id, app_uuid, app_name, dify_app_id, dify_name, seo_title, seo_description, app_type) " +
                         "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            // 打印SQL和参数信息
            log.info("准备执行SQL: {}", sql);
            log.info("原始参数: id={}, articleId={}, appUuid={}, appName={}, difyAppId={}, difyName={}, app_type={}", 
                    id, articleId, appUuid, appName, difyAppId, difyName, appType);
            
            // 格式化UUID
            String formattedId = formatUUID(id);
            String formattedDifyAppId = formatUUID(difyAppId);
            
            log.info("格式化后的UUID: id={}, difyAppId={}", 
                    formattedId, formattedDifyAppId);
            
            // 确保UUID格式正确
            UUID runId = UUID.fromString(formattedId);
            UUID difyAppIdObj = UUID.fromString(formattedDifyAppId);
            
            jdbcTemplate.update(
                sql,
                runId,
                articleId,
                appUuid,  // 直接使用字符串，不再转换为UUID
                appName,
                difyAppIdObj,
                difyName,
                seoTitle,
                seoDescription,
                appType
            );
            
            log.info("成功将文章ID[{}]插入到数据库", articleId);
        } catch (Exception e) {
            log.error("将记录插入到数据库时发生错误", e);
            throw e; // 抛出异常以便上层处理
        }
    }
}
