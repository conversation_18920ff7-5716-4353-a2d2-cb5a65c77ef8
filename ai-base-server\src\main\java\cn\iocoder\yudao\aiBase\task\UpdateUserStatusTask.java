package cn.iocoder.yudao.aiBase.task;

import cn.iocoder.yudao.aiBase.service.AiAppUserPackageService;
import cn.iocoder.yudao.aiBase.service.AiAppUsersService;
import cn.iocoder.yudao.aiBase.service.MedsciUsersService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Configuration
@Slf4j
public class UpdateUserStatusTask {

    @Autowired
    private MedsciUsersService medsciUsersService;

    @Autowired
    private AiAppUsersService aiAppUsersService;

    @Autowired
    private AiAppUserPackageService aiAppUserPackageService;

    @Scheduled(cron = "0 25 0 * * ?")
    protected void updateUserStatus() {
        log.info("同步完成，dt={}", LocalDateTime.now());

        LocalDate today = LocalDate.now(); // 获取当前日期
        LocalDateTime end = today.atStartOfDay();
        try {
            //  处理userPackage昨天过期的数据
            aiAppUserPackageService.handleTask(end);

            // 处理appuser昨天过期的数据
            aiAppUsersService.handleTask(end);

//            // 处理medsciUser昨天过期的数据
//            medsciUsersService.handleTask(end);
        } catch (Exception e) {
            log.error("同步失败，dt={}", LocalDateTime.now(), e);
        }
    }


}
