package cn.iocoder.yudao.aiBase.util;

import cn.iocoder.yudao.aiBase.dto.asr.ASRToken;
import com.alibaba.nls.client.AccessToken;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

@Slf4j
public class AliyunUtil {

    private static final String ALIYUN_AK_ID = "LTAI5tDqSxPf1k5tLpbJRtF6";
    private static final String ALIYUN_AK_SECRET = "******************************";

    public static ASRToken getAsrToken() {
        try {
            AccessToken accessToken = new AccessToken(ALIYUN_AK_ID, ALIYUN_AK_SECRET);
            accessToken.apply();

            ASRToken asrToken = new ASRToken();
            asrToken.setToken(accessToken.getToken());
            asrToken.setExpireTime(accessToken.getExpireTime());
            return asrToken;
        } catch (IOException e) {
            log.error("获取阿里云AccessKeyId失败:{}", e);
            return null;
        }
    }
}
