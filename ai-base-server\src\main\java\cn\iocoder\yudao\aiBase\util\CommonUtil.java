package cn.iocoder.yudao.aiBase.util;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.text.DecimalFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Slf4j
public class CommonUtil {

    public static final DateTimeFormatter DateTimeFormat = DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
    public static final DateTimeFormatter DateTimeFormat1 = DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY);
    public static final DateTimeFormatter DateTimeFormat2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    public static final DateTimeFormatter DateTimeFormat3 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter DateTimeFormat4 = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 判断小数位是否为0
     * @param num
     * @return
     */
    public static Boolean lastIsZero(Float num) {
        return Math.round(num) == num;
    }

    /**
     * 判断小数位是否为0
     * @param num
     * @return
     */
    public static Boolean lastIsZero(Double num) {
        return Math.round(num) == num;
    }

    /**
     * num1/num2 百分比，最多1位小数（小数位是0则舍去）
     * @param obj1
     * @param obj2
     * @return
     */
    public static String getPercent(Object obj1, Object obj2) {
        return getPercent(obj1, obj2, BaseConstant.ZERO_STR);
    }

    /**
     * num1/num2 百分比，最多1位小数（小数位是0则舍去）
     * @param obj1
     * @param obj2
     * @param def
     * @return
     */
    public static String getPercent(Object obj1, Object obj2, String def) {
        if (obj1 == null || obj2 == null) {
            return def;
        }

        double num1 = 0;
        double num2 = 0;
        try {
            num1 = Double.parseDouble(obj1.toString());  // 强制类型转换
            num2 = Double.parseDouble(obj2.toString());  // 强制类型转换
        } catch (ClassCastException e) {
            e.printStackTrace();
            return def;
        }
        if (num2 == 0) {
            return def;
        }

        DecimalFormat df = new DecimalFormat("0.0");
        Double num = (num1 / num2) * 100;
        String str = df.format(num);
        num = Double.parseDouble(str);
        if (lastIsZero(num)) {
            str = Math.round(num) + BaseConstant.EMPTY_STR;
            if (BaseConstant.ZERO_STR.equals(str)) {
                return def;
            }
        }
        return str;
    }

    /**
     * 获取区间随机数
     * @param min
     * @param max
     * @return
     */
    public static Double getRandom(Integer min, Integer max) {
        Random random = new Random();
        return min + (max-min)*random.nextDouble();
    }

    /**
     * 获取客户端IP
     * @param request
     * @return
     */
    public static String getIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        ip = "0:0:0:0:0:0:0:1".equals(ip) ? "127.0.0.1" : ip;
        log.info("获取客户端ip地址:{},Content-Type:{}", ip, request.getHeader("Content-Type"));
        log.info("获取客户端:{}", request.getHeader("clientId"));
        return ip;
    }

    /**
     * 时间戳转换为LocalDateTime
     * @param sec
     * @return
     */
    public static LocalDateTime getLocalDateTime(Long sec) {

        // 首先，将时间戳转换为Instant
        Instant instant = Instant.ofEpochSecond(sec);

        // 然后，你需要选择一个时区来将Instant转换为LocalDateTime
        // 如果你知道数据是在哪个时区生成的，应该使用那个时区
        // 否则，你可以使用系统默认时区或UTC时区
        ZoneId zoneId = ZoneId.systemDefault(); // 或者使用 ZoneId.of("UTC")

        // 将Instant转换为ZonedDateTime
        // 注意：这一步是可选的，因为你可以直接从Instant转换到LocalDateTime
        // 但使用ZonedDateTime可以让你更清楚地看到时区的影响
        // ZonedDateTime zonedDateTime = instant.atZone(zoneId);

        // 直接从Instant转换到LocalDateTime，使用指定的时区偏移
        // 注意：这里我们实际上没有改变时间，只是去掉了时区信息
        // 如果你想要的是“本地”时间（即不考虑时区），这通常是正确的做法
        // 但要小心，这样做可能会引入夏令时相关的问题
        return instant.atZone(zoneId).toLocalDateTime();
    }

    /**
     * 检查邮箱
     * @param email
     * @return
     */
    public static Boolean checkEmail(String email) {
        Pattern EMAIL_PATTERN = Pattern.compile(BaseConstant.EMAIL_REGEX);
        // 使用Pattern对象的matcher方法获取Matcher对象
        Matcher matcher = EMAIL_PATTERN.matcher(email);
        // 调用Matcher对象的matches方法，返回匹配结果
        return matcher.matches();
    }

    /**
     * 上传文件转成File
     * @param multipartFile
     * @return
     */
    public static File convertTo(MultipartFile multipartFile) {
        try {
            // 获取原始文件名
            String fileName = multipartFile.getOriginalFilename();
            // 创建一个临时文件
            File tempFile = File.createTempFile("temp", "_" + fileName);
            // 将 MultipartFile 的内容保存到临时文件中
            multipartFile.transferTo(tempFile);
            return tempFile;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取文件后缀
     * @param filename
     * @return
     */
    public static String getFileExtension(String filename) {
        if (StringUtils.isEmpty(filename)) {
            return BaseConstant.EMPTY_STR;
        }
        int lastDotIndex = filename.lastIndexOf(BaseConstant.DOT_STR);
        if (lastDotIndex == -1) {
            return BaseConstant.EMPTY_STR;
        }
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    public static Boolean checkJson(JSONObject json) {
        Boolean res = true;
        if (json == null || json.isEmpty()) {
            res = false;
        }
//        for (String key : json.keySet()) {
//            if (json.get(key) == null || json.get(key).toString().isEmpty()) {
//                res = false;
//            }
//        }

        return res;
    }

    public static String camelToKebab(String camelCase) {
        if (StringUtils.isBlank(camelCase)) {
            return camelCase;
        }
        StringBuilder kebabCase = new StringBuilder();
        for (char c : camelCase.toCharArray()) {
            if (Character.isUpperCase(c)) {
                if (kebabCase.length() > 0) {
                    kebabCase.append("-");
                }
                kebabCase.append(Character.toLowerCase(c));
            } else {
                kebabCase.append(c);
            }
        }
        return kebabCase.toString();
    }

    /**
     * 高级加密id
     * @param id
     * @return
     */
    public static String high_encryption_id(Long id) {
        Long newId=id;
        int mmm=10;
        int leftEnd=4;
        int rightEnd=2;
        if (null == newId) {
            newId=0L;
        }
        Long id_plus=newId+mmm;
        String id1 = MD5Util.MD5_16bit(id_plus.toString());
        String left_str = id1.substring(0, leftEnd);
        String right_str = id1.substring(id1.length() - rightEnd );
        String replace_str = id.toString().replaceFirst("7", "e");
        String high_encryption_id=left_str+replace_str+right_str;
        return high_encryption_id;
    }

    /**
     * 高级解密id
     * @param id
     * @return
     */
    public static Long high_decryption_id(String id){
        int mmm=10;
        int leftEnd=4;
        int rightEnd=2;
        if (id.length() < 7) {
            return 0L;
        }
        String left_str = id.substring(0, leftEnd);
        String right_str = id.substring(id.length() - rightEnd);
        int newIdLength=id.length()-leftEnd-rightEnd;
        String id22 = id.substring(leftEnd,(leftEnd)+newIdLength);
        String replace_str = id22.replaceFirst("e", "7");
        if (!StringUtils.isNumeric(replace_str)) {
            return 0L;
        }
        Long id3=Long.valueOf(replace_str)+mmm;
        String id3_md5 = MD5Util.MD5_16bit(id3.toString());
        String left_str_2 = id3_md5.substring(0, leftEnd);
        String right_str_2 = id3_md5.substring(id3_md5.length() - rightEnd);
        if (left_str.equals(left_str_2)&& right_str.equals(right_str_2)) {
            return Long.valueOf(replace_str);
        }else {
            return 0L;
        }
    }


}
