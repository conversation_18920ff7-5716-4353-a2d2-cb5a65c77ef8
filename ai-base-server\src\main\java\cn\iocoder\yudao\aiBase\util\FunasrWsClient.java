//
// Copyright FunASR (https://github.com/alibaba-damo-academy/FunASR). All Rights
// Reserved. MIT License  (https://opensource.org/licenses/MIT)
//
/*
 * // 2022-2023 by <EMAIL>
 */
// java FunasrWsClient
// usage:  FunasrWsClient [-h] [--port PORT] [--host HOST] [--audio_in AUDIO_IN] [--num_threads NUM_THREADS]
//                 [--chunk_size CHUNK_SIZE] [--chunk_interval CHUNK_INTERVAL] [--mode MODE]
package cn.iocoder.yudao.aiBase.util;

import cn.iocoder.yudao.aiBase.dto.asr.ASRParam;
import cn.iocoder.yudao.aiBase.mq.AsrDataMsg;
import cn.iocoder.yudao.aiBase.mq.AsrDataProducer;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.handshake.ServerHandshake;

import java.io.File;
import java.io.FileInputStream;
import java.net.URI;
import java.util.Map;

/**
 * This example demonstrates how to connect to websocket server.
 */
@Slf4j
public class FunasrWsClient extends WebSocketClient {

	private boolean iseof = false;
	public static String wavPath;
	static String mode = "online";
	static String strChunkSize = "5,10,5";
	static int chunkInterval = 10;
	static int sendChunkSize = 1920;
	static String hotwords = "";
	static String fsthotwords = "";

	static String wavName = "wavName";
	static AsrDataProducer producer;

	public class RecWavThread extends Thread {
		private FunasrWsClient funasrClient;

		public RecWavThread(FunasrWsClient funasrClient) {
			this.funasrClient = funasrClient;
		}

		public void run() {
			this.funasrClient.recWav();
		}
	}

	public FunasrWsClient(URI serverUri, Draft draft) {
		super(serverUri, draft);
	}

	public FunasrWsClient(URI serverURI, ASRParam param) {
		super(serverURI);

		FunasrWsClient.strChunkSize = param.getChunkSize();
		FunasrWsClient.chunkInterval = param.getChunkInterval();
		FunasrWsClient.wavPath = param.getAudioIn();
		FunasrWsClient.mode = param.getMode();
		FunasrWsClient.hotwords= param.getHotwords();
		FunasrWsClient.sendChunkSize = param.getSendChunkSize();
		FunasrWsClient.wavName = param.getWavName();
		FunasrWsClient.producer = param.getProducer();
	}

	public FunasrWsClient(URI serverUri, Map<String, String> httpHeaders) {
		super(serverUri, httpHeaders);
	}

	public void getSslContext(String keyfile, String certfile) {
		// TODO
		return;
	}

	// send json at first time
	public void sendJson(
			String mode, String strChunkSize, int chunkInterval, String wavName, boolean isSpeaking, String suffix) {
		try {

			JSONObject obj = new JSONObject();
			obj.put("mode", mode);
			JSONArray array = new JSONArray();
			String[] chunkList = strChunkSize.split(",");
			for (int i = 0; i < chunkList.length; i++) {
				array.add(Integer.valueOf(chunkList[i].trim()));
			}

			obj.put("chunk_size", array);
			obj.put("chunk_interval", Integer.valueOf(chunkInterval));
			obj.put("wav_name", wavName);
			obj.put("hotwords", FunasrWsClient.hotwords);

			if (suffix.equals("wav")) {
				suffix = "pcm";
			}
			obj.put("wav_format", suffix);
			if (isSpeaking) {
				obj.put("is_speaking", true);
			} else {
				obj.put("is_speaking", false);
			}
			log.info("sendJson: " + obj);
			// return;

			send(obj.toString());

			return;
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// send json at end of wav
	public void sendEof() {
		try {
			JSONObject obj = new JSONObject();

			obj.put("is_speaking", false);

			log.info("sendEof: " + obj);
			// return;

			send(obj.toString());
			iseof = true;
			return;
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// function for rec wav file
	public void recWav() {
		String fileName = FunasrWsClient.wavPath;
		String suffix = fileName.split("\\.")[fileName.split("\\.").length - 1];
		sendJson(mode, strChunkSize, chunkInterval, wavName, true, suffix);
		File file = new File(FunasrWsClient.wavPath);

		int chunkSize = sendChunkSize;
		byte[] bytes = new byte[chunkSize];

		int readSize = 0;
		try (FileInputStream fis = new FileInputStream(file)) {
			if (FunasrWsClient.wavPath.endsWith(".wav")) {
				fis.read(bytes, 0, 44); //skip first 44 wav header
			}
			readSize = fis.read(bytes, 0, chunkSize);
			while (readSize > 0) {
				// send when it is chunk size
				if (readSize==chunkSize) {
					send(bytes); // send buf to server

				} else {
					// send when at last or not is chunk size
					byte[] tmpBytes = new byte[readSize];
					for (int i = 0; i < readSize; i++) {
						tmpBytes[i] = bytes[i];
					}
					send(tmpBytes);
				}
				// if not in offline mode, we simulate online stream by sleep
				if (!mode.equals("offline")) {
					Thread.sleep(Integer.valueOf(chunkSize / 32));
				}

				readSize = fis.read(bytes, 0, chunkSize);
			}

			if (!mode.equals("offline")) {
				// if not offline, we send eof and wait for 3 seconds to close
				Thread.sleep(2000);
				sendEof();
				Thread.sleep(3000);
				close();
			} else {
				// if offline, just send eof
				sendEof();
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public void onOpen(ServerHandshake handshakedata) {
		RecWavThread thread = new RecWavThread(this);
		thread.start();
	}

	@Override
	public void onMessage(String message) {
		JSONObject jsonObject = new JSONObject();
		log.info("received: " + message);
		try {
			jsonObject = JSONObject.parseObject(message);
			producer.send(AsrDataMsg.builder().sigHeader(AsrDataMsg.ASR_RECEIVED).payload(jsonObject).build());

			log.info("text: " + jsonObject.get("text"));
			if (jsonObject.containsKey("timestamp")) {
				log.info("timestamp: " + jsonObject.get("timestamp"));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		if (iseof && mode.equals("offline") && !jsonObject.containsKey("is_final")) {
			close();
		}

		if (iseof && mode.equals("offline") && jsonObject.containsKey("is_final") && jsonObject.get("is_final").equals("false")) {
			close();
		}
	}

	@Override
	public void onClose(int code, String reason, boolean remote) {
		log.info("onClose: {}-{}-{}", code, reason, remote);
	}

	@Override
	public void onError(Exception ex) {
		log.info("ex: {}", ex);
		ex.printStackTrace();
		// if the error is fatal then onClose will be called additionally
	}

}
