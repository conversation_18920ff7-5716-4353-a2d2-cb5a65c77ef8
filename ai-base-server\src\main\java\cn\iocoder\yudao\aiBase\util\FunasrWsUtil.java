package cn.iocoder.yudao.aiBase.util;

import cn.iocoder.yudao.aiBase.dto.asr.ASRParam;

import java.net.URI;

public class FunasrWsUtil {
	public static void sendToFunAsr(ASRParam param) {

		class ClientThread implements Runnable {
			ASRParam param; // 添加对 param 的引用

			ClientThread(ASRParam asrParam) {
				this.param = asrParam; // 初始化 param
			}

			public void run() {
				try {
					int RATE = 16000;
					String[] chunkList = param.getChunkSize().split(","); // 使用 param
					int int_chunk_size = 60 * Integer.valueOf(chunkList[1].trim()) / param.getChunkInterval(); // 使用 param
					int CHUNK = Integer.valueOf(RATE / 1000 * int_chunk_size);

					param.setSendChunkSize(CHUNK * 2);

					String wsAddress = "ws://" + param.getHost() + ":" + param.getPort();
					FunasrWsClient c = new FunasrWsClient(new URI(wsAddress), param);

					c.connect();

					System.out.println("wsAddress:" + wsAddress);
				} catch (Exception e) {
					e.printStackTrace();
					System.out.println("e:" + e);
				}
			}
		}

		for (int i = 0; i < param.getNumThreads(); i++) {
			System.out.println("Thread1 is running...");
			Thread t = new Thread(new ClientThread(param));
			t.start();
		}
	}
}
