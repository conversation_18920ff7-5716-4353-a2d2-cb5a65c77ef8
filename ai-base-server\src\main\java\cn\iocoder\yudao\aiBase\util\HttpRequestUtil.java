package cn.iocoder.yudao.aiBase.util;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.ByteArrayInputStream;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class HttpRequestUtil {

    /**
     * 方法map
     */
    public static Map<String, Method> methodMap = Collections.unmodifiableMap(new HashMap<String, Method>(){{
        put("get", Method.GET);
        put("post", Method.POST);
        put("delete", Method.DELETE);
    }});

    /**
     * get请求
     * @param url
     * @param addLog
     * @param headers
     * @return
     */
    public static JSONObject get(String url, Boolean addLog, Map<String, String> headers) {
        return execute("get", url, addLog, null, headers);
    }

    /**
     * post请求
     * @param url
     * @param addLog
     * @param param
     * @param headers
     * @return
     */
    public static JSONObject post(String url, Boolean addLog, Map<String, Object> param, Map<String, String> headers) {
        return execute("post", url, addLog, param, headers);
    }

    /**
     * delete请求
     * @param url
     * @param addLog
     * @param param
     * @param headers
     * @return
     */
    public static JSONObject delete(String url, Boolean addLog, Map<String, Object> param, Map<String, String> headers) {
        return execute("delete", url, addLog, param, headers);
    }

    /**
     * 执行请求
     * @param method
     * @param url
     * @param addLog
     * @param param
     * @param headers
     * @return
     */
    public static JSONObject execute(String method, String url, Boolean addLog, Map<String, Object> param, Map<String, String> headers) {
        HttpRequest http = new HttpRequest(UrlBuilder.of(url));
        http.setMethod(methodMap.get(method));
        http.addHeaders(headers);
        http.timeout(120000); // 设置超时时间为5秒
        if (param != null) {
            http.body(JSON.toJSONString(param));
        }
        HttpResponse response = http.execute();
        JSONObject res = JSON.parseObject(response.body());
        if (addLog) {
            log.error("请求-{}-url: {}, param: {}, headers: {}, response: {}", method, url, JSON.toJSONString(param), JSON.toJSONString(headers), res);
        }
        if (response.isOk()) {
            return res;
        }
        throw new RuntimeException(res.getString("message"));
    }

    /**
     * 添加json header
     * @return
     */
    public static Map<String, String>addHeader() {
        return addHeader(null, "Content-Type", "application/json");
    }

    /**
     * 添加header
     * @param headers
     * @param key
     * @param value
     * @return
     */
    public static Map<String, String>addHeader(Map<String, String> headers, String key, String value) {
        if (headers == null) {
            headers = new HashMap<>();
        }
        headers.put(key, value);
        return  headers;
    }


    public static ResponseEntity<InputStreamResource> fileDownload(String internalUrl) throws Exception {
        // 调用内部接口获取文件字节数据
        byte[] fileBytes = HttpUtil.downloadBytes(internalUrl);

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        // 假设文件名可以通过URL路径获取，或者从内部接口响应中提取
        String fileName = internalUrl.substring(internalUrl.lastIndexOf('/') + 1);
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        // 返回文件流
        return new ResponseEntity<>(new InputStreamResource(new ByteArrayInputStream(fileBytes)), headers, HttpStatus.OK);
    }


}
