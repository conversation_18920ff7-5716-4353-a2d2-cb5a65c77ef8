package cn.iocoder.yudao.aiBase.util;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JwtTokenUtil {

    public static final String jwtSecretString = "TmL3uRUkNIhE29pJqio4ObKt7YHXZBdD";

    /**
     * 获取jwt token
     * @param roles
     * @param userId
     * @return
     */
    public static String hasuraTwt(List<String>roles, String userId, Integer days) {
        Long now = new Date().getTime();
        Long time = days*24*3600*1000L;
        Date expiryDate = new Date(time + now); // 设置过期时间为30天
        Map<String, Object> hasuraClaim = new HashMap<>();
        hasuraClaim.put("x-hasura-allowed-roles", roles);
        hasuraClaim.put("x-hasura-default-role", "anonymous");
        hasuraClaim.put("x-hasura-user-id", userId.toString());
        SecretKey key = Keys.hmacShaKeyFor(jwtSecretString.getBytes(StandardCharsets.UTF_8));
        String compact = Jwts.builder().claim("https://hasura.io/jwt/claims", hasuraClaim)
            .setExpiration(expiryDate)
            .signWith(key, SignatureAlgorithm.HS256)
            .compact();

        return compact;
    }

    /**
     * 检查token
     * @param token
     * @return
     */
    public static Boolean checkToken(String token) {
        Boolean res = false;
        try {
            SecretKey key = Keys.hmacShaKeyFor(jwtSecretString.getBytes(StandardCharsets.UTF_8));
            Jws<Claims> claimsJws = Jwts.parserBuilder().setSigningKey(key).build().parseClaimsJws(token.replace("Bearer", BaseConstant.EMPTY_STR));
            Claims claim = claimsJws.getBody();
            res = new Date().before(claim.getExpiration());
        } catch (Exception e) {
            e.printStackTrace();
            res = false;
        }
        return res;
    }

}
