package cn.iocoder.yudao.aiBase.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5Util {

    /**
     * Determine encrypt algorithm MD5
     */
    private static final String ALGORITHM_MD5 = "MD5";
    /**
     * UTF-8 Encoding
     */
    private static final String UTF_8 = "UTF-8";

    /**
     * 使用md5的算法进行加密返回16位
     */
    public static String MD5_16bit(String str){
        if(str != null){
            return MD5_32bit(str).substring(8, 24);
        }else{
            return null;
        }
    }

    /**
     * 使用md5的算法进行加密返回32位
     */
    public static String MD5_32bit(String str) {
        if (str != null) {
            MessageDigest md;
            try {
                md = MessageDigest.getInstance(ALGORITHM_MD5);
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException("没有md5这个算法！");
            }
            //Use specified byte update digest.
            md.update(str.getBytes());
            byte[] b = md.digest();
            StringBuilder su = new StringBuilder();
            //byte array switch hexadecimal number.
            for (int offset = 0, bLen = b.length; offset < bLen; offset++) {
                String haxHex = Integer.toHexString(b[offset] & 0xFF);
                if (haxHex.length() < 2) {
                    su.append("0");
                }
                su.append(haxHex);
            }
            return su.toString();
        } else {
            return null;
        }
    }

}
