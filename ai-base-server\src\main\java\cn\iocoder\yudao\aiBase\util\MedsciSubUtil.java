package cn.iocoder.yudao.aiBase.util;

import cn.iocoder.yudao.aiBase.dto.alipay.BusinessBuildPayOrderIdRequest;
import cn.iocoder.yudao.aiBase.dto.alipay.Result;
import cn.iocoder.yudao.aiBase.dto.request.ContractDataReqVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MedsciSubUtil {

    public static final String AccessAppId = "ms_xiaozhi_ai";

    public static final String SignScene = "INDUSTRY|TEST";

    public static final String PayChannel = "ALI";

    public static final String PaySource = "MS_XIAOZHI";

    public static final String PayType = "WITHHOLD";


    public static final String Alipay = "alipay";

    public static final String AlipaySubEvent = Alipay + ".subscription";

    /** 主站用户订阅事件 */
    public static final String CreateSubEvent = AlipaySubEvent + ".created";

    /** 主站用户取消订阅事件*/
    public static final String CancelSubEvent = AlipaySubEvent + ".deleted";

    /** 主站用户支付成功事件*/
    public static final String PaidEvent = AlipaySubEvent + ".paid";

    /** 免费 */
    public static final String FreeEvent = "needPay.false";


    /**post 订阅*/
    public static final String CreateSub = "https://class.medsci.cn/api/payment/alipay/contract";

    /**post 取消订阅*/
    public static final String CancelSub = "https://class.medsci.cn/api/payment/alipay/unwind";

    /**post 查询订阅*/
    public static final String QuerySub = "https://class.medsci.cn/api/payment/alipay/contractQuery";

    /**
     * 构建支付链接
     */
    public static final String PayBuild = "https://class.medsci.cn/api/payment/pay/build";

    /**发起支付*/
    public static final String PayOrder = "https://class.medsci.cn/api/payment/pay/order";


    public static String createSub(ContractDataReqVO param, Boolean addLog) {
        try {
            param.setAccessAppId(AccessAppId);
//            param.setSignScene(SignScene);
            String p = JSON.toJSONString(param);
            JSONObject pJson = JSONObject.parseObject(p);
            JSONObject res = HttpRequestUtil.post(CreateSub, addLog, pJson, HttpRequestUtil.addHeader());
            Result result = JSON.parseObject(res.toJSONString(), Result.class);
            if (result.isSuccess()) {
                return result.getData().toString();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String cancelSub(String agreementNo, Boolean addLog) {
        try {
            JSONObject pJson = new JSONObject();
            pJson.put("agreementNo", agreementNo);
            JSONObject res = HttpRequestUtil.post(CancelSub, addLog, pJson, HttpRequestUtil.addHeader());
            Result result = JSON.parseObject(res.toJSONString(), Result.class);
            if (result.isSuccess()) {
                return agreementNo;
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 查询订阅
     * @param agreementNo
     * @param addLog
     * @return
     */
    public static Integer querySub(String agreementNo, Boolean addLog) {
        try {
            JSONObject res = HttpRequestUtil.get(QuerySub+"?agreementNo="+agreementNo, addLog, HttpRequestUtil.addHeader());
            Result result = JSON.parseObject(res.toJSONString(), Result.class);
            if (result.isSuccess()) {
                 return Integer.parseInt(result.getData().toString());
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 构建支付链接
     * @param param
     * @param addLog
     * @return
     */
    public static String payBuild(BusinessBuildPayOrderIdRequest param, String agreementNo, Boolean addLog) {
        try {
            String p = JSON.toJSONString(param);
            JSONObject pJson = JSONObject.parseObject(p);
            JSONObject res = HttpRequestUtil.post(PayBuild, addLog, pJson, HttpRequestUtil.addHeader());
            Result result = JSON.parseObject(res.toJSONString(), Result.class);
            if (result.isSuccess()) {
                p = JSON.toJSONString(result.getData());
                pJson = JSONObject.parseObject(p);
                return payOrder(pJson.getString("payOrderId"), agreementNo, addLog);
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 构建支付链接
     *
     * @param appOrderId
     * @param agreementNo
     * @param addLog
     * @return
     */
    public static String payBuild(String appOrderId, String agreementNo, Boolean addLog) {
        BusinessBuildPayOrderIdRequest param = new BusinessBuildPayOrderIdRequest();
        param.setAppOrderId(appOrderId);
        param.setAccessAppId(MedsciSubUtil.AccessAppId);
        param.setPayChannel(MedsciSubUtil.PayChannel);
        param.setPaySource(MedsciSubUtil.PaySource);
        param.setPayType(MedsciSubUtil.PayType);
        return payBuild(param, agreementNo, addLog);
    }

    /**
     * 发起支付
     * @param payOrderId
     * @param agreementNo
     * @param addLog
     * @return
     */
    public static String payOrder(String payOrderId, String agreementNo, Boolean addLog) {
        try {
            JSONObject pJson = new JSONObject();
            pJson.put("payOrderId", payOrderId);
            pJson.put("accessAppId", AccessAppId);
            pJson.put("agreementNo", agreementNo);
            JSONObject res = HttpRequestUtil.post(PayOrder, addLog, pJson, HttpRequestUtil.addHeader());
            Result result = JSON.parseObject(res.toJSONString(), Result.class);
            if (result.isSuccess()) {
                return payOrderId;
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }




}
