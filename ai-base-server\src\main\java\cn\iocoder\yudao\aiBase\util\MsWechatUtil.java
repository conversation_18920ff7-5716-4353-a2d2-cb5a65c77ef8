package cn.iocoder.yudao.aiBase.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

@Slf4j
public class MsWechatUtil {

//	public static final String SEND_QY_WECHAT = "https://office.workbench.medsci.cn/system/enterprise-lib/common-message-push";
	public static final String SEND_QY_WECHAT = "https://erp-uat.medon.com.cn/system/enterprise-lib/common-message-push";
	public static final String OWNER_EMAIL = "<EMAIL>";

	public static void sendQyWechat(String content) {
		sendQyWechat(null, content);
	}

	/**
	 * 发送企业微信消息
	 * @param erpIds
	 * @param content
	 */
	public static void sendQyWechat(List<String> erpIds, String content) {
		try {
			if (erpIds == null) {
				erpIds = Arrays.asList(OWNER_EMAIL, "JiYu");
			}
			JSONObject param = new JSONObject();
			param.put("erpId", erpIds);
			param.put("content", content);
			param.put("userType", "email");
			param.put("type", "weChat");
			JSONObject resJson = HttpRequestUtil.post(SEND_QY_WECHAT, true, param, HttpRequestUtil.addHeader());
			log.info("resJson: {}" + resJson);
		}	catch (Exception e) {
			e.printStackTrace();
		}
	}


}
