package cn.iocoder.yudao.aiBase.util;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import com.stripe.Stripe;
import com.stripe.model.*;
import com.stripe.model.checkout.Session;
import com.stripe.net.Webhook;
import com.stripe.param.*;
import com.stripe.param.checkout.SessionCreateParams;
import com.stripe.param.checkout.SessionListParams;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class StripeUtil {
	private static final String STRIPE_API_KEY = "***********************************************************************************************************";
	private static final String STRIPE_TEST_KEY = "sk_test_51QWbBYFdQ6MsaevAVBXQlEK3Ly9kaonhyQ0XmOA4l1tgW6BqfAaVA6A9H1YGeyxpvl9CgLGmvSmrvu5no70w3PMj00AGkQpTyI";

	private static final String ENDPOINT_SECRET = "whsec_18d4392e681a36378b85450808d629494aab8208fca3a3f8eca9c1c2bcb0aff0";

	public static final String PAYMENT_PAID = "paid";
	public static final String PAYMENT_UNPAID = "unpaid";

	public static final Map<String, String> currencyMap = new HashMap<>();
	static {
		currencyMap.put("人民币", "cny");
		currencyMap.put("美元", "usd");
	}

	/**
	 * 创建用户
	 *
	 * @param active
	 * @param email
	 * @param realName
	 * @return
	 */
	public static Customer createCustomer(String active, String email, String realName) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		Customer customer = null;
		try {
			customer = getCustomerByEmail(active, email);
			if (customer == null) {
				customer = Customer.create(CustomerCreateParams.builder().setEmail(email).setName(realName).build());
			}
			log.info("customer:{}", customer);
			return customer;
		} catch (Exception e) {
			e.printStackTrace();
			return customer;
		}
	}

	/**
	 * 获取用户
	 * @param customerId
	 * @return
	 */
	public static Customer getCustomer(String active, String customerId) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		try {
			return Customer.retrieve(customerId);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 根据邮箱获取用户
	 * @param email
	 * @return
	 */
	public static Customer getCustomerByEmail(String active, String email) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		try {
			List<Customer>list =  Customer.list(CustomerListParams.builder().setEmail(email).setLimit(1L).build()).getData();
			return list.isEmpty() ? null : list.get(BaseConstant.ZERO);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 创建产品
	 * @param productName
	 * @param description
	 * @return
	 */
	public static Product createProduct(String active, String productName, String description) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		try {
			ProductCreateParams params = ProductCreateParams.builder().setName(productName).setDescription(description).build();
			Product product = Product.create(params);
			log.info("product:{}", product);
			return product;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 获取产品
	 * @param productId
	 * @return
	 */
	public static Product getProduct(String active, String productId) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		try {
			return Product.retrieve(productId);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 更新产品
	 * @param productId
	 * @return
	 */
	public static Product updateProduct(String active, String productId, String productName, String description) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		try {
			Product product = getProduct(active, productId);
			if (product != null) {
				ProductUpdateParams params = ProductUpdateParams.builder().setName(productName).setDescription(description).build();
				return product.update(params);
			}
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 创建价格
	 * @param productId
	 * @param currency
	 * @param unitAmount
	 * @param interval
	 * @param count
	 * @return
	 */
	public static Price createPrice(String active, String productId, String currency, BigDecimal unitAmount, PriceCreateParams.Recurring.Interval interval, Long count) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		try {
			PriceCreateParams params =
				PriceCreateParams.builder()
					.setCurrency(currencyMap.getOrDefault(currency, "usd"))
					.setUnitAmountDecimal(unitAmount)
					.setRecurring(
						PriceCreateParams.Recurring.builder()
							.setIntervalCount(count)
							.setInterval(interval)
							.build()
					)
					.setProduct(productId)
					.build();
			Price price = Price.create(params);
			log.info("price:{}", price);
			return price;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 创建订阅链接，同一个客户只能使用一种货币结算
	 * @param customerId
	 * @param priceId
	 * @param successUrl
	 * @param cancelUrl
	 * @return
	 */
	public static Session createCheckoutSession(String active, String customerId, String priceId, String successUrl, String cancelUrl) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		try {
			SessionCreateParams params = SessionCreateParams.builder()
				.addLineItem(SessionCreateParams.LineItem.builder().setPrice(priceId).setQuantity(1L).build())
				.setMode(SessionCreateParams.Mode.SUBSCRIPTION)
				.setCustomer(customerId)
				.setSuccessUrl(successUrl + "?session_id={CHECKOUT_SESSION_ID}")
				.setCancelUrl(cancelUrl)
				.build();

			Session session = Session.create(params);
			log.info("session:{}", session);
			return session;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 获取CheckoutSession
	 * @param sessionId
	 * @return
	 */
	public static Session getCheckoutSession(String active, String sessionId) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		try {
			Session session = Session.retrieve(sessionId);
			log.info("session:{}", session);
			return session;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 根据subId或piId获取CheckoutSession
	 * @param subId
	 * @param piId
	 * @return
	 */
	public static Session getCheckoutSessionBySubIdOrPiId(String active, String subId, String piId) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		try {
			SessionListParams params = SessionListParams.builder().setSubscription(subId).setPaymentIntent(piId).setLimit(1L).build();
			List<Session> sessions = Session.list(params).getData();
			Session session = sessions.isEmpty() ? null : sessions.get(BaseConstant.ZERO);
			log.info("session:{}", session);
			return session;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	/**
	 * 获取Invoice
	 * @param invoiceId
	 * @return
	 */
	public static Invoice getInvoice(String active, String invoiceId) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		try {
			return Invoice.retrieve(invoiceId);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 获取Event
	 * @param eventId
	 * @return
	 */
	public static Event getEvent(String active, String eventId) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		try {
			return Event.retrieve(eventId);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 获取订阅
	 * @param subId
	 * @return
	 */
	public static Subscription getSubscription(String active, String subId) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		try {
			return Subscription.retrieve(subId);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 取消订阅
	 * @param subId
	 * @return
	 */
	public static String cancelSubscription(String active, String subId) {

		try {
			Subscription subscription = getSubscription(active, subId);
			if (subscription != null) {
				SubscriptionCancelParams params = SubscriptionCancelParams.builder().build();
				subscription.cancel(params);
				return subId;
			}
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 接收事件推送
	 * @param sigHeader
	 * @param payload
	 */
	public static Event webhook(String active, String sigHeader, String payload) {
		Stripe.apiKey = BaseConstant.PROD_STR.equals(active) ? STRIPE_API_KEY : STRIPE_TEST_KEY;
		Event event = null;
		try {
			event = Webhook.constructEvent(payload, sigHeader, ENDPOINT_SECRET);
			return event;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}




}
