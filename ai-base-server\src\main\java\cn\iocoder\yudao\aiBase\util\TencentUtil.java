package cn.iocoder.yudao.aiBase.util;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sts.v20180813.StsClient;
import com.tencentcloudapi.sts.v20180813.models.AssumeRoleRequest;
import com.tencentcloudapi.sts.v20180813.models.AssumeRoleResponse;
import com.tencentcloudapi.sts.v20180813.models.Credentials;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.io.UnsupportedEncodingException;
import java.util.stream.Collectors;

@Slf4j
public class TencentUtil {

	public static final String STS_SECRET_ID = "https://gw.tvs.qq.com";
	public static final String STS_SECRET_KEY = "wss://gw.tvs.qq.com";
	public static final String STS_REGION = "ap-beijing";

	public static Credentials getAssumeRole() {
		// 创建凭证对象
		Credential cred = new Credential(STS_SECRET_ID, STS_SECRET_KEY);

		HttpProfile httpProfile = new HttpProfile();
		httpProfile.setEndpoint("sts.tencentcloudapi.com");

		// 客户端配置
		ClientProfile clientProfile = new ClientProfile();
		clientProfile.setHttpProfile(httpProfile);

		// 初始化 STS 客户端
		StsClient client = new StsClient(cred, STS_REGION, clientProfile);

		// 构造 AssumeRole 请求
		AssumeRoleRequest req = new AssumeRoleRequest();
		req.setRoleArn("qcs::cam::uin/1234567890:role/example-role"); // 替换为你的角色 ARN
		req.setRoleSessionName("daiichi-sankyo"); // 替换为你需要的会话名称
		req.setDurationSeconds(43200L); // 临时凭证有效时间，单位秒，默认1800秒（30分钟）

		try {
			AssumeRoleResponse response = client.AssumeRole(req);

			// 提取临时凭证信息
			Credentials credentials = response.getCredentials();
			log.info("getAssumeRole:{}", credentials);
			return credentials;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}



}
