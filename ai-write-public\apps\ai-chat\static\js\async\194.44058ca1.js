"use strict";(self.webpackChunkdify_chat_web=self.webpackChunkdify_chat_web||[]).push([["194"],{58438:function(t,e,r){r.d(e,{k:()=>f});var a=r(1921),i=r(84015),s=r(71257),n=r(67998),l=r(46471),o=r(84458),d=r(8321),c=r(27796),h=r(11021),g=r(18020),u=r(91631),p=(0,h.Z)(function(t){return(0,g.Z)((0,c.Z)(t,1,u.Z,!0))}),y=r(80935),b=r(65457);class f{constructor(t={}){this._isDirected=!Object.prototype.hasOwnProperty.call(t,"directed")||t.directed,this._isMultigraph=!!Object.prototype.hasOwnProperty.call(t,"multigraph")&&t.multigraph,this._isCompound=!!Object.prototype.hasOwnProperty.call(t,"compound")&&t.compound,this._label=void 0,this._defaultNodeLabelFn=a.Z(void 0),this._defaultEdgeLabelFn=a.Z(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children["\0"]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}isDirected(){return this._isDirected}isMultigraph(){return this._isMultigraph}isCompound(){return this._isCompound}setGraph(t){return this._label=t,this}graph(){return this._label}setDefaultNodeLabel(t){return i.Z(t)||(t=a.Z(t)),this._defaultNodeLabelFn=t,this}nodeCount(){return this._nodeCount}nodes(){return s.Z(this._nodes)}sources(){var t=this;return n.Z(this.nodes(),function(e){return l.Z(t._in[e])})}sinks(){var t=this;return n.Z(this.nodes(),function(e){return l.Z(t._out[e])})}setNodes(t,e){var r=arguments,a=this;return o.Z(t,function(t){r.length>1?a.setNode(t,e):a.setNode(t)}),this}setNode(t,e){return Object.prototype.hasOwnProperty.call(this._nodes,t)?arguments.length>1&&(this._nodes[t]=e):(this._nodes[t]=arguments.length>1?e:this._defaultNodeLabelFn(t),this._isCompound&&(this._parent[t]="\0",this._children[t]={},this._children["\0"][t]=!0),this._in[t]={},this._preds[t]={},this._out[t]={},this._sucs[t]={},++this._nodeCount),this}node(t){return this._nodes[t]}hasNode(t){return Object.prototype.hasOwnProperty.call(this._nodes,t)}removeNode(t){if(Object.prototype.hasOwnProperty.call(this._nodes,t)){var e=t=>this.removeEdge(this._edgeObjs[t]);delete this._nodes[t],this._isCompound&&(this._removeFromParentsChildList(t),delete this._parent[t],o.Z(this.children(t),t=>{this.setParent(t)}),delete this._children[t]),o.Z(s.Z(this._in[t]),e),delete this._in[t],delete this._preds[t],o.Z(s.Z(this._out[t]),e),delete this._out[t],delete this._sucs[t],--this._nodeCount}return this}setParent(t,e){if(!this._isCompound)throw Error("Cannot set parent in a non-compound graph");if(d.Z(e))e="\0";else{e+="";for(var r=e;!d.Z(r);r=this.parent(r))if(r===t)throw Error("Setting "+e+" as parent of "+t+" would create a cycle");this.setNode(e)}return this.setNode(t),this._removeFromParentsChildList(t),this._parent[t]=e,this._children[e][t]=!0,this}_removeFromParentsChildList(t){delete this._children[this._parent[t]][t]}parent(t){if(this._isCompound){var e=this._parent[t];if("\0"!==e)return e}}children(t){if(d.Z(t)&&(t="\0"),this._isCompound){var e=this._children[t];if(e)return s.Z(e)}else if("\0"===t)return this.nodes();else if(this.hasNode(t))return[]}predecessors(t){var e=this._preds[t];if(e)return s.Z(e)}successors(t){var e=this._sucs[t];if(e)return s.Z(e)}neighbors(t){var e=this.predecessors(t);if(e)return p(e,this.successors(t))}isLeaf(t){var e;return 0===(this.isDirected()?this.successors(t):this.neighbors(t)).length}filterNodes(t){var e=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});e.setGraph(this.graph());var r=this;o.Z(this._nodes,function(r,a){t(a)&&e.setNode(a,r)}),o.Z(this._edgeObjs,function(t){e.hasNode(t.v)&&e.hasNode(t.w)&&e.setEdge(t,r.edge(t))});var a={};return this._isCompound&&o.Z(e.nodes(),function(t){e.setParent(t,function t(i){var s=r.parent(i);return void 0===s||e.hasNode(s)?(a[i]=s,s):s in a?a[s]:t(s)}(t))}),e}setDefaultEdgeLabel(t){return i.Z(t)||(t=a.Z(t)),this._defaultEdgeLabelFn=t,this}edgeCount(){return this._edgeCount}edges(){return y.Z(this._edgeObjs)}setPath(t,e){var r=this,a=arguments;return b.Z(t,function(t,i){return a.length>1?r.setEdge(t,i,e):r.setEdge(t,i),i}),this}setEdge(){var t,e,r,a,i=!1,s=arguments[0];"object"==typeof s&&null!==s&&"v"in s?(t=s.v,e=s.w,r=s.name,2==arguments.length&&(a=arguments[1],i=!0)):(t=s,e=arguments[1],r=arguments[3],arguments.length>2&&(a=arguments[2],i=!0)),t=""+t,e=""+e,d.Z(r)||(r=""+r);var n=w(this._isDirected,t,e,r);if(Object.prototype.hasOwnProperty.call(this._edgeLabels,n))return i&&(this._edgeLabels[n]=a),this;if(!d.Z(r)&&!this._isMultigraph)throw Error("Cannot set a named edge when isMultigraph = false");this.setNode(t),this.setNode(e),this._edgeLabels[n]=i?a:this._defaultEdgeLabelFn(t,e,r);var l=function(t,e,r,a){var i=""+e,s=""+r;if(!t&&i>s){var n=i;i=s,s=n}var l={v:i,w:s};return a&&(l.name=a),l}(this._isDirected,t,e,r);return t=l.v,e=l.w,Object.freeze(l),this._edgeObjs[n]=l,x(this._preds[e],t),x(this._sucs[t],e),this._in[e][n]=l,this._out[t][n]=l,this._edgeCount++,this}edge(t,e,r){var a=1==arguments.length?_(this._isDirected,arguments[0]):w(this._isDirected,t,e,r);return this._edgeLabels[a]}hasEdge(t,e,r){var a=1==arguments.length?_(this._isDirected,arguments[0]):w(this._isDirected,t,e,r);return Object.prototype.hasOwnProperty.call(this._edgeLabels,a)}removeEdge(t,e,r){var a=1==arguments.length?_(this._isDirected,arguments[0]):w(this._isDirected,t,e,r),i=this._edgeObjs[a];return i&&(t=i.v,e=i.w,delete this._edgeLabels[a],delete this._edgeObjs[a],m(this._preds[e],t),m(this._sucs[t],e),delete this._in[e][a],delete this._out[t][a],this._edgeCount--),this}inEdges(t,e){var r=this._in[t];if(r){var a=y.Z(r);return e?n.Z(a,function(t){return t.v===e}):a}}outEdges(t,e){var r=this._out[t];if(r){var a=y.Z(r);return e?n.Z(a,function(t){return t.w===e}):a}}nodeEdges(t,e){var r=this.inEdges(t,e);if(r)return r.concat(this.outEdges(t,e))}}function x(t,e){t[e]?t[e]++:t[e]=1}function m(t,e){--t[e]||delete t[e]}function w(t,e,r,a){var i=""+e,s=""+r;if(!t&&i>s){var n=i;i=s,s=n}return i+"\x01"+s+"\x01"+(d.Z(a)?"\0":a)}function _(t,e){return w(t,e.v,e.w,e.name)}f.prototype._nodeCount=0,f.prototype._edgeCount=0},48657:function(t,e,r){r.d(e,{k:()=>a.k});var a=r(58438)},59382:function(t,e,r){r.d(e,{Z:()=>s});var a=r(25021),i=r(83367);let s=(t,e)=>a.Z.lang.round(i.Z.parse(t)[e])},1209:function(t,e,r){r.d(e,{Z:()=>i});var a=r(94735);let i=function(t){return(0,a.Z)(t,4)}},83858:function(t,e,r){r.d(e,{diagram:()=>el});var a=r(12091),i=r(40381),s=r(8128),n=r(27066),l=r(79486),o=r(1209),d=r(59382),c=r(48522),h=r(91007),g=r(48657),u=function(){var t=(0,l.eW)(function(t,e,r,a){for(r=r||{},a=t.length;a--;r[t[a]]=e);return r},"o"),e=[1,7],r=[1,13],a=[1,14],i=[1,15],s=[1,19],n=[1,16],o=[1,17],d=[1,18],c=[8,30],h=[8,21,28,29,30,31,32,40,44,47],g=[1,23],u=[1,24],p=[8,15,16,21,28,29,30,31,32,40,44,47],y=[8,15,16,21,27,28,29,30,31,32,40,44,47],b=[1,49],f={trace:(0,l.eW)(function(){},"trace"),yy:{},symbols_:{error:2,spaceLines:3,SPACELINE:4,NL:5,separator:6,SPACE:7,EOF:8,start:9,BLOCK_DIAGRAM_KEY:10,document:11,stop:12,statement:13,link:14,LINK:15,START_LINK:16,LINK_LABEL:17,STR:18,nodeStatement:19,columnsStatement:20,SPACE_BLOCK:21,blockStatement:22,classDefStatement:23,cssClassStatement:24,styleStatement:25,node:26,SIZE:27,COLUMNS:28,"id-block":29,end:30,block:31,NODE_ID:32,nodeShapeNLabel:33,dirList:34,DIR:35,NODE_DSTART:36,NODE_DEND:37,BLOCK_ARROW_START:38,BLOCK_ARROW_END:39,classDef:40,CLASSDEF_ID:41,CLASSDEF_STYLEOPTS:42,DEFAULT:43,class:44,CLASSENTITY_IDS:45,STYLECLASS:46,style:47,STYLE_ENTITY_IDS:48,STYLE_DEFINITION_DATA:49,$accept:0,$end:1},terminals_:{2:"error",4:"SPACELINE",5:"NL",7:"SPACE",8:"EOF",10:"BLOCK_DIAGRAM_KEY",15:"LINK",16:"START_LINK",17:"LINK_LABEL",18:"STR",21:"SPACE_BLOCK",27:"SIZE",28:"COLUMNS",29:"id-block",30:"end",31:"block",32:"NODE_ID",35:"DIR",36:"NODE_DSTART",37:"NODE_DEND",38:"BLOCK_ARROW_START",39:"BLOCK_ARROW_END",40:"classDef",41:"CLASSDEF_ID",42:"CLASSDEF_STYLEOPTS",43:"DEFAULT",44:"class",45:"CLASSENTITY_IDS",46:"STYLECLASS",47:"style",48:"STYLE_ENTITY_IDS",49:"STYLE_DEFINITION_DATA"},productions_:[0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[34,1],[34,2],[33,3],[33,4],[23,3],[23,3],[24,3],[25,3]],performAction:(0,l.eW)(function(t,e,r,a,i,s,n){var l=s.length-1;switch(i){case 4:a.getLogger().debug("Rule: separator (NL) ");break;case 5:a.getLogger().debug("Rule: separator (Space) ");break;case 6:a.getLogger().debug("Rule: separator (EOF) ");break;case 7:a.getLogger().debug("Rule: hierarchy: ",s[l-1]),a.setHierarchy(s[l-1]);break;case 8:a.getLogger().debug("Stop NL ");break;case 9:a.getLogger().debug("Stop EOF ");break;case 10:a.getLogger().debug("Stop NL2 ");break;case 11:a.getLogger().debug("Stop EOF2 ");break;case 12:a.getLogger().debug("Rule: statement: ",s[l]),"number"==typeof s[l].length?this.$=s[l]:this.$=[s[l]];break;case 13:a.getLogger().debug("Rule: statement #2: ",s[l-1]),this.$=[s[l-1]].concat(s[l]);break;case 14:a.getLogger().debug("Rule: link: ",s[l],t),this.$={edgeTypeStr:s[l],label:""};break;case 15:a.getLogger().debug("Rule: LABEL link: ",s[l-3],s[l-1],s[l]),this.$={edgeTypeStr:s[l],label:s[l-1]};break;case 18:let o=parseInt(s[l]),d=a.generateId();this.$={id:d,type:"space",label:"",width:o,children:[]};break;case 23:a.getLogger().debug("Rule: (nodeStatement link node) ",s[l-2],s[l-1],s[l]," typestr: ",s[l-1].edgeTypeStr);let c=a.edgeStrToEdgeData(s[l-1].edgeTypeStr);this.$=[{id:s[l-2].id,label:s[l-2].label,type:s[l-2].type,directions:s[l-2].directions},{id:s[l-2].id+"-"+s[l].id,start:s[l-2].id,end:s[l].id,label:s[l-1].label,type:"edge",directions:s[l].directions,arrowTypeEnd:c,arrowTypeStart:"arrow_open"},{id:s[l].id,label:s[l].label,type:a.typeStr2Type(s[l].typeStr),directions:s[l].directions}];break;case 24:a.getLogger().debug("Rule: nodeStatement (abc88 node size) ",s[l-1],s[l]),this.$={id:s[l-1].id,label:s[l-1].label,type:a.typeStr2Type(s[l-1].typeStr),directions:s[l-1].directions,widthInColumns:parseInt(s[l],10)};break;case 25:a.getLogger().debug("Rule: nodeStatement (node) ",s[l]),this.$={id:s[l].id,label:s[l].label,type:a.typeStr2Type(s[l].typeStr),directions:s[l].directions,widthInColumns:1};break;case 26:a.getLogger().debug("APA123",this?this:"na"),a.getLogger().debug("COLUMNS: ",s[l]),this.$={type:"column-setting",columns:"auto"===s[l]?-1:parseInt(s[l])};break;case 27:a.getLogger().debug("Rule: id-block statement : ",s[l-2],s[l-1]),a.generateId(),this.$={...s[l-2],type:"composite",children:s[l-1]};break;case 28:a.getLogger().debug("Rule: blockStatement : ",s[l-2],s[l-1],s[l]);let h=a.generateId();this.$={id:h,type:"composite",label:"",children:s[l-1]};break;case 29:a.getLogger().debug("Rule: node (NODE_ID separator): ",s[l]),this.$={id:s[l]};break;case 30:a.getLogger().debug("Rule: node (NODE_ID nodeShapeNLabel separator): ",s[l-1],s[l]),this.$={id:s[l-1],label:s[l].label,typeStr:s[l].typeStr,directions:s[l].directions};break;case 31:a.getLogger().debug("Rule: dirList: ",s[l]),this.$=[s[l]];break;case 32:a.getLogger().debug("Rule: dirList: ",s[l-1],s[l]),this.$=[s[l-1]].concat(s[l]);break;case 33:a.getLogger().debug("Rule: nodeShapeNLabel: ",s[l-2],s[l-1],s[l]),this.$={typeStr:s[l-2]+s[l],label:s[l-1]};break;case 34:a.getLogger().debug("Rule: BLOCK_ARROW nodeShapeNLabel: ",s[l-3],s[l-2]," #3:",s[l-1],s[l]),this.$={typeStr:s[l-3]+s[l],label:s[l-2],directions:s[l-1]};break;case 35:case 36:this.$={type:"classDef",id:s[l-1].trim(),css:s[l].trim()};break;case 37:this.$={type:"applyClass",id:s[l-1].trim(),styleClass:s[l].trim()};break;case 38:this.$={type:"applyStyles",id:s[l-1].trim(),stylesStr:s[l].trim()}}},"anonymous"),table:[{9:1,10:[1,2]},{1:[3]},{11:3,13:4,19:5,20:6,21:e,22:8,23:9,24:10,25:11,26:12,28:r,29:a,31:i,32:s,40:n,44:o,47:d},{8:[1,20]},t(c,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,21:e,28:r,29:a,31:i,32:s,40:n,44:o,47:d}),t(h,[2,16],{14:22,15:g,16:u}),t(h,[2,17]),t(h,[2,18]),t(h,[2,19]),t(h,[2,20]),t(h,[2,21]),t(h,[2,22]),t(p,[2,25],{27:[1,25]}),t(h,[2,26]),{19:26,26:12,32:s},{11:27,13:4,19:5,20:6,21:e,22:8,23:9,24:10,25:11,26:12,28:r,29:a,31:i,32:s,40:n,44:o,47:d},{41:[1,28],43:[1,29]},{45:[1,30]},{48:[1,31]},t(y,[2,29],{33:32,36:[1,33],38:[1,34]}),{1:[2,7]},t(c,[2,13]),{26:35,32:s},{32:[2,14]},{17:[1,36]},t(p,[2,24]),{11:37,13:4,14:22,15:g,16:u,19:5,20:6,21:e,22:8,23:9,24:10,25:11,26:12,28:r,29:a,31:i,32:s,40:n,44:o,47:d},{30:[1,38]},{42:[1,39]},{42:[1,40]},{46:[1,41]},{49:[1,42]},t(y,[2,30]),{18:[1,43]},{18:[1,44]},t(p,[2,23]),{18:[1,45]},{30:[1,46]},t(h,[2,28]),t(h,[2,35]),t(h,[2,36]),t(h,[2,37]),t(h,[2,38]),{37:[1,47]},{34:48,35:b},{15:[1,50]},t(h,[2,27]),t(y,[2,33]),{39:[1,51]},{34:52,35:b,39:[2,31]},{32:[2,15]},t(y,[2,34]),{39:[2,32]}],defaultActions:{20:[2,7],23:[2,14],50:[2,15],52:[2,32]},parseError:(0,l.eW)(function(t,e){if(e.recoverable)this.trace(t);else{var r=Error(t);throw r.hash=e,r}},"parseError"),parse:(0,l.eW)(function(t){var e=this,r=[0],a=[],i=[null],s=[],n=this.table,o="",d=0,c=0,h=0,g=s.slice.call(arguments,1),u=Object.create(this.lexer),p={yy:{}};for(var y in this.yy)Object.prototype.hasOwnProperty.call(this.yy,y)&&(p.yy[y]=this.yy[y]);u.setInput(t,p.yy),p.yy.lexer=u,p.yy.parser=this,void 0===u.yylloc&&(u.yylloc={});var b=u.yylloc;s.push(b);var f=u.options&&u.options.ranges;function x(){var t;return"number"!=typeof(t=a.pop()||u.lex()||1)&&(t instanceof Array&&(t=(a=t).pop()),t=e.symbols_[t]||t),t}"function"==typeof p.yy.parseError?this.parseError=p.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,(0,l.eW)(function(t){r.length=r.length-2*t,i.length=i.length-t,s.length=s.length-t},"popStack"),(0,l.eW)(x,"lex");for(var m,w,_,k,L,S,E,v,W,D={};;){if(_=r[r.length-1],this.defaultActions[_]?k=this.defaultActions[_]:(null==m&&(m=x()),k=n[_]&&n[_][m]),void 0===k||!k.length||!k[0]){var C="";for(S in W=[],n[_])this.terminals_[S]&&S>2&&W.push("'"+this.terminals_[S]+"'");C=u.showPosition?"Parse error on line "+(d+1)+":\n"+u.showPosition()+"\nExpecting "+W.join(", ")+", got '"+(this.terminals_[m]||m)+"'":"Parse error on line "+(d+1)+": Unexpected "+(1==m?"end of input":"'"+(this.terminals_[m]||m)+"'"),this.parseError(C,{text:u.match,token:this.terminals_[m]||m,line:u.yylineno,loc:b,expected:W})}if(k[0]instanceof Array&&k.length>1)throw Error("Parse Error: multiple actions possible at state: "+_+", token: "+m);switch(k[0]){case 1:r.push(m),i.push(u.yytext),s.push(u.yylloc),r.push(k[1]),m=null,w?(m=w,w=null):(c=u.yyleng,o=u.yytext,d=u.yylineno,b=u.yylloc,h>0&&h--);break;case 2:if(E=this.productions_[k[1]][1],D.$=i[i.length-E],D._$={first_line:s[s.length-(E||1)].first_line,last_line:s[s.length-1].last_line,first_column:s[s.length-(E||1)].first_column,last_column:s[s.length-1].last_column},f&&(D._$.range=[s[s.length-(E||1)].range[0],s[s.length-1].range[1]]),void 0!==(L=this.performAction.apply(D,[o,c,d,p.yy,k[1],i,s].concat(g))))return L;E&&(r=r.slice(0,-1*E*2),i=i.slice(0,-1*E),s=s.slice(0,-1*E)),r.push(this.productions_[k[1]][0]),i.push(D.$),s.push(D._$),v=n[r[r.length-2]][r[r.length-1]],r.push(v);break;case 3:return!0}}return!0},"parse")};function x(){this.yy={}}return f.lexer={EOF:1,parseError:(0,l.eW)(function(t,e){if(this.yy.parser)this.yy.parser.parseError(t,e);else throw Error(t)},"parseError"),setInput:(0,l.eW)(function(t,e){return this.yy=e||this.yy||{},this._input=t,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,l.eW)(function(){var t=this._input[0];return this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t,t.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t},"input"),unput:(0,l.eW)(function(t){var e=t.length,r=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e),this.offset-=e;var a=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),r.length-1&&(this.yylineno-=r.length-1);var i=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:r?(r.length===a.length?this.yylloc.first_column:0)+a[a.length-r.length].length-r[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[i[0],i[0]+this.yyleng-e]),this.yyleng=this.yytext.length,this},"unput"),more:(0,l.eW)(function(){return this._more=!0,this},"more"),reject:(0,l.eW)(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:(0,l.eW)(function(t){this.unput(this.match.slice(t))},"less"),pastInput:(0,l.eW)(function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,l.eW)(function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,l.eW)(function(){var t=this.pastInput(),e=Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},"showPosition"),test_match:(0,l.eW)(function(t,e){var r,a,i;if(this.options.backtrack_lexer&&(i={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(i.yylloc.range=this.yylloc.range.slice(0))),(a=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=a.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:a?a[a.length-1].length-a[a.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],r=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),r)return r;if(this._backtrack)for(var s in i)this[s]=i[s];return!1},"test_match"),next:(0,l.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var t,e,r,a,i=this._currentRules(),s=0;s<i.length;s++)if((r=this._input.match(this.rules[i[s]]))&&(!e||r[0].length>e[0].length)){if(e=r,a=s,this.options.backtrack_lexer){if(!1!==(t=this.test_match(r,i[s])))return t;if(!this._backtrack)return!1;e=!1;continue}if(!this.options.flex)break}return e?!1!==(t=this.test_match(e,i[a]))&&t:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,l.eW)(function(){var t=this.next();return t||this.lex()},"lex"),begin:(0,l.eW)(function(t){this.conditionStack.push(t)},"begin"),popState:(0,l.eW)(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,l.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,l.eW)(function(t){return(t=this.conditionStack.length-1-Math.abs(t||0))>=0?this.conditionStack[t]:"INITIAL"},"topState"),pushState:(0,l.eW)(function(t){this.begin(t)},"pushState"),stateStackSize:(0,l.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:(0,l.eW)(function(t,e,r,a){switch(r){case 0:return 10;case 1:return t.getLogger().debug("Found space-block"),31;case 2:return t.getLogger().debug("Found nl-block"),31;case 3:return t.getLogger().debug("Found space-block"),29;case 4:t.getLogger().debug(".",e.yytext);break;case 5:t.getLogger().debug("_",e.yytext);break;case 6:return 5;case 7:return e.yytext=-1,28;case 8:return e.yytext=e.yytext.replace(/columns\s+/,""),t.getLogger().debug("COLUMNS (LEX)",e.yytext),28;case 9:case 77:case 78:case 100:this.pushState("md_string");break;case 10:return"MD_STR";case 11:case 35:case 80:this.popState();break;case 12:this.pushState("string");break;case 13:t.getLogger().debug("LEX: POPPING STR:",e.yytext),this.popState();break;case 14:return t.getLogger().debug("LEX: STR end:",e.yytext),"STR";case 15:return e.yytext=e.yytext.replace(/space\:/,""),t.getLogger().debug("SPACE NUM (LEX)",e.yytext),21;case 16:return e.yytext="1",t.getLogger().debug("COLUMNS (LEX)",e.yytext),21;case 17:return 43;case 18:return"LINKSTYLE";case 19:return"INTERPOLATE";case 20:return this.pushState("CLASSDEF"),40;case 21:return this.popState(),this.pushState("CLASSDEFID"),"DEFAULT_CLASSDEF_ID";case 22:return this.popState(),this.pushState("CLASSDEFID"),41;case 23:return this.popState(),42;case 24:return this.pushState("CLASS"),44;case 25:return this.popState(),this.pushState("CLASS_STYLE"),45;case 26:return this.popState(),46;case 27:return this.pushState("STYLE_STMNT"),47;case 28:return this.popState(),this.pushState("STYLE_DEFINITION"),48;case 29:return this.popState(),49;case 30:return this.pushState("acc_title"),"acc_title";case 31:return this.popState(),"acc_title_value";case 32:return this.pushState("acc_descr"),"acc_descr";case 33:return this.popState(),"acc_descr_value";case 34:this.pushState("acc_descr_multiline");break;case 36:return"acc_descr_multiline_value";case 37:return 30;case 38:case 39:case 41:case 42:case 45:return this.popState(),t.getLogger().debug("Lex: (("),"NODE_DEND";case 40:return this.popState(),t.getLogger().debug("Lex: ))"),"NODE_DEND";case 43:return this.popState(),t.getLogger().debug("Lex: (-"),"NODE_DEND";case 44:return this.popState(),t.getLogger().debug("Lex: -)"),"NODE_DEND";case 46:return this.popState(),t.getLogger().debug("Lex: ]]"),"NODE_DEND";case 47:return this.popState(),t.getLogger().debug("Lex: ("),"NODE_DEND";case 48:return this.popState(),t.getLogger().debug("Lex: ])"),"NODE_DEND";case 49:case 50:return this.popState(),t.getLogger().debug("Lex: /]"),"NODE_DEND";case 51:return this.popState(),t.getLogger().debug("Lex: )]"),"NODE_DEND";case 52:return this.popState(),t.getLogger().debug("Lex: )"),"NODE_DEND";case 53:return this.popState(),t.getLogger().debug("Lex: ]>"),"NODE_DEND";case 54:return this.popState(),t.getLogger().debug("Lex: ]"),"NODE_DEND";case 55:return t.getLogger().debug("Lexa: -)"),this.pushState("NODE"),36;case 56:return t.getLogger().debug("Lexa: (-"),this.pushState("NODE"),36;case 57:return t.getLogger().debug("Lexa: ))"),this.pushState("NODE"),36;case 58:case 60:case 61:case 62:case 65:return t.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;case 59:return t.getLogger().debug("Lex: ((("),this.pushState("NODE"),36;case 63:return t.getLogger().debug("Lexc: >"),this.pushState("NODE"),36;case 64:return t.getLogger().debug("Lexa: (["),this.pushState("NODE"),36;case 66:case 67:case 68:case 69:case 70:case 71:case 72:return this.pushState("NODE"),36;case 73:return t.getLogger().debug("Lexa: ["),this.pushState("NODE"),36;case 74:return this.pushState("BLOCK_ARROW"),t.getLogger().debug("LEX ARR START"),38;case 75:return t.getLogger().debug("Lex: NODE_ID",e.yytext),32;case 76:return t.getLogger().debug("Lex: EOF",e.yytext),8;case 79:return"NODE_DESCR";case 81:t.getLogger().debug("Lex: Starting string"),this.pushState("string");break;case 82:t.getLogger().debug("LEX ARR: Starting string"),this.pushState("string");break;case 83:return t.getLogger().debug("LEX: NODE_DESCR:",e.yytext),"NODE_DESCR";case 84:t.getLogger().debug("LEX POPPING"),this.popState();break;case 85:t.getLogger().debug("Lex: =>BAE"),this.pushState("ARROW_DIR");break;case 86:return e.yytext=e.yytext.replace(/^,\s*/,""),t.getLogger().debug("Lex (right): dir:",e.yytext),"DIR";case 87:return e.yytext=e.yytext.replace(/^,\s*/,""),t.getLogger().debug("Lex (left):",e.yytext),"DIR";case 88:return e.yytext=e.yytext.replace(/^,\s*/,""),t.getLogger().debug("Lex (x):",e.yytext),"DIR";case 89:return e.yytext=e.yytext.replace(/^,\s*/,""),t.getLogger().debug("Lex (y):",e.yytext),"DIR";case 90:return e.yytext=e.yytext.replace(/^,\s*/,""),t.getLogger().debug("Lex (up):",e.yytext),"DIR";case 91:return e.yytext=e.yytext.replace(/^,\s*/,""),t.getLogger().debug("Lex (down):",e.yytext),"DIR";case 92:return e.yytext="]>",t.getLogger().debug("Lex (ARROW_DIR end):",e.yytext),this.popState(),this.popState(),"BLOCK_ARROW_END";case 93:return t.getLogger().debug("Lex: LINK","#"+e.yytext+"#"),15;case 94:case 95:case 96:return t.getLogger().debug("Lex: LINK",e.yytext),15;case 97:case 98:case 99:return t.getLogger().debug("Lex: START_LINK",e.yytext),this.pushState("LLABEL"),16;case 101:return t.getLogger().debug("Lex: Starting string"),this.pushState("string"),"LINK_LABEL";case 102:return this.popState(),t.getLogger().debug("Lex: LINK","#"+e.yytext+"#"),15;case 103:case 104:return this.popState(),t.getLogger().debug("Lex: LINK",e.yytext),15;case 105:return t.getLogger().debug("Lex: COLON",e.yytext),e.yytext=e.yytext.slice(1),27}},"anonymous"),rules:[/^(?:block-beta\b)/,/^(?:block\s+)/,/^(?:block\n+)/,/^(?:block:)/,/^(?:[\s]+)/,/^(?:[\n]+)/,/^(?:((\u000D\u000A)|(\u000A)))/,/^(?:columns\s+auto\b)/,/^(?:columns\s+[\d]+)/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:space[:]\d+)/,/^(?:space\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\s+)/,/^(?:DEFAULT\s+)/,/^(?:\w+\s+)/,/^(?:[^\n]*)/,/^(?:class\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:style\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:end\b\s*)/,/^(?:\(\(\()/,/^(?:\)\)\))/,/^(?:[\)]\))/,/^(?:\}\})/,/^(?:\})/,/^(?:\(-)/,/^(?:-\))/,/^(?:\(\()/,/^(?:\]\])/,/^(?:\()/,/^(?:\]\))/,/^(?:\\\])/,/^(?:\/\])/,/^(?:\)\])/,/^(?:[\)])/,/^(?:\]>)/,/^(?:[\]])/,/^(?:-\))/,/^(?:\(-)/,/^(?:\)\))/,/^(?:\))/,/^(?:\(\(\()/,/^(?:\(\()/,/^(?:\{\{)/,/^(?:\{)/,/^(?:>)/,/^(?:\(\[)/,/^(?:\()/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\[\\)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:\[)/,/^(?:<\[)/,/^(?:[^\(\[\n\-\)\{\}\s\<\>:]+)/,/^(?:$)/,/^(?:["][`])/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:\]>\s*\()/,/^(?:,?\s*right\s*)/,/^(?:,?\s*left\s*)/,/^(?:,?\s*x\s*)/,/^(?:,?\s*y\s*)/,/^(?:,?\s*up\s*)/,/^(?:,?\s*down\s*)/,/^(?:\)\s*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*~~[\~]+\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:["][`])/,/^(?:["])/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?::\d+)/],conditions:{STYLE_DEFINITION:{rules:[29],inclusive:!1},STYLE_STMNT:{rules:[28],inclusive:!1},CLASSDEFID:{rules:[23],inclusive:!1},CLASSDEF:{rules:[21,22],inclusive:!1},CLASS_STYLE:{rules:[26],inclusive:!1},CLASS:{rules:[25],inclusive:!1},LLABEL:{rules:[100,101,102,103,104],inclusive:!1},ARROW_DIR:{rules:[86,87,88,89,90,91,92],inclusive:!1},BLOCK_ARROW:{rules:[77,82,85],inclusive:!1},NODE:{rules:[38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,78,81],inclusive:!1},md_string:{rules:[10,11,79,80],inclusive:!1},space:{rules:[],inclusive:!1},string:{rules:[13,14,83,84],inclusive:!1},acc_descr_multiline:{rules:[35,36],inclusive:!1},acc_descr:{rules:[33],inclusive:!1},acc_title:{rules:[31],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,12,15,16,17,18,19,20,24,27,30,32,34,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,93,94,95,96,97,98,99,105],inclusive:!0}}},(0,l.eW)(x,"Parser"),x.prototype=f,f.Parser=x,new x}();u.parser=u;var p=new Map,y=[],b=new Map,f="color",x="fill",m=(0,l.nV)(),w=new Map,_=(0,l.eW)(t=>l.SY.sanitizeText(t,m),"sanitizeText"),k=(0,l.eW)(function(t,e=""){let r=w.get(t);r||(r={id:t,styles:[],textStyles:[]},w.set(t,r)),null!=e&&e.split(",").forEach(t=>{let e=t.replace(/([^;]*);/,"$1").trim();if(RegExp(f).exec(t)){let t=e.replace(x,"bgFill").replace(f,x);r.textStyles.push(t)}r.styles.push(e)})},"addStyleClass"),L=(0,l.eW)(function(t,e=""){let r=p.get(t);null!=e&&(r.styles=e.split(","))},"addStyle2Node"),S=(0,l.eW)(function(t,e){t.split(",").forEach(function(t){let r=p.get(t);if(void 0===r){let e=t.trim();r={id:e,type:"na",children:[]},p.set(e,r)}r.classes||(r.classes=[]),r.classes.push(e)})},"setCssClass"),E=(0,l.eW)((t,e)=>{let r=t.flat(),a=[];for(let t of r){if(t.label&&(t.label=_(t.label)),"classDef"===t.type){k(t.id,t.css);continue}if("applyClass"===t.type){S(t.id,t?.styleClass??"");continue}if("applyStyles"===t.type){t?.stylesStr&&L(t.id,t?.stylesStr);continue}if("column-setting"===t.type)e.columns=t.columns??-1;else if("edge"===t.type){let e=(b.get(t.id)??0)+1;b.set(t.id,e),t.id=e+"-"+t.id,y.push(t)}else{t.label||("composite"===t.type?t.label="":t.label=t.id);let e=p.get(t.id);if(void 0===e?p.set(t.id,t):("na"!==t.type&&(e.type=t.type),t.label!==t.id&&(e.label=t.label)),t.children&&E(t.children,t),"space"===t.type){let e=t.width??1;for(let r=0;r<e;r++){let e=(0,o.Z)(t);e.id=e.id+"-"+r,p.set(e.id,e),a.push(e)}}else void 0===e&&a.push(t)}}e.children=a},"populateBlockDatabase"),v=[],W={id:"root",type:"composite",children:[],columns:-1},D=(0,l.eW)(()=>{l.cM.debug("Clear called"),(0,l.ZH)(),p=new Map([["root",W={id:"root",type:"composite",children:[],columns:-1}]]),v=[],w=new Map,y=[],b=new Map},"clear");function C(t){switch(l.cM.debug("typeStr2Type",t),t){case"[]":return"square";case"()":return l.cM.debug("we have a round"),"round";case"(())":return"circle";case">]":return"rect_left_inv_arrow";case"{}":return"diamond";case"{{}}":return"hexagon";case"([])":return"stadium";case"[[]]":return"subroutine";case"[()]":return"cylinder";case"((()))":return"doublecircle";case"[//]":return"lean_right";case"[\\\\]":return"lean_left";case"[/\\]":return"trapezoid";case"[\\/]":return"inv_trapezoid";case"<[]>":return"block_arrow";default:return"na"}}function N(t){return(l.cM.debug("typeStr2Type",t),"=="===t)?"thick":"normal"}function $(t){switch(t.trim()){case"--x":return"arrow_cross";case"--o":return"arrow_circle";default:return"arrow_point"}}(0,l.eW)(C,"typeStr2Type"),(0,l.eW)(N,"edgeTypeStr2Type"),(0,l.eW)($,"edgeStrToEdgeData");var M=0,T=(0,l.eW)(()=>(M++,"id-"+Math.random().toString(36).substr(2,12)+"-"+M),"generateId"),O=(0,l.eW)(t=>{W.children=t,E(t,W),v=W.children},"setHierarchy"),I=(0,l.eW)(t=>{let e=p.get(t);return e?e.columns?e.columns:e.children?e.children.length:-1:-1},"getColumns"),B=(0,l.eW)(()=>[...p.values()],"getBlocksFlat"),z=(0,l.eW)(()=>v||[],"getBlocks"),R=(0,l.eW)(()=>y,"getEdges"),A=(0,l.eW)(t=>p.get(t),"getBlock"),P=(0,l.eW)(t=>{p.set(t.id,t)},"setBlock"),Y=(0,l.eW)(()=>console,"getLogger"),Z=(0,l.eW)(function(){return w},"getClasses"),F={getConfig:(0,l.eW)(()=>(0,l.iE)().block,"getConfig"),typeStr2Type:C,edgeTypeStr2Type:N,edgeStrToEdgeData:$,getLogger:Y,getBlocksFlat:B,getBlocks:z,getEdges:R,setHierarchy:O,getBlock:A,setBlock:P,getColumns:I,getClasses:Z,clear:D,generateId:T},j=(0,l.eW)((t,e)=>{let r=d.Z,a=r(t,"r"),i=r(t,"g"),s=r(t,"b");return c.Z(a,i,s,e)},"fade"),X=(0,l.eW)(t=>`.label {
    font-family: ${t.fontFamily};
    color: ${t.nodeTextColor||t.textColor};
  }
  .cluster-label text {
    fill: ${t.titleColor};
  }
  .cluster-label span,p {
    color: ${t.titleColor};
  }



  .label text,span,p {
    fill: ${t.nodeTextColor||t.textColor};
    color: ${t.nodeTextColor||t.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${t.mainBkg};
    stroke: ${t.nodeBorder};
    stroke-width: 1px;
  }
  .flowchart-label text {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }

  .arrowheadPath {
    fill: ${t.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${t.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${t.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${t.edgeLabelBackground};
    rect {
      opacity: 0.5;
      background-color: ${t.edgeLabelBackground};
      fill: ${t.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${j(t.edgeLabelBackground,.5)};
    // background-color:
  }

  .node .cluster {
    // fill: ${j(t.mainBkg,.5)};
    fill: ${j(t.clusterBkg,.5)};
    stroke: ${j(t.clusterBorder,.2)};
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${t.titleColor};
  }

  .cluster span,p {
    color: ${t.titleColor};
  }
  /* .cluster div {
    color: ${t.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${t.fontFamily};
    font-size: 12px;
    background: ${t.tertiaryColor};
    border: 1px solid ${t.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${t.textColor};
  }
`,"getStyles"),H=(0,l.eW)((t,e,r,a)=>{e.forEach(e=>{tt[e](t,r,a)})},"insertMarkers"),U=(0,l.eW)((t,e,r)=>{l.cM.trace("Making markers for ",r),t.append("defs").append("marker").attr("id",r+"_"+e+"-extensionStart").attr("class","marker extension "+e).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),t.append("defs").append("marker").attr("id",r+"_"+e+"-extensionEnd").attr("class","marker extension "+e).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),K=(0,l.eW)((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-compositionStart").attr("class","marker composition "+e).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),t.append("defs").append("marker").attr("id",r+"_"+e+"-compositionEnd").attr("class","marker composition "+e).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),V=(0,l.eW)((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-aggregationStart").attr("class","marker aggregation "+e).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),t.append("defs").append("marker").attr("id",r+"_"+e+"-aggregationEnd").attr("class","marker aggregation "+e).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),q=(0,l.eW)((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-dependencyStart").attr("class","marker dependency "+e).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),t.append("defs").append("marker").attr("id",r+"_"+e+"-dependencyEnd").attr("class","marker dependency "+e).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),G=(0,l.eW)((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-lollipopStart").attr("class","marker lollipop "+e).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),t.append("defs").append("marker").attr("id",r+"_"+e+"-lollipopEnd").attr("class","marker lollipop "+e).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),J=(0,l.eW)((t,e,r)=>{t.append("marker").attr("id",r+"_"+e+"-pointEnd").attr("class","marker "+e).attr("viewBox","0 0 10 10").attr("refX",6).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),t.append("marker").attr("id",r+"_"+e+"-pointStart").attr("class","marker "+e).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),Q=(0,l.eW)((t,e,r)=>{t.append("marker").attr("id",r+"_"+e+"-circleEnd").attr("class","marker "+e).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),t.append("marker").attr("id",r+"_"+e+"-circleStart").attr("class","marker "+e).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),tt={extension:U,composition:K,aggregation:V,dependency:q,lollipop:G,point:J,circle:Q,cross:(0,l.eW)((t,e,r)=>{t.append("marker").attr("id",r+"_"+e+"-crossEnd").attr("class","marker cross "+e).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),t.append("marker").attr("id",r+"_"+e+"-crossStart").attr("class","marker cross "+e).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),barb:(0,l.eW)((t,e,r)=>{t.append("defs").append("marker").attr("id",r+"_"+e+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","strokeWidth").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb")},te=(0,l.nV)()?.block?.padding??8;function tr(t,e){if(0===t||!Number.isInteger(t))throw Error("Columns must be an integer !== 0.");if(e<0||!Number.isInteger(e))throw Error("Position must be a non-negative integer."+e);if(t<0)return{px:e,py:0};if(1===t)return{px:0,py:e};let r=Math.floor(e/t);return{px:e%t,py:r}}(0,l.eW)(tr,"calculateBlockPosition");var ta=(0,l.eW)(t=>{let e=0,r=0;for(let a of t.children){let{width:i,height:s,x:n,y:o}=a.size??{width:0,height:0,x:0,y:0};l.cM.debug("getMaxChildSize abc95 child:",a.id,"width:",i,"height:",s,"x:",n,"y:",o,a.type),"space"!==a.type&&(i>e&&(e=i/(t.widthInColumns??1)),s>r&&(r=s))}return{width:e,height:r}},"getMaxChildSize");function ti(t,e,r=0,a=0){l.cM.debug("setBlockSizes abc95 (start)",t.id,t?.size?.x,"block width =",t?.size,"sieblingWidth",r),t?.size?.width||(t.size={width:r,height:a,x:0,y:0});let i=0,s=0;if(t.children?.length>0){for(let r of t.children)ti(r,e);let n=ta(t);for(let e of(i=n.width,s=n.height,l.cM.debug("setBlockSizes abc95 maxWidth of",t.id,":s children is ",i,s),t.children))e.size&&(l.cM.debug(`abc95 Setting size of children of ${t.id} id=${e.id} ${i} ${s} ${JSON.stringify(e.size)}`),e.size.width=i*(e.widthInColumns??1)+te*((e.widthInColumns??1)-1),e.size.height=s,e.size.x=0,e.size.y=0,l.cM.debug(`abc95 updating size of ${t.id} children child:${e.id} maxWidth:${i} maxHeight:${s}`));for(let r of t.children)ti(r,e,i,s);let o=t.columns??-1,d=0;for(let e of t.children)d+=e.widthInColumns??1;let c=t.children.length;o>0&&o<d&&(c=o);let h=Math.ceil(d/c),g=c*(i+te)+te,u=h*(s+te)+te;if(g<r){l.cM.debug(`Detected to small siebling: abc95 ${t.id} sieblingWidth ${r} sieblingHeight ${a} width ${g}`),g=r,u=a;let e=(r-c*te-te)/c,n=(a-h*te-te)/h;for(let r of(l.cM.debug("Size indata abc88",t.id,"childWidth",e,"maxWidth",i),l.cM.debug("Size indata abc88",t.id,"childHeight",n,"maxHeight",s),l.cM.debug("Size indata abc88 xSize",c,"padding",te),t.children))r.size&&(r.size.width=e,r.size.height=n,r.size.x=0,r.size.y=0)}if(l.cM.debug(`abc95 (finale calc) ${t.id} xSize ${c} ySize ${h} columns ${o}${t.children.length} width=${Math.max(g,t.size?.width||0)}`),g<(t?.size?.width||0)){g=t?.size?.width||0;let e=o>0?Math.min(t.children.length,o):t.children.length;if(e>0){let r=(g-e*te-te)/e;for(let e of(l.cM.debug("abc95 (growing to fit) width",t.id,g,t.size?.width,r),t.children))e.size&&(e.size.width=r)}}t.size={width:g,height:u,x:0,y:0}}l.cM.debug("setBlockSizes abc94 (done)",t.id,t?.size?.x,t?.size?.width,t?.size?.y,t?.size?.height)}function ts(t,e){l.cM.debug(`abc85 layout blocks (=>layoutBlocks) ${t.id} x: ${t?.size?.x} y: ${t?.size?.y} width: ${t?.size?.width}`);let r=t.columns??-1;if(l.cM.debug("layoutBlocks columns abc95",t.id,"=>",r,t),t.children&&t.children.length>0){let a=t?.children[0]?.size?.width??0,i=t.children.length*a+(t.children.length-1)*te;l.cM.debug("widthOfChildren 88",i,"posX");let s=0;l.cM.debug("abc91 block?.size?.x",t.id,t?.size?.x);let n=t?.size?.x?t?.size?.x+(-t?.size?.width/2||0):-te,o=0;for(let a of t.children){if(!a.size)continue;let{width:i,height:d}=a.size,{px:c,py:h}=tr(r,s);if(h!=o&&(o=h,n=t?.size?.x?t?.size?.x+(-t?.size?.width/2||0):-te,l.cM.debug("New row in layout for block",t.id," and child ",a.id,o)),l.cM.debug(`abc89 layout blocks (child) id: ${a.id} Pos: ${s} (px, py) ${c},${h} (${t?.size?.x},${t?.size?.y}) parent: ${t.id} width: ${i}${te}`),t.size){let e=i/2;a.size.x=n+te+e,l.cM.debug(`abc91 layout blocks (calc) px, pyid:${a.id} startingPos=X${n} new startingPosX${a.size.x} ${e} padding=${te} width=${i} halfWidth=${e} => x:${a.size.x} y:${a.size.y} ${a.widthInColumns} (width * (child?.w || 1)) / 2 ${i*(a?.widthInColumns??1)/2}`),n=a.size.x+e,a.size.y=t.size.y-t.size.height/2+h*(d+te)+d/2+te,l.cM.debug(`abc88 layout blocks (calc) px, pyid:${a.id}startingPosX${n}${te}${e}=>x:${a.size.x}y:${a.size.y}${a.widthInColumns}(width * (child?.w || 1)) / 2${i*(a?.widthInColumns??1)/2}`)}a.children&&ts(a,e),s+=a?.widthInColumns??1,l.cM.debug("abc88 columnsPos",a,s)}}l.cM.debug(`layout blocks (<==layoutBlocks) ${t.id} x: ${t?.size?.x} y: ${t?.size?.y} width: ${t?.size?.width}`)}function tn(t,{minX:e,minY:r,maxX:a,maxY:i}={minX:0,minY:0,maxX:0,maxY:0}){if(t.size&&"root"!==t.id){let{x:s,y:n,width:l,height:o}=t.size;s-l/2<e&&(e=s-l/2),n-o/2<r&&(r=n-o/2),s+l/2>a&&(a=s+l/2),n+o/2>i&&(i=n+o/2)}if(t.children)for(let s of t.children)({minX:e,minY:r,maxX:a,maxY:i}=tn(s,{minX:e,minY:r,maxX:a,maxY:i}));return{minX:e,minY:r,maxX:a,maxY:i}}function tl(t){let e=t.getBlock("root");if(!e)return;ti(e,t,0,0),ts(e,t),l.cM.debug("getBlocks",JSON.stringify(e,null,2));let{minX:r,minY:a,maxX:i,maxY:s}=tn(e);return{x:r,y:a,width:i-r,height:s-a}}function to(t,e){e&&t.attr("style",e)}function td(t){let e=(0,h.Ys)(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),r=e.append("xhtml:div"),a=t.label,i=t.isNode?"nodeLabel":"edgeLabel",s=r.append("span");return s.html(a),to(s,t.labelStyle),s.attr("class",i),to(r,t.labelStyle),r.style("display","inline-block"),r.style("white-space","nowrap"),r.attr("xmlns","http://www.w3.org/1999/xhtml"),e.node()}(0,l.eW)(ti,"setBlockSizes"),(0,l.eW)(ts,"layoutBlocks"),(0,l.eW)(tn,"findBounds"),(0,l.eW)(tl,"layout"),(0,l.eW)(to,"applyStyle"),(0,l.eW)(td,"addHtmlLabel");var tc=(0,l.eW)((t,e,r,a)=>{let i=t||"";if("object"==typeof i&&(i=i[0]),(0,l.ku)((0,l.nV)().flowchart.htmlLabels))return i=i.replace(/\\n|\n/g,"<br />"),l.cM.debug("vertexText"+i),td({isNode:a,label:(0,s.EY)((0,n.SH)(i)),labelStyle:e.replace("fill:","color:")});{let t=document.createElementNS("http://www.w3.org/2000/svg","text");t.setAttribute("style",e.replace("color:","fill:"));let a=[];for(let e of"string"==typeof i?i.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(i)?i:[]){let a=document.createElementNS("http://www.w3.org/2000/svg","tspan");a.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),a.setAttribute("dy","1em"),a.setAttribute("x","0"),r?a.setAttribute("class","title-row"):a.setAttribute("class","row"),a.textContent=e.trim(),t.appendChild(a)}return t}},"createLabel"),th=(0,l.eW)((t,e,r,a,i)=>{e.arrowTypeStart&&tu(t,"start",e.arrowTypeStart,r,a,i),e.arrowTypeEnd&&tu(t,"end",e.arrowTypeEnd,r,a,i)},"addEdgeMarkers"),tg={arrow_cross:"cross",arrow_point:"point",arrow_barb:"barb",arrow_circle:"circle",aggregation:"aggregation",extension:"extension",composition:"composition",dependency:"dependency",lollipop:"lollipop"},tu=(0,l.eW)((t,e,r,a,i,s)=>{let n=tg[r];if(!n)return void l.cM.warn(`Unknown arrow type: ${r}`);t.attr(`marker-${e}`,`url(${a}#${i}_${s}-${n}${"start"===e?"Start":"End"})`)},"addEdgeMarker"),tp={},ty={},tb=(0,l.eW)((t,e)=>{let r,a=(0,l.nV)(),i=(0,l.ku)(a.flowchart.htmlLabels),n="markdown"===e.labelType?(0,s.rw)(t,e.label,{style:e.labelStyle,useHtmlLabels:i,addSvgBackground:!0},a):tc(e.label,e.labelStyle),o=t.insert("g").attr("class","edgeLabel"),d=o.insert("g").attr("class","label");d.node().appendChild(n);let c=n.getBBox();if(i){let t=n.children[0],e=(0,h.Ys)(n);c=t.getBoundingClientRect(),e.attr("width",c.width),e.attr("height",c.height)}if(d.attr("transform","translate("+-c.width/2+", "+-c.height/2+")"),tp[e.id]=o,e.width=c.width,e.height=c.height,e.startLabelLeft){let a=tc(e.startLabelLeft,e.labelStyle),i=t.insert("g").attr("class","edgeTerminals"),s=i.insert("g").attr("class","inner");r=s.node().appendChild(a);let n=a.getBBox();s.attr("transform","translate("+-n.width/2+", "+-n.height/2+")"),ty[e.id]||(ty[e.id]={}),ty[e.id].startLeft=i,tf(r,e.startLabelLeft)}if(e.startLabelRight){let a=tc(e.startLabelRight,e.labelStyle),i=t.insert("g").attr("class","edgeTerminals"),s=i.insert("g").attr("class","inner");r=i.node().appendChild(a),s.node().appendChild(a);let n=a.getBBox();s.attr("transform","translate("+-n.width/2+", "+-n.height/2+")"),ty[e.id]||(ty[e.id]={}),ty[e.id].startRight=i,tf(r,e.startLabelRight)}if(e.endLabelLeft){let a=tc(e.endLabelLeft,e.labelStyle),i=t.insert("g").attr("class","edgeTerminals"),s=i.insert("g").attr("class","inner");r=s.node().appendChild(a);let n=a.getBBox();s.attr("transform","translate("+-n.width/2+", "+-n.height/2+")"),i.node().appendChild(a),ty[e.id]||(ty[e.id]={}),ty[e.id].endLeft=i,tf(r,e.endLabelLeft)}if(e.endLabelRight){let a=tc(e.endLabelRight,e.labelStyle),i=t.insert("g").attr("class","edgeTerminals"),s=i.insert("g").attr("class","inner");r=s.node().appendChild(a);let n=a.getBBox();s.attr("transform","translate("+-n.width/2+", "+-n.height/2+")"),i.node().appendChild(a),ty[e.id]||(ty[e.id]={}),ty[e.id].endRight=i,tf(r,e.endLabelRight)}return n},"insertEdgeLabel");function tf(t,e){(0,l.nV)().flowchart.htmlLabels&&t&&(t.style.width=9*e.length+"px",t.style.height="12px")}(0,l.eW)(tf,"setTerminalWidth");var tx=(0,l.eW)((t,e)=>{l.cM.debug("Moving label abc88 ",t.id,t.label,tp[t.id],e);let r=e.updatedPath?e.updatedPath:e.originalPath,a=(0,l.nV)(),{subGraphTitleTotalMargin:s}=(0,i.L)(a);if(t.label){let a=tp[t.id],i=t.x,o=t.y;if(r){let a=n.w8.calcLabelPosition(r);l.cM.debug("Moving label "+t.label+" from (",i,",",o,") to (",a.x,",",a.y,") abc88"),e.updatedPath&&(i=a.x,o=a.y)}a.attr("transform",`translate(${i}, ${o+s/2})`)}if(t.startLabelLeft){let e=ty[t.id].startLeft,a=t.x,i=t.y;if(r){let e=n.w8.calcTerminalLabelPosition(10*!!t.arrowTypeStart,"start_left",r);a=e.x,i=e.y}e.attr("transform",`translate(${a}, ${i})`)}if(t.startLabelRight){let e=ty[t.id].startRight,a=t.x,i=t.y;if(r){let e=n.w8.calcTerminalLabelPosition(10*!!t.arrowTypeStart,"start_right",r);a=e.x,i=e.y}e.attr("transform",`translate(${a}, ${i})`)}if(t.endLabelLeft){let e=ty[t.id].endLeft,a=t.x,i=t.y;if(r){let e=n.w8.calcTerminalLabelPosition(10*!!t.arrowTypeEnd,"end_left",r);a=e.x,i=e.y}e.attr("transform",`translate(${a}, ${i})`)}if(t.endLabelRight){let e=ty[t.id].endRight,a=t.x,i=t.y;if(r){let e=n.w8.calcTerminalLabelPosition(10*!!t.arrowTypeEnd,"end_right",r);a=e.x,i=e.y}e.attr("transform",`translate(${a}, ${i})`)}},"positionEdgeLabel"),tm=(0,l.eW)((t,e)=>{let r=t.x,a=t.y,i=Math.abs(e.x-r),s=Math.abs(e.y-a),n=t.width/2,l=t.height/2;return!!(i>=n)||!!(s>=l)},"outsideNode"),tw=(0,l.eW)((t,e,r)=>{l.cM.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(e)}
  insidePoint : ${JSON.stringify(r)}
  node        : x:${t.x} y:${t.y} w:${t.width} h:${t.height}`);let a=t.x,i=t.y,s=Math.abs(a-r.x),n=t.width/2,o=r.x<e.x?n-s:n+s,d=t.height/2,c=Math.abs(e.y-r.y),h=Math.abs(e.x-r.x);if(Math.abs(i-e.y)*n>Math.abs(a-e.x)*d){let t=r.y<e.y?e.y-d-i:i-d-e.y;o=h*t/c;let a={x:r.x<e.x?r.x+o:r.x-h+o,y:r.y<e.y?r.y+c-t:r.y-c+t};return 0===o&&(a.x=e.x,a.y=e.y),0===h&&(a.x=e.x),0===c&&(a.y=e.y),l.cM.debug(`abc89 topp/bott calc, Q ${c}, q ${t}, R ${h}, r ${o}`,a),a}{let t=c*(o=r.x<e.x?e.x-n-a:a-n-e.x)/h,i=r.x<e.x?r.x+h-o:r.x-h+o,s=r.y<e.y?r.y+t:r.y-t;return l.cM.debug(`sides calc abc89, Q ${c}, q ${t}, R ${h}, r ${o}`,{_x:i,_y:s}),0===o&&(i=e.x,s=e.y),0===h&&(i=e.x),0===c&&(s=e.y),{x:i,y:s}}},"intersection"),t_=(0,l.eW)((t,e)=>{l.cM.debug("abc88 cutPathAtIntersect",t,e);let r=[],a=t[0],i=!1;return t.forEach(t=>{if(tm(e,t)||i)a=t,i||r.push(t);else{let s=tw(e,a,t),n=!1;r.forEach(t=>{n=n||t.x===s.x&&t.y===s.y}),r.some(t=>t.x===s.x&&t.y===s.y)||r.push(s),i=!0}}),r},"cutPathAtIntersect"),tk=(0,l.eW)(function(t,e,r,i,s,n,o){let d,c=r.points;l.cM.debug("abc88 InsertEdge: edge=",r,"e=",e);let g=!1,u=n.node(e.v);var p=n.node(e.w);p?.intersect&&u?.intersect&&((c=c.slice(1,r.points.length-1)).unshift(u.intersect(c[0])),c.push(p.intersect(c[c.length-1]))),r.toCluster&&(l.cM.debug("to cluster abc88",i[r.toCluster]),c=t_(r.points,i[r.toCluster].node),g=!0),r.fromCluster&&(l.cM.debug("from cluster abc88",i[r.fromCluster]),c=t_(c.reverse(),i[r.fromCluster].node).reverse(),g=!0);let y=c.filter(t=>!Number.isNaN(t.y)),b=h.$0Z;r.curve&&("graph"===s||"flowchart"===s)&&(b=r.curve);let{x:f,y:x}=(0,a.o)(r),m=(0,h.jvg)().x(f).y(x).curve(b);switch(r.thickness){case"normal":d="edge-thickness-normal";break;case"thick":case"invisible":d="edge-thickness-thick";break;default:d=""}switch(r.pattern){case"solid":d+=" edge-pattern-solid";break;case"dotted":d+=" edge-pattern-dotted";break;case"dashed":d+=" edge-pattern-dashed"}let w=t.append("path").attr("d",m(y)).attr("id",r.id).attr("class"," "+d+(r.classes?" "+r.classes:"")).attr("style",r.style),_="";((0,l.nV)().flowchart.arrowMarkerAbsolute||(0,l.nV)().state.arrowMarkerAbsolute)&&(_=(_=(_=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search).replace(/\(/g,"\\(")).replace(/\)/g,"\\)")),th(w,r,_,o,s);let k={};return g&&(k.updatedPath=c),k.originalPath=r.points,k},"insertEdge"),tL=(0,l.eW)(t=>{let e=new Set;for(let r of t)switch(r){case"x":e.add("right"),e.add("left");break;case"y":e.add("up"),e.add("down");break;default:e.add(r)}return e},"expandAndDeduplicateDirections"),tS=(0,l.eW)((t,e,r)=>{let a=tL(t),i=e.height+2*r.padding,s=i/2,n=e.width+2*s+r.padding,l=r.padding/2;return a.has("right")&&a.has("left")&&a.has("up")&&a.has("down")?[{x:0,y:0},{x:s,y:0},{x:n/2,y:2*l},{x:n-s,y:0},{x:n,y:0},{x:n,y:-i/3},{x:n+2*l,y:-i/2},{x:n,y:-2*i/3},{x:n,y:-i},{x:n-s,y:-i},{x:n/2,y:-i-2*l},{x:s,y:-i},{x:0,y:-i},{x:0,y:-2*i/3},{x:-2*l,y:-i/2},{x:0,y:-i/3}]:a.has("right")&&a.has("left")&&a.has("up")?[{x:s,y:0},{x:n-s,y:0},{x:n,y:-i/2},{x:n-s,y:-i},{x:s,y:-i},{x:0,y:-i/2}]:a.has("right")&&a.has("left")&&a.has("down")?[{x:0,y:0},{x:s,y:-i},{x:n-s,y:-i},{x:n,y:0}]:a.has("right")&&a.has("up")&&a.has("down")?[{x:0,y:0},{x:n,y:-s},{x:n,y:-i+s},{x:0,y:-i}]:a.has("left")&&a.has("up")&&a.has("down")?[{x:n,y:0},{x:0,y:-s},{x:0,y:-i+s},{x:n,y:-i}]:a.has("right")&&a.has("left")?[{x:s,y:0},{x:s,y:-l},{x:n-s,y:-l},{x:n-s,y:0},{x:n,y:-i/2},{x:n-s,y:-i},{x:n-s,y:-i+l},{x:s,y:-i+l},{x:s,y:-i},{x:0,y:-i/2}]:a.has("up")&&a.has("down")?[{x:n/2,y:0},{x:0,y:-l},{x:s,y:-l},{x:s,y:-i+l},{x:0,y:-i+l},{x:n/2,y:-i},{x:n,y:-i+l},{x:n-s,y:-i+l},{x:n-s,y:-l},{x:n,y:-l}]:a.has("right")&&a.has("up")?[{x:0,y:0},{x:n,y:-s},{x:0,y:-i}]:a.has("right")&&a.has("down")?[{x:0,y:0},{x:n,y:0},{x:0,y:-i}]:a.has("left")&&a.has("up")?[{x:n,y:0},{x:0,y:-s},{x:n,y:-i}]:a.has("left")&&a.has("down")?[{x:n,y:0},{x:0,y:0},{x:n,y:-i}]:a.has("right")?[{x:s,y:-l},{x:s,y:-l},{x:n-s,y:-l},{x:n-s,y:0},{x:n,y:-i/2},{x:n-s,y:-i},{x:n-s,y:-i+l},{x:s,y:-i+l},{x:s,y:-i+l}]:a.has("left")?[{x:s,y:0},{x:s,y:-l},{x:n-s,y:-l},{x:n-s,y:-i+l},{x:s,y:-i+l},{x:s,y:-i},{x:0,y:-i/2}]:a.has("up")?[{x:s,y:-l},{x:s,y:-i+l},{x:0,y:-i+l},{x:n/2,y:-i},{x:n,y:-i+l},{x:n-s,y:-i+l},{x:n-s,y:-l}]:a.has("down")?[{x:n/2,y:0},{x:0,y:-l},{x:s,y:-l},{x:s,y:-i+l},{x:n-s,y:-i+l},{x:n-s,y:-l},{x:n,y:-l}]:[{x:0,y:0}]},"getArrowPoints");function tE(t,e,r,a){var i=t.x,s=t.y,n=i-a.x,l=s-a.y,o=Math.sqrt(e*e*l*l+r*r*n*n),d=Math.abs(e*r*n/o);a.x<i&&(d=-d);var c=Math.abs(e*r*l/o);return a.y<s&&(c=-c),{x:i+d,y:s+c}}function tv(t,e,r){return tE(t,e,e,r)}function tW(t,e,r,a){var i,s,n,l,o,d,c,h,g,u,p,y,b;if(i=e.y-t.y,n=t.x-e.x,o=e.x*t.y-t.x*e.y,g=i*r.x+n*r.y+o,u=i*a.x+n*a.y+o,!(0!==g&&0!==u&&tD(g,u))){if((s=a.y-r.y,l=r.x-a.x,d=a.x*r.y-r.x*a.y,c=s*t.x+l*t.y+d,h=s*e.x+l*e.y+d,!(0!==c&&0!==h&&tD(c,h)))&&0!=(p=i*l-s*n))return y=Math.abs(p/2),{x:(b=n*d-l*o)<0?(b-y)/p:(b+y)/p,y:(b=s*o-i*d)<0?(b-y)/p:(b+y)/p}}}function tD(t,e){return t*e>0}function tC(t,e,r){var a=t.x,i=t.y,s=[],n=Number.POSITIVE_INFINITY,l=Number.POSITIVE_INFINITY;"function"==typeof e.forEach?e.forEach(function(t){n=Math.min(n,t.x),l=Math.min(l,t.y)}):(n=Math.min(n,e.x),l=Math.min(l,e.y));for(var o=a-t.width/2-n,d=i-t.height/2-l,c=0;c<e.length;c++){var h=e[c],g=e[c<e.length-1?c+1:0],u=tW(t,r,{x:o+h.x,y:d+h.y},{x:o+g.x,y:d+g.y});u&&s.push(u)}return s.length?(s.length>1&&s.sort(function(t,e){var a=t.x-r.x,i=t.y-r.y,s=Math.sqrt(a*a+i*i),n=e.x-r.x,l=e.y-r.y,o=Math.sqrt(n*n+l*l);return s<o?-1:+(s!==o)}),s[0]):t}(0,l.eW)(function(t,e){return t.intersect(e)},"intersectNode"),(0,l.eW)(tE,"intersectEllipse"),(0,l.eW)(tv,"intersectCircle"),(0,l.eW)(tW,"intersectLine"),(0,l.eW)(tD,"sameSign"),(0,l.eW)(tC,"intersectPolygon");var tN=(0,l.eW)((t,e)=>{var r,a,i=t.x,s=t.y,n=e.x-i,l=e.y-s,o=t.width/2,d=t.height/2;return Math.abs(l)*o>Math.abs(n)*d?(l<0&&(d=-d),r=0===l?0:d*n/l,a=d):(n<0&&(o=-o),r=o,a=0===n?0:o*l/n),{x:i+r,y:s+a}},"intersectRect"),t$=(0,l.eW)(async(t,e,r,a)=>{let i,o,d=(0,l.nV)(),c=e.useHtmlLabels||(0,l.ku)(d.flowchart.htmlLabels),g=t.insert("g").attr("class",r||"node default").attr("id",e.domId||e.id),u=g.insert("g").attr("class","label").attr("style",e.labelStyle);i=void 0===e.labelText?"":"string"==typeof e.labelText?e.labelText:e.labelText[0];let p=u.node(),y=(o="markdown"===e.labelType?(0,s.rw)(u,(0,l.oO)((0,n.SH)(i),d),{useHtmlLabels:c,width:e.width||d.flowchart.wrappingWidth,classes:"markdown-node-label"},d):p.appendChild(tc((0,l.oO)((0,n.SH)(i),d),e.labelStyle,!1,a))).getBBox(),b=e.padding/2;if((0,l.ku)(d.flowchart.htmlLabels)){let t=o.children[0],e=(0,h.Ys)(o),r=t.getElementsByTagName("img");if(r){let t=""===i.replace(/<img[^>]*>/g,"").trim();await Promise.all([...r].map(e=>new Promise(r=>{function a(){if(e.style.display="flex",e.style.flexDirection="column",t){let t=5*parseInt(d.fontSize?d.fontSize:window.getComputedStyle(document.body).fontSize,10)+"px";e.style.minWidth=t,e.style.maxWidth=t}else e.style.width="100%";r(e)}(0,l.eW)(a,"setupImage"),setTimeout(()=>{e.complete&&a()}),e.addEventListener("error",a),e.addEventListener("load",a)})))}y=t.getBoundingClientRect(),e.attr("width",y.width),e.attr("height",y.height)}return c?u.attr("transform","translate("+-y.width/2+", "+-y.height/2+")"):u.attr("transform","translate(0, "+-y.height/2+")"),e.centerLabel&&u.attr("transform","translate("+-y.width/2+", "+-y.height/2+")"),u.insert("rect",":first-child"),{shapeSvg:g,bbox:y,halfPadding:b,label:u}},"labelHelper"),tM=(0,l.eW)((t,e)=>{let r=e.node().getBBox();t.width=r.width,t.height=r.height},"updateNodeBounds");function tT(t,e,r,a){return t.insert("polygon",":first-child").attr("points",a.map(function(t){return t.x+","+t.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-e/2+","+r/2+")")}(0,l.eW)(tT,"insertPolygonShape");var tO=(0,l.eW)(async(t,e)=>{e.useHtmlLabels||(0,l.nV)().flowchart.htmlLabels||(e.centerLabel=!0);let{shapeSvg:r,bbox:a,halfPadding:i}=await t$(t,e,"node "+e.classes,!0);l.cM.info("Classes = ",e.classes);let s=r.insert("rect",":first-child");return s.attr("rx",e.rx).attr("ry",e.ry).attr("x",-a.width/2-i).attr("y",-a.height/2-i).attr("width",a.width+e.padding).attr("height",a.height+e.padding),tM(e,s),e.intersect=function(t){return tN(e,t)},r},"note"),tI=(0,l.eW)(t=>t?" "+t:"","formatClass"),tB=(0,l.eW)((t,e)=>`${e||"node default"}${tI(t.classes)} ${tI(t.class)}`,"getClassesFromNode"),tz=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a}=await t$(t,e,tB(e,void 0),!0),i=a.width+e.padding+(a.height+e.padding),s=[{x:i/2,y:0},{x:i,y:-i/2},{x:i/2,y:-i},{x:0,y:-i/2}];l.cM.info("Question main (Circle)");let n=tT(r,i,i,s);return n.attr("style",e.style),tM(e,n),e.intersect=function(t){return l.cM.warn("Intersect called"),tC(e,s,t)},r},"question"),tR=(0,l.eW)((t,e)=>{let r=t.insert("g").attr("class","node default").attr("id",e.domId||e.id);return r.insert("polygon",":first-child").attr("points",[{x:0,y:14},{x:14,y:0},{x:0,y:-14},{x:-14,y:0}].map(function(t){return t.x+","+t.y}).join(" ")).attr("class","state-start").attr("r",7).attr("width",28).attr("height",28),e.width=28,e.height=28,e.intersect=function(t){return tv(e,14,t)},r},"choice"),tA=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a}=await t$(t,e,tB(e,void 0),!0),i=a.height+e.padding,s=i/4,n=a.width+2*s+e.padding,l=[{x:s,y:0},{x:n-s,y:0},{x:n,y:-i/2},{x:n-s,y:-i},{x:s,y:-i},{x:0,y:-i/2}],o=tT(r,n,i,l);return o.attr("style",e.style),tM(e,o),e.intersect=function(t){return tC(e,l,t)},r},"hexagon"),tP=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a}=await t$(t,e,void 0,!0),i=a.height+2*e.padding,s=i/2,n=a.width+2*s+e.padding,l=tS(e.directions,a,e),o=tT(r,n,i,l);return o.attr("style",e.style),tM(e,o),e.intersect=function(t){return tC(e,l,t)},r},"block_arrow"),tY=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a}=await t$(t,e,tB(e,void 0),!0),i=a.width+e.padding,s=a.height+e.padding,n=[{x:-s/2,y:0},{x:i,y:0},{x:i,y:-s},{x:-s/2,y:-s},{x:0,y:-s/2}];return tT(r,i,s,n).attr("style",e.style),e.width=i+s,e.height=s,e.intersect=function(t){return tC(e,n,t)},r},"rect_left_inv_arrow"),tZ=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a}=await t$(t,e,tB(e),!0),i=a.width+e.padding,s=a.height+e.padding,n=[{x:-2*s/6,y:0},{x:i-s/6,y:0},{x:i+2*s/6,y:-s},{x:s/6,y:-s}],l=tT(r,i,s,n);return l.attr("style",e.style),tM(e,l),e.intersect=function(t){return tC(e,n,t)},r},"lean_right"),tF=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a}=await t$(t,e,tB(e,void 0),!0),i=a.width+e.padding,s=a.height+e.padding,n=[{x:2*s/6,y:0},{x:i+s/6,y:0},{x:i-2*s/6,y:-s},{x:-s/6,y:-s}],l=tT(r,i,s,n);return l.attr("style",e.style),tM(e,l),e.intersect=function(t){return tC(e,n,t)},r},"lean_left"),tj=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a}=await t$(t,e,tB(e,void 0),!0),i=a.width+e.padding,s=a.height+e.padding,n=[{x:-2*s/6,y:0},{x:i+2*s/6,y:0},{x:i-s/6,y:-s},{x:s/6,y:-s}],l=tT(r,i,s,n);return l.attr("style",e.style),tM(e,l),e.intersect=function(t){return tC(e,n,t)},r},"trapezoid"),tX=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a}=await t$(t,e,tB(e,void 0),!0),i=a.width+e.padding,s=a.height+e.padding,n=[{x:s/6,y:0},{x:i-s/6,y:0},{x:i+2*s/6,y:-s},{x:-2*s/6,y:-s}],l=tT(r,i,s,n);return l.attr("style",e.style),tM(e,l),e.intersect=function(t){return tC(e,n,t)},r},"inv_trapezoid"),tH=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a}=await t$(t,e,tB(e,void 0),!0),i=a.width+e.padding,s=a.height+e.padding,n=[{x:0,y:0},{x:i+s/2,y:0},{x:i,y:-s/2},{x:i+s/2,y:-s},{x:0,y:-s}],l=tT(r,i,s,n);return l.attr("style",e.style),tM(e,l),e.intersect=function(t){return tC(e,n,t)},r},"rect_right_inv_arrow"),tU=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a}=await t$(t,e,tB(e,void 0),!0),i=a.width+e.padding,s=i/2,n=s/(2.5+i/50),l=a.height+n+e.padding,o="M 0,"+n+" a "+s+","+n+" 0,0,0 "+i+" 0 a "+s+","+n+" 0,0,0 "+-i+" 0 l 0,"+l+" a "+s+","+n+" 0,0,0 "+i+" 0 l 0,"+-l,d=r.attr("label-offset-y",n).insert("path",":first-child").attr("style",e.style).attr("d",o).attr("transform","translate("+-i/2+","+-(l/2+n)+")");return tM(e,d),e.intersect=function(t){let r=tN(e,t),a=r.x-e.x;if(0!=s&&(Math.abs(a)<e.width/2||Math.abs(a)==e.width/2&&Math.abs(r.y-e.y)>e.height/2-n)){let i=n*n*(1-a*a/(s*s));0!=i&&(i=Math.sqrt(i)),i=n-i,t.y-e.y>0&&(i=-i),r.y+=i}return r},r},"cylinder"),tK=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a,halfPadding:i}=await t$(t,e,"node "+e.classes+" "+e.class,!0),s=r.insert("rect",":first-child"),n=e.positioned?e.width:a.width+e.padding,o=e.positioned?e.height:a.height+e.padding,d=e.positioned?-n/2:-a.width/2-i,c=e.positioned?-o/2:-a.height/2-i;if(s.attr("class","basic label-container").attr("style",e.style).attr("rx",e.rx).attr("ry",e.ry).attr("x",d).attr("y",c).attr("width",n).attr("height",o),e.props){let t=new Set(Object.keys(e.props));e.props.borders&&(tG(s,e.props.borders,n,o),t.delete("borders")),t.forEach(t=>{l.cM.warn(`Unknown node property ${t}`)})}return tM(e,s),e.intersect=function(t){return tN(e,t)},r},"rect"),tV=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a,halfPadding:i}=await t$(t,e,"node "+e.classes,!0),s=r.insert("rect",":first-child"),n=e.positioned?e.width:a.width+e.padding,o=e.positioned?e.height:a.height+e.padding,d=e.positioned?-n/2:-a.width/2-i,c=e.positioned?-o/2:-a.height/2-i;if(s.attr("class","basic cluster composite label-container").attr("style",e.style).attr("rx",e.rx).attr("ry",e.ry).attr("x",d).attr("y",c).attr("width",n).attr("height",o),e.props){let t=new Set(Object.keys(e.props));e.props.borders&&(tG(s,e.props.borders,n,o),t.delete("borders")),t.forEach(t=>{l.cM.warn(`Unknown node property ${t}`)})}return tM(e,s),e.intersect=function(t){return tN(e,t)},r},"composite"),tq=(0,l.eW)(async(t,e)=>{let{shapeSvg:r}=await t$(t,e,"label",!0);l.cM.trace("Classes = ",e.class);let a=r.insert("rect",":first-child");if(a.attr("width",0).attr("height",0),r.attr("class","label edgeLabel"),e.props){let t=new Set(Object.keys(e.props));e.props.borders&&(tG(a,e.props.borders,0,0),t.delete("borders")),t.forEach(t=>{l.cM.warn(`Unknown node property ${t}`)})}return tM(e,a),e.intersect=function(t){return tN(e,t)},r},"labelRect");function tG(t,e,r,a){let i=[],s=(0,l.eW)(t=>{i.push(t,0)},"addBorder"),n=(0,l.eW)(t=>{i.push(0,t)},"skipBorder");e.includes("t")?(l.cM.debug("add top border"),s(r)):n(r),e.includes("r")?(l.cM.debug("add right border"),s(a)):n(a),e.includes("b")?(l.cM.debug("add bottom border"),s(r)):n(r),e.includes("l")?(l.cM.debug("add left border"),s(a)):n(a),t.attr("stroke-dasharray",i.join(" "))}(0,l.eW)(tG,"applyNodePropertyBorders");var tJ=(0,l.eW)((t,e)=>{let r;r=e.classes?"node "+e.classes:"node default";let a=t.insert("g").attr("class",r).attr("id",e.domId||e.id),i=a.insert("rect",":first-child"),s=a.insert("line"),n=a.insert("g").attr("class","label"),o=e.labelText.flat?e.labelText.flat():e.labelText,d="";d="object"==typeof o?o[0]:o,l.cM.info("Label text abc79",d,o,"object"==typeof o);let c=n.node().appendChild(tc(d,e.labelStyle,!0,!0)),g={width:0,height:0};if((0,l.ku)((0,l.nV)().flowchart.htmlLabels)){let t=c.children[0],e=(0,h.Ys)(c);g=t.getBoundingClientRect(),e.attr("width",g.width),e.attr("height",g.height)}l.cM.info("Text 2",o);let u=o.slice(1,o.length),p=c.getBBox(),y=n.node().appendChild(tc(u.join?u.join("<br/>"):u,e.labelStyle,!0,!0));if((0,l.ku)((0,l.nV)().flowchart.htmlLabels)){let t=y.children[0],e=(0,h.Ys)(y);g=t.getBoundingClientRect(),e.attr("width",g.width),e.attr("height",g.height)}let b=e.padding/2;return(0,h.Ys)(y).attr("transform","translate( "+(g.width>p.width?0:(p.width-g.width)/2)+", "+(p.height+b+5)+")"),(0,h.Ys)(c).attr("transform","translate( "+(g.width<p.width?0:-(p.width-g.width)/2)+", 0)"),g=n.node().getBBox(),n.attr("transform","translate("+-g.width/2+", "+(-g.height/2-b+3)+")"),i.attr("class","outer title-state").attr("x",-g.width/2-b).attr("y",-g.height/2-b).attr("width",g.width+e.padding).attr("height",g.height+e.padding),s.attr("class","divider").attr("x1",-g.width/2-b).attr("x2",g.width/2+b).attr("y1",-g.height/2-b+p.height+b).attr("y2",-g.height/2-b+p.height+b),tM(e,i),e.intersect=function(t){return tN(e,t)},a},"rectWithTitle"),tQ=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a}=await t$(t,e,tB(e,void 0),!0),i=a.height+e.padding,s=a.width+i/4+e.padding,n=r.insert("rect",":first-child").attr("style",e.style).attr("rx",i/2).attr("ry",i/2).attr("x",-s/2).attr("y",-i/2).attr("width",s).attr("height",i);return tM(e,n),e.intersect=function(t){return tN(e,t)},r},"stadium"),t0=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a,halfPadding:i}=await t$(t,e,tB(e,void 0),!0),s=r.insert("circle",":first-child");return s.attr("style",e.style).attr("rx",e.rx).attr("ry",e.ry).attr("r",a.width/2+i).attr("width",a.width+e.padding).attr("height",a.height+e.padding),l.cM.info("Circle main"),tM(e,s),e.intersect=function(t){return l.cM.info("Circle intersect",e,a.width/2+i,t),tv(e,a.width/2+i,t)},r},"circle"),t1=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a,halfPadding:i}=await t$(t,e,tB(e,void 0),!0),s=r.insert("g",":first-child"),n=s.insert("circle"),o=s.insert("circle");return s.attr("class",e.class),n.attr("style",e.style).attr("rx",e.rx).attr("ry",e.ry).attr("r",a.width/2+i+5).attr("width",a.width+e.padding+10).attr("height",a.height+e.padding+10),o.attr("style",e.style).attr("rx",e.rx).attr("ry",e.ry).attr("r",a.width/2+i).attr("width",a.width+e.padding).attr("height",a.height+e.padding),l.cM.info("DoubleCircle main"),tM(e,n),e.intersect=function(t){return l.cM.info("DoubleCircle intersect",e,a.width/2+i+5,t),tv(e,a.width/2+i+5,t)},r},"doublecircle"),t2=(0,l.eW)(async(t,e)=>{let{shapeSvg:r,bbox:a}=await t$(t,e,tB(e,void 0),!0),i=a.width+e.padding,s=a.height+e.padding,n=[{x:0,y:0},{x:i,y:0},{x:i,y:-s},{x:0,y:-s},{x:0,y:0},{x:-8,y:0},{x:i+8,y:0},{x:i+8,y:-s},{x:-8,y:-s},{x:-8,y:0}],l=tT(r,i,s,n);return l.attr("style",e.style),tM(e,l),e.intersect=function(t){return tC(e,n,t)},r},"subroutine"),t3=(0,l.eW)((t,e)=>{let r=t.insert("g").attr("class","node default").attr("id",e.domId||e.id),a=r.insert("circle",":first-child");return a.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),tM(e,a),e.intersect=function(t){return tv(e,7,t)},r},"start"),t4=(0,l.eW)((t,e,r)=>{let a=t.insert("g").attr("class","node default").attr("id",e.domId||e.id),i=70,s=10;return"LR"===r&&(i=10,s=70),tM(e,a.append("rect").attr("x",-1*i/2).attr("y",-1*s/2).attr("width",i).attr("height",s).attr("class","fork-join")),e.height=e.height+e.padding/2,e.width=e.width+e.padding/2,e.intersect=function(t){return tN(e,t)},a},"forkJoin"),t8={rhombus:tz,composite:tV,question:tz,rect:tK,labelRect:tq,rectWithTitle:tJ,choice:tR,circle:t0,doublecircle:t1,stadium:tQ,hexagon:tA,block_arrow:tP,rect_left_inv_arrow:tY,lean_right:tZ,lean_left:tF,trapezoid:tj,inv_trapezoid:tX,rect_right_inv_arrow:tH,cylinder:tU,start:t3,end:(0,l.eW)((t,e)=>{let r=t.insert("g").attr("class","node default").attr("id",e.domId||e.id),a=r.insert("circle",":first-child"),i=r.insert("circle",":first-child");return i.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),a.attr("class","state-end").attr("r",5).attr("width",10).attr("height",10),tM(e,i),e.intersect=function(t){return tv(e,7,t)},r},"end"),note:tO,subroutine:t2,fork:t4,join:t4,class_box:(0,l.eW)((t,e)=>{let r,a=e.padding/2;r=e.classes?"node "+e.classes:"node default";let i=t.insert("g").attr("class",r).attr("id",e.domId||e.id),s=i.insert("rect",":first-child"),n=i.insert("line"),o=i.insert("line"),d=0,c=4,g=i.insert("g").attr("class","label"),u=0,p=e.classData.annotations?.[0],y=e.classData.annotations[0]?"\xab"+e.classData.annotations[0]+"\xbb":"",b=g.node().appendChild(tc(y,e.labelStyle,!0,!0)),f=b.getBBox();if((0,l.ku)((0,l.nV)().flowchart.htmlLabels)){let t=b.children[0],e=(0,h.Ys)(b);f=t.getBoundingClientRect(),e.attr("width",f.width),e.attr("height",f.height)}e.classData.annotations[0]&&(c+=f.height+4,d+=f.width);let x=e.classData.label;void 0!==e.classData.type&&""!==e.classData.type&&((0,l.nV)().flowchart.htmlLabels?x+="&lt;"+e.classData.type+"&gt;":x+="<"+e.classData.type+">");let m=g.node().appendChild(tc(x,e.labelStyle,!0,!0));(0,h.Ys)(m).attr("class","classTitle");let w=m.getBBox();if((0,l.ku)((0,l.nV)().flowchart.htmlLabels)){let t=m.children[0],e=(0,h.Ys)(m);w=t.getBoundingClientRect(),e.attr("width",w.width),e.attr("height",w.height)}c+=w.height+4,w.width>d&&(d=w.width);let _=[];e.classData.members.forEach(t=>{let r=t.getDisplayDetails(),a=r.displayText;(0,l.nV)().flowchart.htmlLabels&&(a=a.replace(/</g,"&lt;").replace(/>/g,"&gt;"));let i=g.node().appendChild(tc(a,r.cssStyle?r.cssStyle:e.labelStyle,!0,!0)),s=i.getBBox();if((0,l.ku)((0,l.nV)().flowchart.htmlLabels)){let t=i.children[0],e=(0,h.Ys)(i);s=t.getBoundingClientRect(),e.attr("width",s.width),e.attr("height",s.height)}s.width>d&&(d=s.width),c+=s.height+4,_.push(i)}),c+=8;let k=[];if(e.classData.methods.forEach(t=>{let r=t.getDisplayDetails(),a=r.displayText;(0,l.nV)().flowchart.htmlLabels&&(a=a.replace(/</g,"&lt;").replace(/>/g,"&gt;"));let i=g.node().appendChild(tc(a,r.cssStyle?r.cssStyle:e.labelStyle,!0,!0)),s=i.getBBox();if((0,l.ku)((0,l.nV)().flowchart.htmlLabels)){let t=i.children[0],e=(0,h.Ys)(i);s=t.getBoundingClientRect(),e.attr("width",s.width),e.attr("height",s.height)}s.width>d&&(d=s.width),c+=s.height+4,k.push(i)}),c+=8,p){let t=(d-f.width)/2;(0,h.Ys)(b).attr("transform","translate( "+(-1*d/2+t)+", "+-1*c/2+")"),u=f.height+4}let L=(d-w.width)/2;return(0,h.Ys)(m).attr("transform","translate( "+(-1*d/2+L)+", "+(-1*c/2+u)+")"),u+=w.height+4,n.attr("class","divider").attr("x1",-d/2-a).attr("x2",d/2+a).attr("y1",-c/2-a+8+u).attr("y2",-c/2-a+8+u),u+=8,_.forEach(t=>{(0,h.Ys)(t).attr("transform","translate( "+-d/2+", "+(-1*c/2+u+4)+")");let e=t?.getBBox();u+=(e?.height??0)+4}),u+=8,o.attr("class","divider").attr("x1",-d/2-a).attr("x2",d/2+a).attr("y1",-c/2-a+8+u).attr("y2",-c/2-a+8+u),u+=8,k.forEach(t=>{(0,h.Ys)(t).attr("transform","translate( "+-d/2+", "+(-1*c/2+u)+")");let e=t?.getBBox();u+=(e?.height??0)+4}),s.attr("style",e.style).attr("class","outer title-state").attr("x",-d/2-a).attr("y",-(c/2)-a).attr("width",d+e.padding).attr("height",c+e.padding),tM(e,s),e.intersect=function(t){return tN(e,t)},i},"class_box")},t5={},t9=(0,l.eW)(async(t,e,r)=>{let a,i;if(e.link){let s;"sandbox"===(0,l.nV)().securityLevel?s="_top":e.linkTarget&&(s=e.linkTarget||"_blank"),a=t.insert("svg:a").attr("xlink:href",e.link).attr("target",s),i=await t8[e.shape](a,e,r)}else a=i=await t8[e.shape](t,e,r);return e.tooltip&&i.attr("title",e.tooltip),e.class&&i.attr("class","node default "+e.class),t5[e.id]=a,e.haveCallback&&t5[e.id].attr("class",t5[e.id].attr("class")+" clickable"),a},"insertNode"),t7=(0,l.eW)(t=>{let e=t5[t.id];l.cM.trace("Transforming node",t.diff,t,"translate("+(t.x-t.width/2-5)+", "+t.width/2+")");let r=t.diff||0;return t.clusterNode?e.attr("transform","translate("+(t.x+r-t.width/2)+", "+(t.y-t.height/2-8)+")"):e.attr("transform","translate("+t.x+", "+t.y+")"),r},"positionNode");function t6(t,e,r=!1){let a,i="default";(t?.classes?.length||0)>0&&(i=(t?.classes??[]).join(" ")),i+=" flowchart-label";let s=0,o="";switch(t.type){case"round":s=5,o="rect";break;case"composite":s=0,o="composite",a=0;break;case"square":case"group":default:o="rect";break;case"diamond":o="question";break;case"hexagon":o="hexagon";break;case"block_arrow":o="block_arrow";break;case"odd":case"rect_left_inv_arrow":o="rect_left_inv_arrow";break;case"lean_right":o="lean_right";break;case"lean_left":o="lean_left";break;case"trapezoid":o="trapezoid";break;case"inv_trapezoid":o="inv_trapezoid";break;case"circle":o="circle";break;case"ellipse":o="ellipse";break;case"stadium":o="stadium";break;case"subroutine":o="subroutine";break;case"cylinder":o="cylinder";break;case"doublecircle":o="doublecircle"}let d=(0,n.be)(t?.styles??[]),c=t.label,h=t.size??{width:0,height:0,x:0,y:0};return{labelStyle:d.labelStyle,shape:o,labelText:c,rx:s,ry:s,class:i,style:d.style,id:t.id,directions:t.directions,width:h.width,height:h.height,x:h.x,y:h.y,positioned:r,intersect:void 0,type:t.type,padding:a??(0,l.iE)()?.block?.padding??0}}async function et(t,e,r){let a=t6(e,r,!1);if("group"===a.type)return;let i=(0,l.iE)(),s=await t9(t,a,{config:i}),n=s.node().getBBox(),o=r.getBlock(a.id);o.size={width:n.width,height:n.height,x:0,y:0,node:s},r.setBlock(o),s.remove()}async function ee(t,e,r){let a=t6(e,r,!0);if("space"!==r.getBlock(a.id).type){let r=(0,l.iE)();await t9(t,a,{config:r}),e.intersect=a?.intersect,t7(a)}}async function er(t,e,r,a){for(let i of e)await a(t,i,r),i.children&&await er(t,i.children,r,a)}async function ea(t,e,r){await er(t,e,r,et)}async function ei(t,e,r){await er(t,e,r,ee)}async function es(t,e,r,a,i){let s=new g.k({multigraph:!0,compound:!0});for(let t of(s.setGraph({rankdir:"TB",nodesep:10,ranksep:10,marginx:8,marginy:8}),r))t.size&&s.setNode(t.id,{width:t.size.width,height:t.size.height,intersect:t.intersect});for(let r of e)if(r.start&&r.end){let e=a.getBlock(r.start),n=a.getBlock(r.end);if(e?.size&&n?.size){let a=e.size,l=n.size,o=[{x:a.x,y:a.y},{x:a.x+(l.x-a.x)/2,y:a.y+(l.y-a.y)/2},{x:l.x,y:l.y}];tk(t,{v:r.start,w:r.end,name:r.id},{...r,arrowTypeEnd:r.arrowTypeEnd,arrowTypeStart:r.arrowTypeStart,points:o,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"},void 0,"block",s,i),r.label&&(await tb(t,{...r,label:r.label,labelStyle:"stroke: #333; stroke-width: 1.5px;fill:none;",arrowTypeEnd:r.arrowTypeEnd,arrowTypeStart:r.arrowTypeStart,points:o,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"}),tx({...r,x:o[1].x,y:o[1].y},{originalPath:o}))}}}(0,l.eW)(t6,"getNodeFromBlock"),(0,l.eW)(et,"calculateBlockSize"),(0,l.eW)(ee,"insertBlockPositioned"),(0,l.eW)(er,"performOperations"),(0,l.eW)(ea,"calculateBlockSizes"),(0,l.eW)(ei,"insertBlocks"),(0,l.eW)(es,"insertEdges");var en=(0,l.eW)(function(t,e){return e.db.getClasses()},"getClasses"),el={parser:u,db:F,renderer:{draw:(0,l.eW)(async function(t,e,r,a){let i,{securityLevel:s,block:n}=(0,l.iE)(),o=a.db;"sandbox"===s&&(i=(0,h.Ys)("#i"+e));let d="sandbox"===s?(0,h.Ys)(i.nodes()[0].contentDocument.body):(0,h.Ys)("body"),c="sandbox"===s?d.select(`[id="${e}"]`):(0,h.Ys)(`[id="${e}"]`);H(c,["point","circle","cross"],a.type,e);let g=o.getBlocks(),u=o.getBlocksFlat(),p=o.getEdges(),y=c.insert("g").attr("class","block");await ea(y,g,o);let b=tl(o);if(await ei(y,g,o),await es(y,p,u,o,e),b){let t=Math.max(1,Math.round(.125*(b.width/b.height))),e=b.height+t+10,r=b.width+10,{useMaxWidth:a}=n;(0,l.v2)(c,e,r,!!a),l.cM.debug("Here Bounds",b,b),c.attr("viewBox",`${b.x-5} ${b.y-5} ${b.width+10} ${b.height+10}`)}},"draw"),getClasses:en},styles:X}}}]);