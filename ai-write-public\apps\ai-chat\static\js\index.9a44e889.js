/*! For license information please see index.9a44e889.js.LICENSE.txt */
(()=>{"use strict";var e={58779:function(e,t,n){let r,a,i,o,s;var l,c,u,d,p,m,f,h,g,v,y=n(52676),x=n(38751),b=n(75271),w=n.t(b,2);let j={mode:"multiApp",user:"",appService:{},enableSetting:!0},S={mode:"singleApp",user:"",appConfig:{requestConfig:{apiBase:"",apiKey:""},answerForm:{enabled:!1,feedbackText:""}}},k=b.createContext(j),C=k.Provider,N=()=>{let[e,t]=(0,b.useState)({}),n=(0,b.useContext)(k),{mode:r}=n;return{currentAppConfig:e,setCurrentAppConfig:t,..."multiApp"===r?j:S,...n}};class E{}class T{}var _=n(20274);let A=e=>!!e&&e.startsWith("temp"),P={sm:0,md:768,lg:1024,xl:1280,"2xl":1536},M=()=>{let{sm:e,md:t}=(0,_.Z)();return!!e&&!t},I="DIFY_CHAT__DIFY_VERSION",O={get version(){return localStorage.getItem(I)||""},set version(version){localStorage.setItem(I,version)}};var L=n(72422),R=n(96038);let $={".medsci.cn":".medsci.cn",".medon.com.cn":".medon.com.cn",localhost:"localhost"},F={set:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{expires:r=365,domain:a,...i}=n,o=a;if(!o){let e=Object.keys($).find(e=>location.origin.includes(e));e&&(o=$[e])}let s={expires:r,...o?{domain:o}:{},...i};R.Z.set(e,t,s)},get:e=>{let t=R.Z.get(e);try{return t||null}catch{return t}}};var z=n(75727),D=n(40833);class H{async baseRequest(e,t){return await fetch(`${this.options.baseURL}${e}`,{...t,headers:{...t.headers,"Content-Type":"application/json",Accept:" application/json"}})}async jsonRequest(e,t){return(await this.baseRequest(e,{...t,headers:{...t.headers,"Content-Type":"application/json"}})).json()}async get(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t&&Object.keys(t).length>0?`?${new URLSearchParams(t).toString()}`:"";return await this.jsonRequest(`${e}${r}`,{method:"GET",headers:n})}async post(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"POST",body:JSON.stringify(t),headers:n})}async put(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"PUT",body:JSON.stringify(t),headers:n})}async delete(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.baseRequest(e,{method:"DELETE",body:JSON.stringify(t),headers:{...n,"Content-Type":"application/json"}})}constructor(e){(0,D._)(this,"options",void 0),this.options=e}}var Z=n(94234),B=n(21256);let U=window.location.hostname,K=new H({baseURL:"ai.medsci.cn"===U?"https://ai.medsci.cn/dev-api/ai-base":"ai.medon.com.cn"===U?"https://ai.medon.com.cn/dev-api/ai-base":"/ai-base"}),W=()=>B.Z.get("ai_apps_lang")?B.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,q=e=>{if(401===e.code){B.Z.remove("userInfo",{domain:".medon.com.cn"}),B.Z.remove("userInfo",{domain:".medsci.cn"}),B.Z.remove("userInfo",{domain:"localhost"}),B.Z.remove("yudaoToken",{domain:"ai.medon.com.cn"}),B.Z.remove("yudaoToken",{domain:"ai.medsci.cn"}),B.Z.remove("yudaoToken",{domain:".medon.com.cn"}),B.Z.remove("yudaoToken",{domain:".medsci.cn"}),B.Z.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("conversation"),localStorage.removeItem("hasuraToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid");let i=W();if(i&&"zh-CN"!=i){var t,n,r,a;window.top.location.href=(null==(t=location)?void 0:t.origin.includes("medon.com.cn"))||(null==(n=location)?void 0:n.origin.includes("medsci.cn"))?`${null==(r=location)?void 0:r.origin}${i?"/"+i:""}/login`:`${null==(a=location)?void 0:a.origin}${i?"/"+i:""}/login`}else window.addLoginDom();return e}return 0!==e.code?(Z.ZP.open({type:"error",content:e.msg}),e):0===e.code?e:void 0},G=class extends T{async createSubscription(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return q(await K.post("/appUser/createSubscription",e,t))}async getAiWriteToken(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return q(await K.post("/index/getAiWriteToken",e,t))}async getSubOrder(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return q(await K.get("/index/getSubOrder",e,t))}async createAliSub(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return q(await K.get("/index/createAliSub",e,t))}async freeLimit(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return q(await K.get("/index/free-limit",e,t))}async getPackageByKey(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return q(await K.get("/index/getPackageByKey",e,t))}async cancelSubscription(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return q(await K.post("/appUser/cancelSubscription?appUuid=",{},e))}async getAppList(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return q(await K.post("/index/getAppList",e,t))}async getAppByConfigKey(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return q(await K.post("/index/getAppByConfigKey",e,t))}async bindAppUser(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return q(await K.post(`/appUser/bindAppUser?appUuid=${e.appUuid}&appNameEn=${e.appNameEn}`,{},t))}async qaList(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return q(await K.post("/index/qa-list",e,t))}};var V=n(50742),X=n(91107),J=n(91904);n.p;let Y={".medsci.cn":".medsci.cn",".medon.com.cn":".medon.com.cn",localhost:"localhost"},Q={set:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{expires:r=365,domain:a,...i}=n,o=a;if(!o){let e=Object.keys(Y).find(e=>location.origin.includes(e));e&&(o=Y[e])}let s={expires:r,...o?{domain:o}:{},...i};B.Z.set(e,t,s)},get:e=>{let t=B.Z.get(e);try{return t||null}catch{return t}},remove:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{domain:n,...r}=t;B.Z.remove(e,{...r,domain:n||Y[window.location.host],path:"/"})},removeInit:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{domain:n,...r}=t;B.Z.remove(e,{domain:void 0})}},ee=()=>Q.get("ai_apps_lang")?Q.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,et=e=>{let{hideGithubIcon:t}=e,n=(0,z.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),r=n?n[1]:"",a=location.origin.includes("medsci.cn")?"https://ai.medsci.cn/"+ee():location.origin.includes("medon.com.cn")?"https://ai.medon.com.cn/"+ee():"http://localhost:3000/"+ee(),i=r.includes("novax")||r.includes("elavax")?r.includes("novax")?location.origin+"/novax":r.includes("elavax")?location.origin+"/elavax":"":a;return(0,y.jsxs)("div",{className:"flex h-16 items-center justify-start py-0 box-border",children:[(0,y.jsx)("div",{className:"h-full flex items-center flex-1 overflow-hidden",children:(0,y.jsx)("a",{href:i,target:"__top",children:(0,y.jsx)("img",{className:`h-6 inline-block hover:cursor-pointer ${r.includes("novax")||r.includes("elavax")?"!h-10":""}`,src:r.includes("novax")||r.includes("elavax")?r.includes("novax")?"https://img.medsci.cn/202506/a75d550504434d22aacaefcc951bc9ec-PBB8bYHyNwIQ.png":r.includes("elavax")?"https://img.medsci.cn/202506/06e46058edd34eea9ae96580a46bf327-FP4DdQMkcnma.png":"":"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",draggable:!1,alt:"logo"})})}),!t&&(0,y.jsx)(J.ZP,{type:"link",href:"https://github.com/lexmin0412/dify-chat",target:"_blank",className:"px-0",children:(0,y.jsx)(X.Z,{className:"text-lg cursor-pointer text-default"})})]})};var en=n(3692),er=n(86910),ea=n(48415),ei=n(96949),eo=n(16615),es=n(30967),el=n.t(es,2),ec={"../../node_modules/.pnpm/@ant-design+cssinjs@1.23.0_react-dom@18.3.1_react@18.3.1/node_modules/@ant-design/cssinjs/es/hooks/useHMR.js":function(e,t,n){n.d(t,{Z:()=>r}),e=n.hmd(e);let r=function(){return!1}},"../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/cjs/react-is.development.js":function(e,t){},"../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/cjs/react-is.production.min.js":function(e,t){var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),c=Symbol.for("react.server_context"),u=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),m=Symbol.for("react.memo"),f=Symbol.for("react.lazy");Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),t.ForwardRef=u,t.isMemo=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case a:case o:case i:case d:case p:return e;default:switch(e=e&&e.$$typeof){case c:case l:case u:case f:case m:case s:return e;default:return t}}case r:return t}}}(e)===m}},"../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/index.js":function(e,t,n){e.exports=n("../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/cjs/react-is.production.min.js")},"../../node_modules/.pnpm/classnames@2.5.1/node_modules/classnames/index.js":function(e){var t={}.hasOwnProperty;function n(){for(var e="",a=0;a<arguments.length;a++){var i=arguments[a];i&&(e=r(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return n.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var a="";for(var i in e)t.call(e,i)&&e[i]&&(a=r(a,i));return a}(i)))}return e}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(n.default=n,e.exports=n):"function"==typeof define&&"object"==typeof define.amd&&define.amd?define("classnames",[],function(){return n}):window.classNames=n}},eu={};function ed(e){var t=eu[e];if(void 0!==t)return t.exports;var n=eu[e]={id:e,loaded:!1,exports:{}};return ec[e](n,n.exports,ed),n.loaded=!0,n.exports}function ep(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function em(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function ef(e,t){if(e){if("string"==typeof e)return ep(e,t);var n=({}).toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ep(e,t):void 0}}function eh(e){return function(e){if(Array.isArray(e))return ep(e)}(e)||em(e)||ef(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}ed.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return ed.d(t,{a:t}),t},ed.d=(e,t)=>{for(var n in t)ed.o(t,n)&&!ed.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},ed.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),ed.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);let eg=b.createContext({}),ev="anticon",ey=b.createContext({getPrefixCls:(e,t)=>t||(e?`ant-${e}`:"ant"),iconPrefixCls:ev}),{Consumer:ex}=ey;function eb(e){if(Array.isArray(e))return e}function ew(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ej(e,t){return eb(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,a=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw a}}return s}}(e,t)||ef(e,t)||ew()}function eS(e){return(eS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ek(e){var t=function(e,t){if("object"!=eS(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=eS(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eS(t)?t:t+""}function eC(e,t,n){return(t=ek(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function eN(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function eE(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?eN(Object(n),!0).forEach(function(t){eC(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):eN(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}let eT=function(e){for(var t,n=0,r=0,a=e.length;a>=4;++r,a-=4)t=(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))*0x5bd1e995+((t>>>16)*59797<<16),t^=t>>>24,n=(65535&t)*0x5bd1e995+((t>>>16)*59797<<16)^(65535&n)*0x5bd1e995+((n>>>16)*59797<<16);switch(a){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n^=255&e.charCodeAt(r),n=(65535&n)*0x5bd1e995+((n>>>16)*59797<<16)}return n^=n>>>13,(((n=(65535&n)*0x5bd1e995+((n>>>16)*59797<<16))^n>>>15)>>>0).toString(36)};function e_(){return!!("undefined"!=typeof window&&window.document&&window.document.createElement)}var eA="data-rc-order",eP="data-rc-priority",eM=new Map;function eI(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.mark;return t?t.startsWith("data-")?t:"data-".concat(t):"rc-util-key"}function eO(e){return e.attachTo?e.attachTo:document.querySelector("head")||document.body}function eL(e){return Array.from((eM.get(e)||e).children).filter(function(e){return"STYLE"===e.tagName})}function eR(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e_())return null;var n=t.csp,r=t.prepend,a=t.priority,i=void 0===a?0:a,o="queue"===r?"prependQueue":r?"prepend":"append",s="prependQueue"===o,l=document.createElement("style");l.setAttribute(eA,o),s&&i&&l.setAttribute(eP,"".concat(i)),null!=n&&n.nonce&&(l.nonce=null==n?void 0:n.nonce),l.innerHTML=e;var c=eO(t),u=c.firstChild;if(r){if(s){var d=(t.styles||eL(c)).filter(function(e){return!!["prepend","prependQueue"].includes(e.getAttribute(eA))&&i>=Number(e.getAttribute(eP)||0)});if(d.length)return c.insertBefore(l,d[d.length-1].nextSibling),l}c.insertBefore(l,u)}else c.appendChild(l);return l}function e$(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=eO(t);return(t.styles||eL(n)).find(function(n){return n.getAttribute(eI(t))===e})}function eF(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e$(e,t);n&&eO(t).removeChild(n)}function ez(e,t){var n,r,a,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=eO(i),s=eL(o),l=eE(eE({},i),{},{styles:s}),c=eM.get(o);if(!c||!function(e,t){if(!e)return!1;if(e.contains)return e.contains(t);for(var n=t;n;){if(n===e)return!0;n=n.parentNode}return!1}(document,c)){var u=eR("",l),d=u.parentNode;eM.set(o,d),o.removeChild(u)}var p=e$(t,l);if(p)return null!=(n=l.csp)&&n.nonce&&p.nonce!==(null==(r=l.csp)?void 0:r.nonce)&&(p.nonce=null==(a=l.csp)?void 0:a.nonce),p.innerHTML!==e&&(p.innerHTML=e),p;var m=eR(e,l);return m.setAttribute(eI(l),t),m}function eD(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],-1===t.indexOf(n)&&({}).propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function eH(e,t,n){var r=b.useRef({});return(!("value"in r.current)||n(r.current.condition,t))&&(r.current.value=e(),r.current.condition=t),r.current.value}var eZ={},eB=[];function eU(e,t){}function eK(e,t){}function eW(e,t,n){t||eZ[n]||(e(!1,n),eZ[n]=!0)}function eq(e,t){eW(eU,e,t)}eq.preMessage=function(e){eB.push(e)},eq.resetWarned=function(){eZ={}},eq.noteOnce=function(e,t){eW(eK,e,t)};let eG=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=new Set;return function e(t,a){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,o=r.has(t);if(eq(!o,"Warning: There may be circular references"),o)return!1;if(t===a)return!0;if(n&&i>1)return!1;r.add(t);var s=i+1;if(Array.isArray(t)){if(!Array.isArray(a)||t.length!==a.length)return!1;for(var l=0;l<t.length;l++)if(!e(t[l],a[l],s))return!1;return!0}if(t&&a&&"object"===eS(t)&&"object"===eS(a)){var c=Object.keys(t);return c.length===Object.keys(a).length&&c.every(function(n){return e(t[n],a[n],s)})}return!1}(e,t)};function eV(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function eX(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ek(r.key),r)}}function eJ(e,t,n){return t&&eX(e.prototype,t),n&&eX(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function eY(e){return e.join("%")}var eQ=function(){function e(t){eV(this,e),eC(this,"instanceId",void 0),eC(this,"cache",new Map),this.instanceId=t}return eJ(e,[{key:"get",value:function(e){return this.opGet(eY(e))}},{key:"opGet",value:function(e){return this.cache.get(e)||null}},{key:"update",value:function(e,t){return this.opUpdate(eY(e),t)}},{key:"opUpdate",value:function(e,t){var n=t(this.cache.get(e));null===n?this.cache.delete(e):this.cache.set(e,n)}}]),e}(),e0="data-token-hash",e1="data-css-hash",e2="__cssinjs_instance__",e4=b.createContext({hashPriority:"low",cache:function(){var e=Math.random().toString(12).slice(2);if("undefined"!=typeof document&&document.head&&document.body){var t=document.body.querySelectorAll("style[".concat(e1,"]"))||[],n=document.head.firstChild;Array.from(t).forEach(function(t){t[e2]=t[e2]||e,t[e2]===e&&document.head.insertBefore(t,n)});var r={};Array.from(document.querySelectorAll("style[".concat(e1,"]"))).forEach(function(t){var n,a=t.getAttribute(e1);r[a]?t[e2]===e&&(null==(n=t.parentNode)||n.removeChild(t)):r[a]=!0})}return new eQ(e)}(),defaultCache:!0});function e5(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function e3(e,t){return(e3=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function e6(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&e3(e,t)}function e8(e){return(e8=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function e9(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(e9=function(){return!!e})()}function e7(e){var t=e9();return function(){var n,r=e8(e);n=t?Reflect.construct(r,arguments,e8(this).constructor):r.apply(this,arguments);if(n&&("object"==eS(n)||"function"==typeof n))return n;if(void 0!==n)throw TypeError("Derived constructors may only return object or undefined");return e5(this)}}var te=function(){function e(){eV(this,e),eC(this,"cache",void 0),eC(this,"keys",void 0),eC(this,"cacheCallTimes",void 0),this.cache=new Map,this.keys=[],this.cacheCallTimes=0}return eJ(e,[{key:"size",value:function(){return this.keys.length}},{key:"internalGet",value:function(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a={map:this.cache};return e.forEach(function(e){if(a){var t;a=null==(t=a)||null==(t=t.map)?void 0:t.get(e)}else a=void 0}),null!=(t=a)&&t.value&&r&&(a.value[1]=this.cacheCallTimes++),null==(n=a)?void 0:n.value}},{key:"get",value:function(e){var t;return null==(t=this.internalGet(e,!0))?void 0:t[0]}},{key:"has",value:function(e){return!!this.internalGet(e)}},{key:"set",value:function(t,n){var r=this;if(!this.has(t)){if(this.size()+1>e.MAX_CACHE_SIZE+e.MAX_CACHE_OFFSET){var a=this.keys.reduce(function(e,t){var n=ej(e,2)[1];return r.internalGet(t)[1]<n?[t,r.internalGet(t)[1]]:e},[this.keys[0],this.cacheCallTimes]),i=ej(a,1)[0];this.delete(i)}this.keys.push(t)}var o=this.cache;t.forEach(function(e,a){if(a===t.length-1)o.set(e,{value:[n,r.cacheCallTimes++]});else{var i=o.get(e);i?i.map||(i.map=new Map):o.set(e,{map:new Map}),o=o.get(e).map}})}},{key:"deleteByPath",value:function(e,t){var n,r=e.get(t[0]);if(1===t.length)return r.map?e.set(t[0],{map:r.map}):e.delete(t[0]),null==(n=r.value)?void 0:n[0];var a=this.deleteByPath(r.map,t.slice(1));return r.map&&0!==r.map.size||r.value||e.delete(t[0]),a}},{key:"delete",value:function(e){if(this.has(e))return this.keys=this.keys.filter(function(t){return!function(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(t,e)}),this.deleteByPath(this.cache,e)}}]),e}();eC(te,"MAX_CACHE_SIZE",20),eC(te,"MAX_CACHE_OFFSET",5);var tt=0,tn=function(){function e(t){eV(this,e),eC(this,"derivatives",void 0),eC(this,"id",void 0),this.derivatives=Array.isArray(t)?t:[t],this.id=tt,0===t.length&&t.length,tt+=1}return eJ(e,[{key:"getDerivativeToken",value:function(e){return this.derivatives.reduce(function(t,n){return n(e,t)},void 0)}}]),e}(),tr=new te;function ta(e){var t=Array.isArray(e)?e:[e];return tr.has(t)||tr.set(t,new tn(t)),tr.get(t)}var ti=new WeakMap,to={},ts=new WeakMap;function tl(e){var t=ts.get(e)||"";return t||(Object.keys(e).forEach(function(n){var r=e[n];t+=n,r instanceof tn?t+=r.id:r&&"object"===eS(r)?t+=tl(r):t+=r}),t=eT(t),ts.set(e,t)),t}function tc(e,t){return eT("".concat(t,"_").concat(tl(e)))}"random-".concat(Date.now(),"-").concat(Math.random()).replace(/\./g,"");var tu=e_();function td(e){return"number"==typeof e?"".concat(e,"px"):e}function tp(e,t,n){var r,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(i)return e;var o=eE(eE({},a),{},(eC(r={},e0,t),eC(r,e1,n),r)),s=Object.keys(o).map(function(e){var t=o[e];return t?"".concat(e,'="').concat(t,'"'):null}).filter(function(e){return e}).join(" ");return"<style ".concat(s,">").concat(e,"</style>")}var tm=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"--".concat(t?"".concat(t,"-"):"").concat(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").replace(/([A-Z]+)([A-Z][a-z0-9]+)/g,"$1-$2").replace(/([a-z])([A-Z0-9])/g,"$1-$2").toLowerCase()},tf=function(e,t,n){var r,a={},i={};return Object.entries(e).forEach(function(e){var t=ej(e,2),r=t[0],o=t[1];if(null!=n&&null!=(s=n.preserve)&&s[r])i[r]=o;else if(("string"==typeof o||"number"==typeof o)&&!(null!=n&&null!=(l=n.ignore)&&l[r])){var s,l,c,u=tm(r,null==n?void 0:n.prefix);a[u]="number"!=typeof o||null!=n&&null!=(c=n.unitless)&&c[r]?String(o):"".concat(o,"px"),i[r]="var(".concat(u,")")}}),[i,(r={scope:null==n?void 0:n.scope},Object.keys(a).length?".".concat(t).concat(null!=r&&r.scope?".".concat(r.scope):"","{").concat(Object.entries(a).map(function(e){var t=ej(e,2),n=t[0],r=t[1];return"".concat(n,":").concat(r,";")}).join(""),"}"):"")]},th=e_()?b.useLayoutEffect:b.useEffect;let tg=function(e,t){var n=b.useRef(!0);th(function(){return e(n.current)},t),th(function(){return n.current=!1,function(){n.current=!0}},[])};var tv=eE({},w).useInsertionEffect,ty=tv?function(e,t,n){return tv(function(){return e(),t()},n)}:function(e,t,n){b.useMemo(e,n),tg(function(){return t(!0)},n)},tx=void 0!==eE({},w).useInsertionEffect?function(e){var t=[],n=!1;return b.useEffect(function(){return n=!1,function(){n=!0,t.length&&t.forEach(function(e){return e()})}},e),function(e){n||t.push(e)}}:function(){return function(e){e()}},tb=ed("../../node_modules/.pnpm/@ant-design+cssinjs@1.23.0_react-dom@18.3.1_react@18.3.1/node_modules/@ant-design/cssinjs/es/hooks/useHMR.js");function tw(e,t,n,r,a){var i=b.useContext(e4).cache,o=eY([e].concat(eh(t))),s=tx([o]);(0,tb.Z)();var l=function(e){i.opUpdate(o,function(t){var r=ej(t||[void 0,void 0],2),a=r[0],i=[void 0===a?0:a,r[1]||n()];return e?e(i):i})};b.useMemo(function(){l()},[o]);var c=i.opGet(o)[1];return ty(function(){null==a||a(c)},function(e){return l(function(t){var n=ej(t,2),r=n[0],i=n[1];return e&&0===r&&(null==a||a(c)),[r+1,i]}),function(){i.opUpdate(o,function(t){var n=ej(t||[],2),a=n[0],l=void 0===a?0:a,c=n[1];return 0==l-1?(s(function(){(e||!i.opGet(o))&&(null==r||r(c,!1))}),null):[l-1,c]})}},[o]),c}var tj={},tS=new Map,tk=function(e,t,n,r){var a=eE(eE({},n.getDerivativeToken(e)),t);return r&&(a=r(a)),a},tC="token";function tN(){return(tN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}let tE={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};var tT="comm",t_="rule",tA="decl",tP=Math.abs,tM=String.fromCharCode;function tI(e,t,n){return e.replace(t,n)}function tO(e,t){return 0|e.charCodeAt(t)}function tL(e,t,n){return e.slice(t,n)}function tR(e){return e.length}function t$(e,t){return t.push(e),e}function tF(e,t){for(var n="",r=0;r<e.length;r++)n+=t(e[r],r,e,t)||"";return n}function tz(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case"@namespace":case tA:return e.return=e.return||e.value;case tT:return"";case"@keyframes":return e.return=e.value+"{"+tF(e.children,r)+"}";case t_:if(!tR(e.value=e.props.join(",")))return""}return tR(n=tF(e.children,r))?e.return=e.value+"{"+n+"}":""}var tD=1,tH=1,tZ=0,tB=0,tU=0,tK="";function tW(e,t,n,r,a,i,o,s){return{value:e,root:t,parent:n,type:r,props:a,children:i,line:tD,column:tH,length:o,return:"",siblings:s}}function tq(){return tU=tB<tZ?tO(tK,tB++):0,tH++,10===tU&&(tH=1,tD++),tU}function tG(){return tO(tK,tB)}function tV(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function tX(e){var t,n;return(t=tB-1,n=function e(t){for(;tq();)switch(tU){case t:return tB;case 34:case 39:34!==t&&39!==t&&e(tU);break;case 40:41===t&&e(t);break;case 92:tq()}return tB}(91===e?e+2:40===e?e+1:e),tL(tK,t,n)).trim()}function tJ(e,t,n,r,a,i,o,s,l,c,u,d){for(var p=a-1,m=0===a?i:[""],f=m.length,h=0,g=0,v=0;h<r;++h)for(var y=0,x=tL(e,p+1,p=tP(g=o[h])),b=e;y<f;++y)(b=(g>0?m[y]+" "+x:tI(x,/&\f/g,m[y])).trim())&&(l[v++]=b);return tW(e,t,n,0===a?t_:s,l,c,u,d)}function tY(e,t,n,r,a){return tW(e,t,n,tA,tL(e,0,r),tL(e,r+1,-1),r,a)}var tQ="data-ant-cssinjs-cache-path",t0="_FILE_STYLE__",t1=!0,t2="_multi_value_";function t4(e){var t,n,r;return tF((r=function e(t,n,r,a,i,o,s,l,c){for(var u,d,p,m,f,h,g=0,v=0,y=s,x=0,b=0,w=0,j=1,S=1,k=1,C=0,N="",E=i,T=o,_=a,A=N;S;)switch(w=C,C=tq()){case 40:if(108!=w&&58==tO(A,y-1)){-1!=(f=A+=tI(tX(C),"&","&\f"),h=tP(g?l[g-1]:0),f.indexOf("&\f",h))&&(k=-1);break}case 34:case 39:case 91:A+=tX(C);break;case 9:case 10:case 13:case 32:A+=function(e){for(;tU=tG();)if(tU<33)tq();else break;return tV(e)>2||tV(tU)>3?"":" "}(w);break;case 92:A+=function(e,t){for(var n;--t&&tq()&&!(tU<48)&&!(tU>102)&&(!(tU>57)||!(tU<65))&&(!(tU>70)||!(tU<97)););return n=tB+(t<6&&32==tG()&&32==tq()),tL(tK,e,n)}(tB-1,7);continue;case 47:switch(tG()){case 42:case 47:t$((u=function(e,t){for(;tq();)if(e+tU===57)break;else if(e+tU===84&&47===tG())break;return"/*"+tL(tK,t,tB-1)+"*"+tM(47===e?e:tq())}(tq(),tB),d=n,p=r,m=c,tW(u,d,p,tT,tM(tU),tL(u,2,-2),0,m)),c),(5==tV(w||1)||5==tV(tG()||1))&&tR(A)&&" "!==tL(A,-1,void 0)&&(A+=" ");break;default:A+="/"}break;case 123*j:l[g++]=tR(A)*k;case 125*j:case 59:case 0:switch(C){case 0:case 125:S=0;case 59+v:-1==k&&(A=tI(A,/\f/g,"")),b>0&&(tR(A)-y||0===j&&47===w)&&t$(b>32?tY(A+";",a,r,y-1,c):tY(tI(A," ","")+";",a,r,y-2,c),c);break;case 59:A+=";";default:if(t$(_=tJ(A,n,r,g,v,i,l,N,E=[],T=[],y,o),o),123===C)if(0===v)e(A,n,_,_,E,o,y,l,T);else{switch(x){case 99:if(110===tO(A,3))break;case 108:if(97===tO(A,2))break;default:v=0;case 100:case 109:case 115:}v?e(t,_,_,a&&t$(tJ(t,_,_,0,0,i,l,N,i,E=[],y,T),T),i,T,y,l,a?E:T):e(A,_,_,_,[""],T,0,l,T)}}g=v=b=0,j=k=1,N=A="",y=s;break;case 58:y=1+tR(A),b=w;default:if(j<1){if(123==C)--j;else if(125==C&&0==j++&&125==(tU=tB>0?tO(tK,--tB):0,tH--,10===tU&&(tH=1,tD--),tU))continue}switch(A+=tM(C),C*j){case 38:k=v>0?1:(A+="\f",-1);break;case 44:l[g++]=(tR(A)-1)*k,k=1;break;case 64:45===tG()&&(A+=tX(tq())),x=tG(),v=y=tR(N=A+=function(e){for(;!tV(tG());)tq();return tL(tK,e,tB)}(tB)),C++;break;case 45:45===w&&2==tR(A)&&(j=0)}}return o}("",null,null,null,[""],(n=t=e,tD=tH=1,tZ=tR(tK=n),tB=0,t=[]),0,[0],t),tK="",r),tz).replace(/\{%%%\:[^;];}/g,";")}function t5(e,t,n){if(!t)return e;var r=".".concat(t),a="low"===n?":where(".concat(r,")"):r;return e.split(",").map(function(e){var t,n=e.trim().split(/\s+/),r=n[0]||"",i=(null==(t=r.match(/^\w+/))?void 0:t[0])||"";return[r="".concat(i).concat(a).concat(r.slice(i.length))].concat(eh(n.slice(1))).join(" ")}).join(",")}var t3=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{root:!0,parentSelectors:[]},a=r.root,i=r.injectHash,o=r.parentSelectors,s=n.hashId,l=n.layer,c=(n.path,n.hashPriority),u=n.transformers,d=void 0===u?[]:u,p=(n.linters,""),m={};function f(t){var r=t.getName(s);if(!m[r]){var a=ej(e(t.style,n,{root:!1,parentSelectors:o}),1)[0];m[r]="@keyframes ".concat(t.getName(s)).concat(a)}}return(function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return t.forEach(function(t){Array.isArray(t)?e(t,n):t&&n.push(t)}),n})(Array.isArray(t)?t:[t]).forEach(function(t){var r="string"!=typeof t||a?t:{};if("string"==typeof r)p+="".concat(r,"\n");else if(r._keyframe)f(r);else{var l=d.reduce(function(e,t){var n;return(null==t||null==(n=t.visit)?void 0:n.call(t,e))||e},r);Object.keys(l).forEach(function(t){var r=l[t];if("object"!==eS(r)||!r||"animationName"===t&&r._keyframe||"object"===eS(r)&&r&&("_skip_check_"in r||t2 in r)){function u(e,t){var n=e.replace(/[A-Z]/g,function(e){return"-".concat(e.toLowerCase())}),r=t;tE[e]||"number"!=typeof r||0===r||(r="".concat(r,"px")),"animationName"===e&&null!=t&&t._keyframe&&(f(t),r=t.getName(s)),p+="".concat(n,":").concat(r,";")}var d,h=null!=(d=null==r?void 0:r.value)?d:r;"object"===eS(r)&&null!=r&&r[t2]&&Array.isArray(h)?h.forEach(function(e){u(t,e)}):u(t,h)}else{var g=!1,v=t.trim(),y=!1;(a||i)&&s?v.startsWith("@")?g=!0:v="&"===v?t5("",s,c):t5(t,s,c):a&&!s&&("&"===v||""===v)&&(v="",y=!0);var x=ej(e(r,n,{root:y,injectHash:g,parentSelectors:[].concat(eh(o),[v])}),2),b=x[0],w=x[1];m=eE(eE({},m),w),p+="".concat(v).concat(b)}})}}),a?l&&(p&&(p="@layer ".concat(l.name," {").concat(p,"}")),l.dependencies&&(m["@layer ".concat(l.name)]=l.dependencies.map(function(e){return"@layer ".concat(e,", ").concat(l.name,";")}).join("\n"))):p="{".concat(p,"}"),[p,m]};function t6(e,t){return eT("".concat(e.join("%")).concat(t))}function t8(){return null}var t9="style";function t7(e,t){var n=e.token,r=e.path,a=e.hashId,i=e.layer,o=e.nonce,s=e.clientOnly,l=e.order,c=void 0===l?0:l,u=b.useContext(e4),d=u.autoClear,p=(u.mock,u.defaultCache),m=u.hashPriority,f=u.container,g=u.ssrInline,v=u.transformers,y=u.linters,x=u.cache,w=u.layer,j=n._tokenKey,S=[j];w&&S.push("layer"),S.push.apply(S,eh(r));var k=tw(t9,S,function(){var e=S.join("|");if(function(e){if(!h&&(h={},e_())){var t,n=document.createElement("div");n.className=tQ,n.style.position="fixed",n.style.visibility="hidden",n.style.top="-9999px",document.body.appendChild(n);var r=getComputedStyle(n).content||"";(r=r.replace(/^"/,"").replace(/"$/,"")).split(";").forEach(function(e){var t=ej(e.split(":"),2),n=t[0],r=t[1];h[n]=r});var a=document.querySelector("style[".concat(tQ,"]"));a&&(t1=!1,null==(t=a.parentNode)||t.removeChild(a)),document.body.removeChild(n)}return!!h[e]}(e)){var n=ej(function(e){var t=h[e],n=null;if(t&&e_())if(t1)n=t0;else{var r=document.querySelector("style[".concat(e1,'="').concat(h[e],'"]'));r?n=r.innerHTML:delete h[e]}return[n,t]}(e),2),o=n[0],l=n[1];if(o)return[o,j,l,{},s,c]}var u=ej(t3(t(),{hashId:a,hashPriority:m,layer:w?i:void 0,path:r.join("-"),transformers:v,linters:y}),2),d=u[0],p=u[1],f=t4(d),g=t6(S,f);return[f,j,g,p,s,c]},function(e,t){var n=ej(e,3)[2];(t||d)&&tu&&eF(n,{mark:e1})},function(e){var t=ej(e,4),n=t[0],r=(t[1],t[2]),a=t[3];if(tu&&n!==t0){var i={mark:e1,prepend:!w&&"queue",attachTo:f,priority:c},s="function"==typeof o?o():o;s&&(i.csp={nonce:s});var l=[],u=[];Object.keys(a).forEach(function(e){e.startsWith("@layer")?l.push(e):u.push(e)}),l.forEach(function(e){ez(t4(a[e]),"_layer-".concat(e),eE(eE({},i),{},{prepend:!0}))});var d=ez(n,r,i);d[e2]=x.instanceId,d.setAttribute(e0,j),u.forEach(function(e){ez(t4(a[e]),"_effect-".concat(e),i)})}}),C=ej(k,3),N=C[0],E=C[1],T=C[2];return function(e){var t,n;return t=g&&!tu&&p?b.createElement("style",tN({},(eC(n={},e0,E),eC(n,e1,T),n),{dangerouslySetInnerHTML:{__html:N}})):b.createElement(t8,null),b.createElement(b.Fragment,null,t,e)}}var ne="cssVar";let nt=function(e,t){var n=e.key,r=e.prefix,a=e.unitless,i=e.ignore,o=e.token,s=e.scope,l=void 0===s?"":s,c=(0,b.useContext)(e4),u=c.cache.instanceId,d=c.container,p=o._tokenKey,m=[].concat(eh(e.path),[n,l,p]);return tw(ne,m,function(){var e=ej(tf(t(),n,{prefix:r,unitless:a,ignore:i,scope:l}),2),o=e[0],s=e[1],c=t6(m,s);return[o,s,c,n]},function(e){var t=ej(e,3)[2];tu&&eF(t,{mark:e1})},function(e){var t=ej(e,3),r=t[1],a=t[2];if(r){var i=ez(r,a,{mark:e1,prepend:"queue",attachTo:d,priority:-999});i[e2]=u,i.setAttribute(e0,n)}})};eC(g={},t9,function(e,t,n){var r=ej(e,6),a=r[0],i=r[1],o=r[2],s=r[3],l=r[4],c=r[5],u=(n||{}).plain;if(l)return null;var d=a,p={"data-rc-order":"prependQueue","data-rc-priority":"".concat(c)};return d=tp(a,i,o,p,u),s&&Object.keys(s).forEach(function(e){if(!t[e]){t[e]=!0;var n=tp(t4(s[e]),i,"_effect-".concat(e),p,u);e.startsWith("@layer")?d=n+d:d+=n}}),[c,o,d]}),eC(g,tC,function(e,t,n){var r=ej(e,5),a=r[2],i=r[3],o=r[4],s=(n||{}).plain;if(!i)return null;var l=a._tokenKey,c=tp(i,o,l,{"data-rc-order":"prependQueue","data-rc-priority":"".concat(-999)},s);return[-999,l,c]}),eC(g,ne,function(e,t,n){var r=ej(e,4),a=r[1],i=r[2],o=r[3],s=(n||{}).plain;if(!a)return null;var l=tp(a,o,i,{"data-rc-order":"prependQueue","data-rc-priority":"".concat(-999)},s);return[-999,i,l]});var nn=function(){function e(t,n){eV(this,e),eC(this,"name",void 0),eC(this,"style",void 0),eC(this,"_keyframe",!0),this.name=t,this.style=n}return eJ(e,[{key:"getName",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e?"".concat(e,"-").concat(this.name):this.name}}]),e}();function nr(e){return e.notSplit=!0,e}nr(["borderTop","borderBottom"]),nr(["borderTop"]),nr(["borderBottom"]),nr(["borderLeft","borderRight"]),nr(["borderLeft"]),nr(["borderRight"]);var na=(0,b.createContext)({});function ni(e,t){for(var n=e,r=0;r<t.length;r+=1){if(null==n)return;n=n[t[r]]}return n}function no(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return t.length&&r&&void 0===n&&!ni(e,t.slice(0,-1))?e:function e(t,n,r,a){if(!n.length)return r;var i,o=eb(n)||em(n)||ef(n)||ew(),s=o[0],l=o.slice(1);return i=t||"number"!=typeof s?Array.isArray(t)?eh(t):eE({},t):[],a&&void 0===r&&1===l.length?delete i[s][l[0]]:i[s]=e(i[s],l,r,a),i}(e,t,n,r)}function ns(e){return Array.isArray(e)?[]:{}}var nl="undefined"==typeof Reflect?Object.keys:Reflect.ownKeys;function nc(){}let nu=b.createContext({}),nd=()=>{let e=()=>{};return e.deprecated=nc,e},np=(0,b.createContext)(void 0);Object.assign({placeholder:"Select date",yearPlaceholder:"Select year",quarterPlaceholder:"Select quarter",monthPlaceholder:"Select month",weekPlaceholder:"Select week",rangePlaceholder:["Start date","End date"],rangeYearPlaceholder:["Start year","End year"],rangeQuarterPlaceholder:["Start quarter","End quarter"],rangeMonthPlaceholder:["Start month","End month"],rangeWeekPlaceholder:["Start week","End week"]},eE(eE({},{yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0}),{},{locale:"en_US",today:"Today",now:"Now",backToToday:"Back to today",ok:"OK",clear:"Clear",week:"Week",month:"Month",year:"Year",timeSelect:"select time",dateSelect:"select date",weekSelect:"Choose a week",monthSelect:"Choose a month",yearSelect:"Choose a year",decadeSelect:"Choose a decade",dateFormat:"M/D/YYYY",dateTimeFormat:"M/D/YYYY HH:mm:ss",previousMonth:"Previous month (PageUp)",nextMonth:"Next month (PageDown)",previousYear:"Last year (Control + left)",nextYear:"Next year (Control + right)",previousDecade:"Last decade",nextDecade:"Next decade",previousCentury:"Last century",nextCentury:"Next century"})),Object.assign({},{placeholder:"Select time",rangePlaceholder:["Start time","End time"]});let nm="${label} is not a valid ${type}",nf={Modal:{okText:"OK",cancelText:"Cancel",justOkText:"OK"},Form:{optional:"(optional)",defaultValidateMessages:{default:"Field validation error for ${label}",required:"Please enter ${label}",enum:"${label} must be one of [${enum}]",whitespace:"${label} cannot be a blank character",date:{format:"${label} date format is invalid",parse:"${label} cannot be converted to a date",invalid:"${label} is an invalid date"},types:{string:nm,method:nm,array:nm,object:nm,number:nm,date:nm,boolean:nm,integer:nm,float:nm,regexp:nm,email:nm,url:nm,hex:nm},string:{len:"${label} must be ${len} characters",min:"${label} must be at least ${min} characters",max:"${label} must be up to ${max} characters",range:"${label} must be between ${min}-${max} characters"},number:{len:"${label} must be equal to ${len}",min:"${label} must be minimum ${min}",max:"${label} must be maximum ${max}",range:"${label} must be between ${min}-${max}"},array:{len:"Must be ${len} ${label}",min:"At least ${min} ${label}",max:"At most ${max} ${label}",range:"The amount of ${label} must be between ${min}-${max}"},pattern:{mismatch:"${label} does not match the pattern ${pattern}"}}}};Object.assign({},nf.Modal);let nh=[],ng=()=>nh.reduce((e,t)=>Object.assign(Object.assign({},e),t),nf.Modal),nv=(0,b.createContext)(void 0),ny=e=>{let{locale:t={},children:n,_ANT_MARK__:r}=e;b.useEffect(()=>(function(e){if(e){let t=Object.assign({},e);return nh.push(t),ng(),()=>{nh=nh.filter(e=>e!==t),ng()}}Object.assign({},nf.Modal)})(null==t?void 0:t.Modal),[t]);let a=b.useMemo(()=>Object.assign(Object.assign({},t),{exist:!0}),[t]);return b.createElement(nv.Provider,{value:a},n)},nx=Math.round;function nb(e,t){let n=e.replace(/^[^(]*\((.*)/,"$1").replace(/\).*/,"").match(/\d*\.?\d+%?/g)||[],r=n.map(e=>parseFloat(e));for(let e=0;e<3;e+=1)r[e]=t(r[e]||0,n[e]||"",e);return n[3]?r[3]=n[3].includes("%")?r[3]/100:r[3]:r[3]=1,r}let nw=(e,t,n)=>0===n?e:e/100;function nj(e,t){let n=t||255;return e>n?n:e<0?0:e}class nS{setR(e){return this._sc("r",e)}setG(e){return this._sc("g",e)}setB(e){return this._sc("b",e)}setA(e){return this._sc("a",e,1)}setHue(e){let t=this.toHsv();return t.h=e,this._c(t)}getLuminance(){function e(e){let t=e/255;return t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}let t=e(this.r);return .2126*t+.7152*e(this.g)+.0722*e(this.b)}getHue(){if(void 0===this._h){let e=this.getMax()-this.getMin();0===e?this._h=0:this._h=nx(60*(this.r===this.getMax()?(this.g-this.b)/e+6*(this.g<this.b):this.g===this.getMax()?(this.b-this.r)/e+2:(this.r-this.g)/e+4))}return this._h}getSaturation(){if(void 0===this._s){let e=this.getMax()-this.getMin();0===e?this._s=0:this._s=e/this.getMax()}return this._s}getLightness(){return void 0===this._l&&(this._l=(this.getMax()+this.getMin())/510),this._l}getValue(){return void 0===this._v&&(this._v=this.getMax()/255),this._v}getBrightness(){return void 0===this._brightness&&(this._brightness=(299*this.r+587*this.g+114*this.b)/1e3),this._brightness}darken(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=this.getHue(),n=this.getSaturation(),r=this.getLightness()-e/100;return r<0&&(r=0),this._c({h:t,s:n,l:r,a:this.a})}lighten(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=this.getHue(),n=this.getSaturation(),r=this.getLightness()+e/100;return r>1&&(r=1),this._c({h:t,s:n,l:r,a:this.a})}mix(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,n=this._c(e),r=t/100,a=e=>(n[e]-this[e])*r+this[e],i={r:nx(a("r")),g:nx(a("g")),b:nx(a("b")),a:nx(100*a("a"))/100};return this._c(i)}tint(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return this.mix({r:255,g:255,b:255,a:1},e)}shade(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return this.mix({r:0,g:0,b:0,a:1},e)}onBackground(e){let t=this._c(e),n=this.a+t.a*(1-this.a),r=e=>nx((this[e]*this.a+t[e]*t.a*(1-this.a))/n);return this._c({r:r("r"),g:r("g"),b:r("b"),a:n})}isDark(){return 128>this.getBrightness()}isLight(){return this.getBrightness()>=128}equals(e){return this.r===e.r&&this.g===e.g&&this.b===e.b&&this.a===e.a}clone(){return this._c(this)}toHexString(){let e="#",t=(this.r||0).toString(16);e+=2===t.length?t:"0"+t;let n=(this.g||0).toString(16);e+=2===n.length?n:"0"+n;let r=(this.b||0).toString(16);if(e+=2===r.length?r:"0"+r,"number"==typeof this.a&&this.a>=0&&this.a<1){let t=nx(255*this.a).toString(16);e+=2===t.length?t:"0"+t}return e}toHsl(){return{h:this.getHue(),s:this.getSaturation(),l:this.getLightness(),a:this.a}}toHslString(){let e=this.getHue(),t=nx(100*this.getSaturation()),n=nx(100*this.getLightness());return 1!==this.a?`hsla(${e},${t}%,${n}%,${this.a})`:`hsl(${e},${t}%,${n}%)`}toHsv(){return{h:this.getHue(),s:this.getSaturation(),v:this.getValue(),a:this.a}}toRgb(){return{r:this.r,g:this.g,b:this.b,a:this.a}}toRgbString(){return 1!==this.a?`rgba(${this.r},${this.g},${this.b},${this.a})`:`rgb(${this.r},${this.g},${this.b})`}toString(){return this.toRgbString()}_sc(e,t,n){let r=this.clone();return r[e]=nj(t,n),r}_c(e){return new this.constructor(e)}getMax(){return void 0===this._max&&(this._max=Math.max(this.r,this.g,this.b)),this._max}getMin(){return void 0===this._min&&(this._min=Math.min(this.r,this.g,this.b)),this._min}fromHexString(e){let t=e.replace("#","");function n(e,n){return parseInt(t[e]+t[n||e],16)}t.length<6?(this.r=n(0),this.g=n(1),this.b=n(2),this.a=t[3]?n(3)/255:1):(this.r=n(0,1),this.g=n(2,3),this.b=n(4,5),this.a=t[6]?n(6,7)/255:1)}fromHsl(e){let{h:t,s:n,l:r,a}=e;if(this._h=t%360,this._s=n,this._l=r,this.a="number"==typeof a?a:1,n<=0){let e=nx(255*r);this.r=e,this.g=e,this.b=e}let i=0,o=0,s=0,l=t/60,c=(1-Math.abs(2*r-1))*n,u=c*(1-Math.abs(l%2-1));l>=0&&l<1?(i=c,o=u):l>=1&&l<2?(i=u,o=c):l>=2&&l<3?(o=c,s=u):l>=3&&l<4?(o=u,s=c):l>=4&&l<5?(i=u,s=c):l>=5&&l<6&&(i=c,s=u);let d=r-c/2;this.r=nx((i+d)*255),this.g=nx((o+d)*255),this.b=nx((s+d)*255)}fromHsv(e){let{h:t,s:n,v:r,a}=e;this._h=t%360,this._s=n,this._v=r,this.a="number"==typeof a?a:1;let i=nx(255*r);if(this.r=i,this.g=i,this.b=i,n<=0)return;let o=t/60,s=Math.floor(o),l=o-s,c=nx(r*(1-n)*255),u=nx(r*(1-n*l)*255),d=nx(r*(1-n*(1-l))*255);switch(s){case 0:this.g=d,this.b=c;break;case 1:this.r=u,this.b=c;break;case 2:this.r=c,this.b=d;break;case 3:this.r=c,this.g=u;break;case 4:this.r=d,this.g=c;break;default:this.g=c,this.b=u}}fromHsvString(e){let t=nb(e,nw);this.fromHsv({h:t[0],s:t[1],v:t[2],a:t[3]})}fromHslString(e){let t=nb(e,nw);this.fromHsl({h:t[0],s:t[1],l:t[2],a:t[3]})}fromRgbString(e){let t=nb(e,(e,t)=>t.includes("%")?nx(e/100*255):e);this.r=t[0],this.g=t[1],this.b=t[2],this.a=t[3]}constructor(e){function t(t){return t[0]in e&&t[1]in e&&t[2]in e}if(eC(this,"isValid",!0),eC(this,"r",0),eC(this,"g",0),eC(this,"b",0),eC(this,"a",1),eC(this,"_h",void 0),eC(this,"_s",void 0),eC(this,"_l",void 0),eC(this,"_v",void 0),eC(this,"_max",void 0),eC(this,"_min",void 0),eC(this,"_brightness",void 0),e)if("string"==typeof e){let t=e.trim();function n(e){return t.startsWith(e)}/^#?[A-F\d]{3,8}$/i.test(t)?this.fromHexString(t):n("rgb")?this.fromRgbString(t):n("hsl")?this.fromHslString(t):(n("hsv")||n("hsb"))&&this.fromHsvString(t)}else if(e instanceof nS)this.r=e.r,this.g=e.g,this.b=e.b,this.a=e.a,this._h=e._h,this._s=e._s,this._l=e._l,this._v=e._v;else if(t("rgb"))this.r=nj(e.r),this.g=nj(e.g),this.b=nj(e.b),this.a="number"==typeof e.a?nj(e.a,1):1;else if(t("hsl"))this.fromHsl(e);else if(t("hsv"))this.fromHsv(e);else throw Error("@ant-design/fast-color: unsupported input "+JSON.stringify(e))}}var nk=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function nC(e,t,n){var r;return(r=Math.round(e.h)>=60&&240>=Math.round(e.h)?n?Math.round(e.h)-2*t:Math.round(e.h)+2*t:n?Math.round(e.h)+2*t:Math.round(e.h)-2*t)<0?r+=360:r>=360&&(r-=360),r}function nN(e,t,n){var r;return 0===e.h&&0===e.s?e.s:((r=n?e.s-.16*t:4===t?e.s+.16:e.s+.05*t)>1&&(r=1),n&&5===t&&r>.1&&(r=.1),r<.06&&(r=.06),Math.round(100*r)/100)}function nE(e,t,n){var r;return Math.round(100*Math.max(0,Math.min(1,n?e.v+.05*t:e.v-.15*t)))/100}function nT(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[],r=new nS(e),a=r.toHsv(),i=5;i>0;i-=1){var o=new nS({h:nC(a,i,!0),s:nN(a,i,!0),v:nE(a,i,!0)});n.push(o)}n.push(r);for(var s=1;s<=4;s+=1){var l=new nS({h:nC(a,s),s:nN(a,s),v:nE(a,s)});n.push(l)}return"dark"===t.theme?nk.map(function(e){var r=e.index,a=e.amount;return new nS(t.backgroundColor||"#141414").mix(n[r],a).toHexString()}):n.map(function(e){return e.toHexString()})}var n_={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},nA=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];nA.primary=nA[5];var nP=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];nP.primary=nP[5];var nM=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];nM.primary=nM[5];var nI=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];nI.primary=nI[5];var nO=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];nO.primary=nO[5];var nL=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];nL.primary=nL[5];var nR=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];nR.primary=nR[5];var n$=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];n$.primary=n$[5];var nF=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];nF.primary=nF[5];var nz=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];nz.primary=nz[5];var nD=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];nD.primary=nD[5];var nH=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];nH.primary=nH[5];var nZ=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];nZ.primary=nZ[5];var nB={red:nA,volcano:nP,orange:nM,gold:nI,yellow:nO,lime:nL,green:nR,cyan:n$,blue:nF,geekblue:nz,purple:nD,magenta:nH,grey:nZ},nU=["#2a1215","#431418","#58181c","#791a1f","#a61d24","#d32029","#e84749","#f37370","#f89f9a","#fac8c3"];nU.primary=nU[5];var nK=["#2b1611","#441d12","#592716","#7c3118","#aa3e19","#d84a1b","#e87040","#f3956a","#f8b692","#fad4bc"];nK.primary=nK[5];var nW=["#2b1d11","#442a11","#593815","#7c4a15","#aa6215","#d87a16","#e89a3c","#f3b765","#f8cf8d","#fae3b7"];nW.primary=nW[5];var nq=["#2b2111","#443111","#594214","#7c5914","#aa7714","#d89614","#e8b339","#f3cc62","#f8df8b","#faedb5"];nq.primary=nq[5];var nG=["#2b2611","#443b11","#595014","#7c6e14","#aa9514","#d8bd14","#e8d639","#f3ea62","#f8f48b","#fafab5"];nG.primary=nG[5];var nV=["#1f2611","#2e3c10","#3e4f13","#536d13","#6f9412","#8bbb11","#a9d134","#c9e75d","#e4f88b","#f0fab5"];nV.primary=nV[5];var nX=["#162312","#1d3712","#274916","#306317","#3c8618","#49aa19","#6abe39","#8fd460","#b2e58b","#d5f2bb"];nX.primary=nX[5];var nJ=["#112123","#113536","#144848","#146262","#138585","#13a8a8","#33bcb7","#58d1c9","#84e2d8","#b2f1e8"];nJ.primary=nJ[5];var nY=["#111a2c","#112545","#15325b","#15417e","#1554ad","#1668dc","#3c89e8","#65a9f3","#8dc5f8","#b7dcfa"];nY.primary=nY[5];var nQ=["#131629","#161d40","#1c2755","#203175","#263ea0","#2b4acb","#5273e0","#7f9ef3","#a8c1f8","#d2e0fa"];nQ.primary=nQ[5];var n0=["#1a1325","#24163a","#301c4d","#3e2069","#51258f","#642ab5","#854eca","#ab7ae0","#cda8f0","#ebd7fa"];n0.primary=n0[5];var n1=["#291321","#40162f","#551c3b","#75204f","#a02669","#cb2b83","#e0529c","#f37fb7","#f8a8cc","#fad2e3"];n1.primary=n1[5];var n2=["#151515","#1f1f1f","#2d2d2d","#393939","#494949","#5a5a5a","#6a6a6a","#7b7b7b","#888888","#969696"];n2.primary=n2[5];let n4={blue:"#1677FF",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#EB2F96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911"},n5=Object.assign(Object.assign({},n4),{colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff4d4f",colorInfo:"#1677ff",colorLink:"",colorTextBase:"",colorBgBase:"",fontFamily:`-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
'Noto Color Emoji'`,fontFamilyCode:"'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInBack:"cubic-bezier(0.71, -0.46, 0.88, 0.6)",motionEaseInQuint:"cubic-bezier(0.755, 0.05, 0.855, 0.06)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:6,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,motion:!0}),n3=e=>{let t=e,n=e,r=e,a=e;return e<6&&e>=5?t=e+1:e<16&&e>=6?t=e+2:e>=16&&(t=16),e<7&&e>=5?n=4:e<8&&e>=7?n=5:e<14&&e>=8?n=6:e<16&&e>=14?n=7:e>=16&&(n=8),e<6&&e>=2?r=1:e>=6&&(r=2),e>4&&e<8?a=4:e>=8&&(a=6),{borderRadius:e,borderRadiusXS:r,borderRadiusSM:n,borderRadiusLG:t,borderRadiusOuter:a}},n6=e=>{let{controlHeight:t}=e;return{controlHeightSM:.75*t,controlHeightXS:.5*t,controlHeightLG:1.25*t}},n8=e=>{let t=function(e){let t=Array.from({length:10}).map((t,n)=>{let r=e*Math.pow(Math.E,(n-1)/5);return 2*Math.floor((n>1?Math.floor(r):Math.ceil(r))/2)});return t[1]=e,t.map(e=>({size:e,lineHeight:(e+8)/e}))}(e),n=t.map(e=>e.size),r=t.map(e=>e.lineHeight),a=n[1],i=n[0],o=n[2],s=r[1],l=r[0],c=r[2];return{fontSizeSM:i,fontSize:a,fontSizeLG:o,fontSizeXL:n[3],fontSizeHeading1:n[6],fontSizeHeading2:n[5],fontSizeHeading3:n[4],fontSizeHeading4:n[3],fontSizeHeading5:n[2],lineHeight:s,lineHeightLG:c,lineHeightSM:l,fontHeight:Math.round(s*a),fontHeightLG:Math.round(c*o),fontHeightSM:Math.round(l*i),lineHeightHeading1:r[6],lineHeightHeading2:r[5],lineHeightHeading3:r[4],lineHeightHeading4:r[3],lineHeightHeading5:r[2]}},n9=(e,t)=>new nS(e).setA(t).toRgbString(),n7=(e,t)=>new nS(e).darken(t).toHexString(),re=e=>{let t=nT(e);return{1:t[0],2:t[1],3:t[2],4:t[3],5:t[4],6:t[5],7:t[6],8:t[4],9:t[5],10:t[6]}},rt=(e,t)=>{let n=e||"#fff",r=t||"#000";return{colorBgBase:n,colorTextBase:r,colorText:n9(r,.88),colorTextSecondary:n9(r,.65),colorTextTertiary:n9(r,.45),colorTextQuaternary:n9(r,.25),colorFill:n9(r,.15),colorFillSecondary:n9(r,.06),colorFillTertiary:n9(r,.04),colorFillQuaternary:n9(r,.02),colorBgSolid:n9(r,1),colorBgSolidHover:n9(r,.75),colorBgSolidActive:n9(r,.95),colorBgLayout:n7(n,4),colorBgContainer:n7(n,0),colorBgElevated:n7(n,0),colorBgSpotlight:n9(r,.85),colorBgBlur:"transparent",colorBorder:n7(n,15),colorBorderSecondary:n7(n,6)}},rn=ta(function(e){n_.pink=n_.magenta,nB.pink=nB.magenta;let t=Object.keys(n4).map(t=>{let n=e[t]===n_[t]?nB[t]:nT(e[t]);return Array.from({length:10},()=>1).reduce((e,r,a)=>(e[`${t}-${a+1}`]=n[a],e[`${t}${a+1}`]=n[a],e),{})}).reduce((e,t)=>e=Object.assign(Object.assign({},e),t),{});return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},e),t),function(e,t){let{generateColorPalettes:n,generateNeutralColorPalettes:r}=t,{colorSuccess:a,colorWarning:i,colorError:o,colorInfo:s,colorPrimary:l,colorBgBase:c,colorTextBase:u}=e,d=n(l),p=n(a),m=n(i),f=n(o),h=n(s),g=r(c,u),v=n(e.colorLink||e.colorInfo),y=new nS(f[1]).mix(new nS(f[3]),50).toHexString();return Object.assign(Object.assign({},g),{colorPrimaryBg:d[1],colorPrimaryBgHover:d[2],colorPrimaryBorder:d[3],colorPrimaryBorderHover:d[4],colorPrimaryHover:d[5],colorPrimary:d[6],colorPrimaryActive:d[7],colorPrimaryTextHover:d[8],colorPrimaryText:d[9],colorPrimaryTextActive:d[10],colorSuccessBg:p[1],colorSuccessBgHover:p[2],colorSuccessBorder:p[3],colorSuccessBorderHover:p[4],colorSuccessHover:p[4],colorSuccess:p[6],colorSuccessActive:p[7],colorSuccessTextHover:p[8],colorSuccessText:p[9],colorSuccessTextActive:p[10],colorErrorBg:f[1],colorErrorBgHover:f[2],colorErrorBgFilledHover:y,colorErrorBgActive:f[3],colorErrorBorder:f[3],colorErrorBorderHover:f[4],colorErrorHover:f[5],colorError:f[6],colorErrorActive:f[7],colorErrorTextHover:f[8],colorErrorText:f[9],colorErrorTextActive:f[10],colorWarningBg:m[1],colorWarningBgHover:m[2],colorWarningBorder:m[3],colorWarningBorderHover:m[4],colorWarningHover:m[4],colorWarning:m[6],colorWarningActive:m[7],colorWarningTextHover:m[8],colorWarningText:m[9],colorWarningTextActive:m[10],colorInfoBg:h[1],colorInfoBgHover:h[2],colorInfoBorder:h[3],colorInfoBorderHover:h[4],colorInfoHover:h[4],colorInfo:h[6],colorInfoActive:h[7],colorInfoTextHover:h[8],colorInfoText:h[9],colorInfoTextActive:h[10],colorLinkHover:v[4],colorLink:v[6],colorLinkActive:v[7],colorBgMask:new nS("#000").setA(.45).toRgbString(),colorWhite:"#fff"})}(e,{generateColorPalettes:re,generateNeutralColorPalettes:rt})),n8(e.fontSize)),function(e){let{sizeUnit:t,sizeStep:n}=e;return{sizeXXL:t*(n+8),sizeXL:t*(n+4),sizeLG:t*(n+2),sizeMD:t*(n+1),sizeMS:t*n,size:t*n,sizeSM:t*(n-1),sizeXS:t*(n-2),sizeXXS:t*(n-3)}}(e)),n6(e)),function(e){let{motionUnit:t,motionBase:n,borderRadius:r,lineWidth:a}=e;return Object.assign({motionDurationFast:`${(n+t).toFixed(1)}s`,motionDurationMid:`${(n+2*t).toFixed(1)}s`,motionDurationSlow:`${(n+3*t).toFixed(1)}s`,lineWidthBold:a+1},n3(r))}(e))}),rr={token:n5,override:{override:n5},hashed:!0},ra=b.createContext(rr),ri=`-ant-${Date.now()}-${Math.random()}`,ro=b.createContext(!1),rs=e=>{let{children:t,disabled:n}=e,r=b.useContext(ro);return b.createElement(ro.Provider,{value:null!=n?n:r},t)},rl=b.createContext(void 0),rc=e=>{let{children:t,size:n}=e,r=b.useContext(rl);return b.createElement(rl.Provider,{value:n||r},t)},{useId:ru}=Object.assign({},w),rd=void 0===ru?()=>"":ru;var rp=ed("../../node_modules/.pnpm/classnames@2.5.1/node_modules/classnames/index.js"),rm=ed.n(rp);function rf(e){return e instanceof HTMLElement||e instanceof SVGElement}var rh=ed("../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/index.js"),rg=Symbol.for("react.element"),rv=Symbol.for("react.transitional.element"),ry=Symbol.for("react.fragment"),rx=Number(b.version.split(".")[0]),rb=function(e,t){"function"==typeof e?e(t):"object"===eS(e)&&e&&"current"in e&&(e.current=t)},rw=function(e){if(!e)return!1;if(rj(e)&&rx>=19)return!0;var t,n,r=(0,rh.isMemo)(e)?e.type.type:e.type;return("function"!=typeof r||!!(null!=(t=r.prototype)&&t.render)||r.$$typeof===rh.ForwardRef)&&("function"!=typeof e||!!(null!=(n=e.prototype)&&n.render)||e.$$typeof===rh.ForwardRef)};function rj(e){return(0,b.isValidElement)(e)&&!(e&&"object"===eS(e)&&(e.$$typeof===rg||e.$$typeof===rv)&&e.type===ry)}var rS=["children"],rk=b.createContext({});function rC(e){var t=e.children,n=eD(e,rS);return b.createElement(rk.Provider,{value:n},t)}var rN=function(e){e6(n,e);var t=e7(n);function n(){return eV(this,n),t.apply(this,arguments)}return eJ(n,[{key:"render",value:function(){return this.props.children}}]),n}(b.Component);function rE(e){var t=b.useRef();return t.current=e,b.useCallback(function(){for(var e,n=arguments.length,r=Array(n),a=0;a<n;a++)r[a]=arguments[a];return null==(e=t.current)?void 0:e.call.apply(e,[t].concat(r))},[])}function rT(e){var t=b.useRef(!1),n=ej(b.useState(e),2),r=n[0],a=n[1];return b.useEffect(function(){return t.current=!1,function(){t.current=!0}},[]),[r,function(e,n){n&&t.current||a(e)}]}var r_="none",rA="appear",rP="enter",rM="leave",rI="none",rO="prepare",rL="start",rR="active",r$="prepared";function rF(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit".concat(e)]="webkit".concat(t),n["Moz".concat(e)]="moz".concat(t),n["ms".concat(e)]="MS".concat(t),n["O".concat(e)]="o".concat(t.toLowerCase()),n}var rz=(l=e_(),c="undefined"!=typeof window?window:{},u={animationend:rF("Animation","AnimationEnd"),transitionend:rF("Transition","TransitionEnd")},l&&("AnimationEvent"in c||delete u.animationend.animation,"TransitionEvent"in c||delete u.transitionend.transition),u),rD={};e_()&&(rD=document.createElement("div").style);var rH={};function rZ(e){if(rH[e])return rH[e];var t=rz[e];if(t)for(var n=Object.keys(t),r=n.length,a=0;a<r;a+=1){var i=n[a];if(Object.prototype.hasOwnProperty.call(t,i)&&i in rD)return rH[e]=t[i],rH[e]}return""}var rB=rZ("animationend"),rU=rZ("transitionend"),rK=!!(rB&&rU),rW=rB||"animationend",rq=rU||"transitionend";function rG(e,t){return e?"object"===eS(e)?e[t.replace(/-\w/g,function(e){return e[1].toUpperCase()})]:"".concat(e,"-").concat(t):null}let rV=function(e){var t=(0,b.useRef)();function n(t){t&&(t.removeEventListener(rq,e),t.removeEventListener(rW,e))}return b.useEffect(function(){return function(){n(t.current)}},[]),[function(r){t.current&&t.current!==r&&n(t.current),r&&r!==t.current&&(r.addEventListener(rq,e),r.addEventListener(rW,e),t.current=r)},n]};var rX=e_()?b.useLayoutEffect:b.useEffect,rJ=function(e){return+setTimeout(e,16)},rY=function(e){return clearTimeout(e)};"undefined"!=typeof window&&"requestAnimationFrame"in window&&(rJ=function(e){return window.requestAnimationFrame(e)},rY=function(e){return window.cancelAnimationFrame(e)});var rQ=0,r0=new Map,r1=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=rQ+=1;return!function t(r){if(0===r)r0.delete(n),e();else{var a=rJ(function(){t(r-1)});r0.set(n,a)}}(t),n};r1.cancel=function(e){var t=r0.get(e);return r0.delete(e),rY(t)};let r2=function(){var e=b.useRef(null);function t(){r1.cancel(e.current)}return b.useEffect(function(){return function(){t()}},[]),[function n(r){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;t();var i=r1(function(){a<=1?r({isCanceled:function(){return i!==e.current}}):n(r,a-1)});e.current=i},t]};var r4=[rO,rL,rR,"end"],r5=[rO,r$];function r3(e){return e===rR||"end"===e}let r6=function(e,t,n){var r=ej(rT(rI),2),a=r[0],i=r[1],o=ej(r2(),2),s=o[0],l=o[1],c=t?r5:r4;return rX(function(){if(a!==rI&&"end"!==a){var e=c.indexOf(a),t=c[e+1],r=n(a);!1===r?i(t,!0):t&&s(function(e){function n(){e.isCanceled()||i(t,!0)}!0===r?n():Promise.resolve(r).then(n)})}},[e,a]),b.useEffect(function(){return function(){l()}},[]),[function(){i(rO,!0)},a]},r8=(d=rK,"object"===eS(rK)&&(d=rK.transitionSupport),(p=b.forwardRef(function(e,t){var n,r=e.visible,a=void 0===r||r,i=e.removeOnLeave,o=void 0===i||i,s=e.forceRender,l=e.children,c=e.motionName,u=e.leavedClassName,p=e.eventProps,m=b.useContext(rk).motion,f=!!(e.motionName&&d&&!1!==m),h=(0,b.useRef)(),g=(0,b.useRef)(),v=function(e,t,n,r){var a,i,o=r.motionEnter,s=void 0===o||o,l=r.motionAppear,c=void 0===l||l,u=r.motionLeave,d=void 0===u||u,p=r.motionDeadline,m=r.motionLeaveImmediately,f=r.onAppearPrepare,h=r.onEnterPrepare,g=r.onLeavePrepare,v=r.onAppearStart,y=r.onEnterStart,x=r.onLeaveStart,w=r.onAppearActive,j=r.onEnterActive,S=r.onLeaveActive,k=r.onAppearEnd,C=r.onEnterEnd,N=r.onLeaveEnd,E=r.onVisibleChanged,T=ej(rT(),2),_=T[0],A=T[1],P=(a=ej(b.useReducer(function(e){return e+1},0),2)[1],i=b.useRef(r_),[rE(function(){return i.current}),rE(function(e){i.current="function"==typeof e?e(i.current):e,a()})]),M=ej(P,2),I=M[0],O=M[1],L=ej(rT(null),2),R=L[0],$=L[1],F=I(),z=(0,b.useRef)(!1),D=(0,b.useRef)(null),H=(0,b.useRef)(!1);function Z(){O(r_),$(null,!0)}var B=rE(function(e){var t,r=I();if(r!==r_){var a=n();if(!e||e.deadline||e.target===a){var i=H.current;r===rA&&i?t=null==k?void 0:k(a,e):r===rP&&i?t=null==C?void 0:C(a,e):r===rM&&i&&(t=null==N?void 0:N(a,e)),i&&!1!==t&&Z()}}}),U=ej(rV(B),1)[0],K=function(e){switch(e){case rA:return eC(eC(eC({},rO,f),rL,v),rR,w);case rP:return eC(eC(eC({},rO,h),rL,y),rR,j);case rM:return eC(eC(eC({},rO,g),rL,x),rR,S);default:return{}}},W=b.useMemo(function(){return K(F)},[F]),q=ej(r6(F,!e,function(e){if(e===rO){var t,r=W[rO];return!!r&&r(n())}return V in W&&$((null==(t=W[V])?void 0:t.call(W,n(),null))||null),V===rR&&F!==r_&&(U(n()),p>0&&(clearTimeout(D.current),D.current=setTimeout(function(){B({deadline:!0})},p))),V===r$&&Z(),!0}),2),G=q[0],V=q[1];H.current=r3(V);var X=(0,b.useRef)(null);rX(function(){if(!z.current||X.current!==t){A(t);var n,r=z.current;z.current=!0,!r&&t&&c&&(n=rA),r&&t&&s&&(n=rP),(r&&!t&&d||!r&&m&&!t&&d)&&(n=rM);var a=K(n);n&&(e||a[rO])?(O(n),G()):O(r_),X.current=t}},[t]),(0,b.useEffect)(function(){(F!==rA||c)&&(F!==rP||s)&&(F!==rM||d)||O(r_)},[c,s,d]),(0,b.useEffect)(function(){return function(){z.current=!1,clearTimeout(D.current)}},[]);var J=b.useRef(!1);(0,b.useEffect)(function(){_&&(J.current=!0),void 0!==_&&F===r_&&((J.current||_)&&(null==E||E(_)),J.current=!0)},[_,F]);var Y=R;return W[rO]&&V===rL&&(Y=eE({transition:"none"},Y)),[F,V,Y,null!=_?_:t]}(f,a,function(){try{var e,t,n,r;return h.current instanceof HTMLElement?h.current:(r=(t=e=g.current)&&"object"===eS(t)&&rf(t.nativeElement)?t.nativeElement:rf(t)?t:null)?r:e instanceof b.Component?null==(n=es.findDOMNode)?void 0:n.call(es,e):null}catch(e){return null}},e),y=ej(v,4),x=y[0],w=y[1],j=y[2],S=y[3],k=b.useRef(S);S&&(k.current=!0);var C=b.useCallback(function(e){h.current=e,rb(t,e)},[t]),N=eE(eE({},p),{},{visible:a});if(l)if(x===r_)E=S?l(eE({},N),C):!o&&k.current&&u?l(eE(eE({},N),{},{className:u}),C):!s&&(o||u)?null:l(eE(eE({},N),{},{style:{display:"none"}}),C);else{w===rO?T="prepare":r3(w)?T="active":w===rL&&(T="start");var E,T,_=rG(c,"".concat(x,"-").concat(T));E=l(eE(eE({},N),{},{className:rm()(rG(c,x),eC(eC({},_,_&&T),c,"string"==typeof c)),style:j}),C)}else E=null;return b.isValidElement(E)&&rw(E)&&(((n=E)&&rj(n)?n.props.propertyIsEnumerable("ref")?n.props.ref:n.ref:null)||(E=b.cloneElement(E,{ref:C}))),b.createElement(rN,{ref:g},E)})).displayName="CSSMotion",p);var r9="keep",r7="remove",ae="removed";function at(e){var t;return eE(eE({},t=e&&"object"===eS(e)&&"key"in e?e:{key:e}),{},{key:String(t.key)})}function an(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.map(at)}var ar=["component","children","onVisibleChanged","onAllRemoved"],aa=["status"],ai=["eventProps","visible","children","motionName","motionAppear","motionEnter","motionLeave","motionLeaveImmediately","motionDeadline","removeOnLeave","leavedClassName","onAppearPrepare","onAppearStart","onAppearActive","onAppearEnd","onEnterStart","onEnterActive","onEnterEnd","onLeaveStart","onLeaveActive","onLeaveEnd"];let ao=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r8,n=function(e){e6(r,e);var n=e7(r);function r(){var e;eV(this,r);for(var t=arguments.length,a=Array(t),i=0;i<t;i++)a[i]=arguments[i];return eC(e5(e=n.call.apply(n,[this].concat(a))),"state",{keyEntities:[]}),eC(e5(e),"removeKey",function(t){e.setState(function(e){return{keyEntities:e.keyEntities.map(function(e){return e.key!==t?e:eE(eE({},e),{},{status:ae})})}},function(){0===e.state.keyEntities.filter(function(e){return e.status!==ae}).length&&e.props.onAllRemoved&&e.props.onAllRemoved()})}),e}return eJ(r,[{key:"render",value:function(){var e=this,n=this.state.keyEntities,r=this.props,a=r.component,i=r.children,o=r.onVisibleChanged,s=(r.onAllRemoved,eD(r,ar)),l=a||b.Fragment,c={};return ai.forEach(function(e){c[e]=s[e],delete s[e]}),delete s.keys,b.createElement(l,s,n.map(function(n,r){var a=n.status,s=eD(n,aa);return b.createElement(t,tN({},c,{key:s.key,visible:"add"===a||a===r9,eventProps:s,onVisibleChanged:function(t){null==o||o(t,{key:s.key}),t||e.removeKey(s.key)}}),function(e,t){return i(eE(eE({},e),{},{index:r}),t)})}))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.keys,r=t.keyEntities;return{keyEntities:(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[],r=0,a=t.length,i=an(e),o=an(t);i.forEach(function(e){for(var t=!1,i=r;i<a;i+=1){var s=o[i];if(s.key===e.key){r<i&&(n=n.concat(o.slice(r,i).map(function(e){return eE(eE({},e),{},{status:"add"})})),r=i),n.push(eE(eE({},s),{},{status:r9})),r+=1,t=!0;break}}t||n.push(eE(eE({},e),{},{status:r7}))}),r<a&&(n=n.concat(o.slice(r).map(function(e){return eE(eE({},e),{},{status:"add"})})));var s={};return n.forEach(function(e){var t=e.key;s[t]=(s[t]||0)+1}),Object.keys(s).filter(function(e){return s[e]>1}).forEach(function(e){(n=n.filter(function(t){var n=t.key,r=t.status;return n!==e||r!==r7})).forEach(function(t){t.key===e&&(t.status=r9)})}),n})(r,an(n)).filter(function(e){var t=r.find(function(t){var n=t.key;return e.key===n});return!t||t.status!==ae||e.status!==r7})}}}]),r}(b.Component);return eC(n,"defaultProps",{component:"div"}),n}(rK);function as(e){return e>=0&&e<=255}let al=function(e,t){let{r:n,g:r,b:a,a:i}=new nS(e).toRgb();if(i<1)return e;let{r:o,g:s,b:l}=new nS(t).toRgb();for(let e=.01;e<=1;e+=.01){let t=Math.round((n-o*(1-e))/e),i=Math.round((r-s*(1-e))/e),c=Math.round((a-l*(1-e))/e);if(as(t)&&as(i)&&as(c))return new nS({r:t,g:i,b:c,a:Math.round(100*e)/100}).toRgbString()}return new nS({r:n,g:r,b:a,a:1}).toRgbString()};var ac=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};function au(e){let{override:t}=e,n=ac(e,["override"]),r=Object.assign({},t);Object.keys(n5).forEach(e=>{delete r[e]});let a=Object.assign(Object.assign({},n),r);return!1===a.motion&&(a.motionDurationFast="0s",a.motionDurationMid="0s",a.motionDurationSlow="0s"),Object.assign(Object.assign(Object.assign({},a),{colorFillContent:a.colorFillSecondary,colorFillContentHover:a.colorFill,colorFillAlter:a.colorFillQuaternary,colorBgContainerDisabled:a.colorFillTertiary,colorBorderBg:a.colorBgContainer,colorSplit:al(a.colorBorderSecondary,a.colorBgContainer),colorTextPlaceholder:a.colorTextQuaternary,colorTextDisabled:a.colorTextQuaternary,colorTextHeading:a.colorText,colorTextLabel:a.colorTextSecondary,colorTextDescription:a.colorTextTertiary,colorTextLightSolid:a.colorWhite,colorHighlight:a.colorError,colorBgTextHover:a.colorFillSecondary,colorBgTextActive:a.colorFill,colorIcon:a.colorTextTertiary,colorIconHover:a.colorText,colorErrorOutline:al(a.colorErrorBg,a.colorBgContainer),colorWarningOutline:al(a.colorWarningBg,a.colorBgContainer),fontSizeIcon:a.fontSizeSM,lineWidthFocus:3*a.lineWidth,lineWidth:a.lineWidth,controlOutlineWidth:2*a.lineWidth,controlInteractiveSize:a.controlHeight/2,controlItemBgHover:a.colorFillTertiary,controlItemBgActive:a.colorPrimaryBg,controlItemBgActiveHover:a.colorPrimaryBgHover,controlItemBgActiveDisabled:a.colorFill,controlTmpOutline:a.colorFillQuaternary,controlOutline:al(a.colorPrimaryBg,a.colorBgContainer),lineType:a.lineType,borderRadius:a.borderRadius,borderRadiusXS:a.borderRadiusXS,borderRadiusSM:a.borderRadiusSM,borderRadiusLG:a.borderRadiusLG,fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:a.sizeXXS,paddingXS:a.sizeXS,paddingSM:a.sizeSM,padding:a.size,paddingMD:a.sizeMD,paddingLG:a.sizeLG,paddingXL:a.sizeXL,paddingContentHorizontalLG:a.sizeLG,paddingContentVerticalLG:a.sizeMS,paddingContentHorizontal:a.sizeMS,paddingContentVertical:a.sizeSM,paddingContentHorizontalSM:a.size,paddingContentVerticalSM:a.sizeXS,marginXXS:a.sizeXXS,marginXS:a.sizeXS,marginSM:a.sizeSM,margin:a.size,marginMD:a.sizeMD,marginLG:a.sizeLG,marginXL:a.sizeXL,marginXXL:a.sizeXXL,boxShadow:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowSecondary:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowTertiary:`
      0 1px 2px 0 rgba(0, 0, 0, 0.03),
      0 1px 6px -1px rgba(0, 0, 0, 0.02),
      0 2px 4px 0 rgba(0, 0, 0, 0.02)
    `,screenXS:480,screenXSMin:480,screenXSMax:575,screenSM:576,screenSMMin:576,screenSMMax:767,screenMD:768,screenMDMin:768,screenMDMax:991,screenLG:992,screenLGMin:992,screenLGMax:1199,screenXL:1200,screenXLMin:1200,screenXLMax:1599,screenXXL:1600,screenXXLMin:1600,boxShadowPopoverArrow:"2px 2px 5px rgba(0, 0, 0, 0.05)",boxShadowCard:`
      0 1px 2px -2px ${new nS("rgba(0, 0, 0, 0.16)").toRgbString()},
      0 3px 6px 0 ${new nS("rgba(0, 0, 0, 0.12)").toRgbString()},
      0 5px 12px 4px ${new nS("rgba(0, 0, 0, 0.09)").toRgbString()}
    `,boxShadowDrawerRight:`
      -6px 0 16px 0 rgba(0, 0, 0, 0.08),
      -3px 0 6px -4px rgba(0, 0, 0, 0.12),
      -9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerLeft:`
      6px 0 16px 0 rgba(0, 0, 0, 0.08),
      3px 0 6px -4px rgba(0, 0, 0, 0.12),
      9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerUp:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerDown:`
      0 -6px 16px 0 rgba(0, 0, 0, 0.08),
      0 -3px 6px -4px rgba(0, 0, 0, 0.12),
      0 -9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)"}),r)}var ad=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};let ap={lineHeight:!0,lineHeightSM:!0,lineHeightLG:!0,lineHeightHeading1:!0,lineHeightHeading2:!0,lineHeightHeading3:!0,lineHeightHeading4:!0,lineHeightHeading5:!0,opacityLoading:!0,fontWeightStrong:!0,zIndexPopupBase:!0,zIndexBase:!0,opacityImage:!0},am={size:!0,sizeSM:!0,sizeLG:!0,sizeMD:!0,sizeXS:!0,sizeXXS:!0,sizeMS:!0,sizeXL:!0,sizeXXL:!0,sizeUnit:!0,sizeStep:!0,motionBase:!0,motionUnit:!0},af={screenXS:!0,screenXSMin:!0,screenXSMax:!0,screenSM:!0,screenSMMin:!0,screenSMMax:!0,screenMD:!0,screenMDMin:!0,screenMDMax:!0,screenLG:!0,screenLGMin:!0,screenLGMax:!0,screenXL:!0,screenXLMin:!0,screenXLMax:!0,screenXXL:!0,screenXXLMin:!0},ah=(e,t,n)=>{let r=n.getDerivativeToken(e),{override:a}=t,i=ad(t,["override"]),o=Object.assign(Object.assign({},r),{override:a});return o=au(o),i&&Object.entries(i).forEach(e=>{let[t,n]=e,{theme:r}=n,a=ad(n,["theme"]),i=a;r&&(i=ah(Object.assign(Object.assign({},o),a),{override:a},r)),o[t]=i}),o};function ag(){let{token:e,hashed:t,theme:n,override:r,cssVar:a}=b.useContext(ra),i=n||rn,[o,s,l]=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=(0,b.useContext)(e4),a=r.cache.instanceId,i=r.container,o=n.salt,s=void 0===o?"":o,l=n.override,c=void 0===l?tj:l,u=n.formatToken,d=n.getComputedToken,p=n.cssVar,m=function(e,t){for(var n=ti,r=0;r<t.length;r+=1){var a=t[r];n.has(a)||n.set(a,new WeakMap),n=n.get(a)}return n.has(to)||n.set(to,e()),n.get(to)}(function(){return Object.assign.apply(Object,[{}].concat(eh(t)))},t),f=tl(m),h=tl(c),g=p?tl(p):"";return tw(tC,[s,e.id,f,h,g],function(){var t,n=d?d(m,c,e):tk(m,c,e,u),r=eE({},n),a="";if(p){var i=ej(tf(n,p.key,{prefix:p.prefix,ignore:p.ignore,unitless:p.unitless,preserve:p.preserve}),2);n=i[0],a=i[1]}var o=tc(n,s);n._tokenKey=o,r._tokenKey=tc(r,s);var l=null!=(t=null==p?void 0:p.key)?t:o;n._themeKey=l,tS.set(l,(tS.get(l)||0)+1);var f="".concat("css","-").concat(eT(o));return n._hashId=f,[n,f,r,a,(null==p?void 0:p.key)||""]},function(e){var t,n,r;t=e[0]._themeKey,tS.set(t,(tS.get(t)||0)-1),r=(n=Array.from(tS.keys())).filter(function(e){return 0>=(tS.get(e)||0)}),n.length-r.length>0&&r.forEach(function(e){"undefined"!=typeof document&&document.querySelectorAll("style[".concat(e0,'="').concat(e,'"]')).forEach(function(e){if(e[e2]===a){var t;null==(t=e.parentNode)||t.removeChild(e)}}),tS.delete(e)})},function(e){var t=ej(e,4),n=t[0],r=t[3];if(p&&r){var o=ez(r,eT("css-variables-".concat(n._themeKey)),{mark:e1,prepend:"queue",attachTo:i,priority:-999});o[e2]=a,o.setAttribute(e0,n._themeKey)}})}(i,[n5,e],{salt:`5.24.8-${t||""}`,override:r,getComputedToken:ah,formatToken:au,cssVar:a&&{prefix:a.prefix,key:a.key,unitless:ap,ignore:am,preserve:af}});return[i,l,t?s:"",o,a]}function av(e){let{children:t}=e,[,n]=ag(),{motion:r}=n,a=b.useRef(!1);return(a.current=a.current||!1===r,a.current)?b.createElement(rC,{motion:r},t):t}let ay=()=>null,ax=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return{boxSizing:"border-box",margin:0,padding:0,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,listStyle:"none",fontFamily:t?"inherit":e.fontFamily}},ab=()=>({display:"inline-flex",alignItems:"center",color:"inherit",fontStyle:"normal",lineHeight:0,textAlign:"center",textTransform:"none",verticalAlign:"-0.125em",textRendering:"optimizeLegibility","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale","> *":{lineHeight:1},svg:{display:"inline-block"}}),aw=e=>({a:{color:e.colorLink,textDecoration:e.linkDecoration,backgroundColor:"transparent",outline:"none",cursor:"pointer",transition:`color ${e.motionDurationSlow}`,"-webkit-text-decoration-skip":"objects","&:hover":{color:e.colorLinkHover},"&:active":{color:e.colorLinkActive},"&:active, &:hover":{textDecoration:e.linkHoverDecoration,outline:0},"&:focus":{textDecoration:e.linkFocusDecoration,outline:0},"&[disabled]":{color:e.colorTextDisabled,cursor:"not-allowed"}}}),aj=e=>({[`.${e}`]:Object.assign(Object.assign({},ab()),{[`.${e} .${e}-icon`]:{display:"block"}})}),aS=(e,t)=>{let[n,r]=ag();return t7({theme:n,token:r,hashId:"",path:["ant-design-icons",e],nonce:()=>null==t?void 0:t.nonce,layer:{name:"antd"}},()=>[aj(e)])};var ak=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};let aC=["getTargetContainer","getPopupContainer","renderEmpty","input","pagination","form","select","button"];function aN(){return r||"ant"}function aE(){return a||ev}let aT=()=>({getPrefixCls:(e,t)=>t||(e?`${aN()}-${e}`:aN()),getIconPrefixCls:aE,getRootPrefixCls:()=>r||aN(),getTheme:()=>i,holderRender:o}),a_=e=>{let{children:t,csp:n,autoInsertSpaceInButton:r,alert:a,anchor:i,form:o,locale:s,componentSize:l,direction:c,space:u,splitter:d,virtual:p,dropdownMatchSelectWidth:m,popupMatchSelectWidth:f,popupOverflow:h,legacyLocale:g,parentContext:v,iconPrefixCls:y,theme:x,componentDisabled:w,segmented:j,statistic:S,spin:k,calendar:C,carousel:N,cascader:E,collapse:T,typography:_,checkbox:A,descriptions:P,divider:M,drawer:I,skeleton:O,steps:L,image:R,layout:$,list:F,mentions:z,modal:D,progress:H,result:Z,slider:B,breadcrumb:U,menu:K,pagination:W,input:q,textArea:G,empty:V,badge:X,radio:J,rate:Y,switch:Q,transfer:ee,avatar:et,message:en,tag:er,table:ea,card:ei,tabs:eo,timeline:es,timePicker:el,upload:ec,notification:eu,tree:ed,colorPicker:ep,datePicker:em,rangePicker:ef,flex:eg,wave:ex,dropdown:eb,warning:ew,tour:ej,tooltip:ek,popover:eC,popconfirm:eN,floatButtonGroup:eE,variant:eT,inputNumber:e_,treeSelect:eA}=e,eP=b.useCallback((t,n)=>{let{prefixCls:r}=e;if(n)return n;let a=r||v.getPrefixCls("");return t?`${a}-${t}`:a},[v.getPrefixCls,e.prefixCls]),eM=y||v.iconPrefixCls||ev,eI=n||v.csp;aS(eM,eI);let eO=function(e,t,n){var r;nd("ConfigProvider");let a=e||{},i=!1!==a.inherit&&t?t:Object.assign(Object.assign({},rr),{hashed:null!=(r=null==t?void 0:t.hashed)?r:rr.hashed,cssVar:null==t?void 0:t.cssVar}),o=rd();return eH(()=>{var r,s;if(!e)return t;let l=Object.assign({},i.components);Object.keys(e.components||{}).forEach(t=>{l[t]=Object.assign(Object.assign({},l[t]),e.components[t])});let c=`css-var-${o.replace(/:/g,"")}`,u=(null!=(r=a.cssVar)?r:i.cssVar)&&Object.assign(Object.assign(Object.assign({prefix:null==n?void 0:n.prefixCls},"object"==typeof i.cssVar?i.cssVar:{}),"object"==typeof a.cssVar?a.cssVar:{}),{key:"object"==typeof a.cssVar&&(null==(s=a.cssVar)?void 0:s.key)||c});return Object.assign(Object.assign(Object.assign({},i),a),{token:Object.assign(Object.assign({},i.token),a.token),components:l,cssVar:u})},[a,i],(e,t)=>e.some((e,n)=>!eG(e,t[n],!0)))}(x,v.theme,{prefixCls:eP("")}),eL={csp:eI,autoInsertSpaceInButton:r,alert:a,anchor:i,locale:s||g,direction:c,space:u,splitter:d,virtual:p,popupMatchSelectWidth:null!=f?f:m,popupOverflow:h,getPrefixCls:eP,iconPrefixCls:eM,theme:eO,segmented:j,statistic:S,spin:k,calendar:C,carousel:N,cascader:E,collapse:T,typography:_,checkbox:A,descriptions:P,divider:M,drawer:I,skeleton:O,steps:L,image:R,input:q,textArea:G,layout:$,list:F,mentions:z,modal:D,progress:H,result:Z,slider:B,breadcrumb:U,menu:K,pagination:W,empty:V,badge:X,radio:J,rate:Y,switch:Q,transfer:ee,avatar:et,message:en,tag:er,table:ea,card:ei,tabs:eo,timeline:es,timePicker:el,upload:ec,notification:eu,tree:ed,colorPicker:ep,datePicker:em,rangePicker:ef,flex:eg,wave:ex,dropdown:eb,warning:ew,tour:ej,tooltip:ek,popover:eC,popconfirm:eN,floatButtonGroup:eE,variant:eT,inputNumber:e_,treeSelect:eA},eR=Object.assign({},v);Object.keys(eL).forEach(e=>{void 0!==eL[e]&&(eR[e]=eL[e])}),aC.forEach(t=>{let n=e[t];n&&(eR[t]=n)}),void 0!==r&&(eR.button=Object.assign({autoInsertSpace:r},eR.button));let e$=eH(()=>eR,eR,(e,t)=>{let n=Object.keys(e),r=Object.keys(t);return n.length!==r.length||n.some(n=>e[n]!==t[n])}),{layer:eF}=b.useContext(e4),ez=b.useMemo(()=>({prefixCls:eM,csp:eI,layer:eF?"antd":void 0}),[eM,eI,eF]),eD=b.createElement(b.Fragment,null,b.createElement(ay,{dropdownMatchSelectWidth:m}),t),eZ=b.useMemo(()=>{var e,t,n,r;return function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=ns(t[0]);return t.forEach(function(e){!function t(n,a){var i=new Set(a),o=ni(e,n),s=Array.isArray(o);if(s||"object"===eS(o)&&null!==o&&Object.getPrototypeOf(o)===Object.prototype){if(!i.has(o)){i.add(o);var l=ni(r,n);s?r=no(r,n,[]):l&&"object"===eS(l)||(r=no(r,n,ns(o))),nl(o).forEach(function(e){t([].concat(eh(n),[e]),i)})}}else r=no(r,n,o)}([])}),r}((null==(e=nf.Form)?void 0:e.defaultValidateMessages)||{},(null==(n=null==(t=e$.locale)?void 0:t.Form)?void 0:n.defaultValidateMessages)||{},(null==(r=e$.form)?void 0:r.validateMessages)||{},(null==o?void 0:o.validateMessages)||{})},[e$,null==o?void 0:o.validateMessages]);Object.keys(eZ).length>0&&(eD=b.createElement(np.Provider,{value:eZ},eD)),s&&(eD=b.createElement(ny,{locale:s,_ANT_MARK__:"internalMark"},eD)),(eM||eI)&&(eD=b.createElement(na.Provider,{value:ez},eD)),l&&(eD=b.createElement(rc,{size:l},eD)),eD=b.createElement(av,null,eD);let eB=b.useMemo(()=>{let e=eO||{},{algorithm:t,token:n,components:r,cssVar:a}=e,i=ak(e,["algorithm","token","components","cssVar"]),o=t&&(!Array.isArray(t)||t.length>0)?ta(t):rn,s={};Object.entries(r||{}).forEach(e=>{let[t,n]=e,r=Object.assign({},n);"algorithm"in r&&(!0===r.algorithm?r.theme=o:(Array.isArray(r.algorithm)||"function"==typeof r.algorithm)&&(r.theme=ta(r.algorithm)),delete r.algorithm),s[t]=r});let l=Object.assign(Object.assign({},n5),n);return Object.assign(Object.assign({},i),{theme:o,token:l,components:s,override:Object.assign({override:l},s),cssVar:a})},[eO]);return x&&(eD=b.createElement(ra.Provider,{value:eB},eD)),e$.warning&&(eD=b.createElement(nu.Provider,{value:e$.warning},eD)),void 0!==w&&(eD=b.createElement(rs,{disabled:w},eD)),b.createElement(ey.Provider,{value:e$},eD)},aA=e=>{let t=b.useContext(ey),n=b.useContext(nv);return b.createElement(a_,Object.assign({parentContext:t,legacyLocale:n},e))};function aP(){aP=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(t,n,r,i){var o,s,l,c,u=Object.create((n&&n.prototype instanceof g?n:g).prototype);return a(u,"_invoke",{value:(o=t,s=r,l=new E(i||[]),c=p,function(t,n){if(c===m)throw Error("Generator is already running");if(c===f){if("throw"===t)throw n;return{value:e,done:!0}}for(l.method=t,l.arg=n;;){var r=l.delegate;if(r){var a=function t(n,r){var a=r.method,i=n.iterator[a];if(i===e)return r.delegate=null,"throw"===a&&n.iterator.return&&(r.method="return",r.arg=e,t(n,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+a+"' method")),h;var o=d(i,n.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,h;var s=o.arg;return s?s.done?(r[n.resultName]=s.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,h):s:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,h)}(r,l);if(a){if(a===h)continue;return a}}if("next"===l.method)l.sent=l._sent=l.arg;else if("throw"===l.method){if(c===p)throw c=f,l.arg;l.dispatchException(l.arg)}else"return"===l.method&&l.abrupt("return",l.arg);c=m;var i=d(o,s,l);if("normal"===i.type){if(c=l.done?f:"suspendedYield",i.arg===h)continue;return{value:i.arg,done:l.done}}"throw"===i.type&&(c=f,l.method="throw",l.arg=i.arg)}})}),u}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var p="suspendedStart",m="executing",f="completed",h={};function g(){}function v(){}function y(){}var x={};c(x,o,function(){return this});var b=Object.getPrototypeOf,w=b&&b(b(T([])));w&&w!==n&&r.call(w,o)&&(x=w);var j=y.prototype=g.prototype=Object.create(x);function S(e){["next","throw","return"].forEach(function(t){c(e,t,function(e){return this._invoke(t,e)})})}function k(e,t){var n;a(this,"_invoke",{value:function(a,i){function o(){return new t(function(n,o){!function n(a,i,o,s){var l=d(e[a],e,i);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==eS(u)&&r.call(u,"__await")?t.resolve(u.__await).then(function(e){n("next",e,o,s)},function(e){n("throw",e,o,s)}):t.resolve(u).then(function(e){c.value=e,o(c)},function(e){return n("throw",e,o,s)})}s(l.arg)}(a,i,n,o)})}return n=n?n.then(o,o):o()}})}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function T(t){if(t||""===t){var n=t[o];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw TypeError(eS(t)+" is not iterable")}return v.prototype=y,a(j,"constructor",{value:y,configurable:!0}),a(y,"constructor",{value:v,configurable:!0}),v.displayName=c(y,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,c(e,l,"GeneratorFunction")),e.prototype=Object.create(j),e},t.awrap=function(e){return{__await:e}},S(k.prototype),c(k.prototype,s,function(){return this}),t.AsyncIterator=k,t.async=function(e,n,r,a,i){void 0===i&&(i=Promise);var o=new k(u(e,n,r,a),i);return t.isGeneratorFunction(n)?o:o.next().then(function(e){return e.done?e.value:o.next()})},S(j),c(j,l,"Generator"),c(j,o,function(){return this}),c(j,"toString",function(){return"[object Generator]"}),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=T,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(r,a){return s.type="throw",s.arg=t,n.next=r,a&&(n.method="next",n.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),N(n),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;N(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:T(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),h}},t}function aM(e,t,n,r,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,a)}function aI(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function o(e){aM(i,r,a,o,s,"next",e)}function s(e){aM(i,r,a,o,s,"throw",e)}o(void 0)})}}aA.ConfigContext=ey,aA.SizeContext=rl,aA.config=e=>{let{prefixCls:t,iconPrefixCls:n,theme:s,holderRender:l}=e;if(void 0!==t&&(r=t),void 0!==n&&(a=n),"holderRender"in e&&(o=l),s)if(Object.keys(s).some(e=>e.endsWith("Color"))){let e=function(e,t){let n={},r=(e,t)=>{let n=e.clone();return(n=(null==t?void 0:t(n))||n).toRgbString()},a=(e,t)=>{let a=new nS(e),i=nT(a.toRgbString());n[`${t}-color`]=r(a),n[`${t}-color-disabled`]=i[1],n[`${t}-color-hover`]=i[4],n[`${t}-color-active`]=i[6],n[`${t}-color-outline`]=a.clone().setA(.2).toRgbString(),n[`${t}-color-deprecated-bg`]=i[0],n[`${t}-color-deprecated-border`]=i[2]};if(t.primaryColor){a(t.primaryColor,"primary");let e=new nS(t.primaryColor),i=nT(e.toRgbString());i.forEach((e,t)=>{n[`primary-${t+1}`]=e}),n["primary-color-deprecated-l-35"]=r(e,e=>e.lighten(35)),n["primary-color-deprecated-l-20"]=r(e,e=>e.lighten(20)),n["primary-color-deprecated-t-20"]=r(e,e=>e.tint(20)),n["primary-color-deprecated-t-50"]=r(e,e=>e.tint(50)),n["primary-color-deprecated-f-12"]=r(e,e=>e.setA(.12*e.a));let o=new nS(i[0]);n["primary-color-active-deprecated-f-30"]=r(o,e=>e.setA(.3*e.a)),n["primary-color-active-deprecated-d-02"]=r(o,e=>e.darken(2))}t.successColor&&a(t.successColor,"success"),t.warningColor&&a(t.warningColor,"warning"),t.errorColor&&a(t.errorColor,"error"),t.infoColor&&a(t.infoColor,"info");let i=Object.keys(n).map(t=>`--${e}-${t}: ${n[t]};`);return`
  :root {
    ${i.join("\n")}
  }
  `.trim()}(aN(),s);e_()&&ez(e,`${ri}-dynamic-theme`)}else i=s},aA.useConfig=function(){return{componentDisabled:(0,b.useContext)(ro),componentSize:(0,b.useContext)(rl)}},Object.defineProperty(aA,"SizeContext",{get:()=>rl});var aO=eE({},el),aL=aO.version,aR=aO.render,a$=aO.unmountComponentAtNode;try{Number((aL||"").split(".")[0])>=18&&(v=aO.createRoot)}catch(e){}function aF(e){var t=aO.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;t&&"object"===eS(t)&&(t.usingClientEntryPoint=e)}var az="__rc_react_root__";function aD(){return(aD=aI(aP().mark(function e(t){return aP().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",Promise.resolve().then(function(){var e;null==(e=t[az])||e.unmount(),delete t[az]}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function aH(){return(aH=aI(aP().mark(function e(t){return aP().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===v){e.next=2;break}return e.abrupt("return",function(e){return aD.apply(this,arguments)}(t));case 2:a$(t);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}let aZ=(e,t)=>(!function(e,t){var n;if(v)return aF(!0),n=t[az]||v(t),aF(!1),n.render(e),t[az]=n;null==aR||aR(e,t)}(e,t),()=>(function(e){return aH.apply(this,arguments)})(t)),aB={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"}}]},name:"check-circle",theme:"filled"};function aU(e){var t;return null==e||null==(t=e.getRootNode)?void 0:t.call(e)}function aK(e){return"object"===eS(e)&&"string"==typeof e.name&&"string"==typeof e.theme&&("object"===eS(e.icon)||"function"==typeof e.icon)}function aW(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(e).reduce(function(t,n){var r=e[n];return"class"===n?(t.className=r,delete t.class):(delete t[n],t[n.replace(/-(.)/g,function(e,t){return t.toUpperCase()})]=r),t},{})}function aq(e){return e?Array.isArray(e)?e:[e]:[]}var aG=function(e){var t=(0,b.useContext)(na),n=t.csp,r=t.prefixCls,a=t.layer,i="\n.anticon {\n  display: inline-flex;\n  align-items: center;\n  color: inherit;\n  font-style: normal;\n  line-height: 0;\n  text-align: center;\n  text-transform: none;\n  vertical-align: -0.125em;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.anticon > * {\n  line-height: 1;\n}\n\n.anticon svg {\n  display: inline-block;\n}\n\n.anticon::before {\n  display: none;\n}\n\n.anticon .anticon-icon {\n  display: block;\n}\n\n.anticon[tabindex] {\n  cursor: pointer;\n}\n\n.anticon-spin::before,\n.anticon-spin {\n  display: inline-block;\n  -webkit-animation: loadingCircle 1s infinite linear;\n  animation: loadingCircle 1s infinite linear;\n}\n\n@-webkit-keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n";r&&(i=i.replace(/anticon/g,r)),a&&(i="@layer ".concat(a," {\n").concat(i,"\n}")),(0,b.useEffect)(function(){var t,r=aU(t=e.current)instanceof ShadowRoot?aU(t):null;ez(i,"@ant-design-icons",{prepend:!a,csp:n,attachTo:r})},[])},aV=["icon","className","onClick","style","primaryColor","secondaryColor"],aX={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1},aJ=function(e){var t,n,r=e.icon,a=e.className,i=e.onClick,o=e.style,s=e.primaryColor,l=e.secondaryColor,c=eD(e,aV),u=b.useRef(),d=aX;if(s&&(d={primaryColor:s,secondaryColor:l||nT(s)[0]}),aG(u),t=aK(r),n="icon should be icon definiton, but got ".concat(r),eq(t,"[@ant-design/icons] ".concat(n)),!aK(r))return null;var p=r;return p&&"function"==typeof p.icon&&(p=eE(eE({},p),{},{icon:p.icon(d.primaryColor,d.secondaryColor)})),function e(t,n,r){return r?b.createElement(t.tag,eE(eE({key:n},aW(t.attrs)),r),(t.children||[]).map(function(r,a){return e(r,"".concat(n,"-").concat(t.tag,"-").concat(a))})):b.createElement(t.tag,eE({key:n},aW(t.attrs)),(t.children||[]).map(function(r,a){return e(r,"".concat(n,"-").concat(t.tag,"-").concat(a))}))}(p.icon,"svg-".concat(p.name),eE(eE({className:a,onClick:i,style:o,"data-icon":p.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},c),{},{ref:u}))};function aY(e){var t=ej(aq(e),2),n=t[0],r=t[1];return aJ.setTwoToneColors({primaryColor:n,secondaryColor:r})}aJ.displayName="IconReact",aJ.getTwoToneColors=function(){return eE({},aX)},aJ.setTwoToneColors=function(e){var t=e.primaryColor,n=e.secondaryColor;aX.primaryColor=t,aX.secondaryColor=n||nT(t)[0],aX.calculated=!!n};var aQ=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];aY(nF.primary);var a0=b.forwardRef(function(e,t){var n=e.className,r=e.icon,a=e.spin,i=e.rotate,o=e.tabIndex,s=e.onClick,l=e.twoToneColor,c=eD(e,aQ),u=b.useContext(na),d=u.prefixCls,p=void 0===d?"anticon":d,m=u.rootClassName,f=rm()(m,p,eC(eC({},"".concat(p,"-").concat(r.name),!!r.name),"".concat(p,"-spin"),!!a||"loading"===r.name),n),h=o;void 0===h&&s&&(h=-1);var g=ej(aq(l),2),v=g[0],y=g[1];return b.createElement("span",tN({role:"img","aria-label":r.name},c,{ref:t,tabIndex:h,onClick:s,className:f}),b.createElement(aJ,{icon:r,primaryColor:v,secondaryColor:y,style:i?{msTransform:"rotate(".concat(i,"deg)"),transform:"rotate(".concat(i,"deg)")}:void 0}))});a0.displayName="AntdIcon",a0.getTwoToneColor=function(){var e=aJ.getTwoToneColors();return e.calculated?[e.primaryColor,e.secondaryColor]:e.primaryColor},a0.setTwoToneColor=aY;var a1=b.forwardRef(function(e,t){return b.createElement(a0,tN({},e,{ref:t,icon:aB}))});let a2={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"}}]},name:"close-circle",theme:"filled"};var a4=b.forwardRef(function(e,t){return b.createElement(a0,tN({},e,{ref:t,icon:a2}))});let a5={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"exclamation-circle",theme:"filled"};var a3=b.forwardRef(function(e,t){return b.createElement(a0,tN({},e,{ref:t,icon:a5}))});let a6={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"info-circle",theme:"filled"};var a8=b.forwardRef(function(e,t){return b.createElement(a0,tN({},e,{ref:t,icon:a6}))});let a9={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"}}]},name:"loading",theme:"outlined"};var a7=b.forwardRef(function(e,t){return b.createElement(a0,tN({},e,{ref:t,icon:a9}))}),ie={MAC_ENTER:3,BACKSPACE:8,TAB:9,NUM_CENTER:12,ENTER:13,SHIFT:16,CTRL:17,ALT:18,PAUSE:19,CAPS_LOCK:20,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,PRINT_SCREEN:44,INSERT:45,DELETE:46,ZERO:48,ONE:49,TWO:50,THREE:51,FOUR:52,FIVE:53,SIX:54,SEVEN:55,EIGHT:56,NINE:57,QUESTION_MARK:63,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,META:91,WIN_KEY_RIGHT:92,CONTEXT_MENU:93,NUM_ZERO:96,NUM_ONE:97,NUM_TWO:98,NUM_THREE:99,NUM_FOUR:100,NUM_FIVE:101,NUM_SIX:102,NUM_SEVEN:103,NUM_EIGHT:104,NUM_NINE:105,NUM_MULTIPLY:106,NUM_PLUS:107,NUM_MINUS:109,NUM_PERIOD:110,NUM_DIVISION:111,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,NUMLOCK:144,SEMICOLON:186,DASH:189,EQUALS:187,COMMA:188,PERIOD:190,SLASH:191,APOSTROPHE:192,SINGLE_QUOTE:222,OPEN_SQUARE_BRACKET:219,BACKSLASH:220,CLOSE_SQUARE_BRACKET:221,WIN_KEY:224,MAC_FF_META:224,WIN_IME:229,isTextModifyingKeyEvent:function(e){var t=e.keyCode;if(e.altKey&&!e.ctrlKey||e.metaKey||t>=ie.F1&&t<=ie.F12)return!1;switch(t){case ie.ALT:case ie.CAPS_LOCK:case ie.CONTEXT_MENU:case ie.CTRL:case ie.DOWN:case ie.END:case ie.ESC:case ie.HOME:case ie.INSERT:case ie.LEFT:case ie.MAC_FF_META:case ie.META:case ie.NUMLOCK:case ie.NUM_CENTER:case ie.PAGE_DOWN:case ie.PAGE_UP:case ie.PAUSE:case ie.PRINT_SCREEN:case ie.RIGHT:case ie.SHIFT:case ie.UP:case ie.WIN_KEY:case ie.WIN_KEY_RIGHT:return!1;default:return!0}},isCharacterKey:function(e){if(e>=ie.ZERO&&e<=ie.NINE||e>=ie.NUM_ZERO&&e<=ie.NUM_MULTIPLY||e>=ie.A&&e<=ie.Z||-1!==window.navigator.userAgent.indexOf("WebKit")&&0===e)return!0;switch(e){case ie.SPACE:case ie.QUESTION_MARK:case ie.NUM_PLUS:case ie.NUM_MINUS:case ie.NUM_PERIOD:case ie.NUM_DIVISION:case ie.SEMICOLON:case ie.DASH:case ie.EQUALS:case ie.COMMA:case ie.PERIOD:case ie.SLASH:case ie.APOSTROPHE:case ie.SINGLE_QUOTE:case ie.OPEN_SQUARE_BRACKET:case ie.BACKSLASH:case ie.CLOSE_SQUARE_BRACKET:return!0;default:return!1}}},it="".concat("accept acceptCharset accessKey action allowFullScreen allowTransparency\n    alt async autoComplete autoFocus autoPlay capture cellPadding cellSpacing challenge\n    charSet checked classID className colSpan cols content contentEditable contextMenu\n    controls coords crossOrigin data dateTime default defer dir disabled download draggable\n    encType form formAction formEncType formMethod formNoValidate formTarget frameBorder\n    headers height hidden high href hrefLang htmlFor httpEquiv icon id inputMode integrity\n    is keyParams keyType kind label lang list loop low manifest marginHeight marginWidth max maxLength media\n    mediaGroup method min minLength multiple muted name noValidate nonce open\n    optimum pattern placeholder poster preload radioGroup readOnly rel required\n    reversed role rowSpan rows sandbox scope scoped scrolling seamless selected\n    shape size sizes span spellCheck src srcDoc srcLang srcSet start step style\n    summary tabIndex target title type useMap value width wmode wrap"," ").concat("onCopy onCut onPaste onCompositionEnd onCompositionStart onCompositionUpdate onKeyDown\n    onKeyPress onKeyUp onFocus onBlur onChange onInput onSubmit onClick onContextMenu onDoubleClick\n    onDrag onDragEnd onDragEnter onDragExit onDragLeave onDragOver onDragStart onDrop onMouseDown\n    onMouseEnter onMouseLeave onMouseMove onMouseOut onMouseOver onMouseUp onSelect onTouchCancel\n    onTouchEnd onTouchMove onTouchStart onScroll onWheel onAbort onCanPlay onCanPlayThrough\n    onDurationChange onEmptied onEncrypted onEnded onError onLoadedData onLoadedMetadata\n    onLoadStart onPause onPlay onPlaying onProgress onRateChange onSeeked onSeeking onStalled onSuspend onTimeUpdate onVolumeChange onWaiting onLoad onError").split(/[\s\n]+/);function ir(e,t){return 0===e.indexOf(t)}var ia=b.forwardRef(function(e,t){var n=e.prefixCls,r=e.style,a=e.className,i=e.duration,o=void 0===i?4.5:i,s=e.showProgress,l=e.pauseOnHover,c=void 0===l||l,u=e.eventKey,d=e.content,p=e.closable,m=e.closeIcon,f=void 0===m?"x":m,h=e.props,g=e.onClick,v=e.onNoticeClose,y=e.times,x=e.hovering,w=ej(b.useState(!1),2),j=w[0],S=w[1],k=ej(b.useState(0),2),C=k[0],N=k[1],E=ej(b.useState(0),2),T=E[0],_=E[1],A=x||j,P=o>0&&s,M=function(){v(u)};b.useEffect(function(){if(!A&&o>0){var e=Date.now()-T,t=setTimeout(function(){M()},1e3*o-T);return function(){c&&clearTimeout(t),_(Date.now()-e)}}},[o,A,y]),b.useEffect(function(){if(!A&&P&&(c||0===T)){var e,t=performance.now();return!function n(){cancelAnimationFrame(e),e=requestAnimationFrame(function(e){var r=Math.min((e+T-t)/(1e3*o),1);N(100*r),r<1&&n()})}(),function(){c&&cancelAnimationFrame(e)}}},[o,T,A,P,y]);var I=b.useMemo(function(){return"object"===eS(p)&&null!==p?p:p?{closeIcon:f}:{}},[p,f]),O=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t=!1===n?{aria:!0,data:!0,attr:!0}:!0===n?{aria:!0}:eE({},n);var r={};return Object.keys(e).forEach(function(n){(t.aria&&("role"===n||ir(n,"aria-"))||t.data&&ir(n,"data-")||t.attr&&it.includes(n))&&(r[n]=e[n])}),r}(I,!0),L=100-(!C||C<0?0:C>100?100:C),R="".concat(n,"-notice");return b.createElement("div",tN({},h,{ref:t,className:rm()(R,a,eC({},"".concat(R,"-closable"),p)),style:r,onMouseEnter:function(e){var t;S(!0),null==h||null==(t=h.onMouseEnter)||t.call(h,e)},onMouseLeave:function(e){var t;S(!1),null==h||null==(t=h.onMouseLeave)||t.call(h,e)},onClick:g}),b.createElement("div",{className:"".concat(R,"-content")},d),p&&b.createElement("a",tN({tabIndex:0,className:"".concat(R,"-close"),onKeyDown:function(e){("Enter"===e.key||"Enter"===e.code||e.keyCode===ie.ENTER)&&M()},"aria-label":"Close"},O,{onClick:function(e){e.preventDefault(),e.stopPropagation(),M()}}),I.closeIcon),P&&b.createElement("progress",{className:"".concat(R,"-progress"),max:"100",value:L},L+"%"))}),ii=b.createContext({});let io=function(e){var t=e.children,n=e.classNames;return b.createElement(ii.Provider,{value:{classNames:n}},t)},is=function(e){var t,n,r,a={offset:8,threshold:3,gap:16};return e&&"object"===eS(e)&&(a.offset=null!=(t=e.offset)?t:8,a.threshold=null!=(n=e.threshold)?n:3,a.gap=null!=(r=e.gap)?r:16),[!!e,a]};var il=["className","style","classNames","styles"];let ic=function(e){var t=e.configList,n=e.placement,r=e.prefixCls,a=e.className,i=e.style,o=e.motion,s=e.onAllNoticeRemoved,l=e.onNoticeClose,c=e.stack,u=(0,b.useContext)(ii).classNames,d=(0,b.useRef)({}),p=ej((0,b.useState)(null),2),m=p[0],f=p[1],h=ej((0,b.useState)([]),2),g=h[0],v=h[1],y=t.map(function(e){return{config:e,key:String(e.key)}}),x=ej(is(c),2),w=x[0],j=x[1],S=j.offset,k=j.threshold,C=j.gap,N=w&&(g.length>0||y.length<=k),E="function"==typeof o?o(n):o;return(0,b.useEffect)(function(){w&&g.length>1&&v(function(e){return e.filter(function(e){return y.some(function(t){return e===t.key})})})},[g,y,w]),(0,b.useEffect)(function(){var e,t;w&&d.current[null==(e=y[y.length-1])?void 0:e.key]&&f(d.current[null==(t=y[y.length-1])?void 0:t.key])},[y,w]),b.createElement(ao,tN({key:n,className:rm()(r,"".concat(r,"-").concat(n),null==u?void 0:u.list,a,eC(eC({},"".concat(r,"-stack"),!!w),"".concat(r,"-stack-expanded"),N)),style:i,keys:y,motionAppear:!0},E,{onAllRemoved:function(){s(n)}}),function(e,t){var a=e.config,i=e.className,o=e.style,s=e.index,c=a.key,p=a.times,f=String(c),h=a.className,x=a.style,j=a.classNames,k=a.styles,E=eD(a,il),T=y.findIndex(function(e){return e.key===f}),_={};if(w){var A=y.length-1-(T>-1?T:s-1),P="top"===n||"bottom"===n?"-50%":"0";if(A>0){_.height=N?null==(M=d.current[f])?void 0:M.offsetHeight:null==m?void 0:m.offsetHeight;for(var M,I,O,L,R=0,$=0;$<A;$++)R+=(null==(L=d.current[y[y.length-1-$].key])?void 0:L.offsetHeight)+C;var F=(N?R:A*S)*(n.startsWith("top")?1:-1),z=!N&&null!=m&&m.offsetWidth&&null!=(I=d.current[f])&&I.offsetWidth?((null==m?void 0:m.offsetWidth)-2*S*(A<3?A:3))/(null==(O=d.current[f])?void 0:O.offsetWidth):1;_.transform="translate3d(".concat(P,", ").concat(F,"px, 0) scaleX(").concat(z,")")}else _.transform="translate3d(".concat(P,", 0, 0)")}return b.createElement("div",{ref:t,className:rm()("".concat(r,"-notice-wrapper"),i,null==j?void 0:j.wrapper),style:eE(eE(eE({},o),_),null==k?void 0:k.wrapper),onMouseEnter:function(){return v(function(e){return e.includes(f)?e:[].concat(eh(e),[f])})},onMouseLeave:function(){return v(function(e){return e.filter(function(e){return e!==f})})}},b.createElement(ia,tN({},E,{ref:function(e){T>-1?d.current[f]=e:delete d.current[f]},prefixCls:r,classNames:j,styles:k,className:rm()(h,null==u?void 0:u.notice),style:x,times:p,key:c,eventKey:c,onNoticeClose:l,hovering:w&&g.length>0})))})};var iu=b.forwardRef(function(e,t){var n=e.prefixCls,r=void 0===n?"rc-notification":n,a=e.container,i=e.motion,o=e.maxCount,s=e.className,l=e.style,c=e.onAllRemoved,u=e.stack,d=e.renderNotifications,p=ej(b.useState([]),2),m=p[0],f=p[1],h=function(e){var t,n=m.find(function(t){return t.key===e});null==n||null==(t=n.onClose)||t.call(n),f(function(t){return t.filter(function(t){return t.key!==e})})};b.useImperativeHandle(t,function(){return{open:function(e){f(function(t){var n,r=eh(t),a=r.findIndex(function(t){return t.key===e.key}),i=eE({},e);return a>=0?(i.times=((null==(n=t[a])?void 0:n.times)||0)+1,r[a]=i):(i.times=0,r.push(i)),o>0&&r.length>o&&(r=r.slice(-o)),r})},close:function(e){h(e)},destroy:function(){f([])}}});var g=ej(b.useState({}),2),v=g[0],y=g[1];b.useEffect(function(){var e={};m.forEach(function(t){var n=t.placement,r=void 0===n?"topRight":n;r&&(e[r]=e[r]||[],e[r].push(t))}),Object.keys(v).forEach(function(t){e[t]=e[t]||[]}),y(e)},[m]);var x=function(e){y(function(t){var n=eE({},t);return(n[e]||[]).length||delete n[e],n})},w=b.useRef(!1);if(b.useEffect(function(){Object.keys(v).length>0?w.current=!0:w.current&&(null==c||c(),w.current=!1)},[v]),!a)return null;var j=Object.keys(v);return(0,es.createPortal)(b.createElement(b.Fragment,null,j.map(function(e){var t=v[e],n=b.createElement(ic,{key:e,configList:t,placement:e,prefixCls:r,className:null==s?void 0:s(e),style:null==l?void 0:l(e),motion:i,onNoticeClose:h,onAllNoticeRemoved:x,stack:u});return d?d(n,{prefixCls:r,key:e}):n})),a)}),id=["getContainer","motion","prefixCls","maxCount","className","style","onAllRemoved","stack","renderNotifications"],ip=function(){return document.body},im=0;let ih=e=>{let[,,,,t]=ag();return t?`${e}-css-var`:""};var ig=eJ(function e(){eV(this,e)}),iv="CALC_UNIT",iy=RegExp(iv,"g");function ix(e){return"number"==typeof e?"".concat(e).concat(iv):e}var ib=function(e){e6(n,e);var t=e7(n);function n(e,r){eV(this,n),eC(e5(a=t.call(this)),"result",""),eC(e5(a),"unitlessCssVar",void 0),eC(e5(a),"lowPriority",void 0);var a,i=eS(e);return a.unitlessCssVar=r,e instanceof n?a.result="(".concat(e.result,")"):"number"===i?a.result=ix(e):"string"===i&&(a.result=e),a}return eJ(n,[{key:"add",value:function(e){return e instanceof n?this.result="".concat(this.result," + ").concat(e.getResult()):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," + ").concat(ix(e))),this.lowPriority=!0,this}},{key:"sub",value:function(e){return e instanceof n?this.result="".concat(this.result," - ").concat(e.getResult()):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," - ").concat(ix(e))),this.lowPriority=!0,this}},{key:"mul",value:function(e){return this.lowPriority&&(this.result="(".concat(this.result,")")),e instanceof n?this.result="".concat(this.result," * ").concat(e.getResult(!0)):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," * ").concat(e)),this.lowPriority=!1,this}},{key:"div",value:function(e){return this.lowPriority&&(this.result="(".concat(this.result,")")),e instanceof n?this.result="".concat(this.result," / ").concat(e.getResult(!0)):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," / ").concat(e)),this.lowPriority=!1,this}},{key:"getResult",value:function(e){return this.lowPriority||e?"(".concat(this.result,")"):this.result}},{key:"equal",value:function(e){var t=this,n=(e||{}).unit,r=!0;return("boolean"==typeof n?r=n:Array.from(this.unitlessCssVar).some(function(e){return t.result.includes(e)})&&(r=!1),this.result=this.result.replace(iy,r?"px":""),void 0!==this.lowPriority)?"calc(".concat(this.result,")"):this.result}}]),n}(ig),iw=function(e){e6(n,e);var t=e7(n);function n(e){var r;return eV(this,n),eC(e5(r=t.call(this)),"result",0),e instanceof n?r.result=e.result:"number"==typeof e&&(r.result=e),r}return eJ(n,[{key:"add",value:function(e){return e instanceof n?this.result+=e.result:"number"==typeof e&&(this.result+=e),this}},{key:"sub",value:function(e){return e instanceof n?this.result-=e.result:"number"==typeof e&&(this.result-=e),this}},{key:"mul",value:function(e){return e instanceof n?this.result*=e.result:"number"==typeof e&&(this.result*=e),this}},{key:"div",value:function(e){return e instanceof n?this.result/=e.result:"number"==typeof e&&(this.result/=e),this}},{key:"equal",value:function(){return this.result}}]),n}(ig);let ij=function(e,t){var n="css"===e?ib:iw;return function(e){return new n(e,t)}},iS=function(e,t){return"".concat([t,e.replace(/([A-Z]+)([A-Z][a-z]+)/g,"$1-$2").replace(/([a-z])([A-Z])/g,"$1-$2")].filter(Boolean).join("-"))},ik=function(e,t,n,r){var a=eE({},t[e]);null!=r&&r.deprecatedTokens&&r.deprecatedTokens.forEach(function(e){var t=ej(e,2),n=t[0],r=t[1];(null!=a&&a[n]||null!=a&&a[r])&&(null!=a[r]||(a[r]=null==a?void 0:a[n]))});var i=eE(eE({},n),a);return Object.keys(i).forEach(function(e){i[e]===t[e]&&delete i[e]}),i};var iC="undefined"!=typeof CSSINJS_STATISTIC,iN=!0;function iE(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(!iC)return Object.assign.apply(Object,[{}].concat(t));iN=!1;var r={};return t.forEach(function(e){"object"===eS(e)&&Object.keys(e).forEach(function(t){Object.defineProperty(r,t,{configurable:!0,enumerable:!0,get:function(){return e[t]}})})}),iN=!0,r}var iT={};function i_(){}let iA=function(e){var t,n=e,r=i_;return iC&&"undefined"!=typeof Proxy&&(t=new Set,n=new Proxy(e,{get:function(e,n){if(iN){var r;null==(r=t)||r.add(n)}return e[n]}}),r=function(e,n){var r;iT[e]={global:Array.from(t),component:eE(eE({},null==(r=iT[e])?void 0:r.component),n)}}),{token:n,keys:t,flush:r}},iP=function(e,t,n){if("function"==typeof n){var r;return n(iE(t,null!=(r=t[e])?r:{}))}return null!=n?n:{}};var iM=new(function(){function e(){eV(this,e),eC(this,"map",new Map),eC(this,"objectIDMap",new WeakMap),eC(this,"nextID",0),eC(this,"lastAccessBeat",new Map),eC(this,"accessBeat",0)}return eJ(e,[{key:"set",value:function(e,t){this.clear();var n=this.getCompositeKey(e);this.map.set(n,t),this.lastAccessBeat.set(n,Date.now())}},{key:"get",value:function(e){var t=this.getCompositeKey(e),n=this.map.get(t);return this.lastAccessBeat.set(t,Date.now()),this.accessBeat+=1,n}},{key:"getCompositeKey",value:function(e){var t=this;return e.map(function(e){return e&&"object"===eS(e)?"obj_".concat(t.getObjectID(e)):"".concat(eS(e),"_").concat(e)}).join("|")}},{key:"getObjectID",value:function(e){if(this.objectIDMap.has(e))return this.objectIDMap.get(e);var t=this.nextID;return this.objectIDMap.set(e,t),this.nextID+=1,t}},{key:"clear",value:function(){var e=this;if(this.accessBeat>1e4){var t=Date.now();this.lastAccessBeat.forEach(function(n,r){t-n>6e5&&(e.map.delete(r),e.lastAccessBeat.delete(r))}),this.accessBeat=0}}}]),e}());let iI=function(){return{}},{genStyleHooks:iO,genComponentStyleHook:iL,genSubStyleComponent:iR}=function(e){var t=e.useCSP,n=void 0===t?iI:t,r=e.useToken,a=e.usePrefix,i=e.getResetStyles,o=e.getCommonStyle,s=e.getCompUnitless;function l(t,s,l){var c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},u=Array.isArray(t)?t:[t,t],d=ej(u,1)[0],p=u.join("-"),m=e.layer||{name:"antd"};return function(e){var t,u,f=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,h=r(),g=h.theme,v=h.realToken,y=h.hashId,x=h.token,w=h.cssVar,j=a(),S=j.rootPrefixCls,k=j.iconPrefixCls,C=n(),N=w?"css":"js",E=(t=function(){var e=new Set;return w&&Object.keys(c.unitless||{}).forEach(function(t){e.add(tm(t,w.prefix)),e.add(tm(t,iS(d,w.prefix)))}),ij(N,e)},u=[N,d,null==w?void 0:w.prefix],b.useMemo(function(){var e=iM.get(u);if(e)return e;var n=t();return iM.set(u,n),n},u)),T="js"===N?{max:Math.max,min:Math.min}:{max:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return"max(".concat(t.map(function(e){return td(e)}).join(","),")")},min:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return"min(".concat(t.map(function(e){return td(e)}).join(","),")")}},_=T.max,A=T.min,P={theme:g,token:x,hashId:y,nonce:function(){return C.nonce},clientOnly:c.clientOnly,layer:m,order:c.order||-999};return"function"==typeof i&&t7(eE(eE({},P),{},{clientOnly:!1,path:["Shared",S]}),function(){return i(x,{prefix:{rootPrefixCls:S,iconPrefixCls:k},csp:C})}),[t7(eE(eE({},P),{},{path:[p,e,k]}),function(){if(!1===c.injectStyle)return[];var t=iA(x),n=t.token,r=t.flush,a=iP(d,v,l),i=".".concat(e),u=ik(d,v,a,{deprecatedTokens:c.deprecatedTokens});w&&a&&"object"===eS(a)&&Object.keys(a).forEach(function(e){a[e]="var(".concat(tm(e,iS(d,w.prefix)),")")});var p=iE(n,{componentCls:i,prefixCls:e,iconCls:".".concat(k),antCls:".".concat(S),calc:E,max:_,min:A},w?a:u),m=s(p,{hashId:y,prefixCls:e,rootPrefixCls:S,iconPrefixCls:k});r(d,u);var h="function"==typeof o?o(p,e,f,c.resetFont):null;return[!1===c.resetStyle?null:h,m]}),y]}}return{genStyleHooks:function(e,t,n,a){var i,o,c,u,d,p,m,f,h,g=Array.isArray(e)?e[0]:e;function v(e){return"".concat(String(g)).concat(e.slice(0,1).toUpperCase()).concat(e.slice(1))}var y=(null==a?void 0:a.unitless)||{},x=eE(eE({},"function"==typeof s?s(e):{}),{},eC({},v("zIndexPopup"),!0));Object.keys(y).forEach(function(e){x[v(e)]=y[e]});var w=eE(eE({},a),{},{unitless:x,prefixToken:v}),j=l(e,t,n,w),S=(i=g,o=n,u=(c=w).unitless,p=void 0===(d=c.injectStyle)||d,m=c.prefixToken,f=c.ignore,h=function(e){var t=e.rootCls,n=e.cssVar,a=void 0===n?{}:n,s=r().realToken;return nt({path:[i],prefix:a.prefix,key:a.key,unitless:u,ignore:f,token:s,scope:t},function(){var e=iP(i,s,o),t=ik(i,s,e,{deprecatedTokens:null==c?void 0:c.deprecatedTokens});return Object.keys(e).forEach(function(e){t[m(e)]=t[e],delete t[e]}),t}),null},function(e){var t=r().cssVar;return[function(n){return p&&t?b.createElement(b.Fragment,null,b.createElement(h,{rootCls:e,cssVar:t,component:i}),n):n},null==t?void 0:t.key]});return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=ej(j(e,t),2)[1],r=ej(S(t),2);return[r[0],n,r[1]]}},genSubStyleComponent:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=l(e,t,n,eE({resetStyle:!1,order:-998},r));return function(e){var t=e.prefixCls,n=e.rootCls,r=void 0===n?t:n;return a(t,r),null}},genComponentStyleHook:l}}({usePrefix:()=>{let{getPrefixCls:e,iconPrefixCls:t}=(0,b.useContext)(ey);return{rootPrefixCls:e(),iconPrefixCls:t}},useToken:()=>{let[e,t,n,r,a]=ag();return{theme:e,realToken:t,hashId:n,token:r,cssVar:a}},useCSP:()=>{let{csp:e}=(0,b.useContext)(ey);return null!=e?e:{}},getResetStyles:(e,t)=>{var n;let r=aw(e);return[r,{"&":r},aj(null!=(n=null==t?void 0:t.prefix.iconPrefixCls)?n:ev)]},getCommonStyle:(e,t,n,r)=>{let a=`[class^="${t}"], [class*=" ${t}"]`,i=n?`.${n}`:a,o={boxSizing:"border-box","&::before, &::after":{boxSizing:"border-box"}},s={};return!1!==r&&(s={fontFamily:e.fontFamily,fontSize:e.fontSize}),{[i]:Object.assign(Object.assign(Object.assign({},s),o),{[a]:o})}},getCompUnitless:()=>ap}),i$=e=>{let{componentCls:t,iconCls:n,boxShadow:r,colorText:a,colorSuccess:i,colorError:o,colorWarning:s,colorInfo:l,fontSizeLG:c,motionEaseInOutCirc:u,motionDurationSlow:d,marginXS:p,paddingXS:m,borderRadiusLG:f,zIndexPopup:h,contentPadding:g,contentBg:v}=e,y=`${t}-notice`,x=new nn("MessageMoveIn",{"0%":{padding:0,transform:"translateY(-100%)",opacity:0},"100%":{padding:m,transform:"translateY(0)",opacity:1}}),b=new nn("MessageMoveOut",{"0%":{maxHeight:e.height,padding:m,opacity:1},"100%":{maxHeight:0,padding:0,opacity:0}}),w={padding:m,textAlign:"center",[`${t}-custom-content`]:{display:"flex",alignItems:"center"},[`${t}-custom-content > ${n}`]:{marginInlineEnd:p,fontSize:c},[`${y}-content`]:{display:"inline-block",padding:g,background:v,borderRadius:f,boxShadow:r,pointerEvents:"all"},[`${t}-success > ${n}`]:{color:i},[`${t}-error > ${n}`]:{color:o},[`${t}-warning > ${n}`]:{color:s},[`${t}-info > ${n},
      ${t}-loading > ${n}`]:{color:l}};return[{[t]:Object.assign(Object.assign({},ax(e)),{color:a,position:"fixed",top:p,width:"100%",pointerEvents:"none",zIndex:h,[`${t}-move-up`]:{animationFillMode:"forwards"},[`
        ${t}-move-up-appear,
        ${t}-move-up-enter
      `]:{animationName:x,animationDuration:d,animationPlayState:"paused",animationTimingFunction:u},[`
        ${t}-move-up-appear${t}-move-up-appear-active,
        ${t}-move-up-enter${t}-move-up-enter-active
      `]:{animationPlayState:"running"},[`${t}-move-up-leave`]:{animationName:b,animationDuration:d,animationPlayState:"paused",animationTimingFunction:u},[`${t}-move-up-leave${t}-move-up-leave-active`]:{animationPlayState:"running"},"&-rtl":{direction:"rtl",span:{direction:"rtl"}}})},{[t]:{[`${y}-wrapper`]:Object.assign({},w)}},{[`${t}-notice-pure-panel`]:Object.assign(Object.assign({},w),{padding:0,textAlign:"start"})}]},iF=iO("Message",e=>[i$(iE(e,{height:150}))],e=>({zIndexPopup:e.zIndexPopupBase+1e3+10,contentBg:e.colorBgElevated,contentPadding:`${(e.controlHeightLG-e.fontSize*e.lineHeight)/2}px ${e.paddingSM}px`}));var iz=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};let iD={info:b.createElement(a8,null),success:b.createElement(a1,null),error:b.createElement(a4,null),warning:b.createElement(a3,null),loading:b.createElement(a7,null)},iH=e=>{let{prefixCls:t,type:n,icon:r,children:a}=e;return b.createElement("div",{className:rm()(`${t}-custom-content`,`${t}-${n}`)},r||iD[n],b.createElement("span",null,a))},iZ={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"}}]},name:"close",theme:"outlined"};var iB=b.forwardRef(function(e,t){return b.createElement(a0,tN({},e,{ref:t,icon:iZ}))});function iU(e){let t,n=new Promise(n=>{t=e(()=>{n(!0)})}),r=()=>{null==t||t()};return r.then=(e,t)=>n.then(e,t),r.promise=n,r}var iK=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};let iW=e=>{let{children:t,prefixCls:n}=e,r=ih(n),[a,i,o]=iF(n,r);return a(b.createElement(io,{classNames:{list:rm()(i,o,r)}},t))},iq=(e,t)=>{let{prefixCls:n,key:r}=t;return b.createElement(iW,{prefixCls:n,key:r},e)},iG=b.forwardRef((e,t)=>{let{top:n,prefixCls:r,getContainer:a,maxCount:i,duration:o=3,rtl:s,transitionName:l,onAllRemoved:c}=e,{getPrefixCls:u,getPopupContainer:d,message:p,direction:m}=b.useContext(ey),f=r||u("message"),h=b.createElement("span",{className:`${f}-close-x`},b.createElement(iB,{className:`${f}-close-icon`})),[g,v]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.getContainer,n=void 0===t?ip:t,r=e.motion,a=e.prefixCls,i=e.maxCount,o=e.className,s=e.style,l=e.onAllRemoved,c=e.stack,u=e.renderNotifications,d=eD(e,id),p=ej(b.useState(),2),m=p[0],f=p[1],h=b.useRef(),g=b.createElement(iu,{container:m,ref:h,prefixCls:a,motion:r,maxCount:i,className:o,style:s,onAllRemoved:l,stack:c,renderNotifications:u}),v=ej(b.useState([]),2),y=v[0],x=v[1],w=rE(function(e){var t=function(){for(var e={},t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return n.forEach(function(t){t&&Object.keys(t).forEach(function(n){var r=t[n];void 0!==r&&(e[n]=r)})}),e}(d,e);(null===t.key||void 0===t.key)&&(t.key="rc-notification-".concat(im),im+=1),x(function(e){return[].concat(eh(e),[{type:"open",config:t}])})}),j=b.useMemo(function(){return{open:w,close:function(e){x(function(t){return[].concat(eh(t),[{type:"close",key:e}])})},destroy:function(){x(function(e){return[].concat(eh(e),[{type:"destroy"}])})}}},[]);return b.useEffect(function(){f(n())}),b.useEffect(function(){if(h.current&&y.length){var e,t;y.forEach(function(e){switch(e.type){case"open":h.current.open(e.config);break;case"close":h.current.close(e.key);break;case"destroy":h.current.destroy()}}),x(function(n){return e===n&&t||(e=n,t=n.filter(function(e){return!y.includes(e)})),t})}},[y]),[j,g]}({prefixCls:f,style:()=>({left:"50%",transform:"translateX(-50%)",top:null!=n?n:8}),className:()=>rm()({[`${f}-rtl`]:null!=s?s:"rtl"===m}),motion:()=>({motionName:null!=l?l:`${f}-move-up`}),closable:!1,closeIcon:h,duration:o,getContainer:()=>(null==a?void 0:a())||(null==d?void 0:d())||document.body,maxCount:i,onAllRemoved:c,renderNotifications:iq});return b.useImperativeHandle(t,()=>Object.assign(Object.assign({},g),{prefixCls:f,message:p})),v}),iV=0;function iX(e){let t=b.useRef(null);return nd("Message"),[b.useMemo(()=>{let e=e=>{var n;null==(n=t.current)||n.close(e)},n=n=>{if(!t.current){let e=()=>{};return e.then=()=>{},e}let{open:r,prefixCls:a,message:i}=t.current,o=`${a}-notice`,{content:s,icon:l,type:c,key:u,className:d,style:p,onClose:m}=n,f=iK(n,["content","icon","type","key","className","style","onClose"]),h=u;return null==h&&(iV+=1,h=`antd-message-${iV}`),iU(t=>(r(Object.assign(Object.assign({},f),{key:h,content:b.createElement(iH,{prefixCls:a,type:c,icon:l},s),placement:"top",className:rm()(c&&`${o}-${c}`,d,null==i?void 0:i.className),style:Object.assign(Object.assign({},null==i?void 0:i.style),p),onClose:()=>{null==m||m(),t()}})),()=>{e(h)}))},r={open:n,destroy:n=>{var r;void 0!==n?e(n):null==(r=t.current)||r.destroy()}};return["info","success","warning","error","loading"].forEach(e=>{r[e]=(t,r,a)=>{let i,o;return"function"==typeof r?o=r:(i=r,o=a),n(Object.assign(Object.assign({onClose:o,duration:i},t&&"object"==typeof t&&"content"in t?t:{content:t}),{type:e}))}}),r},[]),b.createElement(iG,Object.assign({key:"message-holder"},e,{ref:t}))]}let iJ=null,iY=e=>e(),iQ=[],i0={};function i1(){let{getContainer:e,duration:t,rtl:n,maxCount:r,top:a}=i0,i=(null==e?void 0:e())||document.body;return{getContainer:()=>i,duration:t,rtl:n,maxCount:r,top:a}}let i2=b.forwardRef((e,t)=>{let{messageConfig:n,sync:r}=e,{getPrefixCls:a}=(0,b.useContext)(ey),i=i0.prefixCls||a("message"),o=(0,b.useContext)(eg),[s,l]=iX(Object.assign(Object.assign(Object.assign({},n),{prefixCls:i}),o.message));return b.useImperativeHandle(t,()=>{let e=Object.assign({},s);return Object.keys(e).forEach(t=>{e[t]=function(){return r(),s[t].apply(s,arguments)}}),{instance:e,sync:r}}),l}),i4=b.forwardRef((e,t)=>{let[n,r]=b.useState(i1),a=()=>{r(i1)};b.useEffect(a,[]);let i=aT(),o=i.getRootPrefixCls(),s=i.getIconPrefixCls(),l=i.getTheme(),c=b.createElement(i2,{ref:t,sync:a,messageConfig:n});return b.createElement(aA,{prefixCls:o,iconPrefixCls:s,theme:l},i.holderRender?i.holderRender(c):c)});function i5(){if(!iJ){let e=document.createDocumentFragment(),t={fragment:e};iJ=t,iY(()=>{aZ(b.createElement(i4,{ref:e=>{let{instance:n,sync:r}=e||{};Promise.resolve().then(()=>{!t.instance&&n&&(t.instance=n,t.sync=r,i5())})}}),e)});return}iJ.instance&&(iQ.forEach(e=>{let{type:t,skipped:n}=e;if(!n)switch(t){case"open":iY(()=>{let t=iJ.instance.open(Object.assign(Object.assign({},i0),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":iY(()=>{null==iJ||iJ.instance.destroy(e.key)});break;default:iY(()=>{var n;let r=(n=iJ.instance)[t].apply(n,eh(e.args));null==r||r.then(e.resolve),e.setCloseFn(r)})}}),iQ=[])}let i3={open:function(e){let t=iU(t=>{let n,r={type:"open",config:e,resolve:t,setCloseFn:e=>{n=e}};return iQ.push(r),()=>{n?iY(()=>{n()}):r.skipped=!0}});return i5(),t},destroy:e=>{iQ.push({type:"destroy",key:e}),i5()},config:function(e){i0=Object.assign(Object.assign({},i0),e),iY(()=>{var e;null==(e=null==iJ?void 0:iJ.sync)||e.call(iJ)})},useMessage:function(e){return iX(e)},_InternalPanelDoNotUseOrYouWillBeFired:e=>{let{prefixCls:t,className:n,type:r,icon:a,content:i}=e,o=iz(e,["prefixCls","className","type","icon","content"]),{getPrefixCls:s}=b.useContext(ey),l=t||s("message"),c=ih(l),[u,d,p]=iF(l,c);return u(b.createElement(ia,Object.assign({},o,{prefixCls:l,className:rm()(n,d,`${l}-notice-pure-panel`,p,c),eventKey:"pure",duration:null,content:b.createElement(iH,{prefixCls:l,type:r,icon:a},i)})))}};["success","info","warning","error","loading"].forEach(e=>{i3[e]=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];aT();let a=iU(t=>{let r,a={type:e,args:n,resolve:t,setCloseFn:e=>{r=e}};return iQ.push(a),()=>{r?iY(()=>{r()}):a.skipped=!0}});return i5(),a}});class i6 extends Error{constructor(e){super("Unauthorized"),this.name="UnauthorizedError",this.message=e}}let i8=class{async baseRequest(e,t){let n=await fetch(`${this.options.baseURL}${e}`,{...t,headers:{...t.headers,Authorization:`Bearer ${this.options.apiKey}`,Accept:"text/event-stream, application/json"}});if(n.headers.get("X-Version")){let e=n.headers.get("X-Version");e&&e!==O.version&&(O.version=e)}if(401===n.status)throw i3.error("未授权, 请检查你的配置"),new i6("Unauthorized");return n}async jsonRequest(e,t){let n=await this.baseRequest(e,{...t,headers:{...t.headers,"Content-Type":"application/json"}}),r=await n.json();return 0===r.code?r.data:401==r.code?r:void i3.error(r.msg)}async get(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t?`?${new URLSearchParams(t).toString()}`:"";return await this.jsonRequest(`${e}${r}`,{method:"GET",headers:n})}async post(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"POST",body:JSON.stringify(t),headers:n})}async delete(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"DELETE",body:JSON.stringify(t),headers:n})}constructor(e){var t,n;n=void 0,(t="options")in this?Object.defineProperty(this,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):this[t]=n,this.options=e}};function i9(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class i7{updateOptions(e){this.options=e,this.baseRequest=new i8({baseURL:e.apiBase,apiKey:e.apiKey,appId:e.appId})}async getAppInfo(){return this.baseRequest.get("/info")}async getAppMeta(){return this.baseRequest.get("/meta")}getConversationList(e){return this.baseRequest.get("/conversations",{user:this.options.user,limit:((null==e?void 0:e.limit)||100).toString(),appId:this.options.appId})}sendMessage(e){return e.appId=this.options.appId,this.baseRequest.baseRequest("/chat-messages",{method:"POST",body:JSON.stringify(e),headers:{"Content-Type":"application/json"}})}async stopTask(e){return this.baseRequest.post(`/chat-messages/${e}/stop`,{user:this.options.user,appId:this.options.appId})}async uploadFile(e){let t=new FormData;return t.append("file",e),t.append("user",this.options.user),t.append("appId",this.options.appId),this.baseRequest.baseRequest("/files/upload",{method:"POST",body:t}).then(e=>e.json())}async getNextSuggestions(e){return this.baseRequest.get(`/messages/${e.message_id}/suggested`,{appId:this.options.appId,user:this.options.user})}feedbackMessage(e){let{messageId:t,...n}=e;return this.baseRequest.post(`/messages/${t}/feedbacks`,{...n,user:this.options.user,appId:this.options.appId})}async text2Audio(e){return this.baseRequest.baseRequest("/text-to-audio",{method:"POST",body:JSON.stringify({...e,user:this.options.user}),headers:{"Content-Type":"application/json"}})}async audio2Text(e){let t=new FormData;return t.append("file",e),t.append("user",this.options.user),this.baseRequest.baseRequest("/audio-to-text",{method:"POST",body:t}).then(e=>e.json())}constructor(e){i9(this,"options",void 0),i9(this,"baseRequest",void 0),i9(this,"getAppParameters",()=>this.baseRequest.get("/parameters",{appId:this.options.appId})),i9(this,"renameConversation",e=>{let{conversation_id:t,...n}=e;return this.baseRequest.post(`/conversations/${t}/name`,{...n,user:this.options.user,appId:this.options.appId})}),i9(this,"deleteConversation",(e,t)=>this.baseRequest.delete(`/conversations/${e}?appId=${t}`,{user:this.options.user})),i9(this,"getConversationHistory",e=>this.baseRequest.get("/messages",{user:this.options.user,conversation_id:e,appId:this.options.appId})),this.options=e,this.baseRequest=new i8({baseURL:e.apiBase,apiKey:e.apiKey,appId:e.appId})}}let oe=e=>new i7(e);var ot=((m={}).MESSAGE="message",m.AGENT_MESSAGE="agent_message",m.AGENT_THOUGHT="agent_thought",m.MESSAGE_FILE="message_file",m.MESSAGE_END="message_end",m.TTS_MESSAGE="tts_message",m.TTS_MESSAGE_END="tts_message_end",m.MESSAGE_REPLACE="message_replace",m.ERROR="error",m.PING="ping",m.WORKFLOW_STARTED="workflow_started",m.WORKFLOW_FINISHED="workflow_finished",m.WORKFLOW_NODE_STARTED="node_started",m.WORKFLOW_NODE_FINISHED="node_finished",m),on=n(45186),or=n(62472),oa=n(63522),oi=n(44312),oo=n(79016),os=n(707),ol=n(83882),oc=n(17148),ou=n(55879),od=n(87092);function op(e){let{formInstance:t}=e,n=on.Z.useWatch("answerForm.enabled",t);return(0,y.jsxs)(on.Z,{autoComplete:"off",form:t,labelAlign:"left",labelCol:{span:5},initialValues:{"answerForm.enabled":!1},children:[(0,y.jsxs)("div",{className:"text-base mb-3 flex items-center",children:[(0,y.jsx)("div",{className:"h-4 w-1 bg-primary rounded"}),(0,y.jsx)("div",{className:"ml-2 font-semibold",children:"请求配置"})]}),(0,y.jsx)(on.Z.Item,{label:"API Base",name:"apiBase",rules:[{required:!0}],tooltip:"Dify API 的域名+版本号前缀，如 https://api.dify.ai/v1",required:!0,children:(0,y.jsx)(oc.Z,{autoComplete:"new-password",placeholder:"请输入 API BASE"})}),(0,y.jsx)(on.Z.Item,{label:"API Secret",name:"apiKey",tooltip:"Dify App 的 API Secret (以 app- 开头)",rules:[{required:!0}],required:!0,children:(0,y.jsx)(oc.Z.Password,{autoComplete:"new-password",placeholder:"请输入 API Secret"})}),(0,y.jsxs)("div",{className:"text-base mb-3 flex items-center",children:[(0,y.jsx)("div",{className:"h-4 w-1 bg-primary rounded"}),(0,y.jsx)("div",{className:"ml-2 font-semibold",children:"更多配置"})]}),(0,y.jsx)(on.Z.Item,{label:"表单回复",name:"answerForm.enabled",tooltip:"当工作流需要回复表单给用户填写时，建议开启此功能",rules:[{required:!0}],required:!0,children:(0,y.jsx)(od.Z,{placeholder:"请选择",options:[{label:"启用",value:!0},{label:"禁用",value:!1}]})}),n?(0,y.jsx)(on.Z.Item,{label:"提交消息文本",name:"answerForm.feedbackText",tooltip:"当启用表单回复时，用户填写表单并提交后，默认会以用户角色将填写的表单数据作为消息文本发送，如果配置了此字段，将会固定展示配置的字段值",children:(0,y.jsx)(oc.Z,{placeholder:"请输入提交消息文本"})}):null]})}function om(e){var t;let{activeAppId:n,getAppList:r,appListLoading:a,appList:i,onDeleteSuccess:o,...s}=e,{user:l,appService:c}=N(),[u,d]=(0,b.useState)(),[p,m]=(0,b.useState)(!1),[f]=on.Z.useForm(),[h,g]=(0,b.useState)(),v=M(),{t:x}=(0,ou.$G)(),[w,j]=(0,b.useState)(!1),{runAsync:S,loading:k}=(0,ea.Z)(async e=>c.addApp(e),{manual:!0,onSuccess:()=>{m(!1),Z.ZP.success(x("app.addAppConfigSuccess")),r()}}),{runAsync:C,loading:E}=(0,ea.Z)(async e=>c.updateApp(e),{manual:!0,onSuccess:()=>{m(!1),Z.ZP.success(x("app.updateAppConfigSuccess")),r()}});(0,b.useEffect)(()=>{p||f.resetFields()},[p]);let T=null==i?void 0:i.find(e=>e.id===u);return(0,y.jsxs)(or.Z,{width:700,title:"应用配置管理",...s,children:[(0,y.jsxs)("div",{className:"w-full h-full overflow-hidden flex flex-col",children:[(0,y.jsx)("div",{className:"pb-3 flex-1 overflow-y-auto",children:(0,y.jsx)(V.Z,{spinning:a,children:(0,y.jsx)(oa.Z,{gutter:16*!v,className:"w-full",children:(null==i?void 0:i.length)?null==i?void 0:i.map(e=>{var t;return(0,y.jsx)(oi.Z,{span:v?24:12,children:(0,y.jsxs)("div",{className:"p-3 bg-white mb-3 border border-solid border-gray-200 rounded-lg cursor-pointer hover:border-primary hover:text-primary",onClick:()=>{var t,n;d(e.id),f.setFieldsValue({apiBase:e.requestConfig.apiBase,apiKey:e.requestConfig.apiKey,"answerForm.enabled":(null==(t=e.answerForm)?void 0:t.enabled)||!1,"answerForm.feedbackText":(null==(n=e.answerForm)?void 0:n.feedbackText)||""}),g("edit"),m(!0)},children:[(0,y.jsxs)("div",{className:"w-full flex items-center overflow-hidden",children:[(0,y.jsxs)("div",{className:"flex-1 font-semibold truncate",children:[n===e.id&&"【当前】",e.info.name]}),(0,y.jsx)(oo.Z,{className:"inline-flex items-center",children:(0,y.jsx)(os.Z,{onPopupClick:e=>e.stopPropagation(),cancelText:"取消",okText:"确定",title:"确定删除应用吗？",onConfirm:async()=>{await c.deleteApp(e.id),Z.ZP.success("删除应用成功"),r(),null==o||o(e.id)},children:(0,y.jsx)(eo.Z,{onClick:e=>e.stopPropagation(),className:"p-0 text-red-500"})})})]}),(0,y.jsx)("div",{title:e.info.description,className:"truncate text-sm mt-2 text-desc h-6 leading-6",children:e.info.description}),(0,y.jsxs)("div",{className:"mt-3 text-desc truncate",title:e.info.tags.join(", "),children:["标签：",(null==(t=e.info.tags)?void 0:t.length)?e.info.tags.join(", "):(0,y.jsx)(y.Fragment,{children:"无"})]})]})},e.id)}):(0,y.jsx)(ol.Z,{className:"mx-auto",description:"暂无应用"})})})}),(0,y.jsx)(J.ZP,{type:"primary",size:"large",block:!0,onClick:()=>{d(""),g("create"),f.resetFields(),m(!0)},children:"添加应用"})]}),(0,y.jsxs)(or.Z,{width:600,title:`${"create"===h?"添加应用配置":`应用配置详情 - ${null==T?void 0:T.info.name}`}`,open:p,onClose:()=>m(!1),extra:(0,y.jsxs)(oo.Z,{children:[(0,y.jsx)(J.ZP,{onClick:()=>m(!1),children:"取消"}),(0,y.jsx)(J.ZP,{type:"primary",loading:k||E||w,onClick:async()=>{await f.validateFields(),j(!0);try{let e=f.getFieldsValue(),t=null==i?void 0:i.find(e=>e.id===u),n=new i7({user:l,apiBase:e.apiBase,apiKey:e.apiKey}),a={info:await n.getAppInfo(),requestConfig:{apiBase:e.apiBase,apiKey:e.apiKey},answerForm:{enabled:e["answerForm.enabled"],feedbackText:e["answerForm.feedbackText"]}};"edit"===h?await C({id:t.id,...a}):await S({id:Math.random().toString(),...a}),r()}catch(e){console.error("保存应用配置失败",e),Z.ZP.error(`保存应用配置失败: ${e}`)}finally{j(!1)}},children:"create"===h?"确定":"更新"})]}),children:["edit"===h?(0,y.jsxs)(on.Z,{labelAlign:"left",labelCol:{span:5},layout:"horizontal",children:[(0,y.jsxs)("div",{className:"text-base mb-3 flex items-center",children:[(0,y.jsx)("div",{className:"h-4 w-1 bg-primary rounded"}),(0,y.jsx)("div",{className:"ml-2 font-semibold",children:"基本信息"})]}),(0,y.jsx)(on.Z.Item,{label:"应用名称",children:(0,y.jsx)(oc.Z,{disabled:!0,value:null==T?void 0:T.info.name})}),(0,y.jsx)(on.Z.Item,{label:"应用描述",children:(0,y.jsx)(oc.Z,{disabled:!0,value:null==T?void 0:T.info.name})}),(0,y.jsx)(on.Z.Item,{label:"应用标签",children:(null==T||null==(t=T.info.tags)?void 0:t.length)?(0,y.jsx)("div",{className:"text-default",children:T.info.tags.join(", ")}):(0,y.jsx)(y.Fragment,{children:"无"})})]}):null,(0,y.jsx)(op,{formInstance:f})]})]})}var of=n(45549),oh=n(52503),og=n(63627),ov=n(81088),oy=n(44416),ox=n(47041),ob=n(79183),ow=n(51850),oj=n(53509),oS=n(8394);let ok=new Map;ok.set("document",["txt","md","mdx","markdown","pdf","html","xlsx","xls","doc","docx","csv","eml","msg","pptx","ppt","xml","epub"]),ok.set("image",["jpg","jpeg","png","gif","webp","svg"]),ok.set("audio",["mp3","m4a","wav","webm","amr"]),ok.set("video",["mp4","mov","mpeg","mpga"]),ok.set("custom",[]);let oC=e=>e.split(".").pop(),oN=e=>{let t=e.split(".").pop(),n=null;return ok.forEach((e,r)=>{e.indexOf(t)>-1&&(n=r)}),n},oE=e=>e>1048576?`${(e/1024/1024).toFixed(2)} MB`:`${(e/1024).toFixed(2)} KB`,oT=e=>{var t;let{content:n,isRequesting:r,onChange:a,onSubmit:i,className:o,onCancel:s,uploadFileApi:l,appParameters:c,onFocus:u}=e,[d,p]=(0,b.useState)(!1),[m,f]=(0,b.useState)([]),[h,g]=(0,b.useState)(new Map),v=(0,b.useMemo)(()=>{if(!(null==c?void 0:c.file_upload.enabled))return[];let e=[];return c.file_upload.allowed_file_types.forEach(t=>{ok.get(t)&&e.push(...ok.get(t)||[])}),e},[null==c?void 0:c.file_upload]),x=async e=>{let t=[...m],n={uid:e.uid,name:e.name,status:"uploading",size:e.size,type:e.type,originFileObj:e},{clear:r}=(()=>{let e=0;f([...t,{...n,percent:e}]);let r=setInterval(()=>{if(e>=99)return void clearInterval(r);e+=1,f([...t,{...n,percent:e}])},100);return{clear:()=>clearInterval(r)}})(),a=await l(e);if((null==a?void 0:a.code)!==0)return void Z.ZP.error(null==a?void 0:a.msg);r(),f([...t,{...n,percent:100,status:"done"}]),g(t=>{var n;let r=new Map(t);return r.set(null==e?void 0:e.uid,null==a||null==(n=a.data)?void 0:n.id),r})},w=(0,b.useRef)(null),j=(0,y.jsx)(ow.Z.Header,{title:"上传文件",open:d,onOpenChange:p,styles:{content:{padding:0}},children:(0,y.jsx)(oj.Z,{beforeUpload:async e=>{let t=oC(e.name);return v.length>0&&!v.includes(t)?Z.ZP.error(`不支持的文件类型: ${t}`):x(e),!1},items:m,placeholder:e=>"drop"===e?{title:"Drop file here"}:{icon:(0,y.jsx)(ox.Z,{}),title:"点击或拖拽文件到此区域上传",description:(0,y.jsxs)("div",{children:["支持的文件类型：",v.join(", ")]})},getDropContainer:()=>{var e;return null==(e=w.current)?void 0:e.nativeElement},onRemove:e=>{f(t=>t.filter(t=>t.uid!==e.uid))}})});return(0,y.jsx)(ow.Z,{allowSpeech:null==c?void 0:c.speech_to_text.enabled,header:j,value:n,onChange:a,onFocus:u,prefix:(null==c||null==(t=c.file_upload)?void 0:t.enabled)?(0,y.jsx)(oS.Z,{dot:m.length>0&&!d,children:(0,y.jsx)(J.ZP,{onClick:()=>p(!d),icon:(0,y.jsx)(ob.Z,{})})}):null,style:{boxShadow:"0px -2px 12px 4px #efefef"},loading:r,className:o,onSubmit:async e=>{if((null==m?void 0:m.length)&&!m.every(e=>"done"===e.status))return void Z.ZP.error("请等待所有文件上传完成");await i(e,{files:(null==m?void 0:m.map(e=>{let t=oN(e.name);return{...e,type:t||"document",transfer_method:"local_file",upload_file_id:h.get(e.uid)}}))||[]}),f([]),p(!1)},onCancel:s})};var o_=n(82120),oA=n(810),oP=n(96002),oM=n(66642),oI=n(83191);n(98647);var oO=n(13224),oL=n(12599),oR=n(6785),o$=n(96858),oF=n(78869),oz=n(30322),oD=n(92445),oH=(n(27276),n(259)),oZ=n(30781),oB=n(45709),oU=n(17118),oK=n(74266),oW=n(73790);s=null,"undefined"!=typeof window&&(s=oZ.Z.mermaidAPI);let oq=e=>{let t=new Blob([new TextEncoder().encode(e)],{type:"image/svg+xml;charset=utf-8"});return new Promise((e,n)=>{let r=new FileReader;r.onloadend=()=>e(r.result),r.onerror=n,r.readAsDataURL(t)})},oG=e=>{let{ref:t,...n}=e,[r,a]=(0,b.useState)(null),[i,o]=(0,b.useState)("classic"),l=(0,oB.Z)(n.PrimitiveCode),[c,u]=(0,b.useState)(!0),d=(0,b.useRef)(0),[p,m]=(0,b.useState)(""),[f,h]=(0,b.useState)(""),g=(0,b.useCallback)(async e=>{a(null),u(!0);try{if("undefined"!=typeof window&&s){let t=await s.render("flowchart",e),n=await oq(t.svg.replaceAll("<br>","<br/>"));a(n),u(!1)}}catch(e){l===n.PrimitiveCode&&(u(!1),m(e.message))}},[n.PrimitiveCode]);return(0,b.useEffect)(()=>{"undefined"!=typeof window&&(oZ.Z.initialize({startOnLoad:!0,theme:"neutral",look:i,flowchart:{htmlLabels:!0,useMaxWidth:!0}}),g(n.PrimitiveCode))},[i]),(0,b.useEffect)(()=>{d.current&&window.clearTimeout(d.current),d.current=window.setTimeout(()=>{g(n.PrimitiveCode)},300)},[n.PrimitiveCode]),(0,y.jsxs)("div",{ref:t,children:[(0,y.jsx)("div",{className:"msh-segmented msh-segmented-sm css-23bs09 css-var-r1",children:(0,y.jsx)("div",{className:"msh-segmented-group",children:(0,y.jsx)("label",{className:"msh-segmented-item m-2 flex w-[200px] items-center space-x-1",children:(0,y.jsxs)(oW.ZP.Group,{value:i,buttonStyle:"solid",optionType:"button",onChange:e=>{"handDrawn"===e.target.value?o("handDrawn"):o("classic")},children:[(0,y.jsx)(oW.ZP,{value:"classic",children:"经典"}),(0,y.jsx)(oW.ZP,{value:"handDrawn",children:"手绘"})]})})})}),r&&(0,y.jsx)("div",{className:"mermaid object-fit: cover h-auto w-full cursor-pointer",onClick:()=>h(r),children:r&&(0,y.jsx)("img",{src:r,alt:"mermaid_chart"})}),c&&(0,y.jsx)("div",{className:"px-[26px] py-4",children:(0,y.jsx)(oK.Z,{})}),p&&(0,y.jsxs)("div",{className:"px-[26px] py-4",children:[(0,y.jsx)(oU.Z,{className:"h-6 w-6 text-red-500"}),"\xa0",p]})]})};oG.displayName="Flowchart";let oV=e=>{var t;return"string"==typeof e?e.includes("[ENDTHINKFLAG]"):Array.isArray(e)?e.some(e=>oV(e)):null!=e&&null!=(t=e.props)&&!!t.children&&oV(e.props.children)},oX=e=>{var t;return"string"==typeof e?e.replace("[ENDTHINKFLAG]",""):Array.isArray(e)?e.map(e=>oX(e)):(null==e||null==(t=e.props)?void 0:t.children)?b.cloneElement(e,{...e.props,children:oX(e.props.children)}):e},oJ=e=>{let[t]=(0,b.useState)(Date.now()),[n,r]=(0,b.useState)(0),[a,i]=(0,b.useState)(!1),o=(0,b.useRef)();return(0,b.useEffect)(()=>(o.current=setInterval(()=>{a||r(Math.floor((Date.now()-t)/100)/10)},100),()=>{o.current&&clearInterval(o.current)}),[t,a]),(0,b.useEffect)(()=>{oV(e)&&(i(!0),o.current&&clearInterval(o.current))},[e]),{elapsedTime:n,isComplete:a}},oY=e=>{let{children:t,...n}=e,{elapsedTime:r,isComplete:a}=oJ(t),i=oX(t);return n["data-think"]?(0,y.jsxs)("details",{...!a&&{open:!0},className:"group",children:[(0,y.jsx)("summary",{className:"flex cursor-pointer select-none list-none items-center whitespace-nowrap font-bold text-gray-500",children:(0,y.jsxs)("div",{className:"flex shrink-0 items-center",children:[(0,y.jsx)("svg",{className:"mr-2 h-3 w-3 transition-transform duration-500 group-open:rotate-90",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,y.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),a?`已深度思考(${r.toFixed(1)}s)`:`深度思考中...(${r.toFixed(1)}s)`]})}),(0,y.jsx)("div",{className:"border-l mt-1 rounded-lg border-gray-300 text-gray-500 p-3 bg-gray-50",children:i})]}):(0,y.jsx)("details",{...n,children:t})};var oQ=n(69267),o0=n(12737);let o1=e=>{let{content:t}=e,n=(0,b.useRef)(null),[r,a]=(0,b.useState)(""),[i,o]=(0,b.useState)({width:"undefined"!=typeof window?window.innerWidth:0,height:"undefined"!=typeof window?window.innerHeight:0}),s=e=>{let t=new XMLSerializer().serializeToString(e),n=Buffer.from(t).toString("base64");return`data:image/svg+xml;base64,${n}`};return(0,b.useEffect)(()=>{let e=()=>{o({width:window.innerWidth,height:window.innerHeight})};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,b.useEffect)(()=>{if(n.current)try{n.current.innerHTML="";let e=(0,oQ.Wj)().addTo(n.current),r=new DOMParser().parseFromString(t,"image/svg+xml").documentElement;if(!(r instanceof SVGElement))throw Error("Invalid SVG content");let i=Number.parseInt(r.getAttribute("width")||"400",10),o=Number.parseInt(r.getAttribute("height")||"600",10);e.viewbox(0,0,i,o),n.current.style.width=`${Math.min(i,298)}px`,e.svg(o0.Z.sanitize(t)).click(()=>{a(s(r))})}catch(e){n.current&&(n.current.innerHTML='<span style="padding: 1rem;">Error rendering SVG. Wait for the image content to complete.</span>')}},[t,i]),(0,y.jsx)(y.Fragment,{children:(0,y.jsx)("div",{ref:n,style:{maxHeight:"80vh",display:"flex",justifyContent:"center",alignItems:"center",cursor:"pointer",wordBreak:"break-word",whiteSpace:"normal",margin:"0 auto"}})})};var o2=n(5914),o4=n(53075),o5=n(92752),o3=n(1274),o6=n(29981),o8=n.n(o6),o9=n(64919),o7=n(56366),se=n(75510);let st=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,se.m6)(o8()(t))};(0,o9.Z)({html:!0,breaks:!0}).use(o3.Z).use(o7.Z,{delimiters:[{left:"\\[",right:"\\]",display:!0},{left:"\\(",right:"\\)",display:!1},{left:"$$",right:"$$",display:!1}]});let sn=e=>{let{isSVG:t,setIsSVG:n}=e;return(0,y.jsx)(J.ZP,{onClick:()=>{n(e=>!e)},children:(0,y.jsx)("div",{className:st("h-4 w-4"),children:(0,y.jsx)(o5.Z,{})})})};var sr=((f=sr||{}).TEXT="text",f.PASSWORD="password",f.EMAIL="email",f.NUMBER="number",f.DATE="date",f.TIME="time",f.DATETIME="datetime",f.CHECKBOX="checkbox",f.SELECT="select",f);let sa=e=>{let{node:t,onSend:n}=e,[r,a]=(0,b.useState)({});(0,b.useEffect)(()=>{let e={};t.children.forEach(t=>{["input","textarea"].includes(t.tagName)&&(e[t.properties.name]=t.properties.value)}),a(e)},[t.children]);let i=e=>{let t={};return e.forEach(e=>{["input","textarea"].includes(e.tagName)&&(t[e.properties.name]=r[e.properties.name])}),t},o=e=>{e.preventDefault();let r=t.properties.dataFormat||"text",a=i(t.children);if("json"===r)console.log("即将发送",r,a),null==n||n(a);else{let e=Object.entries(a).map(e=>{let[t,n]=e;return`${t}: ${n}`}).join("\n");null==n||n(e)}};return(0,y.jsx)("form",{autoComplete:"off",className:"flex flex-col self-stretch pb-3",onSubmit:e=>{e.preventDefault(),e.stopPropagation()},children:t.children.filter(e=>"element"===e.type).map((e,t)=>{var n,i;if("label"===e.tagName)return(0,y.jsx)("label",{htmlFor:e.properties.for,className:"system-md-semibold my-2 text-text-secondary",children:(null==(n=e.children[0])?void 0:n.value)||""},t);if("input"===e.tagName&&Object.values(sr).includes(e.properties.type))return(0,y.jsx)(oc.Z,{type:e.properties.type,name:e.properties.name,placeholder:e.properties.placeholder,value:r[e.properties.name],onChange:t=>{a(n=>({...n,[e.properties.name]:t.target.value}))}},t);if("textarea"===e.tagName)return(0,y.jsx)(oc.Z.TextArea,{name:e.properties.name,placeholder:e.properties.placeholder,value:r[e.properties.name],onChange:t=>{a(n=>({...n,[e.properties.name]:t.target.value}))}},t);if("button"===e.tagName){let n=e.properties.dataVariant;return e.properties.dataSize,(0,y.jsx)(J.ZP,{type:"primary",variant:n,className:"mt-4",onClick:o,children:(null==(i=e.children[0])?void 0:i.value)||""},t)}return(0,y.jsxs)("p",{children:["Unsupported tag: ",e.tagName]},t)})})};sa.displayName="MarkdownForm";let si={sql:"SQL",javascript:"JavaScript",java:"Java",typescript:"TypeScript",vbscript:"VBScript",css:"CSS",html:"HTML",xml:"XML",php:"PHP",python:"Python",yaml:"Yaml",mermaid:"Mermaid",markdown:"MarkDown",makefile:"MakeFile",echarts:"ECharts",shell:"Shell",powershell:"PowerShell",json:"JSON",latex:"Latex",svg:"SVG"},so=e=>e?e in si?si[e]:e.charAt(0).toUpperCase()+e.substring(1):"Plain",ss=e=>{if("string"!=typeof e)return e;let t=/```[\s\S]*?```/g,n=e.match(t)||[],r=e.replace(t,"CODE_BLOCK_PLACEHOLDER");return r=(0,oH.Z)([e=>e.replace(/\\\[(.*?)\\\]/g,(e,t)=>`$$${t}$$`),e=>e.replace(/\\\[([\s\S]*?)\\\]/g,(e,t)=>`$$${t}$$`),e=>e.replace(/\\\((.*?)\\\)/g,(e,t)=>`$$${t}$$`),e=>e.replace(/(^|[^\\])\$(.+?)\$/g,(e,t,n)=>`${t}$${n}$`)])(r),n.forEach(e=>{r=r.replace("CODE_BLOCK_PLACEHOLDER",e)}),r},sl=e=>(0,oH.Z)([e=>e.replace("<think>\n","<details data-think=true>\n"),e=>e.replace("\n</think>","\n[ENDTHINKFLAG]</details>")])(e),sc=(0,b.memo)(e=>{let{inline:t,className:n,children:r,...a}=e,[i,o]=(0,b.useState)(!0),s=/language-(\w+)/.exec(n||""),l=null==s?void 0:s[1],c=so(l||""),u=(0,b.useMemo)(()=>{if("echarts"===l)try{return JSON.parse(String(r).replace(/\n$/,""))}catch(e){}return JSON.parse('{"title":{"text":"ECharts error - Wrong JSON format."}}')},[l,r]),d=(0,b.useMemo)(()=>{let e=String(r).replace(/\n$/,"");return"mermaid"===l&&i?(0,y.jsx)(oG,{PrimitiveCode:e}):"echarts"===l?(0,y.jsx)("div",{style:{minHeight:"350px",minWidth:"100%",overflowX:"scroll"},children:(0,y.jsx)(sh,{children:(0,y.jsx)(oI.Z,{option:u,style:{minWidth:"700px"}})})}):"svg"===l&&i?(0,y.jsx)(sh,{children:(0,y.jsx)(o1,{content:e})}):(0,y.jsx)(oz.Z,{...a,style:oD.Z,customStyle:{paddingLeft:12,borderBottomLeftRadius:"10px",borderBottomRightRadius:"10px",backgroundColor:"var(--color-components-input-bg-normal)"},language:null==s?void 0:s[1],showLineNumbers:!0,PreTag:"div",children:e})},[l,s,a,r,u,i]);return t||!s?(0,y.jsx)("code",{...a,className:n,children:r}):(0,y.jsxs)("div",{className:"relative",children:[(0,y.jsxs)("div",{className:"flex h-8 items-center justify-between rounded-t-[10px] border-b border-divider-subtle bg-components-input-bg-normal p-1 pl-3",children:[(0,y.jsx)("div",{className:"text-gray-700",children:c}),(0,y.jsxs)("div",{className:"flex items-center gap-1",children:[["mermaid","svg"].includes(l)&&(0,y.jsx)(sn,{isSVG:i,setIsSVG:o}),(0,y.jsx)(J.ZP,{children:(0,y.jsx)(o2.Z,{onClick:async()=>{await (0,o4.v)(String(r).replace(/\n$/,"")),Z.ZP.success("复制成功")}})})]})]}),d]})});sc.displayName="CodeBlock";let su=(0,b.memo)(e=>{var t;let{node:n}=e,r=(null==(t=n.children[0])?void 0:t.value)||"";return`<script>${r}</script>`});su.displayName="ScriptBlock";let sd=e=>{let{node:t}=e,n=t.children;return n&&n[0]&&"tagName"in n[0]&&"img"===n[0].tagName?(0,y.jsx)(y.Fragment,{children:Array.isArray(e.children)?(0,y.jsx)("p",{children:e.children.slice(1)}):null}):(0,y.jsx)("p",{children:e.children})},sp=e=>{let{src:t}=e;return(0,y.jsx)("img",{src:t})},sm=e=>{var t;let{node:n,...r}=e;return(0,y.jsx)("a",{...r,target:"_blank",className:"cursor-pointer underline !decoration-primary-700 decoration-dashed",children:n.children[0]?null==(t=n.children[0])?void 0:t.value:"Download"})};function sf(e){let{onSubmit:t}=e,n=(0,oH.Z)([sl,ss])(e.markdownText);return(0,y.jsx)("div",{className:"text-default",children:(0,y.jsx)(oM.UG,{remarkPlugins:[o$.Z,[oO.Z,{singleDollarTextMath:!1}],oL.Z],rehypePlugins:[oR.Z,oF.Z,()=>e=>{let t=e=>{var n;"element"===e.type&&(null==(n=e.properties)?void 0:n.ref)&&delete e.properties.ref,"element"!==e.type||/^[a-z][a-z0-9]*$/i.test(e.tagName)||(e.type="text",e.value=`<${e.tagName}`),e.children&&e.children.forEach(t)};e.children.forEach(t)}],disallowedElements:["iframe","head","html","meta","link","style","body",...e.customDisallowedElements||[]],components:{code:sc,img:sp,a:sm,p:sd,form:e=>(0,y.jsx)(sa,{...e,onSend:e=>{t(JSON.stringify({...e,isFormSubmit:!0}),{inputs:e})}}),script:su,details:oY},children:n})})}class sh extends b.Component{componentDidCatch(e,t){this.setState({hasError:!0}),console.error(e,t)}render(){return this.state.hasError?(0,y.jsxs)("div",{children:["Oops! An error occurred. This could be due to an ECharts runtime error or invalid SVG content. ",(0,y.jsx)("br",{}),"(see the browser console for more information)"]}):this.props.children}constructor(e){super(e),this.state={hasError:!1}}}var sg=n(34014),sv=n(6997),sy=n(82288);function sx(e){let{text:t}=e;return t?(0,y.jsx)("pre",{className:"!m-0 !p-0 !bg-white !border-none",children:t}):"空"}function sb(e){let{uniqueKey:t,items:n,className:r}=e;if(!(null==n?void 0:n.length))return null;let a=n.map(e=>({title:(0,y.jsx)("div",{className:"text-base",children:e.tool?`已使用 ${e.tool}`:"暂无标题"}),status:"success",icon:(0,y.jsx)(sg.Z,{}),description:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(sy.Z,{className:"mt-3 min-w-chat-card",size:"small",items:[{key:`${t}-tool_input`,label:"输入",children:(0,y.jsx)(sx,{text:e.tool_input})},{key:`${t}-observation`,label:"输出",children:(0,y.jsx)(sx,{text:e.observation})}]}),(0,y.jsx)("pre",{className:"border-none",children:e.thought})]})}));return(null==a?void 0:a.length)?(0,y.jsx)(sv.Z,{className:r,items:a}):null}var sw=n(61124),sj=n(50374),sS=n(42634);function sk(e){let{files:t}=e;return(null==t?void 0:t.length)?t.every(e=>"image"===e.type)?(0,y.jsx)("div",{className:"flex flex-wrap",children:t.map(e=>(0,y.jsx)(sS.TV,{children:(0,y.jsx)(sS.HI,{src:e.url,children:(0,y.jsx)("img",{src:e.url&&(location.origin.includes(".medon.com.cn")||location.origin.includes(".medsci.cn")?location.origin+e.url:"https://ai-base.medon.com.cn"+e.url),alt:e.filename,className:"w-24 h-24 cursor-zoom-in mr-2 rounded-lg",style:{objectFit:"cover"}},e.id)})},e.id))}):(0,y.jsx)(y.Fragment,{children:t.map((e,t)=>{var n;return(0,y.jsxs)("a",{title:"点击下载文件",href:e.url,target:"_blank",className:"p-3 bg-gray-50 rounded-lg w-60 flex items-center cursor-pointer no-underline mb-2",children:["image"===e.type?(0,y.jsx)(sw.Z,{className:"text-3xl text-gray-400 mr-2"}):(0,y.jsx)(sj.Z,{className:"text-3xl text-gray-400 mr-2"}),(0,y.jsxs)("div",{className:"overflow-hidden",children:[(0,y.jsx)("div",{className:"text-default truncate",children:(null==(n=e.filename)?void 0:n.split("_").pop())||e.filename}),e.size?(0,y.jsx)("div",{className:"text-desc truncate",children:oE(e.size)}):null]})]},e.id+t)})}):null}function sC(e){let{items:t}=e;return(null==t?void 0:t.length)?(0,y.jsxs)("div",{className:"pb-3",children:[(0,y.jsxs)("div",{className:"flex items-center text-gray-400",children:[(0,y.jsx)("span",{className:"mr-3 text-sm",children:"引用"}),(0,y.jsx)("div",{className:"flex-1 border-gray-400 border-dashed border-0 border-t h-0"})]}),t.map(e=>(0,y.jsx)("div",{className:"mt-2 truncate",children:(0,y.jsx)("a",{className:"text-gray-600",target:"_blank",href:"javascript:void(0)",title:e.document_name,children:e.document_name})},e.id))]}):null}function sN(e){var t;let{appConfig:n,onSubmit:r,messageItem:{id:a,status:i,error:o,agentThoughts:s,workflows:l,files:c,content:u,retrieverResources:d,role:p}}=e,m=(0,b.useMemo)(()=>{let e=u.startsWith("{")&&u.endsWith("}");if("local"===p||"user"===p&&e){var t,r,a;if((null==(t=n.answerForm)?void 0:t.enabled)&&(null==(r=n.answerForm)?void 0:r.feedbackText))try{return JSON.parse(u).isFormSubmit?null==(a=n.answerForm)?void 0:a.feedbackText:u}catch(e){console.log("computedContent json 解析失败",e)}}return u},[u,null==n?void 0:n.answerForm,p]);return"error"===i?(0,y.jsxs)("p",{className:"text-red-700",children:[(0,y.jsx)(o_.Z,{className:"mr-2"}),(0,y.jsx)("span",{children:o})]}):"success"!==i||u||(null==c?void 0:c.length)||(null==s?void 0:s.length)||(null==l||null==(t=l.nodes)?void 0:t.length)||(null==d?void 0:d.length)?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(sb,{uniqueKey:a,items:s,className:"mt-3"}),(null==c?void 0:c.length)?(0,y.jsx)("div",{className:"mt-3",children:(0,y.jsx)(sk,{files:c})}):null,(0,y.jsx)("div",{className:"local"===p||"user"===p?"":"md:min-w-chat-card",children:(0,y.jsx)(sf,{markdownText:m,onSubmit:r})}),(0,y.jsx)(sC,{items:d})]}):(0,y.jsxs)("p",{className:"text-orange-600",children:[(0,y.jsx)(o_.Z,{className:"mr-2"}),(0,y.jsx)("span",{children:"消息内容为空"}),(0,y.jsx)(oP.Z,{title:"可能是用户在生成内容的过程中点击了停止响应按钮",children:(0,y.jsx)(oA.Z,{className:"ml-2"})})]})}n(70003);var sE=n(53024),sT=n(9702),s_=n(10543),sA=n(89696),sP=n(33331),sM=n(16017);function sI(e){let{icon:t,loading:n=!1,active:r=!1,onClick:a}=e,i=b.cloneElement(t,{className:r?"text-primary":""});return(0,y.jsxs)("div",{className:"relative",children:[(0,y.jsx)(J.ZP,{color:"default",variant:"text",size:"small",icon:i,onClick:a}),(0,y.jsx)(V.Z,{className:"absolute left-0 top-0 w-full h-full",spinning:n})]})}function sO(e){let{messageId:t,messageContent:n,feedback:{rating:r,callback:a},feedbackApi:i,ttsApi:o,ttsConfig:s,onSubmit:l}=e,c="like"===r,u="dislike"===r,[d,p]=(0,sM.Z)({like:!1,dislike:!1}),[m,f]=(0,b.useState)(!1),[h,g]=(0,b.useState)(""),{runAsync:v}=(0,ea.Z)(e=>i({messageId:t.replace("-answer",""),rating:e,content:""}),{manual:!0,onSuccess(){Z.ZP.success("操作成功"),null==a||a()},onFinally(){p({like:!1,dislike:!1})}}),x=async e=>{let t=new Audio;t.src=e,t.play(),f(!0),t.addEventListener("ended",()=>{f(!1)})},{runAsync:w,loading:j}=(0,ea.Z)(e=>o({text:e}).then(e=>e.blob()).then(e=>{let t=URL.createObjectURL(e);g(t),x(t)}),{manual:!0}),S=[{icon:(0,y.jsx)(sE.Z,{}),hidden:!1,onClick:()=>{null==l||l(t)}},{icon:(0,y.jsx)(o2.Z,{}),onClick:async()=>{await (0,o4.v)(n),Z.ZP.success("复制成功")},active:!1,loading:!1,hidden:!1},{icon:(0,y.jsx)(sT.Z,{}),onClick:()=>{p({like:!0}),v(c?null:"like")},active:c,loading:d.like,hidden:!1},{icon:(0,y.jsx)(s_.Z,{}),onClick:()=>{p({dislike:!0}),v(u?null:"dislike")},active:u,loading:d.dislike,hidden:!1},{icon:m?(0,y.jsx)(sA.Z,{}):(0,y.jsx)(sP.Z,{}),onClick:()=>{h?x(h):w(n)},active:m,loading:j,hidden:!(null==s?void 0:s.enabled)}];return(0,y.jsx)(oo.Z,{children:S.map((e,t)=>!e.hidden&&(0,y.jsx)(sI,{icon:e.icon,onClick:e.onClick,active:e.active,loading:e.loading},t))})}var sL=n(92266),sR=n(21397),s$=n(2016),sF=n(97004),sz=n(48161),sD=n(71747);let sH=(e,t)=>(0,y.jsxs)(oo.Z,{align:"start",children:[e,(0,y.jsx)("span",{children:t})]}),sZ=e=>{var t,n,r,a;let{onPromptItemClick:i,appParameters:o,description:s,appIcon:l,appConfig:c}=e,u=M(),d=(0,b.useMemo)(()=>(sH((0,y.jsx)(sL.Z,{style:{color:"#FF4D4F"}}),"Hot Topics"),sH((0,y.jsx)(sR.Z,{style:{color:"#1890FF"}}),"Design Guide"),s$.Z,sF.Z,sz.Z,null==o?void 0:o.suggested_questions)?[{key:"remote",label:sH((0,y.jsx)(sL.Z,{style:{color:"#FF4D4F"}}),"Hot Topics"),description:"What are you interested in?",children:o.suggested_questions.map((e,t)=>({key:"index-"+t,description:e}))}]:[],[u]);return(0,y.jsx)("div",{className:"flex justify-center w-full px-3 box-border mx-auto",children:(0,y.jsxs)(oo.Z,{direction:"vertical",className:"pt-8 w-full md:!w-3/4",children:[(0,y.jsx)(sD.Z,{variant:"borderless",icon:l||"https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp",title:(null==(t=c.info)?void 0:t.name)||"Hello, I'm MedSci xAI",description:s||"MedSci xAI"}),(null==(n=d[0].children)?void 0:n.length)||(null==o?void 0:o.opening_statement)?(0,y.jsx)(oy.Z,{title:null==o?void 0:o.opening_statement,vertical:u,items:d,styles:{list:{width:"100%",display:(null==(a=d[0])||null==(r=a.children)?void 0:r.length)?"":"none"},item:u?{width:"100%"}:{flex:1}},onItemClick:i}):null]})})};var sB=n(7712),sU=n(19175),sK=n(23421),sW=e=>{var t;let{messageItems:n,isRequesting:r,nextSuggestions:a,onPromptsItemClick:i,onSubmit:o,onCancel:s,conversationId:l,feedbackCallback:c,difyApi:u,appParameters:d,appConfig:p,onFocus:m}=e;console.log("Chatbox",p);let f=(0,z.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),h=f?f[1]:"",[g,v]=(0,b.useState)(""),x=M(),w=B.Z.get("userInfo"),[j,S]=(0,b.useState)(w&&JSON.parse(w).avatar||"https://img.medsci.cn/web/img/user_icon.png"),k={ai:{placement:"start",avatar:x?void 0:{icon:(0,y.jsx)("img",{src:h.includes("novax")||h.includes("elavax")?null==p||null==(t=p.info)?void 0:t.appIcon:"https://static.medsci.cn/ai-write/robot-08128bd4.png",alt:"robot"}),style:{background:"#fde3cf"}},style:x?void 0:{maxWidth:"calc(100% - 44px)"}},user:{placement:"end",avatar:x?void 0:{icon:j?(0,y.jsx)("img",{src:j,alt:"avatar",onError:()=>{S("https://img.medsci.cn/web/img/user_icon.png")}}):(0,y.jsx)(og.Z,{}),style:{background:j?"":"#87d068",border:"none"}},style:x?void 0:{maxWidth:"calc(100% - 44px)",marginLeft:"44px"}}},C=(0,b.useMemo)(()=>{let e=new Map;return null==n?void 0:n.map(t=>{var n;return"user"===t.role&&e.set(t.id,t),{key:`${t.id}-${t.role}`,content:t.content,messageRender:()=>(0,y.jsx)(sN,{appConfig:p,onSubmit:o,messageItem:t}),role:"local"===t.role?"user":t.role,footer:"ai"===t.role&&(0,y.jsxs)("div",{className:"flex items-center",children:[(0,y.jsx)(sO,{ttsConfig:null==d?void 0:d.text_to_speech,feedbackApi:e=>u.feedbackMessage(e),ttsApi:e=>u.text2Audio(e),messageId:t.id,messageContent:t.content,feedback:{rating:null==(n=t.feedback)?void 0:n.rating,callback:()=>{null==c||c(l)}},onSubmit:n=>{let r=e.get(t.id);n&&r&&o(r.content,{files:(e=>{if(e)return e.map(e=>{var t;return{name:(null==(t=e.filename)?void 0:t.split("_").pop())||e.filename,url:e.url,transfer_method:e.transfer_method,type:e.type,upload_file_id:e.upload_file_id}})})(r.files)})}}),t.created_at&&(0,y.jsxs)("div",{className:"ml-3 text-sm text-desc",children:["回复时间：",t.created_at]})]})}})},[n,l,u,c,p,o]),N=(0,b.useRef)(null),E=(0,b.useDeferredValue)(C);return new URLSearchParams(window.top.location.search),(0,b.useEffect)(()=>{N.current&&N.current.scrollTo({behavior:"smooth",top:N.current.scrollHeight})},[E]),(0,y.jsx)("div",{className:"w-full h-full overflow-hidden my-0 mx-auto box-border flex flex-col gap-4 relative bg-white",children:(0,y.jsxs)("div",{className:"w-full h-full overflow-auto pt-4 pb-48",ref:N,children:[!(null==C?void 0:C.length)&&A(l)&&"medsci-ask"!=h&&(0,y.jsx)(sZ,{appParameters:d,onPromptItemClick:i,description:p.info.description,appIcon:p.info.appIcon,appConfig:p}),(0,y.jsx)(ov.Z.List,{items:C,roles:k,className:"flex-1 w-full md:!w-3/4 mx-auto px-3 md:px-0 box-border"}),(0,y.jsxs)("div",{className:"absolute bottom-0 bg-white w-full md:!w-3/4 left-1/2",style:{transform:"translateX(-50%)"},children:[(0,y.jsx)(oy.Z,{className:"text-default p-3 bg-transparent",items:null==a?void 0:a.map((e,t)=>({key:t.toString(),description:e})),onItemClick:i}),(0,y.jsx)("div",{className:"px-3",children:(0,y.jsx)(oT,{appParameters:d,content:g,onChange:e=>v(e),onSubmit:(e,t)=>{e&&(o(e,t),v(""))},isRequesting:r,className:"w-full",uploadFileApi:async function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=await u.uploadFile(...t);return 0!=r.code?(Z.ZP.error(r.msg),Promise.reject()):u.uploadFile(...t)},onCancel:s,onFocus:m})}),(0,y.jsx)("div",{className:"text-gray-400 text-sm text-center h-8 leading-8",children:"内容由 AI 生成, 仅供参考"})]})]})})},sq=e=>{let{deleteConversationPromise:t,renameConversationPromise:n,items:r,activeKey:a,onActiveChange:i,onItemsChange:o,refreshItems:s,appConfig:l,onchangeModal2Open:c}=e,u=(0,z.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),d=u?u[1]:"";B.Z.get("userInfo");let[p]=on.Z.useForm(),m=async e=>{if(A(e))null==o||o(r.filter(t=>t.key!==e));else{var n;await t(e,null==l||null==(n=l.info)?void 0:n.appId),s()}Z.ZP.success("删除成功"),a===e&&(null==i||i(""))},f=e=>{p.setFieldsValue({name:e.label}),sK.Z.confirm({destroyOnClose:!0,title:"会话重命名",content:(0,y.jsx)(on.Z,{form:p,className:"mt-3",children:(0,y.jsx)(on.Z.Item,{name:"name",children:(0,y.jsx)(oc.Z,{placeholder:"请输入"})})}),onOk:async()=>{await p.validateFields();let t=await p.validateFields();await n(e.key,t.name),Z.ZP.success("会话重命名成功"),s()}})};return(0,y.jsx)(sU.Z,{items:r,activeKey:a,onActiveChange:i,menu:e=>({items:[{label:"重命名",key:"rename",icon:(0,y.jsx)(sB.Z,{}),disabled:A(e.key)},d.includes("novax")||d.includes("elavax")?{label:"删除",key:"delete",icon:(0,y.jsx)(eo.Z,{}),danger:!0}:null],onClick:async t=>{switch(t.domEvent.stopPropagation(),t.key){case"delete":await m(e.key);break;case"rename":f(e)}}})})},sG=n(10965),sV=n(95686),sX=n(39016),sJ=n(16483),sY=n.n(sJ),sQ=n(88627);let s0="ai.medsci.cn"===window.location.hostname?"https://ai.medsci.cn":"https://ai.medon.com.cn",s1=new H({baseURL:s0}),s2=class extends E{async getApps(){return await s1.get("/apps")}async getApp(e,t){try{let n=await s1.get("/dev-api/ai-base/index/getAppByUuid",{appUuid:e},t);if(n.data)return{id:n.data.appUuid,info:{name:n.data.appName,description:n.data.appDescription,appUser:n.data.appUser,appId:n.data.dAppUuid,appType:n.data.appType,appIcon:n.data.appIcon,feeTypes:n.data.feeTypes,tags:[],appUuid:n.data.appUuid},requestConfig:{apiBase:`${s0}/dev-api/ai-base/v1`,apiKey:""},answerForm:{enabled:!1}};return void console.warn("No data found for app with id:",e)}catch(e){console.error("Failed to fetch app:",e);return}}async addApp(e){return s1.post("/apps",e)}async updateApp(e){return s1.put(`/apps/${e.id}`,e)}async deleteApp(e){await s1.delete(`/apps/${e}`)}},s4="新对话";function s5(e){let t=(0,b.useRef)(e);return t.current=e,t}var s3=n(80900),s6=n(70699),s8=n(24923);n(77345);let s9=e=>{let{latestProps:t,latestState:n,appParameters:r,getNextSuggestions:a,filesRef:i,abortRef:o,getConversationMessages:s,onConversationIdChange:l,difyApi:c}=e,{user:u}=N(),[d,p]=(0,b.useState)(""),[m]=(0,s3.Z)({request:async(m,f)=>{let{message:h}=m,{onSuccess:g,onUpdate:v,onError:y}=f,x=await c.sendMessage({inputs:(null==h?void 0:h.inputs)||n.current.inputParams,conversation_id:A(t.current.conversationId)?void 0:t.current.conversationId,files:i.current||[],user:u,response_mode:"streaming",query:null==h?void 0:h.content,requestId:"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}),appUuid:e.appUuid}),b="",w=[],j={},S=[];if(200!==x.status){let e=x.statusText||"请求对话接口失败";Z.ZP.error(e),o.current=()=>{y({name:x.status.toString(),message:e})},o.current();return}let k=(0,s6.Z)({readableStream:x.body}).getReader();for(o.current=()=>{null==k||k.cancel(),y({name:"abort",message:"用户已取消"})};k;){let{value:e,done:t}=await k.read();if(t){g({content:b,files:w,workflows:j,agentThoughts:S});break}if(e)if(e.data){let t={};try{t=JSON.parse(e.data)}catch(e){console.error("解析 JSON 失败",e)}t.task_id&&t.task_id!==d&&p(t.task_id),console.log("parsedData",t,ot),t.event===ot.MESSAGE_END&&(g({content:b,files:w,workflows:j,agentThoughts:S}),s(t.conversation_id),l(t.conversation_id),(null==r?void 0:r.suggested_questions_after_answer.enabled)&&a(t.message_id));let n=t.data;if(t.event===ot.WORKFLOW_STARTED)j.status="running",j.nodes=[],v({content:b,files:w,workflows:j,agentThoughts:S});else if(t.event===ot.WORKFLOW_FINISHED)console.log("工作流结束",t),j.status="finished",v({content:b,files:w,workflows:j,agentThoughts:S});else if(t.event===ot.WORKFLOW_NODE_STARTED)j.nodes=[...j.nodes||[],{id:n.id,status:"running",type:n.node_type,title:n.title}],v({content:b,files:w,workflows:j,agentThoughts:S});else if(t.event===ot.WORKFLOW_NODE_FINISHED){var C;j.nodes=null==(C=j.nodes)?void 0:C.map(e=>e.id===n.id?{...e,status:"success",inputs:n.inputs,outputs:n.outputs,process_data:n.process_data,elapsed_time:n.elapsed_time,execution_metadata:n.execution_metadata}:e),v({content:b,files:w,workflows:j,agentThoughts:S})}if(t.event===ot.MESSAGE_FILE&&v({content:b+=`<img src=""${t.url} />`,files:w,workflows:j,agentThoughts:S}),t.event===ot.MESSAGE&&v({content:b+=t.answer,files:w,workflows:j,agentThoughts:S}),t.event===ot.ERROR&&(console.log("错误",t),y({name:`${t.status}: ${t.code}`,message:t.message}),Z.ZP.error(t.message)),t.event===ot.AGENT_MESSAGE){let e=S[S.length-1];if(!e)continue;{let n=t.answer;e.thought+=n}v({content:b,files:w,workflows:j,agentThoughts:S})}if(t.event===ot.AGENT_THOUGHT){let e=S.findIndex(e=>e.position===t.position),n={conversation_id:t.conversation_id,id:t.id,task_id:t.task_id,position:t.position,tool:t.tool,tool_input:t.tool_input,observation:t.observation,message_files:t.message_files,message_id:t.message_id};-1!==e?S[e]=n:S.push(n),v({content:b,files:w,workflows:j,agentThoughts:S})}}else{console.log("没有数据",e);continue}}}}),{onRequest:f,messages:h,setMessages:g}=(0,s8.Z)({agent:m});return{agent:m,onRequest:f,messages:h,setMessages:g,currentTaskId:d}};var s7=n(31896),le=n(57948),lt=n(33327);function ln(e){let{info:t}=e;return(0,y.jsx)("div",{className:"text-default",children:(0,y.jsxs)("div",{className:"flex items-center justify-center flex-col",children:[(0,y.jsx)(le.Z,{className:"text-2xl text-primary"}),(0,y.jsx)("div",{className:"text-2xl font-bold mt-3",children:t.name}),(0,y.jsx)("div",{className:"text-desc text-base max-w-96 mt-3 text-center",children:t.description}),t.tags?(0,y.jsx)("div",{className:"mt-3 text-center",children:t.tags.map(e=>(0,y.jsx)(lt.Z,{className:"mb-2",children:e},e))}):null]})})}let lr=e=>{let{formFilled:t,onStartConversation:n,user_input_form:r,appInfo:a,conversationId:i}=e,o=(0,z.k6)(),{appUuid:s}=(0,z.UO)(),l=new URLSearchParams(window.top.location.search),[c,u]=(0,b.useState)([]),d=(0,b.useRef)(new URLSearchParams(l)),{mode:p}=N(),[m]=on.Z.useForm(),[f,h]=(0,b.useState)({}),g=o.location.pathname.match(/^\/ai-chat\/([^/]+)$/),v=g?g[1]:"";return(0,b.useEffect)(()=>{m.resetFields()},[i]),(0,b.useEffect)(()=>{if(!t&&(null==r?void 0:r.length)||u([]),u((null==r?void 0:r.map(e=>{if(e["text-input"]){var t;let n=e["text-input"],r={type:"input",label:n.label,name:n.variable},a=new URLSearchParams(null==(t=top)?void 0:t.location.search).get(n.variable);return a&&(m.setFieldValue(n.variable,a),f[n.variable]=a,d.current.delete(n.variable)),n.required&&(r.required=!0,r.rules=[{required:!0,message:"请输入"}]),r}return{}}))||[]),l.size!==d.current.size){d.current.has("isNewCvst")&&d.current.delete("isNewCvst");let e=d.current.size?`?${d.current.toString()}`:"";"multiApp"===p?n(f):o.push(`/ai-chat${e}`)}},[r]),(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center -mt-5",children:(0,y.jsx)("div",{className:"max-w-[80vw] w-3/5  px-10 rounded-3xl bg-gray-100 box-border",children:a&&!("medsci-ask"==v||l.get("fromPlatform"))&&(null==r?void 0:r.length)?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(ln,{info:a}),!t&&(null==r?void 0:r.length)?(0,y.jsx)(on.Z,{form:m,className:"mt-6",labelCol:{span:5},children:c.map(e=>(0,y.jsx)(on.Z.Item,{name:e.name,label:e.label,required:e.required,rules:e.rules,children:"input"===e.type?(0,y.jsx)(oc.Z,{placeholder:"请输入"}):"select"===e.type?(0,y.jsx)(od.Z,{placeholder:"请选择"}):"不支持的控件类型"},e.name))}):null,(0,y.jsx)("div",{className:"mt-3 w-full flex justify-center",children:(0,y.jsx)(J.ZP,{type:"primary",icon:(0,y.jsx)(s7.Z,{}),onClick:async()=>{await m.validateFields(),n(m.getFieldsValue())},children:"开始对话"})})]}):!("medsci-ask"==v||l.get("fromPlatform"))&&(null==r?void 0:r.length)?(0,y.jsx)(ol.Z,{description:"请先配置 Dify 应用"}):null})})};function la(e){let t=(0,z.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),n=t?t[1]:"";return(0,y.jsx)("div",{className:"flex h-full items-center flex-[1.5] overflow-hidden justify-center text-primary font-semibold",children:(0,y.jsx)("div",{className:"flex items-center rounded-3xl shadow-md py-1 px-2 text-sm bg-white",children:n.includes("novax")||n.includes("elavax")?(0,y.jsx)("div",{className:"flex items-center",children:e.children}):(0,y.jsx)("a",{href:top.location.href,title:e.children,children:e.children})})})}let li=e=>{var t,n;let{centerChildren:r,showSubscribe:a,subStatusDetail:i,selectedAppId:o,appList:s,appConfig:l}=e,{mode:c}=N(),u=Q.get("userInfo"),d=u&&(null==(t=JSON.parse(u))?void 0:t.avatar)?JSON.parse(u).avatar:"https://img.medsci.cn/web/img/user_icon.png",p=(0,z.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),m=p?p[1]:"",[f,h]=(0,b.useState)(!1);location.origin.includes("medsci.cn")||location.origin.includes("medon.com.cn");let g=e=>{e.target.src="https://img.medsci.cn/web/img/user_icon.png"},v=()=>Q.get("ai_apps_lang")?Q.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,x=async()=>{h(!1),Q.remove("userInfo",{domain:".medon.com.cn"}),Q.remove("userInfo",{domain:".medsci.cn"}),Q.remove("userInfo",{domain:"localhost"}),Q.remove("yudaoToken",{domain:"ai.medon.com.cn"}),Q.remove("yudaoToken",{domain:"ai.medsci.cn"}),Q.remove("yudaoToken",{domain:".medon.com.cn"}),Q.remove("yudaoToken",{domain:".medsci.cn"}),Q.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("conversation"),window.location.origin.includes("medsci.cn")?window.top.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.top.location.href:window.top.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.top.location.href};return(0,y.jsxs)("div",{className:"h-12 !leading-[3rem] px-4 text-base top-0 z-20 bg-white w-full shadow-sm font-semibold justify-between flex items-center box-border",children:[(0,y.jsx)("div",{className:"flex-1 overflow-hidden flex items-center justify-center",children:r}),"zh-CN"==v()&&!m.includes("novax")&&!m.includes("elavax")&&(0,y.jsx)("div",{onClick:()=>a(),className:"px-[15px] py-[4px] flex items-center h-[28px] rounded border-none mr-[8px] text-xs text-[#614018]",style:{backgroundImage:"linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"},children:"免费"==i.packageType?"升级订阅":"连续包月"==i.packageType||"连续包年"==i.packageType?"修改订阅":"订阅"}),m.includes("novax")||m.includes("elavax")?(0,y.jsx)(la,{children:o?(0,y.jsx)(y.Fragment,{children:(0,y.jsx)(ei.Z,{className:"mr-2 ml-2",arrow:!0,placement:"bottom",trigger:["click"],menu:{style:{},selectedKeys:[o],items:[...(null==s?void 0:s.map(e=>{let t=o===e.id;return{key:e.id,label:(0,y.jsx)("div",{className:t?"text-primary":"text-default",children:null==e?void 0:e.info.name}),onClick:()=>{if(m.includes("novax")||m.includes("elavax")){window.top.location.href=location.origin+(window.top.location.href.includes("ai-chat")?"/ai-chat/":"/chat/")+`${null==e?void 0:e.info.appNameEn}`;return}},icon:(0,y.jsx)(en.Z,{})}}))||[]]},children:(0,y.jsxs)("div",{className:"cursor-pointer flex items-center",children:[(0,y.jsx)("span",{className:"cursor-pointer w-[75px] inline-block whitespace-nowrap overflow-hidden text-ellipsis",children:null==l||null==(n=l.info)?void 0:n.name}),(0,y.jsx)(er.Z,{className:"ml-1"})]})})}):null}):null,(0,y.jsx)("div",{className:"px-[15px] py-[4px] mr-[8px] h-[28px] flex items-center",style:{background:"#f1f5f9"},children:(0,y.jsx)("a",{style:{borderRadius:"4px",fontSize:"12px",color:"#666",lineHeight:"1"},className:"backImg ",href:location.origin.includes(".medon.com.cn")?"https://ai.medon.com.cn/"+v():location.origin.includes(".medsci.cn")?"https://ai.medsci.cn/"+v():"/"+v(),target:"_top",children:"返回首页"})}),!u&&(0,y.jsx)("div",{className:"hover:cursor-pointer",onClick:()=>{let e=v();if(e&&"zh-CN"!==e)window.top.location.href=location.origin+"/"+e+"/login";else{var t,n;null==(t=(n=window).addLoginDom)||t.call(n)}},children:"登录"}),(0,y.jsx)(sG.Z,{placement:"bottomLeft",trigger:"hover",arrow:f,overlayStyle:{width:300,height:163},content:(0,y.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",position:"relative",paddingBottom:"40px"},children:[(0,y.jsx)("a",{className:"exit text-right w-full text-[#333333]",onClick:x,children:"退出"}),(0,y.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},className:"iconHeader bg-write",children:[u&&JSON.parse(u||"").userId&&(0,y.jsx)("img",{src:d,onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),style:{width:"60px",height:"60px"},onError:g,alt:"avatar"}),(0,y.jsx)("span",{className:"account",children:u&&JSON.parse(u||"").userName})]})]}),children:(0,y.jsx)("a",{href:"#",children:(0,y.jsx)("div",{className:"img-area  leading-none",children:u&&JSON.parse(u||"").userId&&(d?(0,y.jsx)("img",{src:d,onError:g,onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),style:{width:"32px",height:"32px"},alt:"avatar"}):(0,y.jsx)(og.Z,{}))})})})]})};var lo=n(69935);let ls=e=>{var t,n,r;let{userInfo:a,currentItem:i,onClose:o,appConfig:s}=e;a=JSON.parse(a)||{};let[l,c]=(0,b.useState)(!1),[u,d]=(0,b.useState)({}),[p,m]=(0,b.useState)(null),[f,h]=(0,b.useState)(""),[g,v]=(0,b.useState)(a.avatar||"https://img.medsci.cn/web/img/user_icon.png"),[x,w]=(0,b.useState)(window.innerWidth>768),[j,S]=(0,b.useState)(null),[k,C]=(0,b.useState)(null),N=e=>({"tool.Support_Alipay_Payment":"支持支付宝支付","tool.Meisi_Account":"梅斯账号","tool.Please_activate_after_reading_and_agreeing_to_the_agreement":"请阅读并同意协议后激活","tool.Free_Trial":"免费试用","market.subscribe":"订阅","tool.Month":"月","tool.Quarter":"季","tool.Year":"年","tool.sS":"订阅成功"})[e]||e,E=new G;window.location.origin.includes("medon.com.cn")||window.location.origin.includes("medsci.cn");let T=()=>{w(window.innerWidth>768)},_=(e,t)=>{d(e),m(t),(null==e?void 0:e.coinType)=="人民币"&&0!==e.feePrice&&M(e,s.id)},A=e=>{let t=setInterval(async()=>{var n;let r=await E.getSubOrder({piId:`${e}`},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});(null==r||null==(n=r.data)?void 0:n.payStatus)==="PAID"&&(window.location.reload(),clearInterval(t))},2e3);C(t)},P=()=>B.Z.get("ai_apps_lang")?B.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,M=async(e,t)=>{if(!(null==e?void 0:e.coinType))return void Z.ZP.warning("请选择订阅服务周期");let n=P();if(a.userId){let n={appUuid:t||"",priceId:e.priceId,monthNum:e.monthNum,packageKey:e.packageKey,packageType:e.type};try{c(!0);let t=await E.createSubscription(n,{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});if((null==t?void 0:t.code)==0)if(c(!1),"人民币"==e.coinType&&0!==e.feePrice){let e=t.data,n=window.location.origin,r=(n.includes(".medsci.cn")||n.includes(".medon.com.cn"),`${n}/payLink/${encodeURIComponent(e)}`);h(r),S(JSON.parse(e).piId),k&&clearInterval(k),A(JSON.parse(e).piId)}else Z.ZP.success(N("tool.sS")),setTimeout(()=>{window.top.location.href=t.data},1e3)}catch(e){c(!1),console.error(e)}}else n&&"zh-CN"!==n?window.top.location.href=location.origin+"/"+n+"/login":console.log("Trigger login")};return(0,b.useEffect)(()=>{var e;return"写作"===i.appType&&localStorage.setItem(`appWrite-${s.id}`,JSON.stringify({appUuid:s.id,directoryMd:i.directoryMd})),T(),window.addEventListener("resize",T),x&&(null==(e=i.feeTypes)?void 0:e.length)===1&&_(i.feeTypes[0],0),()=>{window.removeEventListener("resize",T),k&&clearInterval(k)}},[i,x]),(0,y.jsx)("div",{id:"app",children:(0,y.jsxs)("div",{className:"scale",children:[(0,y.jsxs)("div",{className:"micro_header",children:[(0,y.jsx)("div",{className:"micro_left",children:(0,y.jsxs)("div",{className:"avatar",children:[(0,y.jsx)("img",{src:g,onError:()=>{v("https://img.medsci.cn/web/img/user_icon.png")},alt:""}),(0,y.jsx)("span",{className:"t1",children:a.realName||a.userName})]})}),(0,y.jsx)("div",{className:"micro_right",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png",alt:"",onClick:()=>{k&&clearInterval(k),null==o||o(!1)}})})]}),(0,y.jsxs)("div",{className:"micro_main",children:[(0,y.jsx)("div",{className:"micro_main_top",children:(0,y.jsx)("div",{className:"micro_main-sp",children:(0,y.jsx)("div",{className:"micro_main_temp",children:((null==(t=i.feeTypes[0])?void 0:t.coinType)=="美元"||(null==(n=i.feeTypes)?void 0:n.length)>1)&&(0,y.jsx)("div",{className:"swiper-vip",children:null==(r=i.feeTypes)?void 0:r.map((e,t)=>(0,y.jsxs)("div",{className:"swiper-vip-item",onClick:()=>_(e,t),children:[(0,y.jsx)("div",{className:"newer",style:{left:t%4==0&&0!==t?"6px":"-1px"}}),(0,y.jsxs)("div",{className:`swiper-vip-item-child ${p===t?"sactvie":""}`,children:[(0,y.jsx)("div",{className:"title",children:e.type}),(0,y.jsxs)("div",{className:"pricePc",children:[(0,y.jsx)("span",{children:"人民币"===e.coinType?"\xa5":"$"})," ",e.feePrice]})]})]},t))})})})}),(0,y.jsx)("div",{className:"micro_main_middle",children:(0,y.jsxs)("div",{className:"micro_main_middle_banner",children:[(0,y.jsxs)("div",{className:"micro_main_middle_title",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png",alt:""}),i.name]}),(0,y.jsx)("div",{className:"micro_main_middle_content",children:i.description})]})}),u.coinType&&0!==u.feePrice&&(null==u?void 0:u.coinType)=="人民币"&&(0,y.jsx)("div",{className:"micro_main_bottom",children:(0,y.jsx)("div",{className:"micro_pay",children:(0,y.jsxs)("div",{className:"micro_pay_right",children:[l&&(0,y.jsx)("div",{className:"noQrCode"}),!l&&f&&(0,y.jsx)("div",{className:"qr-code",children:(0,y.jsx)(lo.Q,{value:f,size:131,fgColor:"#000",level:"L"})}),(0,y.jsxs)("div",{className:"price",children:[(0,y.jsxs)("div",{className:"micro_way",children:[(0,y.jsx)("div",{className:"box",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})}),(0,y.jsx)("span",{children:N("tool.Support_Alipay_Payment")})]}),(0,y.jsxs)("span",{className:"t1",children:[N("tool.Support_Alipay_Payment"),(0,y.jsx)("span",{className:"bd",children:u.feePrice}),"人民币"===u.coinType?"\xa5":"$","/",3===u.monthNum?N("tool.Quarter"):12===u.monthNum?N("tool.Year"):N("tool.Month")]}),(0,y.jsxs)("span",{className:"t2",children:[N("tool.Meisi_Account"),"：",a.userName]}),(0,y.jsxs)("span",{className:"t3",onClick:()=>{window.open("https://www.medsci.cn/about/index.do?id=27")},children:[N("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"),(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})]})]})]})})}),u.coinType&&0===u.feePrice&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>M(u,s.id),children:N("tool.Free_Trial")})}),u.coinType&&u.feePrice>0&&(null==u?void 0:u.coinType)=="美元"&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>M(u,s.id),children:N("market.subscribe")})})]})]})})};var ll=n(69922);let lc=e=>{var t;let{userInfo:n={},currentItem:r={},onClose:a}=e,[i,o]=(0,b.useState)(!0),[s,l]=(0,b.useState)(!0),[c,u]=(0,b.useState)(!1),[d,p]=(0,b.useState)({}),[m,f]=(0,b.useState)(!1),[h,g]=(0,b.useState)(!1),[v,x]=(0,b.useState)(!1),[w,j]=(0,b.useState)(!1),[S,k]=(0,b.useState)((null==n?void 0:n.avatar)||"https://img.medsci.cn/web/img/user_icon.png"),C=(0,b.useRef)(null),N=new G;window.location.origin.includes("medon.com.cn")||window.location.origin.includes("medsci.cn"),(0,b.useEffect)(()=>{var e,t;let a=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${a}px`),"写作"===r.appType&&localStorage.setItem(`appWrite-${r.appUuid}`,JSON.stringify({appUuid:r.appUuid,directoryMd:r.directoryMd})),(null==(e=r.feeTypes)?void 0:e.length)===1&&r.feeTypes[0].feePrice>=0&&p(r.feeTypes[0]),(null==(t=JSON.parse(n))?void 0:t.userId)&&u(!0),"medsci"===new URLSearchParams(window.location.search).get("source")&&g(!0)},[r,n]);let E=()=>navigator.userAgent.includes("medsci_app"),T=()=>B.Z.get("ai_apps_lang")?B.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,_=()=>{let e=T();if(e&&"zh-CN"!==e)window.top.location.href=location.origin+"/"+e+"/login";else{var t,n;null==(t=(n=window).addLoginDom)||t.call(n)}},A=e=>{p(e)},P=async(e,t,r)=>{var a;if(!v&&r){j(!0),setTimeout(()=>j(!1),500);return}let i=T();if(null==(a=JSON.parse(n))?void 0:a.userId){let n={appUuid:t||"",priceId:e.priceId,monthNum:e.monthNum,packageKey:e.packageKey,packageType:e.type};try{let t=await N.createSubscription(n,{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});if(0==t.code)if("人民币"==e.coinType&&0!==e.feePrice){let e=await N.createAliSub(JSON.parse(t.data));e&&0==e.code&&(window.top.location.href=e.data)}else Z.ZP.success("订阅成功"),setTimeout(()=>{window.top.location.href=t.data},1e3)}catch(e){Z.ZP.error("订阅失败")}}else i&&"zh-CN"!==i?window.top.location.href=location.origin+"/"+i+"/login":_()};return(0,y.jsxs)("div",{className:`vip ${E()?"sp":""}`,children:[!E()&&(0,y.jsx)("div",{className:"vip-head text-center py-2 text-lg font-medium text-gray-800",children:r.name}),(0,y.jsxs)("div",{className:"vip-introduce bg-gray-900 pt-9 pb-4 relative",children:[(0,y.jsx)("img",{className:"crown absolute right-1 top-0 h-9 object-contain",src:"https://static.medsci.cn/public-image/ms-image/1dcd7d10-58af-11ec-8e2f-1389d01aad85_crown.png",alt:""}),(0,y.jsx)("div",{className:"box mx-3 flex items-center justify-between bg-[url('https://static.medsci.cn/public-image/ms-image/21ad8b80-58b1-11ec-8e2f-1389d01aad85_vip-h5.png')] bg-cover p-3 rounded",children:c?(0,y.jsxs)("div",{className:"box-left flex items-center",children:[(0,y.jsx)("img",{className:"avatar w-8 h-8 rounded-full mr-3",src:S,alt:"",onError:()=>{k("https://img.medsci.cn/web/img/user_icon.png")}}),(0,y.jsx)("div",{className:"box-word",children:(0,y.jsx)("span",{className:"t1 text-lg text-[#58342c]",children:JSON.parse(n).realName||JSON.parse(n).userName})})]}):(0,y.jsxs)("div",{className:"box-left-1 flex items-center",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/web/img/user_icon.png",alt:"",className:"w-8 h-8 rounded-full mr-3"}),(0,y.jsxs)("div",{className:"left2",children:[(0,y.jsx)("span",{className:"t1 text-lg text-[#58342c] cursor-pointer",onClick:_,children:"立即登录"}),(0,y.jsx)("span",{className:"t2 text-sm text-[#7f5947] mt-1",children:"请登录后购买"})]})]})})]}),(0,y.jsxs)("div",{className:"vip-main bg-white rounded-t-3xl -mt-14 z-10 pb-24",children:[(0,y.jsx)("div",{className:"vip-one pl-3",children:(0,y.jsx)("div",{className:"big overflow-auto mr-3",children:(0,y.jsx)("ul",{ref:C,className:"flex mt-2 mb-1",children:null==(t=r.feeTypes)?void 0:t.map((e,t)=>(0,y.jsxs)("li",{className:`min-w-[135px] w-[135px] bg-white border border-gray-300 rounded-lg mr-3 text-center cursor-pointer ${e.type===d.type?"sactvie":""}`,onClick:()=>A(e),children:[(0,y.jsx)("div",{className:"title text-center ellipsis-2-lines mx-auto mt-6 max-w-[102.5px] text-sm font-medium text-gray-800",children:e.type}),(0,y.jsxs)("div",{className:"prices text-2xl font-bold text-orange-400 mt-2 mb-6",children:[(0,y.jsx)("span",{className:"text-lg",children:"人民币"===e.coinType?"\xa5":"$"}),e.feePrice]}),e.originalPrice&&(0,y.jsxs)("div",{className:"isfava relative text-xs text-gray-500 line-through",children:["人民币"===e.coinType?"\xa5":"$",e.feePrice]})]},t))})})}),(0,y.jsx)("div",{className:"vip-two border border-gray-200 rounded m-3 p-2",children:(0,y.jsxs)("div",{className:"vip-two_banner bg-[url('https://img.medsci.cn/202503/48a49f899b4544e39a864d1821b3555a-lRXRIQx3q1VB.png')] bg-cover p-3",children:[(0,y.jsxs)("div",{className:"vip-two_title flex items-center text-sm font-medium text-orange-600",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png",alt:"",className:"w-2 h-2 mx-1"}),r.name]}),(0,y.jsx)("div",{className:"vip-two_content bg-white p-3 text-sm text-gray-700 leading-5 max-h-[calc(var(--vh)*20)] overflow-auto rounded",children:r.description})]})}),d.feePrice>0&&(null==d?void 0:d.coinType)=="人民币"&&(0,y.jsx)("div",{className:"vip-three mt-3",children:(0,y.jsxs)("div",{className:`pay mx-3 p-4 rounded-lg bg-gradient-to-b from-[#fffaf6] to-[#fff8f0] ${m?"h-28":""}`,children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:"",className:"w-15 mb-3"}),(0,y.jsxs)("div",{className:"item flex justify-between items-center bg-white border border-orange-300 rounded p-2 mb-3 cursor-pointer",onClick:()=>{o(!1),l(!0)},children:[(0,y.jsxs)("div",{className:"item-left flex items-center",children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:"",className:"w-5 h-5 mr-2"}),(0,y.jsx)("span",{className:"text-sm text-gray-800",children:"支付宝支付"})]}),(0,y.jsx)("div",{className:"item-right w-5 h-5",children:s&&(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:"",className:"w-full"})})]})]})})]}),d.feePrice>=0&&(0,y.jsx)("div",{className:"vip-pay btns fixed bottom-0 w-full bg-white shadow-lg flex justify-around items-center h-24 z-20",children:0!==d.feePrice&&(null==d?void 0:d.coinType)=="人民币"?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{className:"pay-left ml-2",children:[(0,y.jsx)("div",{className:"t1 text-sm font-medium text-orange-400 mb-1",children:r.name}),(0,y.jsxs)("div",{className:`t2 flex items-start text-xs text-gray-500 ${w?"shake":""}`,children:[(0,y.jsx)(ll.Z,{checked:v,onChange:e=>{x(e.target.checked)},style:{fontSize:"14px"},className:"mr-1"}),(0,y.jsxs)("span",{onClick:()=>{let e="https://www.medsci.cn/about/index.do?id=27";E()?window.top.location.href=e:window.open(e)},className:"cursor-pointer",children:["请在阅读并同意",(0,y.jsx)("span",{className:"text-blue-500",children:"协议"}),"后开通"]})]})]}),(0,y.jsx)("div",{className:"pay-right mr-2 w-48 h-12 bg-gradient-to-r from-[#fde39b] to-[#fbd786] rounded-full flex items-center justify-center cursor-pointer",onClick:()=>P(d,r.appUuid,"ali"),children:(0,y.jsxs)("span",{className:"text-sm text-[#614018]",children:[d.feePrice,"元确认协议并支付"]})})]}):(0,y.jsx)(J.ZP,{type:"primary",className:"w-48 h-12 rounded-full",onClick:()=>P(d,r.appUuid,""),children:0===d.feePrice?"免费试用":"订阅"})})]})},lu=e=>{var t,n,r,a,i,o;let{userInfo:s,currentItem:l,onClose:c,appConfig:u,subStatusDetail:d}=e;s=s&&JSON.parse(s);let[p,m]=(0,b.useState)(!1),[f,h]=(0,b.useState)({}),[g,v]=(0,b.useState)(null),[x,w]=(0,b.useState)(""),[j,S]=(0,b.useState)(0),[k,C]=(0,b.useState)((null==s?void 0:s.avatar)||"https://img.medsci.cn/web/img/user_icon.png"),[N,E]=(0,b.useState)(window.innerWidth>768),[T,_]=(0,b.useState)(null),[A,P]=(0,b.useState)(null);(0,b.useEffect)(()=>{var e;l="zh-CN"==z()?d:l,(null==d||null==(e=d.feeTypes)?void 0:e.length)>0&&"zh-CN"==z()&&(null==d||d.feeTypes.forEach((e,t)=>{d.packageType==e.type&&(v(t),h(e))}))},[]);let M=e=>({"tool.Support_Alipay_Payment":"支持支付宝支付","tool.Meisi_Account":"梅斯账号","tool.Please_activate_after_reading_and_agreeing_to_the_agreement":"请阅读并同意协议后激活","tool.Free_Trial":"免费试用","market.subscribe":"订阅","tool.Month":"月","tool.Quarter":"季","tool.Year":"年","tool.sS":"订阅成功"})[e]||e,I=new G;window.location.origin.includes("medon.com.cn")||window.location.origin.includes("medsci.cn");let O=()=>{window.open("https://www.medsci.cn/about/index.do?id=27")},L=()=>{E(window.innerWidth>768)},R=()=>{A&&clearInterval(A),null==c||c(!1)},$=(e,t)=>{h(e),v(t),(null==e?void 0:e.coinType)=="人民币"&&0!==e.feePrice&&H(e,u.id)},F=e=>{let t=setInterval(async()=>{var n;let r=await I.getSubOrder({piId:`${e}`},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});(null==r||null==(n=r.data)?void 0:n.payStatus)=="PAID"&&(window.location.reload(),clearInterval(t))},2e3);P(t)},z=()=>B.Z.get("ai_apps_lang")?B.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,D=async()=>{let e=await I.freeLimit({},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});S(null==e?void 0:e.data)},H=async(e,t)=>{if(!(null==e?void 0:e.coinType))return void Z.ZP.warning("请选择订阅服务周期");let n=z();if(null==s?void 0:s.userId){let n={appUuid:t,priceId:e.priceId,monthNum:e.monthNum,packageKey:e.packageKey,packageType:e.type};try{m(!0);let t=await I.createSubscription(n,{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});if((null==t?void 0:t.code)==0)if(m(!1),"人民币"==e.coinType&&0!==e.feePrice){let e=t.data,n=window.location.origin,r=(n.includes(".medsci.cn")||n.includes(".medon.com.cn"),`${n}/payLink/${encodeURIComponent(e)}`);w(r),_(JSON.parse(e).piId),F(JSON.parse(e).piId)}else Z.ZP.success(M("tool.sS")),setTimeout(()=>{window.top.location.href=t.data},1e3)}catch(e){m(!1),console.error(e)}}else if(n&&"zh-CN"!=n)window.top.location.href=location.origin+"/"+n+"/login";else{var r,a;null==(r=(a=window).addLoginDom)||r.call(a)}};return(0,b.useEffect)(()=>{var e;return D(),"写作"==l.appType&&localStorage.setItem(`appWrite-${u.id}`,JSON.stringify({appUuid:u.id,directoryMd:l.directoryMd})),L(),window.addEventListener("resize",L),N&&(null==(e=l.feeTypes)?void 0:e.length)==1&&$(l.feeTypes[0],0),()=>{window.removeEventListener("resize",L),A&&clearInterval(A)}},[l,N]),(0,y.jsx)("div",{id:"app",children:(0,y.jsxs)("div",{className:"scale",children:[(0,y.jsxs)("div",{className:"micro_header",children:[(0,y.jsx)("div",{className:"micro_left",children:(0,y.jsxs)("div",{className:"avatar",children:[(0,y.jsx)("img",{src:k,onError:()=>{C("https://img.medsci.cn/web/img/user_icon.png")},alt:""}),(0,y.jsx)("span",{className:"t1",children:(null==s?void 0:s.realName)||(null==s?void 0:s.userName)})]})}),(0,y.jsx)("div",{className:"micro_right",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png",alt:"",onClick:R})})]}),(0,y.jsxs)("div",{className:"micro_main",children:[(0,y.jsx)("div",{className:"micro_main_top",children:(0,y.jsx)("div",{className:"micro_main-sp",children:(0,y.jsxs)("div",{className:"micro_main_temp",children:[((null==(t=l.feeTypes[0])?void 0:t.coinType)=="美元"||(null==(n=l.feeTypes)?void 0:n.length)>1)&&"zh-CN"==z()&&(0,y.jsx)("div",{className:"swiper-vip",children:null==(r=l.feeTypes)?void 0:r.map((e,t)=>(0,y.jsxs)("div",{className:`swiper-vip-item ${(null==d?void 0:d.packageType)=="连续包月"&&"免费"==e.type||(null==d?void 0:d.packageType)=="连续包月"&&"连续包年"==e.type||(null==d?void 0:d.packageType)=="连续包年"&&"免费"==e.type||(null==d?void 0:d.packageType)=="连续包年"&&"连续包月"==e.type?"noClick":""}`,onClick:()=>$(e,t),children:[(0,y.jsx)("div",{className:"newer",style:{left:t%4==0&&0!==t?"6px":"-1px"}}),(0,y.jsxs)("div",{className:`swiper-vip-item-child ${g==t?"sactvie":""}`,children:[(0,y.jsx)("div",{className:"title",children:e.type}),(0,y.jsxs)("div",{className:"pricePc",children:[(0,y.jsx)("span",{children:"人民币"==e.coinType?"\xa5":"$"})," ",e.feePrice]})]})]},t))}),((null==(a=l.feeTypes[0])?void 0:a.coinType)=="美元"||(null==(i=l.feeTypes)?void 0:i.length)>1)&&"zh-CN"!=z()&&(0,y.jsx)("div",{className:"swiper-vip",children:null==(o=l.feeTypes)?void 0:o.map((e,t)=>(0,y.jsxs)("div",{className:"swiper-vip-item",onClick:()=>$(e,t),children:[(0,y.jsx)("div",{className:"newer",style:{left:t%4==0&&0!==t?"6px":"-1px"}}),(0,y.jsxs)("div",{className:`swiper-vip-item-child ${g==t?"sactvie":""}`,children:[(0,y.jsx)("div",{className:"title",children:e.type}),(0,y.jsxs)("div",{className:"pricePc",children:[(0,y.jsx)("span",{children:"人民币"==e.coinType?"\xa5":"$"})," ",e.feePrice]})]})]},t))})]})})}),z()&&"zh-CN"==z()?(0,y.jsx)("div",{className:"micro_main_middle",children:(0,y.jsxs)("div",{className:"micro_main_middle_banner",children:[(0,y.jsxs)("div",{className:"micro_main_middle_title",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"}),"梅斯小智 订阅说明"]}),(0,y.jsxs)("div",{className:"micro_main_middle_content",children:[(0,y.jsxs)("div",{children:["免费：每个自然月内，每个智能体的使用上限",j,"次。次月开始重新计次。"]}),(0,y.jsx)("div",{children:"连续包月：订阅之日起一个月内，每个智能体不限使用次数。"}),(0,y.jsx)("div",{children:"连续包年：订阅之日起一年内，每个智能体不限使用次数"})]})]})}):(0,y.jsx)("div",{className:"micro_main_middle",children:(0,y.jsxs)("div",{className:"micro_main_middle_banner",children:[(0,y.jsxs)("div",{className:"micro_main_middle_title",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png",alt:""}),l.name]}),(0,y.jsx)("div",{className:"micro_main_middle_content",children:l.description})]})}),z()&&"zh-CN"==z()?(0,y.jsxs)("div",{className:"micro_main_bottom onborder",children:[("1"==d.subStatus||"3"==d.subStatus)&&f.type==d.packageType&&(0,y.jsxs)("div",{className:"result",children:[d.subAt," 已订阅"]}),"1"==d.subStatus&&"免费"==d.packageType&&"免费"==f.type&&(0,y.jsx)("div",{className:"result",children:"免费使用中…"}),"3"==d.subStatus&&(0,y.jsxs)("div",{className:"result",children:[d.unSubAt," 取消订阅"]}),"3"==d.subStatus&&(0,y.jsxs)("div",{className:"result",children:["您的订阅可使用至 ",d.expireAt]}),"连续包月"==d.packageType&&"1"==d.subStatus&&(0,y.jsx)("div",{className:"result",children:"连续包月中…"}),"连续包年"==d.packageType&&"1"==d.subStatus&&(0,y.jsx)("div",{className:"result",children:"连续包年中…"}),(0,y.jsxs)("div",{className:"btns",children:["连续包月"==d.packageType&&"1"==d.subStatus?(0,y.jsx)("button",{className:"cursor-pointer",onClick:()=>{sQ.Vq.confirm({title:"提示",message:`取消包月在${d.expireAt}号生效，再次使用需要重新订阅。是否确认取消？`,confirmButtonColor:"#D7813F",onCancel:()=>{},onConfirm:async()=>{await I.cancelSubscription({},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`}),R()}})},children:"取消包月"}):null,"连续包年"==d.packageType&&"1"==d.subStatus?(0,y.jsx)("button",{className:"cursor-pointer",onClick:()=>{sQ.Vq.confirm({title:"提示",message:`取消包月在${d.expireAt}号生次生效，再次使用需要重新订阅。是否确认取消？`,confirmButtonColor:"#D7813F",onCancel:()=>{R()},onConfirm:async()=>{await I.cancelSubscription({},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`}),R()}})},children:"取消包年"}):null]}),(0,y.jsx)("div",{className:"micro_pay",children:f.feePrice>0&&"免费"!=f.type&&"0"==d.subStatus||"免费"!=f.type&&"2"==d.subStatus||"免费"==d.packageType&&"免费"!=f.type?(0,y.jsx)("div",{className:"micro_pay",children:(0,y.jsxs)("div",{className:"micro_pay_right",children:[p&&(0,y.jsx)("div",{className:"noQrCode"}),!p&&x&&(0,y.jsx)("div",{className:"qr-code",children:(0,y.jsx)(lo.Q,{value:x,size:131,fgColor:"#000",level:"L"})}),(0,y.jsxs)("div",{className:"price",children:[(0,y.jsxs)("div",{className:"micro_way",children:[(0,y.jsx)("div",{className:"box",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})}),(0,y.jsx)("span",{children:M("tool.Support_Alipay_Payment")})]}),(0,y.jsxs)("span",{className:"t1",children:[M("tool.Support_Alipay_Payment"),(0,y.jsx)("span",{className:"bd",children:f.feePrice}),"人民币"==f.coinType?"\xa5":"$","/",3==f.monthNum?M("tool.Quarter"):12==f.monthNum?M("tool.Year"):M("tool.Month")]}),(0,y.jsxs)("span",{className:"t2",children:[M("tool.Meisi_Account"),"：",null==s?void 0:s.userName]}),(0,y.jsxs)("span",{className:"t3",onClick:O,children:[M("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"),(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})]})]})]})}):null})]}):null,f.coinType&&0!==f.feePrice&&"zh-CN"!=z()&&(null==f?void 0:f.coinType)=="人民币"&&(0,y.jsx)("div",{className:"micro_main_bottom",children:(0,y.jsx)("div",{className:"micro_pay",children:(0,y.jsxs)("div",{className:"micro_pay_right",children:[p&&(0,y.jsx)("div",{className:"noQrCode"}),!p&&x&&(0,y.jsx)("div",{className:"qr-code",children:(0,y.jsx)(lo.Q,{value:x,size:131,fgColor:"#000",level:"L"})}),(0,y.jsxs)("div",{className:"price",children:[(0,y.jsxs)("div",{className:"micro_way",children:[(0,y.jsx)("div",{className:"box",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})}),(0,y.jsx)("span",{children:M("tool.Support_Alipay_Payment")})]}),(0,y.jsxs)("span",{className:"t1",children:[M("tool.Support_Alipay_Payment"),(0,y.jsx)("span",{className:"bd",children:f.feePrice}),"人民币"==f.coinType?"\xa5":"$","/",3==f.monthNum?M("tool.Quarter"):12==f.monthNum?M("tool.Year"):M("tool.Month")]}),(0,y.jsxs)("span",{className:"t2",children:[M("tool.Meisi_Account"),"：",null==s?void 0:s.userName]}),(0,y.jsxs)("span",{className:"t3",onClick:O,children:[M("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"),(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})]})]})]})})}),f.coinType&&"zh-CN"==z()&&0==f.feePrice&&("0"==d.subStatus||"2"==d.subStatus)&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>H(f,u.id),children:M("tool.Free_Trial")})}),f.coinType&&"zh-CN"!=z()&&0==f.feePrice&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>H(f,u.id),children:M("tool.Free_Trial")})}),f.coinType&&f.feePrice>0&&(null==f?void 0:f.coinType)=="美元"&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>H(f,u.id),children:M("market.subscribe")})})]})]})})},ld=e=>{var t,n;let{userInfo:r,currentItem:a={},onClose:i,subStatusDetail:o={}}=e,[s,l]=(0,b.useState)(!0),[c,u]=(0,b.useState)(!0),[d,p]=(0,b.useState)(!1),[m,f]=(0,b.useState)({}),[h,g]=(0,b.useState)(!1),[v,x]=(0,b.useState)(!1),[w,j]=(0,b.useState)(!1),[S,k]=(0,b.useState)(!1),[C,N]=(0,b.useState)(0),[E,T]=(0,b.useState)((null==r?void 0:r.avatar)||"https://img.medsci.cn/web/img/user_icon.png"),_=(0,b.useRef)(null),A=new G;window.location.origin.includes("medon.com.cn")||window.location.origin.includes("medsci.cn"),(0,b.useEffect)(()=>{var e,t;R();let n=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${n}px`),"写作"==a.appType&&localStorage.setItem(`appWrite-${a.appUuid}`,JSON.stringify({appUuid:a.appUuid,directoryMd:a.directoryMd})),(null==(e=a.feeTypes)?void 0:e.length)==1&&a.feeTypes[0].feePrice>=0&&f(a.feeTypes[0]),r&&(null==(t=JSON.parse(r))?void 0:t.userId)&&p(!0),"medsci"==new URLSearchParams(window.top.location.search).get("source")&&x(!0)},[a,r]),(0,b.useEffect)(()=>{var e;a.feeTypes="zh-CN"==M()?null==o?void 0:o.feeTypes:a.feeTypes,(null==o||null==(e=o.feeTypes)?void 0:e.length)>0&&"zh-CN"==M()&&(null==o||o.feeTypes.forEach((e,t)=>{(null==o?void 0:o.packageType)==e.type&&f(e)}))},[]);let P=()=>navigator.userAgent.includes("medsci_app"),M=()=>B.Z.get("ai_apps_lang")?B.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,I=()=>{let e=M();if(e&&"zh-CN"!=e)window.top.location.href=location.origin+"/"+e+"/login";else{var t,n;null==(t=(n=window).addLoginDom)||t.call(n)}},O=()=>{l(!1),u(!0)},L=()=>{let e="https://www.medsci.cn/about/index.do?id=27";P()?window.top.location.href=e:window.open(e)},R=async()=>{let e=await A.freeLimit({},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});N(null==e?void 0:e.data)},$=e=>{f(e)},F=e=>{j(e.target.checked)},z=async(e,t,n)=>{var a;if(!w&&n){k(!0),setTimeout(()=>k(!1),500);return}let i=M();if(r&&(null==(a=JSON.parse(r))?void 0:a.userId)){let n={appUuid:t,priceId:e.priceId,monthNum:e.monthNum,packageKey:e.packageKey,packageType:e.type};try{let t=await A.createSubscription(n,{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});if(0==t.code)if("人民币"==e.coinType&&0!==e.feePrice){let e=await A.createAliSub(JSON.parse(t.data));e&&0==e.code&&(window.top.location.href=e.data)}else setTimeout(()=>{window.top.location.href=t.data},1e3)}catch(e){Z.ZP.error("订阅失败")}}else i&&"zh-CN"!=i?window.top.location.href=location.origin+"/"+i+"/login":I()};return(0,y.jsxs)("div",{className:`vip ${P()?"sp":""}`,children:[!P()&&(0,y.jsx)("div",{className:"vip-head text-center py-2 text-lg font-medium text-gray-800",children:a.name}),(0,y.jsxs)("div",{className:"vip-introduce bg-gray-900 pt-9 pb-4 relative",children:[(0,y.jsx)("img",{className:"crown absolute right-1 top-0 h-9 object-contain",src:"https://static.medsci.cn/public-image/ms-image/1dcd7d10-58af-11ec-8e2f-1389d01aad85_crown.png",alt:""}),(0,y.jsx)("div",{className:"box mx-3 flex items-center justify-between bg-[url('https://static.medsci.cn/public-image/ms-image/21ad8b80-58b1-11ec-8e2f-1389d01aad85_vip-h5.png')] bg-cover p-3 rounded",children:d?(0,y.jsxs)("div",{className:"box-left flex items-center",children:[(0,y.jsx)("img",{className:"avatar w-8 h-8 rounded-full mr-3",src:E,alt:"",onError:()=>{T("https://img.medsci.cn/web/img/user_icon.png")}}),(0,y.jsx)("div",{className:"box-word",children:(0,y.jsx)("span",{className:"t1 text-lg text-[#58342c]",children:JSON.parse(r).realName||JSON.parse(r).userName})})]}):(0,y.jsxs)("div",{className:"box-left-1 flex items-center",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/web/img/user_icon.png",alt:"",className:"w-8 h-8 rounded-full mr-3"}),(0,y.jsxs)("div",{className:"left2",children:[(0,y.jsx)("span",{className:"t1 text-lg text-[#58342c] cursor-pointer",onClick:I,children:"立即登录"}),(0,y.jsx)("span",{className:"t2 text-sm text-[#7f5947] mt-1",children:"请登录后购买"})]})]})})]}),(0,y.jsxs)("div",{style:{paddingBottom:"zh-CN"==M()&&("3"==o.subStatus||"1"==o.subStatus&&"免费"==o.packageType)&&m.type==o.packageType?"0":""},className:"vip-main bg-white rounded-t-3xl -mt-14 z-10 ",children:[(0,y.jsx)("div",{className:"vip-one pl-3",children:(0,y.jsx)("div",{className:"big overflow-auto mr-3",children:"zh-CN"==M()?(0,y.jsx)("ul",{ref:_,className:"flex mt-2 mb-1",children:null==(t=a.feeTypes)?void 0:t.map((e,t)=>(0,y.jsxs)("li",{className:`min-w-[135px] w-[135px] bg-white border border-gray-300 rounded-lg mr-3 text-center cursor-pointer ${e.type==m.type?"sactvie":""} ${(null==o?void 0:o.packageType)=="连续包月"&&"免费"==e.type||(null==o?void 0:o.packageType)=="连续包月"&&"连续包年"==e.type||(null==o?void 0:o.packageType)=="连续包年"&&"免费"==e.type||(null==o?void 0:o.packageType)=="连续包年"&&"连续包月"==e.type?"noClick":""}`,onClick:()=>$(e),children:[(0,y.jsx)("div",{className:"title text-center ellipsis-2-lines mx-auto mt-6 max-w-[102.5px] text-sm font-medium text-gray-800",children:e.type}),(0,y.jsxs)("div",{className:"prices text-2xl font-bold text-orange-400 mt-2 mb-6",children:[(0,y.jsx)("span",{className:"text-lg",children:"人民币"==e.coinType?"\xa5":"$"}),e.feePrice]}),e.originalPrice&&(0,y.jsxs)("div",{className:"isfava relative text-xs text-gray-500 line-through",children:["人民币"==e.coinType?"\xa5":"$",e.feePrice]})]},t))}):(0,y.jsx)("ul",{ref:_,className:"flex mt-2 mb-1",children:null==(n=a.feeTypes)?void 0:n.map((e,t)=>(0,y.jsxs)("li",{className:`min-w-[135px] w-[135px] bg-white border border-gray-300 rounded-lg mr-3 text-center cursor-pointer ${e.type==m.type?"sactvie":""}`,onClick:()=>$(e),children:[(0,y.jsx)("div",{className:"title text-center ellipsis-2-lines mx-auto mt-6 max-w-[102.5px] text-sm font-medium text-gray-800",children:e.type}),(0,y.jsxs)("div",{className:"prices text-2xl font-bold text-orange-400 mt-2 mb-6",children:[(0,y.jsx)("span",{className:"text-lg",children:"人民币"==e.coinType?"\xa5":"$"}),e.feePrice]}),e.originalPrice&&(0,y.jsxs)("div",{className:"isfava relative text-xs text-gray-500 line-through",children:["人民币"==e.coinType?"\xa5":"$",e.feePrice]})]},t))})})}),"zh-CN"==M()?(0,y.jsxs)("div",{children:[(0,y.jsx)("div",{className:"vip-two",style:{paddingBottom:"0"},children:(0,y.jsxs)("div",{className:"vip-two_banner",children:[(0,y.jsxs)("div",{className:"vip-two_title",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"}),"梅斯小智 订阅说明"]}),(0,y.jsxs)("div",{className:"vip-two_content",children:[(0,y.jsxs)("div",{children:["免费：每个自然月内，每个智能体的使用上限",C,"次。次月开始重新计次。"]}),(0,y.jsx)("div",{children:"连续包月：订阅之日起一个月内，每个智能体不限使用次数。"}),(0,y.jsx)("div",{children:"连续包年：订阅之日起一年内，每个智能体不限使用次数"})]})]})}),((null==o?void 0:o.subStatus)=="1"||(null==o?void 0:o.subStatus)=="3")&&m.type==(null==o?void 0:o.packageType)&&(0,y.jsxs)("div",{style:{textAlign:"center",fontSize:"14px"},children:[null==o?void 0:o.subAt," 已订阅"]}),(null==o?void 0:o.subStatus)=="1"&&(null==o?void 0:o.packageType)=="免费"&&"免费"==m.type&&(0,y.jsx)("div",{style:{textAlign:"center",fontSize:"14px"},children:"免费使用中…"}),(null==o?void 0:o.subStatus)=="3"&&m.type==o.packageType&&(0,y.jsxs)("div",{style:{textAlign:"center",fontSize:"14px"},children:[null==o?void 0:o.unSubAt," 取消订阅"]}),(null==o?void 0:o.subStatus)=="3"&&m.type==o.packageType&&(0,y.jsxs)("div",{style:{textAlign:"center",fontSize:"14px"},children:["您的订阅可使用至 ",null==o?void 0:o.expireAt]}),(null==o?void 0:o.packageType)=="连续包月"&&(null==o?void 0:o.subStatus)=="1",(null==o?void 0:o.packageType)=="连续包月"&&(null==o?void 0:o.subStatus)=="1"?(0,y.jsx)("div",{style:{textAlign:"center",fontSize:"14px"},children:"连续包月中…"}):null,(null==o?void 0:o.packageType)=="连续包年"&&(null==o?void 0:o.subStatus)=="1"&&(0,y.jsx)("div",{style:{textAlign:"center",fontSize:"14px"},children:"连续包年中…"})]}):(0,y.jsx)("div",{className:"vip-two border border-gray-200 rounded m-3 p-2",children:(0,y.jsxs)("div",{className:"vip-two_banner bg-[url('https://img.medsci.cn/202503/48a49f899b4544e39a864d1821b3555a-lRXRIQx3q1VB.png')] bg-cover p-3",children:[(0,y.jsxs)("div",{className:"vip-two_title flex items-center text-sm font-medium text-orange-600",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png",alt:"",className:"w-2 h-2 mx-1"}),a.name]}),(0,y.jsx)("div",{className:"vip-two_content bg-white p-3 text-sm text-gray-700 leading-5 max-h-[calc(var(--vh)*20)] overflow-auto rounded",children:a.description})]})}),"zh-CN"==M()&&(0,y.jsxs)("div",{className:`vip-pay btns ${"连续包月"==o.packageType&&"1"==o.subStatus||"连续包年"==o.packageType&&"1"==o.subStatus?"":"CN_btns"}`,children:[(null==o?void 0:o.packageType)=="连续包月"&&(null==o?void 0:o.subStatus)=="1"&&(0,y.jsx)(J.ZP,{onClick:()=>{sQ.Vq.confirm({title:"提示",message:`取消包月在${null==o?void 0:o.expireAt}号生效，再次使用需要重新订阅。是否确认取消？`,confirmButtonColor:"#D7813F",onCancel:()=>{},onConfirm:async()=>{await A.cancelSubscription({},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`}),i()}})},type:"primary",children:"取消包月"}),(null==o?void 0:o.packageType)=="连续包年"&&(null==o?void 0:o.subStatus)=="1"&&(0,y.jsx)(J.ZP,{onClick:()=>{sQ.Vq.confirm({title:"提示",message:`取消包月在${null==o?void 0:o.expireAt}号生次生效，再次使用需要重新订阅。是否确认取消？`,confirmButtonColor:"#D7813F",onCancel:()=>{},onConfirm:async()=>{await A.cancelSubscription({},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`}),i()}})},type:"primary",children:"取消包年"})]}),m.feePrice>0&&(null==m?void 0:m.coinType)=="人民币"&&"zh-CN"==M()&&"免费"!=m.type&&(null==o?void 0:o.subStatus)=="0"||"免费"!=m.type&&(null==o?void 0:o.subStatus)=="2"||(null==o?void 0:o.packageType)=="免费"&&"免费"!=m.type&&(0,y.jsx)("div",{className:"vip-three mt-3",children:(0,y.jsxs)("div",{className:`pay mx-3 p-4 rounded-lg bg-gradient-to-b from-[#fffaf6] to-[#fff8f0] ${h?"h-28":""}`,children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:"",className:"w-15 mb-3"}),(0,y.jsxs)("div",{className:"item flex justify-between items-center bg-white border border-orange-300 rounded p-2 mb-3 cursor-pointer",onClick:O,children:[(0,y.jsxs)("div",{className:"item-left flex items-center",children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:"",className:"w-5 h-5 mr-2"}),(0,y.jsx)("span",{className:"text-sm text-gray-800",children:"支付宝支付"})]}),(0,y.jsx)("div",{className:"item-right w-5 h-5",children:c&&(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:"",className:"w-full"})})]})]})}),m.feePrice>0&&(null==m?void 0:m.coinType)=="人民币"&&"zh-CN"!=M()&&(0,y.jsx)("div",{className:"vip-three mt-3",children:(0,y.jsxs)("div",{className:`pay mx-3 p-4 rounded-lg bg-gradient-to-b from-[#fffaf6] to-[#fff8f0] ${h?"h-28":""}`,children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:"",className:"w-15 mb-3"}),(0,y.jsxs)("div",{className:"item flex justify-between items-center bg-white border border-orange-300 rounded p-2 mb-3 cursor-pointer",onClick:O,children:[(0,y.jsxs)("div",{className:"item-left flex items-center",children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:"",className:"w-5 h-5 mr-2"}),(0,y.jsx)("span",{className:"text-sm text-gray-800",children:"支付宝支付"})]}),(0,y.jsx)("div",{className:"item-right w-5 h-5",children:c&&(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:"",className:"w-full"})})]})]})})]}),m.feePrice>=0&&"zh-CN"==M()&&("免费"==m.type&&"0"==o.subStatus||"免费"!=m.type&&"0"==o.subStatus||"免费"!=m.type&&"2"==o.subStatus||"免费"==o.packageType&&"免费"!=m.type)?(0,y.jsx)("div",{className:`vip-pay btns fixed bottom-0 w-full bg-white shadow-lg flex justify-around items-center h-24 z-20  ${((null==o?void 0:o.subStatus)=="1"||(null==o?void 0:o.subStatus)=="3")&&"免费"==m.type?"CN_btns":""}`,children:m.feePrice>0?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{className:"pay-left ml-2",children:[(0,y.jsx)("div",{className:"t1 text-sm font-medium text-orange-400 mb-1",children:a.name}),(0,y.jsxs)("div",{className:`t2 flex items-start text-xs text-gray-500 ${S?"shake":""}`,children:[(0,y.jsx)(ll.Z,{checked:w,onChange:F,style:{fontSize:"14px"},className:"mr-1"}),(0,y.jsxs)("span",{onClick:L,className:"cursor-pointer",children:["请在阅读并同意",(0,y.jsx)("span",{className:"text-blue-500",children:"协议"}),"后开通"]})]})]}),(0,y.jsx)("div",{className:"pay-right mr-2 w-48 h-12 bg-gradient-to-r from-[#fde39b] to-[#fbd786] rounded-full flex items-center justify-center cursor-pointer",onClick:()=>z(m,a.appUuid,"ali"),children:(0,y.jsxs)("span",{className:"text-sm text-[#614018]",children:[m.feePrice,"元确认协议并支付"]})})]}):(0,y.jsx)(J.ZP,{className:"w-48 h-12 rounded-full",onClick:()=>z(m,a.appUuid,""),children:0==m.feePrice?"免费试用":"订阅"})}):null,m.feePrice>=0&&"zh-CN"!=M()?(0,y.jsx)("div",{className:"vip-pay btns fixed bottom-0 w-full bg-white shadow-lg flex justify-around items-center h-24 z-20",children:m.feePrice>0?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{className:"pay-left ml-2",children:[(0,y.jsx)("div",{className:"t1 text-sm font-medium text-orange-400 mb-1",children:a.name}),(0,y.jsxs)("div",{className:`t2 flex items-start text-xs text-gray-500 ${S?"shake":""}`,children:[(0,y.jsx)(ll.Z,{checked:w,onChange:F,style:{fontSize:"14px"},className:"mr-1"}),(0,y.jsxs)("span",{onClick:L,className:"cursor-pointer",children:["请在阅读并同意",(0,y.jsx)("span",{className:"text-blue-500",children:"协议"}),"后开通"]})]})]}),(0,y.jsx)("div",{className:"pay-right mr-2 w-48 h-12 bg-gradient-to-r from-[#fde39b] to-[#fbd786] rounded-full flex items-center justify-center cursor-pointer",onClick:()=>z(m,a.appUuid,"ali"),children:(0,y.jsxs)("span",{className:"text-sm text-[#614018]",children:[m.feePrice,"元确认协议并支付"]})})]}):(0,y.jsx)(J.ZP,{type:"primary",className:"w-48 h-12 rounded-full",onClick:()=>z(m,a.appUuid,""),children:0==m.feePrice?"免费试用":"订阅"})}):null]})};function lp(e){var t,n,r,a,i,o,s,l,c;let{appConfig:u,appInfo:d,appParameters:p,difyApi:m,conversationId:f,conversationItems:h,conversationName:g,onConversationIdChange:v,conversationListLoading:x,onAddConversation:w,onItemsChange:j,conversationItemsChangeCallback:S,appConfigLoading:k,handleStartConfig:C,inputParams:N,setInputParams:E,modal2OpenF:T,onClose:_,onsubmit:P,subStatusDetails:I,changeSubStatusDetails:O,selectedAppId:L,appList:R}=e,$=(0,z.k6)(),F=new s2,D=new G,H=new URLSearchParams(window.top.location.search),U=M(),K=$.location.pathname.match(/^\/ai-chat\/([^/]+)$/),W=K?K[1]:"",q=(0,b.useRef)(()=>{});(0,b.useEffect)(()=>()=>{q.current()},[]),(0,b.useEffect)(()=>{ex(T)},[T]);let[X,Y]=(0,b.useState)(!1),[Q,ee]=(0,b.useState)([]),[et,en]=(0,b.useState)([]),er=s5({conversationId:f}),ea=s5({inputParams:N}),ei=(0,b.useRef)([]),eo=async e=>{en((await m.getNextSuggestions({message_id:e})).data)},es=async e=>{var t,n,r,a,i;if(!e||A(e))return;let o=await m.getConversationHistory(e);if(!(null==o||null==(t=o.data)?void 0:t.length))return;let s=[];(null==o||null==(n=o.data)?void 0:n.length)&&(null==(r=Object.values(null==(i=o.data)||null==(a=i[0])?void 0:a.inputs))?void 0:r.length)&&E({article_id:H.get("article_id"),qa_id:H.get("qa_id")}),o.data.forEach(e=>{let t=sY()(1e3*e.created_at).format("YYYY-MM-DD HH:mm:ss");s.push({id:e.id,content:e.query,status:"success",isHistory:!0,files:e.message_files,role:"user",created_at:t},{id:e.id,content:e.answer,status:"error"===e.status?e.status:"success",error:e.error||"",isHistory:!0,feedback:e.feedback,agentThoughts:e.agent_thoughts||[],retrieverResources:e.retriever_resources||[],role:"ai",created_at:t})}),ed([]),ee(s),(null==s?void 0:s.length)&&(null==p?void 0:p.suggested_questions_after_answer.enabled)&&eo(s[s.length-1].id)},{agent:el,onRequest:ec,messages:eu,setMessages:ed,currentTaskId:ep}=s9({latestProps:er,latestState:ea,filesRef:ei,getNextSuggestions:eo,appParameters:p,abortRef:q,getConversationMessages:es,onConversationIdChange:v,difyApi:m,appUuid:(null==u?void 0:u.id)||""}),em=async()=>{f&&!A(f)&&await es(f),Y(!1)};(0,b.useEffect)(()=>{Y(!0),ed([]),en([]),ee([]),em(),A(f)&&("medsci-ask"!=W&&E({}),H.get("article_id")&&(E({article_id:H.get("article_id"),qa_id:H.get("qa_id")}),D.qaList({articleId:H.get("article_id")||"",encryptionId:H.get("qa_id")||""},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`,Accept:"application/json"}).then(e=>{var t;P(eg((null==e||null==(t=e.data[0])?void 0:t.question)||""))})))},[f]);let ef=async e=>{var t;let n=ev();if("nologin"==m.options.user)return console.log("未登录 onFocus",n),n&&"zh-CN"!=n?window.top.location.href=location.origin+"/"+n+"/login":window.addLoginDom(),!1;if(W.includes("elavax-pro")||W.includes("novax-pro")){let t=await D.bindAppUser({appUuid:(null==u?void 0:u.id)||"",appNameEn:W},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`,Accept:"application/json"});if((null==t?void 0:t.data.remainNum)!=0)return ec({content:e.data.description}),!1}let r=K?K[1]:"",a=await F.getApp(r,{Authorization:`Bearer ${B.Z.get("yudaoToken")}`,Accept:"application/json"});if(!(null==a?void 0:a.info.appUser)||(null==a||null==(t=a.info.appUser)?void 0:t.status)==2)return document.getElementsByTagName("textarea")[0].blur(),ex(!0),!1;ec({content:e.data.description})},eh=(0,b.useMemo)(()=>{var e,t;return!((null==p||null==(e=p.user_input_form)?void 0:e.length)&&(!f||A(f)))||(null==p||null==(t=p.user_input_form)?void 0:t.every(e=>!!N[e["text-input"].variable]))||!1},[p,N,f]),eg=(e,t)=>{if(document.getElementsByTagName("textarea").length>0&&document.getElementsByTagName("textarea")[0].blur(),!eh){let e=(null==p?void 0:p.user_input_form.filter(e=>{let t=e["text-input"];return!N[t.variable]&&t.required}).map(e=>e["text-input"].label))||[];Z.ZP.error(`${e.join("、")}不能为空`);return}ei.current=(null==t?void 0:t.files)||[],ec({content:e,files:null==t?void 0:t.files})},ev=()=>B.Z.get("ai_apps_lang")?B.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,[ey,ex]=(0,b.useState)(!1),eb=e=>{ex(e),_(e)},ew=async()=>{var e;let t=ev();if("nologin"==m.options.user)return console.log("未登录 onFocus",t),t&&"zh-CN"!=t?top.location.href=location.origin+"/"+t+"/login":window.addLoginDom(),!1;if(W.includes("elavax-pro")||W.includes("novax-pro")){let e=await D.bindAppUser({appUuid:(null==u?void 0:u.id)||"",appNameEn:W},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`,Accept:"application/json"});if((null==e?void 0:e.data.remainNum)!=0)return!1}let n=K?K[1]:"",r=await F.getApp(n,{Authorization:`Bearer ${B.Z.get("yudaoToken")}`,Accept:"application/json"});return!!(null==r?void 0:r.info.appUser)&&(null==r||null==(e=r.info)?void 0:e.appUser.status)!=2||(document.getElementsByTagName("textarea")[0].blur(),ex(!0),!1)},ej=(0,b.useMemo)(()=>eu.map(e=>({id:e.id,status:e.status,error:e.message.error||"",workflows:e.message.workflows,agentThoughts:e.message.agentThoughts,retrieverResources:e.message.retrieverResources,files:e.message.files,content:e.message.content,role:"local"===e.status?"user":"ai"})),[eu]),eS=(0,y.jsx)(sG.Z,{trigger:["click"],content:(0,y.jsxs)("div",{className:"w-60",children:[(0,y.jsx)("div",{className:"text-base font-semibold",children:"对话列表"}),(0,y.jsx)(V.Z,{spinning:x,children:(null==h?void 0:h.length)?(0,y.jsx)(sq,{renameConversationPromise:(e,t)=>null==m?void 0:m.renameConversation({conversation_id:e,name:t}),appConfig:u,deleteConversationPromise:null==m?void 0:m.deleteConversation,items:h,activeKey:f,onActiveChange:v,onItemsChange:j,refreshItems:S,onchangeModal2Open:e=>ex(e)}):(0,y.jsx)(ol.Z,{description:"暂无会话"})}),(0,y.jsx)(J.ZP,{className:"mt-3",onClick:w,block:!0,type:"primary",children:"新增对话"})]}),placement:U?"bottom":"bottomLeft",children:(0,y.jsxs)("div",{className:"flex w-full  items-center justify-start",children:[(0,y.jsx)(sX.Z,{className:"mr-3 cursor-pointer"}),(0,y.jsx)("span",{className:"truncate max-w-[80%]",children:g||s4})]})});return x||k?(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):u?(0,y.jsxs)("div",{className:"flex h-screen flex-col overflow-hidden flex-1",children:[!U&&ey?(0,y.jsx)(sK.Z,{centered:!0,open:ey,onOk:()=>ex(!1),onCancel:()=>ex(!1),children:(null==u?void 0:u.id)&&((null==u||null==(n=u.info)||null==(t=n.appNameEn)?void 0:t.includes("elavax"))||(null==u||null==(a=u.info)||null==(r=a.appNameEn)?void 0:r.includes("novax")))?(0,y.jsx)(ls,{onClose:eb,appConfig:u,currentItem:null==u?void 0:u.info,userInfo:B.Z.get("userInfo"),subStatusDetail:I}):(0,y.jsx)(lu,{onClose:eb,appConfig:u,currentItem:null==u?void 0:u.info,userInfo:B.Z.get("userInfo"),subStatusDetail:I})}):null,U&&ey?(0,y.jsx)(sQ.GI,{visible:ey,style:{width:"100%",height:"90%"},position:"bottom",closeable:!0,round:!0,onClose:()=>eb(!1),children:(null==u?void 0:u.id)&&((null==u||null==(o=u.info)||null==(i=o.appNameEn)?void 0:i.includes("elavax"))||(null==u||null==(l=u.info)||null==(s=l.appNameEn)?void 0:s.includes("novax")))?(0,y.jsx)(lc,{onClose:()=>eb(!1),currentItem:null==u?void 0:u.info,userInfo:B.Z.get("userInfo"),subStatusDetail:I}):(0,y.jsx)(ld,{onClose:()=>eb(!1),currentItem:null==u?void 0:u.info,userInfo:B.Z.get("userInfo"),subStatusDetail:I})}):null,U?(0,y.jsx)(li,{subStatusDetail:I,appConfig:u,appList:R,selectedAppId:L,showSubscribe:()=>{O(),ex(!0)},centerChildren:eS}):null,(0,y.jsxs)("div",{className:"flex-1 overflow-hidden relative",children:[X?(0,y.jsx)("div",{className:"absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):null,h.length?f&&eh?(0,y.jsx)(sW,{appConfig:u,conversationId:f,appParameters:p,nextSuggestions:et,messageItems:[...Q,...ej],isRequesting:el.isRequesting(),onPromptsItemClick:ef,onSubmit:eg,onCancel:async()=>{q.current(),ep&&(await m.stopTask(ep),es(f))},feedbackApi:m.feedbackMessage,feedbackCallback:e=>{es(e)},uploadFileApi:m.uploadFile,difyApi:m,onFocus:ew}):(null==p||null==(c=p.user_input_form)?void 0:c.length)?(0,y.jsx)(lr,{conversationId:f,formFilled:eh,onStartConversation:e=>{E(e),f||"medsci-ask"==W||w()},appInfo:d,user_input_form:null==p?void 0:p.user_input_form}):(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):(0,y.jsx)(lr,{conversationId:f,formFilled:eh,onStartConversation:e=>{E(e),f||w()},appInfo:d,user_input_form:null==p?void 0:p.user_input_form})]})]}):(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(ol.Z,{description:"请先配置 Dify 应用",children:(0,y.jsx)(J.ZP,{type:"primary",onClick:C,children:"开始配置"})})})}var lm=n(33405);let{Option:lf}=od.Z,lh=[{code:"zh-CN",name:"简体中文",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"zh-TW",name:"繁體中文",flag:"\uD83C\uDDF9\uD83C\uDDFC"},{code:"en",name:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{code:"vi",name:"Tiếng Việt",flag:"\uD83C\uDDFB\uD83C\uDDF3"},{code:"es",name:"Espa\xf1ol",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"ar",name:"العربية",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"id",name:"Bahasa Indonesia",flag:"\uD83C\uDDEE\uD83C\uDDE9"},{code:"pt",name:"Portugu\xeas",flag:"\uD83C\uDDE7\uD83C\uDDF7"},{code:"ja",name:"日本語",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"ko",name:"한국어",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"ms",name:"Bahasa Melayu",flag:"\uD83C\uDDF2\uD83C\uDDFE"}],lg=e=>{let{className:t,size:n="middle",showIcon:r=!0}=e,{i18n:a}=(0,ou.$G)();return(0,y.jsx)(od.Z,{value:a.language,onChange:e=>{a.changeLanguage(e)},className:t,size:n,style:{minWidth:120},suffixIcon:r?(0,y.jsx)(lm.Z,{}):void 0,popupMatchSelectWidth:!1,children:lh.map(e=>(0,y.jsxs)(lf,{value:e.code,children:[(0,y.jsx)("span",{style:{marginRight:8},children:e.flag}),e.name]},e.code))})},lv=(e,t)=>{let[n]=(0,b.useState)(new Map);return(0,b.useEffect)(()=>{let r=new Map(e.map(e=>[e[t],e]));n.clear(),r.forEach((e,t)=>n.set(t,e))},[e,t,n]),n},ly=n.p+"static/image/kefu.1767c230.png",lx=n.p+"static/image/qrcode.74f61641.png",lb=()=>{let[e,t]=(0,b.useState)(!1),[n,r]=(0,b.useState)(!1),[a,i]=(0,b.useState)(!1);return(0,b.useEffect)(()=>{let e=()=>{let e=window.innerWidth<=768;i(e),e?t(!0):t(!1),r(!1)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,y.jsxs)("div",{className:`assistant-container ${e?"is-collapsed":""}`,children:[(0,y.jsx)("div",{className:"assistant-icon",onClick:()=>{let n=!e;t(n),n?setTimeout(()=>{r(!1)},300):r(!0)},children:(0,y.jsx)("img",{src:ly,className:"fas fa-user-astronaut",alt:"客服"})}),(0,y.jsxs)("div",{className:`qr-codes ${n?"is-visible":""}`,children:[(0,y.jsx)("img",{src:lx,alt:"QR Code"}),"扫码添加小助手"]})]})},lw=(0,sV.kc)(e=>{let{token:t,css:n}=e;return{layout:n`
			font-family: AlibabaPuHuiTi, ${t.fontFamily}, sans-serif;
		`,menu:n`
			background: ${t.colorBgLayout}80;
		`}}),lj=e=>{let[t,n]=(0,b.useState)(!1),r=(0,z.k6)(),a=new s2,{extComponents:i,appConfig:o,useAppInit:s,renderCenterTitle:l,handleStartConfig:c,initLoading:u,selectedAppId:d,appList:p}=e,{...m}=N(),{user:f}=m,{t:h}=(0,ou.$G)(),{styles:g}=lw(),[v]=(0,b.useState)(oe({user:f,apiBase:"",apiKey:"",appId:""})),x=new URLSearchParams(window.top.location.search),[w,j]=(0,b.useState)([]),S=lv(w,"key"),[k,C]=(0,b.useState)(!1),[E,T]=(0,b.useState)(),[_,A]=(0,b.useState)(),[P,M]=(0,b.useState)(),[I,O]=(0,b.useState)(!1),[L,R]=(0,b.useState)({}),[$,F]=(0,b.useState)({}),D=r.location.pathname.match(/^\/ai-chat\/([^/]+)$/),H=D?D[1]:"",[B,U]=(0,b.useState)(null),K=()=>{var e;if(null==P||null==(e=P.user_input_form)?void 0:e.length){let e={...L};null==P||P.user_input_form.forEach(t=>{e[t["text-input"].variable]=void 0}),R(e)}},W=async()=>{A(void 0),v&&(O(!0),A({name:o.info.name,description:o.info.description,tags:[]}),M(await v.getAppParameters()),O(!1))};s(v,()=>{W().then(()=>{q().then(()=>{("medsci-ask"==H||x.get("fromPlatform"))&&Y()})}),K(),T(void 0)});let q=async()=>{C(!0);try{var e,t,n;let r=`temp_${Math.random()}`,a=[{key:r,label:s4}];if(en){let n=await (null==v?void 0:v.getConversationList());(null==n?void 0:n.code)==401&&(Q.remove("userInfo",{domain:".medon.com.cn"}),Q.remove("userInfo",{domain:".medsci.cn"}),Q.remove("userInfo",{domain:"localhost"}),Q.remove("yudaoToken",{domain:"ai.medon.com.cn"}),Q.remove("yudaoToken",{domain:"ai.medsci.cn"}),Q.remove("yudaoToken",{domain:".medon.com.cn"}),Q.remove("yudaoToken",{domain:".medsci.cn"}),Q.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("conversation"),window.location.origin.includes("medsci.cn")?window.top.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.top.location.href:window.top.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.top.location.href),a=(null==n||null==(e=n.data)?void 0:e.length)?null==n||null==(t=n.data)?void 0:t.map(e=>({key:e.id,label:e.name})):[{key:r,label:s4}]}j(a),"medsci-ask"==H||x.get("fromPlatform")||T(null==(n=a[0])?void 0:n.key)}catch(e){console.error(e),Z.ZP.error(`${h("common.getConversationListFailed")}: ${e}`)}finally{C(!1)}},X=()=>Q.get("ai_apps_lang")?Q.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,Y=async()=>{var e,t;let i=new G,s=r.location.pathname.match(/^\/ai-chat\/([^/]+)$/),l=s?s[1]:"",c=X();if("nologin"==f)return void(c&&"zh-CN"!=c?window.top.location.href=location.origin+"/"+c+"/login":window.addLoginDom());let u=`temp_${Math.random()}`;if(j(e=>[{key:u,label:s4},...e]),l.includes("elavax-pro")||l.includes("novax-pro")){let e=await i.bindAppUser({appUuid:(null==o?void 0:o.id)||"",appNameEn:l},{Authorization:`Bearer ${Q.get("yudaoToken")}`,Accept:"application/json"});if((null==e?void 0:e.data.remainNum)!=0)return T(u),!1}let d=s?s[1]:"",p=await a.getApp(d,{Authorization:`Bearer ${Q.get("yudaoToken")}`,Accept:"application/json"});if(!p.info.appUser||(null==p||null==(e=p.info.appUser)?void 0:e.status)==2){(null==(t=document.getElementsByTagName("textarea"))?void 0:t.length)&&document.getElementsByTagName("textarea")[0].blur(),n(!0);return}T(u)};(0,b.useEffect)(()=>{ei()},[]),(0,b.useEffect)(()=>{E&&!S.has(E)&&q()},[E]);let ee=(0,b.useMemo)(()=>{var e;return(null==(e=w.find(e=>e.key===E))?void 0:e.label)||s4},[w,E]);(0,b.useEffect)(()=>{o||(j([]),A(void 0),T(""),K())},[o]);let en=Q.get("userInfo"),er=en&&JSON.parse(en).avatar?JSON.parse(en).avatar:"https://img.medsci.cn/web/img/user_icon.png",ea=e=>{e.target.src="https://img.medsci.cn/web/img/user_icon.png"},ei=async()=>{let e=new G;F((await e.getPackageByKey({},{Authorization:`Bearer ${Q.get("yudaoToken")}`,Accept:"application/json"})).data)},[eo,es]=(0,b.useState)(!1),el=async()=>{es(!1),Q.remove("userInfo",{domain:".medon.com.cn"}),Q.remove("userInfo",{domain:".medsci.cn"}),Q.remove("userInfo",{domain:"localhost"}),localStorage.removeItem("hasuraToken"),Q.remove("yudaoToken",{domain:"ai.medon.com.cn"}),Q.remove("yudaoToken",{domain:"ai.medsci.cn"}),Q.remove("yudaoToken",{domain:".medon.com.cn"}),Q.remove("yudaoToken",{domain:".medsci.cn"}),Q.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("conversation"),window.location.origin.includes("medsci.cn")?window.top.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.top.location.href:window.top.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.top.location.href};return(0,y.jsxs)(oh.ZP,{theme:{token:{colorPrimary:"#1669ee",colorText:"#333"}},children:[(0,y.jsxs)("div",{className:`w-full h-screen ${g.layout} flex flex-col overflow-hidden bg-[#eff0f5]`,children:[(0,y.jsx)(lb,{}),(0,y.jsxs)("div",{className:"hidden md:!flex items-center justify-between px-6",children:[(0,y.jsx)("div",{className:`flex-1 overflow-hidden ${o?"":"shadow-sm"}`,children:(0,y.jsx)(et,{hideGithubIcon:!0})}),(0,y.jsx)(la,{children:H.includes("novax")||H.includes("elavax")?l&&_?l({name:_.name,description:_.description,tags:_.tags||[],appUser:null,appId:"",appType:"",appIcon:"",feeTypes:[]}):null:o?o.info.name:""}),(0,y.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,y.jsxs)("div",{className:"flex items-center justify-end text-sm",children:["zh-CN"==X()&&!H.includes("novax")&&!H.includes("elavax")&&(0,y.jsx)("div",{className:"cursor-pointer px-[15px] py-[4px] flex items-center h-[28px] rounded border-none text-xs mr-[8px] text-[#614018]",style:{backgroundImage:"linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"},onClick:()=>{ei(),n(!0)},children:$.packageType==h("payment.free")?h("payment.upgradeSubscription"):$.packageType==h("payment.monthlySubscription")||$.packageType==h("payment.yearlySubscription")?h("payment.modifySubscription"):h("payment.subscribe")}),(0,y.jsx)(lg,{size:"small",className:"mr-3"}),(0,y.jsx)("a",{style:{borderRadius:"4px",background:"#f1f5f9",padding:"6px 10px",fontSize:"12px",color:"#666",marginRight:"23px"},className:"backImg",href:location.origin.includes(".medon.com.cn")?"https://ai.medon.com.cn/"+X():location.origin.includes(".medsci.cn")?"https://ai.medsci.cn/"+X():"/"+X(),target:"_top",children:h("common.goHome")}),!en&&(0,y.jsx)("div",{className:"hover:cursor-pointer",onClick:()=>{let e=X();if(e&&"zh-CN"!==e)window.top.location.href=location.origin+"/"+e+"/login";else{var t,n;null==(t=(n=window).addLoginDom)||t.call(n)}},children:h("common.login")}),(0,y.jsx)(sG.Z,{placement:"bottomLeft",trigger:"hover",arrow:eo,overlayStyle:{width:350,paddingBottom:40},content:(0,y.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",position:"relative",paddingBottom:"40px"},children:[(0,y.jsx)("a",{className:"exit text-right w-full text-[#333333]",onClick:el,children:h("common.logout")}),(0,y.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},className:"iconHeader bg-write",children:[en&&JSON.parse(en||"").userId&&(0,y.jsx)("img",{src:er,onMouseEnter:()=>es(!0),onMouseLeave:()=>es(!1),style:{width:"60px",height:"60px"},onError:ea,alt:"avatar"}),(0,y.jsx)("span",{className:"account",children:en&&JSON.parse(en||"").userName})]})]}),children:(0,y.jsx)("a",{href:"#",children:(0,y.jsx)("div",{className:"img-area",children:en&&JSON.parse(en||"").userId&&(0,y.jsx)("img",{src:er,onMouseEnter:()=>es(!0),onMouseLeave:()=>es(!1),style:{width:"30px",height:"32px"},onError:ea,alt:"avatar"})})})})]})})]}),(0,y.jsx)("div",{className:"flex-1 overflow-hidden flex rounded-3xl bg-white",children:I||u?(0,y.jsx)("div",{className:"absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):o?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{className:`${g.menu} hidden md:!flex w-72 h-full flex-col`,children:[o?(0,y.jsx)(J.ZP,{onClick:()=>{K(),Y()},className:"h-10 leading-10 border border-solid border-gray-200 w-[calc(100%-24px)] mt-3 mx-3 text-default",icon:(0,y.jsx)(of.Z,{}),children:h("common.newConversation")}):null,(0,y.jsx)("div",{className:"px-3",children:(0,y.jsx)(V.Z,{spinning:k,children:(null==w?void 0:w.length)?(0,y.jsx)(sq,{renameConversationPromise:(e,t)=>null==v?void 0:v.renameConversation({conversation_id:e,name:t}),deleteConversationPromise:null==v?void 0:v.deleteConversation,items:w,activeKey:E,onActiveChange:e=>{K(),T(e)},onItemsChange:j,refreshItems:q,appConfig:o,onchangeModal2Open:e=>n(e)}):(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(ol.Z,{className:"pt-6",description:h("common.noSessions")})})})})]}),(0,y.jsx)("div",{className:"flex-1 min-w-0 flex flex-col overflow-hidden",children:ee?(0,y.jsx)(lp,{inputParams:L,setInputParams:R,resetFormValues:K,appConfig:o,appConfigLoading:I,appInfo:_,difyApi:v,conversationId:E,conversationName:ee,conversationItems:w,onConversationIdChange:T,appParameters:P,conversationListLoading:k,onAddConversation:Y,onItemsChange:j,conversationItemsChangeCallback:q,modal2OpenF:t,onsubmit:e=>U(e),subStatusDetails:$,changeSubStatusDetails:()=>{ei()},onClose:e=>{n(e)},selectedAppId:d,appList:p}):""})]}):(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(ol.Z,{description:h("common.noDifyAppConfig"),className:"text-base",children:(0,y.jsx)(J.ZP,{size:"large",type:"primary",onClick:c,children:h("common.startConfig")})})})})]}),i]})},lS=()=>{let{setCurrentAppConfig:e,...t}=N(),{user:n,appService:r,enableSetting:a}=t,i=(0,z.k6)(),o=new G,[s,l]=(0,b.useState)(""),[c,u]=(0,b.useState)(!1),[d,p]=(0,b.useState)(!1),{appId:m}=(0,z.UO)(),f=Q.get("userInfo"),[h,g]=(0,b.useState)(null),v=new URLSearchParams(window.top.location.search),x=i.location.pathname.match(/^\/ai-chat\/([^/]+)$/),w=x?x[1]:"",[j,S]=(0,b.useState)("nologin");(0,b.useEffect)(()=>{l(m)},[m]);let k="ai.medsci.cn"===window.location.hostname?"https://ai.medsci.cn":"https://ai.medon.com.cn",{runAsync:C,data:E,loading:T}=(0,ea.Z)(async()=>{let e=i.location.pathname.match(/^\/ai-chat\/([^/]+)$/),t=e?e[1]:"";if(t)if(t.includes("elavax")||t.includes("novax")){let e=await o.getAppByConfigKey({configKey:t.includes("elavax")?"elavax_apps":"novax_apps"},{Authorization:`Bearer ${Q.get("yudaoToken")}`,Accept:"application/json"});return null==e?void 0:e.data.map(e=>({id:e.appUuid,info:{name:e.appName,description:e.appDescription,appUser:e.appUser,appId:e.dAppUuid,appType:e.appType,appIcon:e.appIcon,feeTypes:e.feeTypes,tags:[],appUuid:e.appUuid,appNameEn:e.appNameEn},requestConfig:{apiKey:Q.get("yudaoToken"),apiBase:`${k}/dev-api/ai-base/v1`},answerForm:{enabled:!1}}))}else{let e=await r.getApp(t,{Authorization:`Bearer ${Q.get("yudaoToken")}`,Accept:"application/json"});if(e.requestConfig.apiKey=Q.get("yudaoToken"),console.log(e),e&&"问答"===e.info.appType)return[e]}return i.replace("/"),[]},{manual:!0,onSuccess:e=>{if(A&&!(null==e?void 0:e.length))return i.replace("/apps"),Promise.resolve([]);if(m)l(m);else if(!s&&(null==e?void 0:e.length)){if(1==e.length&&!w.includes("novax")&&!w.includes("elavax")){var t;l((null==(t=e[0])?void 0:t.id)||"")}(w.includes("novax")||w.includes("elavax"))&&(w.includes("novax")||w.includes("elavax"))&&e.forEach(e=>{e.info.appNameEn==w&&l((null==e?void 0:e.id)||"")})}p(!1)},onError:e=>{Z.ZP.error(`获取应用列表失败: ${e}`),console.error(e),p(!1)}}),_=(0,b.useMemo)(()=>null==E?void 0:E.find(e=>e.id===s),[E,s]),A=M();return((0,L.Z)(async()=>{if(f){let e=new G;if(Q.get("yudaoToken")&&"medsci-ask"!=w||v.get("fromPlatform"));else{S(JSON.parse(f).userName);let{data:t}=await e.getAiWriteToken({userId:JSON.parse(f).userId,userName:JSON.parse(f).userName,realName:JSON.parse(f).realName,avatar:JSON.parse(f).avatar,plaintextUserId:JSON.parse(f).plaintextUserId,mobile:JSON.parse(f).mobile,email:JSON.parse(f).email,fromPlatform:"medsci-ask"==w||v.get("fromPlatform")?"medsci":"",appUuid:w})||{};(null==t?void 0:t.token)?(Q.set("yudaoToken",t.token),localStorage.setItem("hasuraToken",t.htoken),localStorage.setItem("openid",t.openid),localStorage.setItem("socialUserId",t.socialUserId),localStorage.setItem("socialType",t.socialType)):console.error("登录失败: 未返回 token")}}C()}),d)?(0,y.jsx)("div",{className:"absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):_?(0,y.jsx)(lj,{useAppInit:(t,r)=>{(0,b.useEffect)(()=>{let a=null==E?void 0:E.find(e=>e.id===s);a&&(t.updateOptions({appId:a.info.appId,user:n,...a.requestConfig}),e(a),r())},[s])},appConfig:_,initLoading:d,handleStartConfig:()=>{a&&u(!0)},selectedAppId:s,appList:E,extComponents:(0,y.jsx)(y.Fragment,{children:(0,y.jsx)(om,{open:c,onClose:()=>u(!1),activeAppId:s,appList:E,getAppList:C,appListLoading:T,onDeleteSuccess:e=>{e===s&&l("")}})}),renderCenterTitle:()=>{var e;return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(en.Z,{className:"mr-2"}),s?(0,y.jsx)(y.Fragment,{children:(0,y.jsx)(ei.Z,{arrow:!0,placement:"bottom",trigger:["click"],menu:{style:{},selectedKeys:[s],items:[...(null==E?void 0:E.map(e=>{let t=s===e.id;return{key:e.id,label:(0,y.jsx)("div",{className:t?"text-primary":"text-default",children:null==e?void 0:e.info.name}),onClick:()=>{if(w.includes("novax")||w.includes("elavax")){window.top.location.href=location.origin+(window.top.location.href.includes("ai-chat")?"/ai-chat/":"/chat/")+`${null==e?void 0:e.info.appNameEn}`;return}},icon:(0,y.jsx)(en.Z,{})}}))||[]]},children:(0,y.jsxs)("div",{className:"cursor-pointer",children:[(0,y.jsx)("span",{className:"cursor-pointer",children:null==_||null==(e=_.info)?void 0:e.name}),(0,y.jsx)(er.Z,{className:"ml-1"})]})})}):null]})}}):null},lk=()=>{let e=N(),{user:t,appConfig:n}=e;return(0,y.jsx)(lj,{initLoading:!1,handleStartConfig:()=>{},useAppInit:(n,r)=>{let a=async()=>{n.updateOptions({user:t,apiBase:e.appConfig.requestConfig.apiBase,apiKey:e.appConfig.requestConfig.apiKey}),r()};(0,L.Z)(()=>{a()})},appConfig:n,renderCenterTitle:e=>(0,y.jsx)(y.Fragment,{children:null==e?void 0:e.name})})};function lC(){let{user:e,mode:t}=N();return e?"singleApp"===t?(0,y.jsx)(lk,{}):(0,y.jsx)(lS,{}):(0,y.jsx)("div",{className:"w-screen h-screen flex flex-col items-center justify-center",children:(0,y.jsxs)("div",{className:"absolute flex-col w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:[(0,y.jsx)(et,{hideGithubIcon:!0}),(0,y.jsx)("div",{children:"授权登录中..."}),(0,y.jsx)("div",{className:"mt-6",children:(0,y.jsx)(V.Z,{spinning:!0})})]})})}var lN=n(49021),lE=n(94448),lT=JSON.parse('{"common":{"confirm":"确认","cancel":"取消","save":"保存","delete":"删除","edit":"编辑","add":"添加","search":"搜索","loading":"加载中...","submit":"提交","reset":"重置","back":"返回","next":"下一步","previous":"上一步","close":"关闭","open":"打开","copy":"复制","paste":"粘贴","cut":"剪切","select":"选择","selectAll":"全选","clear":"清空","refresh":"刷新","retry":"重试","success":"成功","error":"错误","warning":"警告","info":"信息","login":"登录","logout":"退出","goHome":"返回首页","current":"当前","none":"无","tags":"标签","update":"更新","determine":"确定","newConversation":"新增对话","noSessions":"暂无会话","startConfig":"开始配置","noDifyAppConfig":"暂无 Dify 应用配置","getConversationListFailed":"获取会话列表失败","createNewConversation":"创建新对话","jumpToAuthPage":"跳转到获取授权页","notSubscribed":"没有订阅","alreadySubscribed":"已订阅","getSubscriptionInfo":"获取订阅信息","popupLoginBox":"弹出登录框","checkIfConversationExists":"如果对话 ID 不在当前列表中，则刷新一下","createDifyApiInstance":"创建 Dify API 实例","optimizeConversationListSearch":"优化会话列表查找逻辑（高频操作）","resetInputFormValues":"重置输入表单值","traverseInputParamsSetUndefined":"遍历 inputParams 置为 undefined","getAppInfo":"获取应用信息","getConversationList":"获取对话列表","addTempNewConversation":"添加临时新对话(要到第一次服务器响应有效的对话 ID 时才真正地创建完成)","usePreviousMatchVariable":"使用之前获取的 match 变量","header":"头部","rightIcons":"右侧图标","leftConversationList":"左侧对话列表","rightChatWindow":"右侧聊天窗口 - 移动端全屏","conversationManagement":"对话管理","addSession":"添加会话"},"app":{"title":"AI智能助手","description":"基于人工智能的智能对话助手","management":"应用配置管理","addApp":"添加应用","addAppConfig":"添加应用配置","appConfigDetail":"应用配置详情","basicInfo":"基本信息","appName":"应用名称","appDescription":"应用描述","appTags":"应用标签","noApps":"暂无应用","confirmDeleteApp":"确定删除应用吗？","deleteAppSuccess":"删除应用成功","addAppConfigSuccess":"添加应用配置成功","updateAppConfigSuccess":"更新应用配置成功","saveAppConfigFailed":"保存应用配置失败","saveAppConfigConfirmLoading":"保存应用配置确认按钮的 loading","getDifyAppInfo":"获取 Dify 应用信息","currentActiveAppId":"当前激活的应用 ID","appList":"应用列表","getAppList":"获取应用列表","appListLoading":"应用列表加载中","deleteAppSuccessCallback":"删除应用成功回调","appManagement":"应用管理"},"payment":{"supportAlipay":"支持支付宝支付","meisiAccount":"梅斯账号","pleaseAgreeToTerms":"请阅读并同意协议后激活","freeTrial":"免费试用","subscribe":"订阅","month":"月","quarter":"季","year":"年","subscribeSuccess":"订阅成功","pleaseSelectPeriod":"请选择订阅服务周期","rmb":"人民币","usd":"美元","free":"免费","monthlySubscription":"连续包月","yearlySubscription":"连续包年","subscriptionDescription":"梅斯小智 订阅说明","freeDescription":"免费：每个自然月内，每个智能体的使用上限{num}次。次月开始重新计次。","monthlyDescription":"连续包月：订阅之日起一个月内，每个智能体不限使用次数。","yearlyDescription":"连续包年：订阅之日起一年内，每个智能体不限使用次数","subscribed":"已订阅","freeUsing":"免费使用中…","cancelSubscription":"取消订阅","subscriptionValidUntil":"您的订阅可使用至 {date}","monthlySubscribing":"连续包月中…","yearlySubscribing":"连续包年中…","cancelMonthly":"取消包月","cancelYearly":"取消包年","cancelConfirmTitle":"提示","cancelMonthlyConfirm":"取消包月在{date}号生效，再次使用需要重新订阅。是否确认取消？","cancelYearlyConfirm":"取消包年在{date}号生效，再次使用需要重新订阅。是否确认取消？","qrCodeLevel":"纠错等级高","upgradeSubscription":"升级订阅","modifySubscription":"修改订阅","simulateTranslationFunction":"模拟翻译函数，实际项目中替换为 i18n 库","simulateLanguage":"模拟语言","convertNodeJSTimeoutType":"将 NodeJS.Timeout 类型转换为 React state 可接受的类型","replaceWithUIPrompt":"替换为 UI 提示","writingAppType":"写作","closeIcon":"关闭","replaceWithQRCodeLibrary":"替换为 qrcode.react 或其他二维码库","highErrorCorrectionLevel":"纠错等级高"}}'),l_=JSON.parse('{"common":{"confirm":"確認","cancel":"取消","save":"儲存","delete":"刪除","edit":"編輯","add":"新增","search":"搜尋","loading":"載入中...","submit":"提交","reset":"重置","back":"返回","next":"下一步","previous":"上一步","close":"關閉","open":"開啟","copy":"複製","paste":"貼上","cut":"剪下","select":"選擇","selectAll":"全選","clear":"清空","refresh":"重新整理","retry":"重試","success":"成功","error":"錯誤","warning":"警告","info":"資訊"},"chat":{"title":"智能對話","placeholder":"請輸入您的問題...","send":"發送","newChat":"新建對話","chatHistory":"對話歷史","clearHistory":"清空歷史","typing":"正在輸入...","thinking":"思考中...","regenerate":"重新生成","copy":"複製","like":"點讚","dislike":"點踩","share":"分享","export":"匯出","settings":"設定","model":"模型","temperature":"創造性","maxTokens":"最大長度","systemPrompt":"系統提示詞","userPrompt":"用戶提示詞","assistant":"助手","user":"用戶","system":"系統","startConversation":"開始對話","conversationList":"對話列表","noConversations":"暫無對話","deleteConversation":"刪除對話","renameConversation":"重新命名對話","conversationTitle":"對話標題","enterTitle":"請輸入標題","confirmDelete":"確認刪除此對話嗎？","deleteSuccess":"刪除成功","renameSuccess":"重新命名成功","copySuccess":"複製成功","exportSuccess":"匯出成功","networkError":"網路錯誤，請稍後重試","apiError":"API錯誤，請檢查配置","inputTooLong":"輸入內容過長","inputEmpty":"請輸入內容","modelNotSelected":"請選擇模型","connecting":"連接中...","connected":"已連接","disconnected":"連接斷開","reconnecting":"重新連接中...","sendMessage":"發送訊息","messageHistory":"訊息歷史","clearMessages":"清空訊息","exportMessages":"匯出訊息","importMessages":"匯入訊息","messageCount":"訊息數量","characterCount":"字符數量","wordCount":"單詞數量","readingTime":"閱讀時間","lastUpdated":"最後更新","createdAt":"建立時間","updatedAt":"更新時間"},"app":{"title":"AI智能助手","description":"基於人工智能的智能對話助手","version":"版本","author":"作者","contact":"聯絡我們","feedback":"意見回饋","help":"幫助","about":"關於","privacy":"隱私政策","terms":"服務條款","language":"語言","theme":"主題","lightMode":"淺色模式","darkMode":"深色模式","autoMode":"跟隨系統","fontSize":"字體大小","fontFamily":"字體","layout":"佈局","sidebar":"側邊欄","header":"頂部欄","footer":"底部欄","fullscreen":"全螢幕","exitFullscreen":"退出全螢幕","minimize":"最小化","maximize":"最大化","restore":"還原","pin":"固定","unpin":"取消固定"},"error":{"404":"頁面未找到","500":"伺服器錯誤","403":"存取被拒絕","401":"未授權存取","400":"請求錯誤","timeout":"請求逾時","networkError":"網路連接錯誤","unknownError":"未知錯誤","tryAgain":"請重試","goHome":"返回首頁","contactSupport":"聯絡技術支援","errorCode":"錯誤代碼","errorMessage":"錯誤訊息","errorDetails":"錯誤詳情","reportError":"報告錯誤","errorReported":"錯誤已報告","thankYou":"謝謝您的回饋"}}'),lA=JSON.parse('{"common":{"confirm":"Confirm","cancel":"Cancel","save":"Save","delete":"Delete","edit":"Edit","add":"Add","search":"Search","loading":"Loading...","submit":"Submit","reset":"Reset","back":"Back","next":"Next","previous":"Previous","close":"Close","open":"Open","copy":"Copy","paste":"Paste","cut":"Cut","select":"Select","selectAll":"Select All","clear":"Clear","refresh":"Refresh","retry":"Retry","success":"Success","error":"Error","warning":"Warning","info":"Information"},"chat":{"title":"AI Chat","placeholder":"Please enter your question...","send":"Send","newChat":"New Chat","chatHistory":"Chat History","clearHistory":"Clear History","typing":"Typing...","thinking":"Thinking...","regenerate":"Regenerate","copy":"Copy","like":"Like","dislike":"Dislike","share":"Share","export":"Export","settings":"Settings","model":"Model","temperature":"Creativity","maxTokens":"Max Length","systemPrompt":"System Prompt","userPrompt":"User Prompt","assistant":"Assistant","user":"User","system":"System","startConversation":"Start Conversation","conversationList":"Conversation List","noConversations":"No Conversations","deleteConversation":"Delete Conversation","renameConversation":"Rename Conversation","conversationTitle":"Conversation Title","enterTitle":"Please enter title","confirmDelete":"Are you sure to delete this conversation?","deleteSuccess":"Delete successful","renameSuccess":"Rename successful","copySuccess":"Copy successful","exportSuccess":"Export successful","networkError":"Network error, please try again later","apiError":"API error, please check configuration","inputTooLong":"Input content is too long","inputEmpty":"Please enter content","modelNotSelected":"Please select a model","connecting":"Connecting...","connected":"Connected","disconnected":"Disconnected","reconnecting":"Reconnecting...","sendMessage":"Send Message","messageHistory":"Message History","clearMessages":"Clear Messages","exportMessages":"Export Messages","importMessages":"Import Messages","messageCount":"Message Count","characterCount":"Character Count","wordCount":"Word Count","readingTime":"Reading Time","lastUpdated":"Last Updated","createdAt":"Created At","updatedAt":"Updated At"},"app":{"title":"AI Assistant","description":"AI-powered intelligent conversation assistant","version":"Version","author":"Author","contact":"Contact Us","feedback":"Feedback","help":"Help","about":"About","privacy":"Privacy Policy","terms":"Terms of Service","language":"Language","theme":"Theme","lightMode":"Light Mode","darkMode":"Dark Mode","autoMode":"Follow System","fontSize":"Font Size","fontFamily":"Font Family","layout":"Layout","sidebar":"Sidebar","header":"Header","footer":"Footer","fullscreen":"Fullscreen","exitFullscreen":"Exit Fullscreen","minimize":"Minimize","maximize":"Maximize","restore":"Restore","pin":"Pin","unpin":"Unpin"},"error":{"404":"Page Not Found","500":"Server Error","403":"Access Denied","401":"Unauthorized Access","400":"Bad Request","timeout":"Request Timeout","networkError":"Network Connection Error","unknownError":"Unknown Error","tryAgain":"Please Try Again","goHome":"Go Home","contactSupport":"Contact Support","errorCode":"Error Code","errorMessage":"Error Message","errorDetails":"Error Details","reportError":"Report Error","errorReported":"Error Reported","thankYou":"Thank you for your feedback"}}'),lP=JSON.parse('{"common":{"confirm":"X\xe1c nhận","cancel":"Hủy","save":"Lưu","delete":"X\xf3a","edit":"Chỉnh sửa","add":"Th\xeam","search":"T\xecm kiếm","loading":"Đang tải...","submit":"Gửi","reset":"Đặt lại","back":"Quay lại","next":"Tiếp theo","previous":"Trước đ\xf3","close":"Đ\xf3ng","open":"Mở","copy":"Sao ch\xe9p","paste":"D\xe1n","cut":"Cắt","select":"Chọn","selectAll":"Chọn tất cả","clear":"X\xf3a","refresh":"L\xe0m mới","retry":"Thử lại","success":"Th\xe0nh c\xf4ng","error":"Lỗi","warning":"Cảnh b\xe1o","info":"Th\xf4ng tin"},"chat":{"title":"Tr\xf2 chuyện AI","placeholder":"Vui l\xf2ng nhập c\xe2u hỏi của bạn...","send":"Gửi","newChat":"Cuộc tr\xf2 chuyện mới","chatHistory":"Lịch sử tr\xf2 chuyện","clearHistory":"X\xf3a lịch sử","typing":"Đang nhập...","thinking":"Đang suy nghĩ...","regenerate":"Tạo lại","copy":"Sao ch\xe9p","like":"Th\xedch","dislike":"Kh\xf4ng th\xedch","share":"Chia sẻ","export":"Xuất","settings":"C\xe0i đặt","model":"M\xf4 h\xecnh","temperature":"T\xednh s\xe1ng tạo","maxTokens":"Độ d\xe0i tối đa","systemPrompt":"Lời nhắc hệ thống","userPrompt":"Lời nhắc người d\xf9ng","assistant":"Trợ l\xfd","user":"Người d\xf9ng","system":"Hệ thống","startConversation":"Bắt đầu cuộc tr\xf2 chuyện","conversationList":"Danh s\xe1ch cuộc tr\xf2 chuyện","noConversations":"Kh\xf4ng c\xf3 cuộc tr\xf2 chuyện","deleteConversation":"X\xf3a cuộc tr\xf2 chuyện","renameConversation":"Đổi t\xean cuộc tr\xf2 chuyện","conversationTitle":"Ti\xeau đề cuộc tr\xf2 chuyện","enterTitle":"Vui l\xf2ng nhập ti\xeau đề","confirmDelete":"Bạn c\xf3 chắc chắn muốn x\xf3a cuộc tr\xf2 chuyện n\xe0y?","deleteSuccess":"X\xf3a th\xe0nh c\xf4ng","renameSuccess":"Đổi t\xean th\xe0nh c\xf4ng","copySuccess":"Sao ch\xe9p th\xe0nh c\xf4ng","exportSuccess":"Xuất th\xe0nh c\xf4ng","networkError":"Lỗi mạng, vui l\xf2ng thử lại sau","apiError":"Lỗi API, vui l\xf2ng kiểm tra cấu h\xecnh","inputTooLong":"Nội dung nhập qu\xe1 d\xe0i","inputEmpty":"Vui l\xf2ng nhập nội dung","modelNotSelected":"Vui l\xf2ng chọn m\xf4 h\xecnh","connecting":"Đang kết nối...","connected":"Đ\xe3 kết nối","disconnected":"Mất kết nối","reconnecting":"Đang kết nối lại...","sendMessage":"Gửi tin nhắn","messageHistory":"Lịch sử tin nhắn","clearMessages":"X\xf3a tin nhắn","exportMessages":"Xuất tin nhắn","importMessages":"Nhập tin nhắn","messageCount":"Số lượng tin nhắn","characterCount":"Số k\xfd tự","wordCount":"Số từ","readingTime":"Thời gian đọc","lastUpdated":"Cập nhật lần cuối","createdAt":"Được tạo l\xfac","updatedAt":"Được cập nhật l\xfac"},"app":{"title":"Trợ l\xfd AI","description":"Trợ l\xfd tr\xf2 chuyện th\xf4ng minh được hỗ trợ bởi AI","version":"Phi\xean bản","author":"T\xe1c giả","contact":"Li\xean hệ ch\xfang t\xf4i","feedback":"Phản hồi","help":"Trợ gi\xfap","about":"Về ch\xfang t\xf4i","privacy":"Ch\xednh s\xe1ch bảo mật","terms":"Điều khoản dịch vụ","language":"Ng\xf4n ngữ","theme":"Chủ đề","lightMode":"Chế độ s\xe1ng","darkMode":"Chế độ tối","autoMode":"Theo hệ thống","fontSize":"K\xedch thước ph\xf4ng chữ","fontFamily":"Họ ph\xf4ng chữ","layout":"Bố cục","sidebar":"Thanh b\xean","header":"Đầu trang","footer":"Ch\xe2n trang","fullscreen":"To\xe0n m\xe0n h\xecnh","exitFullscreen":"Tho\xe1t to\xe0n m\xe0n h\xecnh","minimize":"Thu nhỏ","maximize":"Ph\xf3ng to","restore":"Kh\xf4i phục","pin":"Ghim","unpin":"Bỏ ghim"},"error":{"404":"Kh\xf4ng t\xecm thấy trang","500":"Lỗi m\xe1y chủ","403":"Truy cập bị từ chối","401":"Truy cập kh\xf4ng được ph\xe9p","400":"Y\xeau cầu kh\xf4ng hợp lệ","timeout":"Hết thời gian chờ","networkError":"Lỗi kết nối mạng","unknownError":"Lỗi kh\xf4ng x\xe1c định","tryAgain":"Vui l\xf2ng thử lại","goHome":"Về trang chủ","contactSupport":"Li\xean hệ hỗ trợ","errorCode":"M\xe3 lỗi","errorMessage":"Th\xf4ng b\xe1o lỗi","errorDetails":"Chi tiết lỗi","reportError":"B\xe1o c\xe1o lỗi","errorReported":"Đ\xe3 b\xe1o c\xe1o lỗi","thankYou":"Cảm ơn phản hồi của bạn"}}'),lM=JSON.parse('{"common":{"confirm":"Confirmar","cancel":"Cancelar","save":"Guardar","delete":"Eliminar","edit":"Editar","add":"Agregar","search":"Buscar","loading":"Cargando...","submit":"Enviar","reset":"Restablecer","back":"Atr\xe1s","next":"Siguiente","previous":"Anterior","close":"Cerrar","open":"Abrir","copy":"Copiar","paste":"Pegar","cut":"Cortar","select":"Seleccionar","selectAll":"Seleccionar todo","clear":"Limpiar","refresh":"Actualizar","retry":"Reintentar","success":"\xc9xito","error":"Error","warning":"Advertencia","info":"Informaci\xf3n"},"chat":{"title":"Chat IA","placeholder":"Por favor ingrese su pregunta...","send":"Enviar","newChat":"Nueva conversaci\xf3n","chatHistory":"Historial de chat","clearHistory":"Limpiar historial","typing":"Escribiendo...","thinking":"Pensando...","regenerate":"Regenerar","copy":"Copiar","like":"Me gusta","dislike":"No me gusta","share":"Compartir","export":"Exportar","settings":"Configuraci\xf3n","model":"Modelo","temperature":"Creatividad","maxTokens":"Longitud m\xe1xima","systemPrompt":"Prompt del sistema","userPrompt":"Prompt del usuario","assistant":"Asistente","user":"Usuario","system":"Sistema","startConversation":"Iniciar conversaci\xf3n","conversationList":"Lista de conversaciones","noConversations":"Sin conversaciones","deleteConversation":"Eliminar conversaci\xf3n","renameConversation":"Renombrar conversaci\xf3n","conversationTitle":"T\xedtulo de conversaci\xf3n","enterTitle":"Por favor ingrese el t\xedtulo","confirmDelete":"\xbfEst\xe1 seguro de eliminar esta conversaci\xf3n?","deleteSuccess":"Eliminaci\xf3n exitosa","renameSuccess":"Renombrado exitoso","copySuccess":"Copiado exitoso","exportSuccess":"Exportaci\xf3n exitosa","networkError":"Error de red, por favor intente m\xe1s tarde","apiError":"Error de API, por favor verifique la configuraci\xf3n","inputTooLong":"El contenido de entrada es demasiado largo","inputEmpty":"Por favor ingrese contenido","modelNotSelected":"Por favor seleccione un modelo","connecting":"Conectando...","connected":"Conectado","disconnected":"Desconectado","reconnecting":"Reconectando...","sendMessage":"Enviar mensaje","messageHistory":"Historial de mensajes","clearMessages":"Limpiar mensajes","exportMessages":"Exportar mensajes","importMessages":"Importar mensajes","messageCount":"Cantidad de mensajes","characterCount":"Cantidad de caracteres","wordCount":"Cantidad de palabras","readingTime":"Tiempo de lectura","lastUpdated":"\xdaltima actualizaci\xf3n","createdAt":"Creado en","updatedAt":"Actualizado en"},"app":{"title":"Asistente IA","description":"Asistente de conversaci\xf3n inteligente impulsado por IA","version":"Versi\xf3n","author":"Autor","contact":"Cont\xe1ctanos","feedback":"Comentarios","help":"Ayuda","about":"Acerca de","privacy":"Pol\xedtica de privacidad","terms":"T\xe9rminos de servicio","language":"Idioma","theme":"Tema","lightMode":"Modo claro","darkMode":"Modo oscuro","autoMode":"Seguir sistema","fontSize":"Tama\xf1o de fuente","fontFamily":"Familia de fuente","layout":"Dise\xf1o","sidebar":"Barra lateral","header":"Encabezado","footer":"Pie de p\xe1gina","fullscreen":"Pantalla completa","exitFullscreen":"Salir de pantalla completa","minimize":"Minimizar","maximize":"Maximizar","restore":"Restaurar","pin":"Fijar","unpin":"Desfijar"},"error":{"404":"P\xe1gina no encontrada","500":"Error del servidor","403":"Acceso denegado","401":"Acceso no autorizado","400":"Solicitud incorrecta","timeout":"Tiempo de espera agotado","networkError":"Error de conexi\xf3n de red","unknownError":"Error desconocido","tryAgain":"Por favor intente de nuevo","goHome":"Ir al inicio","contactSupport":"Contactar soporte","errorCode":"C\xf3digo de error","errorMessage":"Mensaje de error","errorDetails":"Detalles del error","reportError":"Reportar error","errorReported":"Error reportado","thankYou":"Gracias por sus comentarios"}}'),lI=JSON.parse('{"common":{"confirm":"تأكيد","cancel":"إلغاء","save":"حفظ","delete":"حذف","edit":"تحرير","add":"إضافة","search":"بحث","loading":"جاري التحميل...","submit":"إرسال","reset":"إعادة تعيين","back":"رجوع","next":"التالي","previous":"السابق","close":"إغلاق","open":"فتح","copy":"نسخ","paste":"لصق","cut":"قص","select":"تحديد","selectAll":"تحديد الكل","clear":"مسح","refresh":"تحديث","retry":"إعادة المحاولة","success":"نجح","error":"خطأ","warning":"تحذير","info":"معلومات"},"chat":{"title":"دردشة الذكاء الاصطناعي","placeholder":"يرجى إدخال سؤالك...","send":"إرسال","newChat":"محادثة جديدة","chatHistory":"تاريخ المحادثة","clearHistory":"مسح التاريخ","typing":"جاري الكتابة...","thinking":"جاري التفكير...","regenerate":"إعادة توليد","copy":"نسخ","like":"إعجاب","dislike":"عدم إعجاب","share":"مشاركة","export":"تصدير","settings":"الإعدادات","model":"النموذج","temperature":"الإبداع","maxTokens":"الطول الأقصى","systemPrompt":"موجه النظام","userPrompt":"موجه المستخدم","assistant":"المساعد","user":"المستخدم","system":"النظام","startConversation":"بدء المحادثة","conversationList":"قائمة المحادثات","noConversations":"لا توجد محادثات","deleteConversation":"حذف المحادثة","renameConversation":"إعادة تسمية المحادثة","conversationTitle":"عنوان المحادثة","enterTitle":"يرجى إدخال العنوان","confirmDelete":"هل أنت متأكد من حذف هذه المحادثة؟","deleteSuccess":"تم الحذف بنجاح","renameSuccess":"تمت إعادة التسمية بنجاح","copySuccess":"تم النسخ بنجاح","exportSuccess":"تم التصدير بنجاح","networkError":"خطأ في الشبكة، يرجى المحاولة لاحقاً","apiError":"خطأ في واجهة برمجة التطبيقات، يرجى التحقق من الإعدادات","inputTooLong":"المحتوى المدخل طويل جداً","inputEmpty":"يرجى إدخال المحتوى","modelNotSelected":"يرجى اختيار نموذج","connecting":"جاري الاتصال...","connected":"متصل","disconnected":"منقطع","reconnecting":"جاري إعادة الاتصال...","sendMessage":"إرسال رسالة","messageHistory":"تاريخ الرسائل","clearMessages":"مسح الرسائل","exportMessages":"تصدير الرسائل","importMessages":"استيراد الرسائل","messageCount":"عدد الرسائل","characterCount":"عدد الأحرف","wordCount":"عدد الكلمات","readingTime":"وقت القراءة","lastUpdated":"آخر تحديث","createdAt":"تم الإنشاء في","updatedAt":"تم التحديث في"},"app":{"title":"مساعد الذكاء الاصطناعي","description":"مساعد محادثة ذكي مدعوم بالذكاء الاصطناعي","version":"الإصدار","author":"المؤلف","contact":"اتصل بنا","feedback":"التعليقات","help":"المساعدة","about":"حول","privacy":"سياسة الخصوصية","terms":"شروط الخدمة","language":"اللغة","theme":"المظهر","lightMode":"الوضع الفاتح","darkMode":"الوضع الداكن","autoMode":"تتبع النظام","fontSize":"حجم الخط","fontFamily":"عائلة الخط","layout":"التخطيط","sidebar":"الشريط الجانبي","header":"الرأس","footer":"التذييل","fullscreen":"ملء الشاشة","exitFullscreen":"الخروج من ملء الشاشة","minimize":"تصغير","maximize":"تكبير","restore":"استعادة","pin":"تثبيت","unpin":"إلغاء التثبيت"},"error":{"404":"الصفحة غير موجودة","500":"خطأ في الخادم","403":"تم رفض الوصول","401":"وصول غير مصرح","400":"طلب خاطئ","timeout":"انتهت مهلة الطلب","networkError":"خطأ في اتصال الشبكة","unknownError":"خطأ غير معروف","tryAgain":"يرجى المحاولة مرة أخرى","goHome":"العودة للرئيسية","contactSupport":"اتصل بالدعم","errorCode":"رمز الخطأ","errorMessage":"رسالة الخطأ","errorDetails":"تفاصيل الخطأ","reportError":"الإبلاغ عن خطأ","errorReported":"تم الإبلاغ عن الخطأ","thankYou":"شكراً لك على ملاحظاتك"}}'),lO=JSON.parse('{"common":{"confirm":"Konfirmasi","cancel":"Batal","save":"Simpan","delete":"Hapus","edit":"Edit","add":"Tambah","search":"Cari","loading":"Memuat...","submit":"Kirim","reset":"Reset","back":"Kembali","next":"Selanjutnya","previous":"Sebelumnya","close":"Tutup","open":"Buka","copy":"Salin","paste":"Tempel","cut":"Potong","select":"Pilih","selectAll":"Pilih Semua","clear":"Hapus","refresh":"Refresh","retry":"Coba Lagi","success":"Berhasil","error":"Error","warning":"Peringatan","info":"Informasi"},"chat":{"title":"Chat AI","placeholder":"Silakan masukkan pertanyaan Anda...","send":"Kirim","newChat":"Chat Baru","chatHistory":"Riwayat Chat","clearHistory":"Hapus Riwayat","typing":"Mengetik...","thinking":"Berpikir...","regenerate":"Regenerasi","copy":"Salin","like":"Suka","dislike":"Tidak Suka","share":"Bagikan","export":"Ekspor","settings":"Pengaturan","model":"Model","temperature":"Kreativitas","maxTokens":"Panjang Maksimal","systemPrompt":"Prompt Sistem","userPrompt":"Prompt Pengguna","assistant":"Asisten","user":"Pengguna","system":"Sistem","startConversation":"Mulai Percakapan","conversationList":"Daftar Percakapan","noConversations":"Tidak Ada Percakapan","deleteConversation":"Hapus Percakapan","renameConversation":"Ubah Nama Percakapan","conversationTitle":"Judul Percakapan","enterTitle":"Silakan masukkan judul","confirmDelete":"Apakah Anda yakin ingin menghapus percakapan ini?","deleteSuccess":"Berhasil dihapus","renameSuccess":"Berhasil diubah nama","copySuccess":"Berhasil disalin","exportSuccess":"Berhasil diekspor","networkError":"Error jaringan, silakan coba lagi nanti","apiError":"Error API, silakan periksa konfigurasi","inputTooLong":"Konten input terlalu panjang","inputEmpty":"Silakan masukkan konten","modelNotSelected":"Silakan pilih model","connecting":"Menghubungkan...","connected":"Terhubung","disconnected":"Terputus","reconnecting":"Menghubungkan kembali...","sendMessage":"Kirim Pesan","messageHistory":"Riwayat Pesan","clearMessages":"Hapus Pesan","exportMessages":"Ekspor Pesan","importMessages":"Impor Pesan","messageCount":"Jumlah Pesan","characterCount":"Jumlah Karakter","wordCount":"Jumlah Kata","readingTime":"Waktu Baca","lastUpdated":"Terakhir Diperbarui","createdAt":"Dibuat Pada","updatedAt":"Diperbarui Pada"},"app":{"title":"Asisten AI","description":"Asisten percakapan cerdas yang didukung AI","version":"Versi","author":"Penulis","contact":"Hubungi Kami","feedback":"Umpan Balik","help":"Bantuan","about":"Tentang","privacy":"Kebijakan Privasi","terms":"Syarat Layanan","language":"Bahasa","theme":"Tema","lightMode":"Mode Terang","darkMode":"Mode Gelap","autoMode":"Ikuti Sistem","fontSize":"Ukuran Font","fontFamily":"Keluarga Font","layout":"Tata Letak","sidebar":"Sidebar","header":"Header","footer":"Footer","fullscreen":"Layar Penuh","exitFullscreen":"Keluar Layar Penuh","minimize":"Minimize","maximize":"Maximize","restore":"Restore","pin":"Pin","unpin":"Unpin"},"error":{"404":"Halaman Tidak Ditemukan","500":"Error Server","403":"Akses Ditolak","401":"Akses Tidak Diizinkan","400":"Permintaan Buruk","timeout":"Timeout Permintaan","networkError":"Error Koneksi Jaringan","unknownError":"Error Tidak Dikenal","tryAgain":"Silakan Coba Lagi","goHome":"Ke Beranda","contactSupport":"Hubungi Dukungan","errorCode":"Kode Error","errorMessage":"Pesan Error","errorDetails":"Detail Error","reportError":"Laporkan Error","errorReported":"Error Dilaporkan","thankYou":"Terima kasih atas umpan balik Anda"}}'),lL=JSON.parse('{"common":{"confirm":"Confirmar","cancel":"Cancelar","save":"Salvar","delete":"Excluir","edit":"Editar","add":"Adicionar","search":"Pesquisar","loading":"Carregando...","submit":"Enviar","reset":"Redefinir","back":"Voltar","next":"Pr\xf3ximo","previous":"Anterior","close":"Fechar","open":"Abrir","copy":"Copiar","paste":"Colar","cut":"Cortar","select":"Selecionar","selectAll":"Selecionar Tudo","clear":"Limpar","refresh":"Atualizar","retry":"Tentar Novamente","success":"Sucesso","error":"Erro","warning":"Aviso","info":"Informa\xe7\xe3o"},"chat":{"title":"Chat IA","placeholder":"Por favor, digite sua pergunta...","send":"Enviar","newChat":"Nova Conversa","chatHistory":"Hist\xf3rico de Chat","clearHistory":"Limpar Hist\xf3rico","typing":"Digitando...","thinking":"Pensando...","regenerate":"Regenerar","copy":"Copiar","like":"Curtir","dislike":"N\xe3o Curtir","share":"Compartilhar","export":"Exportar","settings":"Configura\xe7\xf5es","model":"Modelo","temperature":"Criatividade","maxTokens":"Comprimento M\xe1ximo","systemPrompt":"Prompt do Sistema","userPrompt":"Prompt do Usu\xe1rio","assistant":"Assistente","user":"Usu\xe1rio","system":"Sistema","startConversation":"Iniciar Conversa","conversationList":"Lista de Conversas","noConversations":"Nenhuma Conversa","deleteConversation":"Excluir Conversa","renameConversation":"Renomear Conversa","conversationTitle":"T\xedtulo da Conversa","enterTitle":"Por favor, digite o t\xedtulo","confirmDelete":"Tem certeza de que deseja excluir esta conversa?","deleteSuccess":"Exclus\xe3o bem-sucedida","renameSuccess":"Renomea\xe7\xe3o bem-sucedida","copySuccess":"C\xf3pia bem-sucedida","exportSuccess":"Exporta\xe7\xe3o bem-sucedida","networkError":"Erro de rede, tente novamente mais tarde","apiError":"Erro de API, verifique a configura\xe7\xe3o","inputTooLong":"Conte\xfado de entrada muito longo","inputEmpty":"Por favor, digite o conte\xfado","modelNotSelected":"Por favor, selecione um modelo","connecting":"Conectando...","connected":"Conectado","disconnected":"Desconectado","reconnecting":"Reconectando...","sendMessage":"Enviar Mensagem","messageHistory":"Hist\xf3rico de Mensagens","clearMessages":"Limpar Mensagens","exportMessages":"Exportar Mensagens","importMessages":"Importar Mensagens","messageCount":"Contagem de Mensagens","characterCount":"Contagem de Caracteres","wordCount":"Contagem de Palavras","readingTime":"Tempo de Leitura","lastUpdated":"\xdaltima Atualiza\xe7\xe3o","createdAt":"Criado em","updatedAt":"Atualizado em"},"app":{"title":"Assistente IA","description":"Assistente de conversa inteligente alimentado por IA","version":"Vers\xe3o","author":"Autor","contact":"Entre em Contato","feedback":"Feedback","help":"Ajuda","about":"Sobre","privacy":"Pol\xedtica de Privacidade","terms":"Termos de Servi\xe7o","language":"Idioma","theme":"Tema","lightMode":"Modo Claro","darkMode":"Modo Escuro","autoMode":"Seguir Sistema","fontSize":"Tamanho da Fonte","fontFamily":"Fam\xedlia da Fonte","layout":"Layout","sidebar":"Barra Lateral","header":"Cabe\xe7alho","footer":"Rodap\xe9","fullscreen":"Tela Cheia","exitFullscreen":"Sair da Tela Cheia","minimize":"Minimizar","maximize":"Maximizar","restore":"Restaurar","pin":"Fixar","unpin":"Desfixar"},"error":{"404":"P\xe1gina N\xe3o Encontrada","500":"Erro do Servidor","403":"Acesso Negado","401":"Acesso N\xe3o Autorizado","400":"Solicita\xe7\xe3o Inv\xe1lida","timeout":"Timeout da Solicita\xe7\xe3o","networkError":"Erro de Conex\xe3o de Rede","unknownError":"Erro Desconhecido","tryAgain":"Tente Novamente","goHome":"Ir para In\xedcio","contactSupport":"Contatar Suporte","errorCode":"C\xf3digo de Erro","errorMessage":"Mensagem de Erro","errorDetails":"Detalhes do Erro","reportError":"Relatar Erro","errorReported":"Erro Relatado","thankYou":"Obrigado pelo seu feedback"}}'),lR=JSON.parse('{"common":{"confirm":"確認","cancel":"キャンセル","save":"保存","delete":"削除","edit":"編集","add":"追加","search":"検索","loading":"読み込み中...","submit":"送信","reset":"リセット","back":"戻る","next":"次へ","previous":"前へ","close":"閉じる","open":"開く","copy":"コピー","paste":"貼り付け","cut":"切り取り","select":"選択","selectAll":"すべて選択","clear":"クリア","refresh":"更新","retry":"再試行","success":"成功","error":"エラー","warning":"警告","info":"情報"},"chat":{"title":"AIチャット","placeholder":"質問を入力してください...","send":"送信","newChat":"新しいチャット","chatHistory":"チャット履歴","clearHistory":"履歴をクリア","typing":"入力中...","thinking":"考え中...","regenerate":"再生成","copy":"コピー","like":"いいね","dislike":"よくない","share":"共有","export":"エクスポート","settings":"設定","model":"モデル","temperature":"創造性","maxTokens":"最大長","systemPrompt":"システムプロンプト","userPrompt":"ユーザープロンプト","assistant":"アシスタント","user":"ユーザー","system":"システム","startConversation":"会話を開始","conversationList":"会話リスト","noConversations":"会話がありません","deleteConversation":"会話を削除","renameConversation":"会話名を変更","conversationTitle":"会話タイトル","enterTitle":"タイトルを入力してください","confirmDelete":"この会話を削除してもよろしいですか？","deleteSuccess":"削除成功","renameSuccess":"名前変更成功","copySuccess":"コピー成功","exportSuccess":"エクスポート成功","networkError":"ネットワークエラー、後でもう一度お試しください","apiError":"APIエラー、設定を確認してください","inputTooLong":"入力内容が長すぎます","inputEmpty":"内容を入力してください","modelNotSelected":"モデルを選択してください","connecting":"接続中...","connected":"接続済み","disconnected":"切断","reconnecting":"再接続中...","sendMessage":"メッセージを送信","messageHistory":"メッセージ履歴","clearMessages":"メッセージをクリア","exportMessages":"メッセージをエクスポート","importMessages":"メッセージをインポート","messageCount":"メッセージ数","characterCount":"文字数","wordCount":"単語数","readingTime":"読書時間","lastUpdated":"最終更新","createdAt":"作成日時","updatedAt":"更新日時"},"app":{"title":"AIアシスタント","description":"AI搭載のインテリジェント会話アシスタント","version":"バージョン","author":"作者","contact":"お問い合わせ","feedback":"フィードバック","help":"ヘルプ","about":"について","privacy":"プライバシーポリシー","terms":"利用規約","language":"言語","theme":"テーマ","lightMode":"ライトモード","darkMode":"ダークモード","autoMode":"システムに従う","fontSize":"フォントサイズ","fontFamily":"フォントファミリー","layout":"レイアウト","sidebar":"サイドバー","header":"ヘッダー","footer":"フッター","fullscreen":"フルスクリーン","exitFullscreen":"フルスクリーン終了","minimize":"最小化","maximize":"最大化","restore":"復元","pin":"ピン留め","unpin":"ピン留め解除"},"error":{"404":"ページが見つかりません","500":"サーバーエラー","403":"アクセス拒否","401":"未認証アクセス","400":"不正なリクエスト","timeout":"リクエストタイムアウト","networkError":"ネットワーク接続エラー","unknownError":"不明なエラー","tryAgain":"もう一度お試しください","goHome":"ホームに戻る","contactSupport":"サポートに連絡","errorCode":"エラーコード","errorMessage":"エラーメッセージ","errorDetails":"エラー詳細","reportError":"エラーを報告","errorReported":"エラーが報告されました","thankYou":"フィードバックありがとうございます"}}'),l$=JSON.parse('{"common":{"confirm":"확인","cancel":"취소","save":"저장","delete":"삭제","edit":"편집","add":"추가","search":"검색","loading":"로딩 중...","submit":"제출","reset":"재설정","back":"뒤로","next":"다음","previous":"이전","close":"닫기","open":"열기","copy":"복사","paste":"붙여넣기","cut":"잘라내기","select":"선택","selectAll":"모두 선택","clear":"지우기","refresh":"새로고침","retry":"다시 시도","success":"성공","error":"오류","warning":"경고","info":"정보"},"chat":{"title":"AI 채팅","placeholder":"질문을 입력해주세요...","send":"전송","newChat":"새 채팅","chatHistory":"채팅 기록","clearHistory":"기록 지우기","typing":"입력 중...","thinking":"생각 중...","regenerate":"재생성","copy":"복사","like":"좋아요","dislike":"싫어요","share":"공유","export":"내보내기","settings":"설정","model":"모델","temperature":"창의성","maxTokens":"최대 길이","systemPrompt":"시스템 프롬프트","userPrompt":"사용자 프롬프트","assistant":"어시스턴트","user":"사용자","system":"시스템","startConversation":"대화 시작","conversationList":"대화 목록","noConversations":"대화가 없습니다","deleteConversation":"대화 삭제","renameConversation":"대화 이름 변경","conversationTitle":"대화 제목","enterTitle":"제목을 입력해주세요","confirmDelete":"이 대화를 삭제하시겠습니까?","deleteSuccess":"삭제 성공","renameSuccess":"이름 변경 성공","copySuccess":"복사 성공","exportSuccess":"내보내기 성공","networkError":"네트워크 오류, 나중에 다시 시도해주세요","apiError":"API 오류, 설정을 확인해주세요","inputTooLong":"입력 내용이 너무 깁니다","inputEmpty":"내용을 입력해주세요","modelNotSelected":"모델을 선택해주세요","connecting":"연결 중...","connected":"연결됨","disconnected":"연결 끊김","reconnecting":"재연결 중...","sendMessage":"메시지 전송","messageHistory":"메시지 기록","clearMessages":"메시지 지우기","exportMessages":"메시지 내보내기","importMessages":"메시지 가져오기","messageCount":"메시지 수","characterCount":"문자 수","wordCount":"단어 수","readingTime":"읽기 시간","lastUpdated":"마지막 업데이트","createdAt":"생성일","updatedAt":"수정일"},"app":{"title":"AI 어시스턴트","description":"AI 기반 지능형 대화 어시스턴트","version":"버전","author":"작성자","contact":"문의하기","feedback":"피드백","help":"도움말","about":"정보","privacy":"개인정보 정책","terms":"서비스 약관","language":"언어","theme":"테마","lightMode":"라이트 모드","darkMode":"다크 모드","autoMode":"시스템 따라가기","fontSize":"글꼴 크기","fontFamily":"글꼴 패밀리","layout":"레이아웃","sidebar":"사이드바","header":"헤더","footer":"푸터","fullscreen":"전체화면","exitFullscreen":"전체화면 종료","minimize":"최소화","maximize":"최대화","restore":"복원","pin":"고정","unpin":"고정 해제"},"error":{"404":"페이지를 찾을 수 없습니다","500":"서버 오류","403":"접근 거부","401":"인증되지 않은 접근","400":"잘못된 요청","timeout":"요청 시간 초과","networkError":"네트워크 연결 오류","unknownError":"알 수 없는 오류","tryAgain":"다시 시도해주세요","goHome":"홈으로 가기","contactSupport":"지원팀 문의","errorCode":"오류 코드","errorMessage":"오류 메시지","errorDetails":"오류 세부사항","reportError":"오류 신고","errorReported":"오류가 신고되었습니다","thankYou":"피드백 감사합니다"}}'),lF=JSON.parse('{"common":{"confirm":"Sahkan","cancel":"Batal","save":"Simpan","delete":"Padam","edit":"Edit","add":"Tambah","search":"Cari","loading":"Memuatkan...","submit":"Hantar","reset":"Set Semula","back":"Kembali","next":"Seterusnya","previous":"Sebelumnya","close":"Tutup","open":"Buka","copy":"Salin","paste":"Tampal","cut":"Potong","select":"Pilih","selectAll":"Pilih Semua","clear":"Kosongkan","refresh":"Muat Semula","retry":"Cuba Lagi","success":"Berjaya","error":"Ralat","warning":"Amaran","info":"Maklumat"},"chat":{"title":"Sembang AI","placeholder":"Sila masukkan soalan anda...","send":"Hantar","newChat":"Sembang Baru","chatHistory":"Sejarah Sembang","clearHistory":"Kosongkan Sejarah","typing":"Menaip...","thinking":"Berfikir...","regenerate":"Jana Semula","copy":"Salin","like":"Suka","dislike":"Tidak Suka","share":"Kongsi","export":"Eksport","settings":"Tetapan","model":"Model","temperature":"Kreativiti","maxTokens":"Panjang Maksimum","systemPrompt":"Prompt Sistem","userPrompt":"Prompt Pengguna","assistant":"Pembantu","user":"Pengguna","system":"Sistem","startConversation":"Mula Perbualan","conversationList":"Senarai Perbualan","noConversations":"Tiada Perbualan","deleteConversation":"Padam Perbualan","renameConversation":"Namakan Semula Perbualan","conversationTitle":"Tajuk Perbualan","enterTitle":"Sila masukkan tajuk","confirmDelete":"Adakah anda pasti untuk memadam perbualan ini?","deleteSuccess":"Berjaya dipadam","renameSuccess":"Berjaya dinamakan semula","copySuccess":"Berjaya disalin","exportSuccess":"Berjaya dieksport","networkError":"Ralat rangkaian, sila cuba lagi kemudian","apiError":"Ralat API, sila semak konfigurasi","inputTooLong":"Kandungan input terlalu panjang","inputEmpty":"Sila masukkan kandungan","modelNotSelected":"Sila pilih model","connecting":"Menyambung...","connected":"Disambungkan","disconnected":"Terputus","reconnecting":"Menyambung semula...","sendMessage":"Hantar Mesej","messageHistory":"Sejarah Mesej","clearMessages":"Kosongkan Mesej","exportMessages":"Eksport Mesej","importMessages":"Import Mesej","messageCount":"Bilangan Mesej","characterCount":"Bilangan Aksara","wordCount":"Bilangan Perkataan","readingTime":"Masa Membaca","lastUpdated":"Kemaskini Terakhir","createdAt":"Dicipta Pada","updatedAt":"Dikemaskini Pada"},"app":{"title":"Pembantu AI","description":"Pembantu perbualan pintar yang dikuasakan AI","version":"Versi","author":"Pengarang","contact":"Hubungi Kami","feedback":"Maklum Balas","help":"Bantuan","about":"Mengenai","privacy":"Dasar Privasi","terms":"Terma Perkhidmatan","language":"Bahasa","theme":"Tema","lightMode":"Mod Terang","darkMode":"Mod Gelap","autoMode":"Ikut Sistem","fontSize":"Saiz Fon","fontFamily":"Keluarga Fon","layout":"Susun Atur","sidebar":"Bar Sisi","header":"Pengepala","footer":"Pengaki","fullscreen":"Skrin Penuh","exitFullscreen":"Keluar Skrin Penuh","minimize":"Minimumkan","maximize":"Maksimumkan","restore":"Pulihkan","pin":"Pin","unpin":"Nyahpin"},"error":{"404":"Halaman Tidak Dijumpai","500":"Ralat Pelayan","403":"Akses Ditolak","401":"Akses Tidak Dibenarkan","400":"Permintaan Buruk","timeout":"Tamat Masa Permintaan","networkError":"Ralat Sambungan Rangkaian","unknownError":"Ralat Tidak Diketahui","tryAgain":"Sila Cuba Lagi","goHome":"Pergi ke Laman Utama","contactSupport":"Hubungi Sokongan","errorCode":"Kod Ralat","errorMessage":"Mesej Ralat","errorDetails":"Butiran Ralat","reportError":"Laporkan Ralat","errorReported":"Ralat Dilaporkan","thankYou":"Terima kasih atas maklum balas anda"}}');lN.ZP.use(lE.Z).use(ou.Db).init({resources:{"zh-CN":{translation:lT},"zh-TW":{translation:l_},en:{translation:lA},vi:{translation:lP},es:{translation:lM},ar:{translation:lI},id:{translation:lO},pt:{translation:lL},ja:{translation:lR},ko:{translation:l$},ms:{translation:lF}},fallbackLng:"zh-CN",debug:!1,interpolation:{escapeValue:!1},detection:{order:["cookie","localStorage","navigator","htmlTag"],caches:["cookie","localStorage"],cookieMinutes:160,cookieDomain:".medsci.cn"}}),function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:P;(0,_.P)(e)}();let lz=[{path:"/:appUuid",component:()=>(0,y.jsx)(lC,{})}],lD=document.getElementById("root");Q.removeInit("yudaoToken"),lD&&x.createRoot(lD).render((0,y.jsx)(function(){let[e,t]=(0,b.useState)("nologin"),[n,r]=(0,b.useState)(null),a=new URLSearchParams(window.top.location.search),i=(0,z.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),o=i?i[1]:"";return(0,L.Z)(()=>{let e=new G,t=F.get("userInfo");if(t)if(r(JSON.parse(t)),F.get("yudaoToken")&&"medsci-ask"!=o||!a.get("fromPlatform"));else{let n=JSON.parse(t);e.getAiWriteToken({userId:n.userId,userName:n.userName,realName:n.realName,avatar:n.avatar,plaintextUserId:n.plaintextUserId,mobile:n.mobile,email:n.email,fromPlatform:"medsci-ask"==o?"medsci":null,appUuid:o}).then(e=>{let{data:t}=e;(null==t?void 0:t.token)?(F.set("yudaoToken",t.token),localStorage.setItem("hasuraToken",t.htoken),localStorage.setItem("openid",t.openid),localStorage.setItem("socialUserId",t.socialUserId),localStorage.setItem("socialType",t.socialType)):console.error("登录失败: 未返回 token")})}}),(0,b.useEffect)(()=>{if(n){let e=new G,r=F.get("yudaoToken");t(n.userName),r&&"medsci-ask"!=o||a.get("fromPlatform")||e.getAiWriteToken({userId:n.userId,userName:n.userName,realName:n.realName,avatar:n.avatar,plaintextUserId:n.plaintextUserId,mobile:n.mobile,email:n.email,fromPlatform:"medsci-ask"==o||a.get("fromPlatform")?"medsci":null,appUuid:o}).then(e=>{let{data:t}=e||{};(null==t?void 0:t.token)?(F.set("yudaoToken",t.token),localStorage.setItem("hasuraToken",t.htoken),localStorage.setItem("openid",t.openid),localStorage.setItem("socialUserId",t.socialUserId),localStorage.setItem("socialType",t.socialType)):console.error("登录失败: 未返回 token")})}},[n]),(0,b.useEffect)(()=>{e&&console.log("Updated userId====",e)},[e]),(0,b.useEffect)(()=>{F.set("ai_apps_lang",F.get("ai_apps_lang")?F.get("ai_apps_lang"):navigator.browserLanguage||navigator.language)},[]),(0,y.jsx)(z.VK,{basename:"",routes:lz,children:(0,y.jsx)(C,{value:{mode:"multiApp",user:e,appService:new s2,enableSetting:!1},children:(0,y.jsx)(z.AW,{})})})},{}))}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a||"object"==typeof r&&r&&(4&a&&r.__esModule||16&a&&"function"==typeof r.then))return r;var i=Object.create(null);n.r(i);var o={};e=e||[null,t({}),t([]),t(t)];for(var s=2&a&&r;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach(e=>{o[e]=()=>r[e]});return o.default=()=>r,n.d(i,o),i}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/async/"+e+"."+({103:"89ed92cf",113:"e6e53918",139:"fab3c4a9",174:"1a50f52e",18:"cc888385",194:"44058ca1",205:"45736c37",208:"ddb6c055",219:"9951a8b3",254:"fcfa37af",33:"d3140ddd",354:"2ecd0882",360:"f01ca2dd",406:"bf0604ad",469:"fd1e1bbe",476:"198a7d91",503:"d03a97e9",516:"e942ecc7",541:"e01b9e80",584:"7293f8f7",66:"28c91132",672:"696afbca",714:"272bc6bb",733:"0cc83532",751:"d1496734",774:"ca6d64f3",796:"e7c58007",8:"faf715eb",828:"253e6351",836:"41e72eca",854:"f9b7e535",857:"79a042c5",867:"514b7df5",951:"1bfd7079",953:"2ffeeb4d",957:"2743cee0",976:"3df1c38e"})[e]+".js",n.miniCssF=e=>""+e+".css",n.h=()=>"5f1883ccd741f880",n.g=(()=>{if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}})(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="dify-chat-web:";n.l=function(r,a,i,o){if(e[r])return void e[r].push(a);if(void 0!==i)for(var s,l,c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+i){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+i),s.src=r),e[r]=[a];var p=function(t,n){s.onerror=s.onload=null,clearTimeout(m);var a=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),a&&a.forEach(function(e){return e(n)}),t)return t(n)},m=setTimeout(p.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=p.bind(null,s.onerror),s.onload=p.bind(null,s.onload),l&&document.head.appendChild(s)}})(),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e=[];n.O=(t,r,a,i)=>{if(r){i=i||0;for(var o=e.length;o>0&&e[o-1][2]>i;o--)e[o]=e[o-1];e[o]=[r,a,i];return}for(var s=1/0,o=0;o<e.length;o++){for(var[r,a,i]=e[o],l=!0,c=0;c<r.length;c++)(!1&i||s>=i)&&Object.keys(n.O).every(e=>n.O[e](r[c]))?r.splice(c--,1):(l=!1,i<s&&(s=i));if(l){e.splice(o--,1);var u=a();void 0!==u&&(t=u)}}return t}})(),n.p="/ai-chat/",n.rv=()=>"1.3.5",(()=>{var e={980:0};n.f.j=function(t,r){var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var i=new Promise((n,r)=>a=e[t]=[n,r]);r.push(a[2]=i);var o=n.p+n.u(t),s=Error();n.l(o,function(r){if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var i=r&&("load"===r.type?"missing":r.type),o=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+i+": "+o+")",s.name="ChunkLoadError",s.type=i,s.request=o,a[1](s)}},"chunk-"+t,t)}},n.O.j=t=>0===e[t];var t=(t,r)=>{var a,i,[o,s,l]=r,c=0;if(o.some(t=>0!==e[t])){for(a in s)n.o(s,a)&&(n.m[a]=s[a]);if(l)var u=l(n)}for(t&&t(r);c<o.length;c++)i=o[c],n.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return n.O(u)},r=self.webpackChunkdify_chat_web=self.webpackChunkdify_chat_web||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.ruid="bundler=rspack@1.3.5";var r=n.O(void 0,["361","749"],function(){return n(58779)});r=n.O(r)})();