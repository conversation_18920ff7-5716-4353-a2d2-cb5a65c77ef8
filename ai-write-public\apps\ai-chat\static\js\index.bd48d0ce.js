/*! For license information please see index.bd48d0ce.js.LICENSE.txt */
(()=>{"use strict";var e={58779:function(e,t,n){let r,a,i,o,s;var l,c,u,d,p,m,f,h,g,v,y=n(52676),x=n(38751),b=n(75271),j=n.t(b,2);let w={mode:"multiApp",user:"",appService:{},enableSetting:!0},S={mode:"singleApp",user:"",appConfig:{requestConfig:{apiBase:"",apiKey:""},answerForm:{enabled:!1,feedbackText:""}}},N=b.createContext(w),k=N.Provider,C=()=>{let[e,t]=(0,b.useState)({}),n=(0,b.useContext)(N),{mode:r}=n;return{currentAppConfig:e,setCurrentAppConfig:t,..."multiApp"===r?w:S,...n}};class _{}class E{}var T=n(20274);let O=e=>!!e&&e.startsWith("temp"),A={sm:0,md:768,lg:1024,xl:1280,"2xl":1536},I=()=>{let{sm:e,md:t}=(0,T.Z)();return!!e&&!t},P="DIFY_CHAT__DIFY_VERSION",M={get version(){return localStorage.getItem(P)||""},set version(version){localStorage.setItem(P,version)}};var $=n(72422),L=n(96038);let R={".medsci.cn":".medsci.cn",".medon.com.cn":".medon.com.cn",localhost:"localhost"},Z={set:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{expires:r=365,domain:a,...i}=n,o=a;if(!o){let e=Object.keys(R).find(e=>location.origin.includes(e));e&&(o=R[e])}let s={expires:r,...o?{domain:o}:{},...i};L.Z.set(e,t,s)},get:e=>{let t=L.Z.get(e);try{return t||null}catch{return t}}};var F=n(75727),z=n(40833);class D{async baseRequest(e,t){return await fetch(`${this.options.baseURL}${e}`,{...t,headers:{...t.headers,"Content-Type":"application/json",Accept:" application/json"}})}async jsonRequest(e,t){return(await this.baseRequest(e,{...t,headers:{...t.headers,"Content-Type":"application/json"}})).json()}async get(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t&&Object.keys(t).length>0?`?${new URLSearchParams(t).toString()}`:"";return await this.jsonRequest(`${e}${r}`,{method:"GET",headers:n})}async post(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"POST",body:JSON.stringify(t),headers:n})}async put(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"PUT",body:JSON.stringify(t),headers:n})}async delete(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.baseRequest(e,{method:"DELETE",body:JSON.stringify(t),headers:{...n,"Content-Type":"application/json"}})}constructor(e){(0,z._)(this,"options",void 0),this.options=e}}var B=n(94234),H=n(21256);let U=window.location.hostname,W=new D({baseURL:"ai.medsci.cn"===U?"https://ai.medsci.cn/dev-api/ai-base":"ai.medon.com.cn"===U?"https://ai.medon.com.cn/dev-api/ai-base":"/ai-base"}),q=()=>H.Z.get("ai_apps_lang")?H.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,G=e=>{if(401===e.code){H.Z.remove("userInfo",{domain:".medon.com.cn"}),H.Z.remove("userInfo",{domain:".medsci.cn"}),H.Z.remove("userInfo",{domain:"localhost"}),H.Z.remove("yudaoToken",{domain:"ai.medon.com.cn"}),H.Z.remove("yudaoToken",{domain:"ai.medsci.cn"}),H.Z.remove("yudaoToken",{domain:".medon.com.cn"}),H.Z.remove("yudaoToken",{domain:".medsci.cn"}),H.Z.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("conversation"),localStorage.removeItem("hasuraToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid");let i=q();if(i&&"zh-CN"!=i){var t,n,r,a;window.top.location.href=(null==(t=location)?void 0:t.origin.includes("medon.com.cn"))||(null==(n=location)?void 0:n.origin.includes("medsci.cn"))?`${null==(r=location)?void 0:r.origin}${i?"/"+i:""}/login`:`${null==(a=location)?void 0:a.origin}${i?"/"+i:""}/login`}else window.addLoginDom();return e}return 0!==e.code?(B.ZP.open({type:"error",content:e.msg}),e):0===e.code?e:void 0},K=class extends E{async createSubscription(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return G(await W.post("/appUser/createSubscription",e,t))}async getAiWriteToken(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return G(await W.post("/index/getAiWriteToken",e,t))}async getSubOrder(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return G(await W.get("/index/getSubOrder",e,t))}async createAliSub(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return G(await W.get("/index/createAliSub",e,t))}async freeLimit(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return G(await W.get("/index/free-limit",e,t))}async getPackageByKey(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return G(await W.get("/index/getPackageByKey",e,t))}async cancelSubscription(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return G(await W.post("/appUser/cancelSubscription?appUuid=",{},e))}async getAppList(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return G(await W.post("/index/getAppList",e,t))}async getAppByConfigKey(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return G(await W.post("/index/getAppByConfigKey",e,t))}async bindAppUser(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return G(await W.post(`/appUser/bindAppUser?appUuid=${e.appUuid}&appNameEn=${e.appNameEn}`,{},t))}async qaList(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return G(await W.post("/index/qa-list",e,t))}};var V=n(50742),X=n(91107),J=n(91904);n.p;let Q={".medsci.cn":".medsci.cn",".medon.com.cn":".medon.com.cn",localhost:"localhost"},Y={set:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{expires:r=365,domain:a,...i}=n,o=a;if(!o){let e=Object.keys(Q).find(e=>location.origin.includes(e));e&&(o=Q[e])}let s={expires:r,...o?{domain:o}:{},...i};H.Z.set(e,t,s)},get:e=>{let t=H.Z.get(e);try{return t||null}catch{return t}},remove:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{domain:n,...r}=t;H.Z.remove(e,{...r,domain:n||Q[window.location.host],path:"/"})},removeInit:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{domain:n,...r}=t;H.Z.remove(e,{domain:void 0})}},ee=()=>Y.get("ai_apps_lang")?Y.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,et=e=>{let{hideGithubIcon:t}=e,n=(0,F.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),r=n?n[1]:"",a=location.origin.includes("medsci.cn")?"https://ai.medsci.cn/"+ee():location.origin.includes("medon.com.cn")?"https://ai.medon.com.cn/"+ee():"http://localhost:3000/"+ee(),i=r.includes("novax")||r.includes("elavax")?r.includes("novax")?location.origin+"/novax":r.includes("elavax")?location.origin+"/elavax":"":a;return(0,y.jsxs)("div",{className:"flex h-16 items-center justify-start py-0 box-border",children:[(0,y.jsx)("div",{className:"h-full flex items-center flex-1 overflow-hidden",children:(0,y.jsx)("a",{href:i,target:"__top",children:(0,y.jsx)("img",{className:`h-6 inline-block hover:cursor-pointer ${r.includes("novax")||r.includes("elavax")?"!h-10":""}`,src:r.includes("novax")||r.includes("elavax")?r.includes("novax")?"https://img.medsci.cn/202506/a75d550504434d22aacaefcc951bc9ec-PBB8bYHyNwIQ.png":r.includes("elavax")?"https://img.medsci.cn/202506/06e46058edd34eea9ae96580a46bf327-FP4DdQMkcnma.png":"":"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",draggable:!1,alt:"logo"})})}),!t&&(0,y.jsx)(J.ZP,{type:"link",href:"https://github.com/lexmin0412/dify-chat",target:"_blank",className:"px-0",children:(0,y.jsx)(X.Z,{className:"text-lg cursor-pointer text-default"})})]})};var en=n(3692),er=n(86910),ea=n(48415),ei=n(96949),eo=n(16615),es=n(30967),el=n.t(es,2),ec={"../../node_modules/.pnpm/@ant-design+cssinjs@1.23.0_react-dom@18.3.1_react@18.3.1/node_modules/@ant-design/cssinjs/es/hooks/useHMR.js":function(e,t,n){n.d(t,{Z:()=>r}),e=n.hmd(e);let r=function(){return!1}},"../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/cjs/react-is.development.js":function(e,t){},"../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/cjs/react-is.production.min.js":function(e,t){var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),c=Symbol.for("react.server_context"),u=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),m=Symbol.for("react.memo"),f=Symbol.for("react.lazy");Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),t.ForwardRef=u,t.isMemo=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case a:case o:case i:case d:case p:return e;default:switch(e=e&&e.$$typeof){case c:case l:case u:case f:case m:case s:return e;default:return t}}case r:return t}}}(e)===m}},"../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/index.js":function(e,t,n){e.exports=n("../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/cjs/react-is.production.min.js")},"../../node_modules/.pnpm/classnames@2.5.1/node_modules/classnames/index.js":function(e){var t={}.hasOwnProperty;function n(){for(var e="",a=0;a<arguments.length;a++){var i=arguments[a];i&&(e=r(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return n.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var a="";for(var i in e)t.call(e,i)&&e[i]&&(a=r(a,i));return a}(i)))}return e}function r(e,t){return t?e?e+" "+t:e+t:e}e.exports?(n.default=n,e.exports=n):"function"==typeof define&&"object"==typeof define.amd&&define.amd?define("classnames",[],function(){return n}):window.classNames=n}},eu={};function ed(e){var t=eu[e];if(void 0!==t)return t.exports;var n=eu[e]={id:e,loaded:!1,exports:{}};return ec[e](n,n.exports,ed),n.loaded=!0,n.exports}function ep(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function em(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function ef(e,t){if(e){if("string"==typeof e)return ep(e,t);var n=({}).toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ep(e,t):void 0}}function eh(e){return function(e){if(Array.isArray(e))return ep(e)}(e)||em(e)||ef(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}ed.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return ed.d(t,{a:t}),t},ed.d=(e,t)=>{for(var n in t)ed.o(t,n)&&!ed.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},ed.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),ed.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);let eg=b.createContext({}),ev="anticon",ey=b.createContext({getPrefixCls:(e,t)=>t||(e?`ant-${e}`:"ant"),iconPrefixCls:ev}),{Consumer:ex}=ey;function eb(e){if(Array.isArray(e))return e}function ej(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ew(e,t){return eb(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i,o,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,a=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw a}}return s}}(e,t)||ef(e,t)||ej()}function eS(e){return(eS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eN(e){var t=function(e,t){if("object"!=eS(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=eS(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eS(t)?t:t+""}function ek(e,t,n){return(t=eN(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function eC(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function e_(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?eC(Object(n),!0).forEach(function(t){ek(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):eC(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}let eE=function(e){for(var t,n=0,r=0,a=e.length;a>=4;++r,a-=4)t=(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))*0x5bd1e995+((t>>>16)*59797<<16),t^=t>>>24,n=(65535&t)*0x5bd1e995+((t>>>16)*59797<<16)^(65535&n)*0x5bd1e995+((n>>>16)*59797<<16);switch(a){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n^=255&e.charCodeAt(r),n=(65535&n)*0x5bd1e995+((n>>>16)*59797<<16)}return n^=n>>>13,(((n=(65535&n)*0x5bd1e995+((n>>>16)*59797<<16))^n>>>15)>>>0).toString(36)};function eT(){return!!("undefined"!=typeof window&&window.document&&window.document.createElement)}var eO="data-rc-order",eA="data-rc-priority",eI=new Map;function eP(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.mark;return t?t.startsWith("data-")?t:"data-".concat(t):"rc-util-key"}function eM(e){return e.attachTo?e.attachTo:document.querySelector("head")||document.body}function e$(e){return Array.from((eI.get(e)||e).children).filter(function(e){return"STYLE"===e.tagName})}function eL(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!eT())return null;var n=t.csp,r=t.prepend,a=t.priority,i=void 0===a?0:a,o="queue"===r?"prependQueue":r?"prepend":"append",s="prependQueue"===o,l=document.createElement("style");l.setAttribute(eO,o),s&&i&&l.setAttribute(eA,"".concat(i)),null!=n&&n.nonce&&(l.nonce=null==n?void 0:n.nonce),l.innerHTML=e;var c=eM(t),u=c.firstChild;if(r){if(s){var d=(t.styles||e$(c)).filter(function(e){return!!["prepend","prependQueue"].includes(e.getAttribute(eO))&&i>=Number(e.getAttribute(eA)||0)});if(d.length)return c.insertBefore(l,d[d.length-1].nextSibling),l}c.insertBefore(l,u)}else c.appendChild(l);return l}function eR(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=eM(t);return(t.styles||e$(n)).find(function(n){return n.getAttribute(eP(t))===e})}function eZ(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=eR(e,t);n&&eM(t).removeChild(n)}function eF(e,t){var n,r,a,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=eM(i),s=e$(o),l=e_(e_({},i),{},{styles:s}),c=eI.get(o);if(!c||!function(e,t){if(!e)return!1;if(e.contains)return e.contains(t);for(var n=t;n;){if(n===e)return!0;n=n.parentNode}return!1}(document,c)){var u=eL("",l),d=u.parentNode;eI.set(o,d),o.removeChild(u)}var p=eR(t,l);if(p)return null!=(n=l.csp)&&n.nonce&&p.nonce!==(null==(r=l.csp)?void 0:r.nonce)&&(p.nonce=null==(a=l.csp)?void 0:a.nonce),p.innerHTML!==e&&(p.innerHTML=e),p;var m=eL(e,l);return m.setAttribute(eP(l),t),m}function ez(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],-1===t.indexOf(n)&&({}).propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function eD(e,t,n){var r=b.useRef({});return(!("value"in r.current)||n(r.current.condition,t))&&(r.current.value=e(),r.current.condition=t),r.current.value}var eB={},eH=[];function eU(e,t){}function eW(e,t){}function eq(e,t,n){t||eB[n]||(e(!1,n),eB[n]=!0)}function eG(e,t){eq(eU,e,t)}eG.preMessage=function(e){eH.push(e)},eG.resetWarned=function(){eB={}},eG.noteOnce=function(e,t){eq(eW,e,t)};let eK=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=new Set;return function e(t,a){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,o=r.has(t);if(eG(!o,"Warning: There may be circular references"),o)return!1;if(t===a)return!0;if(n&&i>1)return!1;r.add(t);var s=i+1;if(Array.isArray(t)){if(!Array.isArray(a)||t.length!==a.length)return!1;for(var l=0;l<t.length;l++)if(!e(t[l],a[l],s))return!1;return!0}if(t&&a&&"object"===eS(t)&&"object"===eS(a)){var c=Object.keys(t);return c.length===Object.keys(a).length&&c.every(function(n){return e(t[n],a[n],s)})}return!1}(e,t)};function eV(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function eX(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,eN(r.key),r)}}function eJ(e,t,n){return t&&eX(e.prototype,t),n&&eX(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function eQ(e){return e.join("%")}var eY=function(){function e(t){eV(this,e),ek(this,"instanceId",void 0),ek(this,"cache",new Map),this.instanceId=t}return eJ(e,[{key:"get",value:function(e){return this.opGet(eQ(e))}},{key:"opGet",value:function(e){return this.cache.get(e)||null}},{key:"update",value:function(e,t){return this.opUpdate(eQ(e),t)}},{key:"opUpdate",value:function(e,t){var n=t(this.cache.get(e));null===n?this.cache.delete(e):this.cache.set(e,n)}}]),e}(),e0="data-token-hash",e1="data-css-hash",e2="__cssinjs_instance__",e5=b.createContext({hashPriority:"low",cache:function(){var e=Math.random().toString(12).slice(2);if("undefined"!=typeof document&&document.head&&document.body){var t=document.body.querySelectorAll("style[".concat(e1,"]"))||[],n=document.head.firstChild;Array.from(t).forEach(function(t){t[e2]=t[e2]||e,t[e2]===e&&document.head.insertBefore(t,n)});var r={};Array.from(document.querySelectorAll("style[".concat(e1,"]"))).forEach(function(t){var n,a=t.getAttribute(e1);r[a]?t[e2]===e&&(null==(n=t.parentNode)||n.removeChild(t)):r[a]=!0})}return new eY(e)}(),defaultCache:!0});function e3(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function e4(e,t){return(e4=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function e6(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&e4(e,t)}function e8(e){return(e8=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function e9(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(e9=function(){return!!e})()}function e7(e){var t=e9();return function(){var n,r=e8(e);n=t?Reflect.construct(r,arguments,e8(this).constructor):r.apply(this,arguments);if(n&&("object"==eS(n)||"function"==typeof n))return n;if(void 0!==n)throw TypeError("Derived constructors may only return object or undefined");return e3(this)}}var te=function(){function e(){eV(this,e),ek(this,"cache",void 0),ek(this,"keys",void 0),ek(this,"cacheCallTimes",void 0),this.cache=new Map,this.keys=[],this.cacheCallTimes=0}return eJ(e,[{key:"size",value:function(){return this.keys.length}},{key:"internalGet",value:function(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a={map:this.cache};return e.forEach(function(e){if(a){var t;a=null==(t=a)||null==(t=t.map)?void 0:t.get(e)}else a=void 0}),null!=(t=a)&&t.value&&r&&(a.value[1]=this.cacheCallTimes++),null==(n=a)?void 0:n.value}},{key:"get",value:function(e){var t;return null==(t=this.internalGet(e,!0))?void 0:t[0]}},{key:"has",value:function(e){return!!this.internalGet(e)}},{key:"set",value:function(t,n){var r=this;if(!this.has(t)){if(this.size()+1>e.MAX_CACHE_SIZE+e.MAX_CACHE_OFFSET){var a=this.keys.reduce(function(e,t){var n=ew(e,2)[1];return r.internalGet(t)[1]<n?[t,r.internalGet(t)[1]]:e},[this.keys[0],this.cacheCallTimes]),i=ew(a,1)[0];this.delete(i)}this.keys.push(t)}var o=this.cache;t.forEach(function(e,a){if(a===t.length-1)o.set(e,{value:[n,r.cacheCallTimes++]});else{var i=o.get(e);i?i.map||(i.map=new Map):o.set(e,{map:new Map}),o=o.get(e).map}})}},{key:"deleteByPath",value:function(e,t){var n,r=e.get(t[0]);if(1===t.length)return r.map?e.set(t[0],{map:r.map}):e.delete(t[0]),null==(n=r.value)?void 0:n[0];var a=this.deleteByPath(r.map,t.slice(1));return r.map&&0!==r.map.size||r.value||e.delete(t[0]),a}},{key:"delete",value:function(e){if(this.has(e))return this.keys=this.keys.filter(function(t){return!function(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(t,e)}),this.deleteByPath(this.cache,e)}}]),e}();ek(te,"MAX_CACHE_SIZE",20),ek(te,"MAX_CACHE_OFFSET",5);var tt=0,tn=function(){function e(t){eV(this,e),ek(this,"derivatives",void 0),ek(this,"id",void 0),this.derivatives=Array.isArray(t)?t:[t],this.id=tt,0===t.length&&t.length,tt+=1}return eJ(e,[{key:"getDerivativeToken",value:function(e){return this.derivatives.reduce(function(t,n){return n(e,t)},void 0)}}]),e}(),tr=new te;function ta(e){var t=Array.isArray(e)?e:[e];return tr.has(t)||tr.set(t,new tn(t)),tr.get(t)}var ti=new WeakMap,to={},ts=new WeakMap;function tl(e){var t=ts.get(e)||"";return t||(Object.keys(e).forEach(function(n){var r=e[n];t+=n,r instanceof tn?t+=r.id:r&&"object"===eS(r)?t+=tl(r):t+=r}),t=eE(t),ts.set(e,t)),t}function tc(e,t){return eE("".concat(t,"_").concat(tl(e)))}"random-".concat(Date.now(),"-").concat(Math.random()).replace(/\./g,"");var tu=eT();function td(e){return"number"==typeof e?"".concat(e,"px"):e}function tp(e,t,n){var r,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(i)return e;var o=e_(e_({},a),{},(ek(r={},e0,t),ek(r,e1,n),r)),s=Object.keys(o).map(function(e){var t=o[e];return t?"".concat(e,'="').concat(t,'"'):null}).filter(function(e){return e}).join(" ");return"<style ".concat(s,">").concat(e,"</style>")}var tm=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"--".concat(t?"".concat(t,"-"):"").concat(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").replace(/([A-Z]+)([A-Z][a-z0-9]+)/g,"$1-$2").replace(/([a-z])([A-Z0-9])/g,"$1-$2").toLowerCase()},tf=function(e,t,n){var r,a={},i={};return Object.entries(e).forEach(function(e){var t=ew(e,2),r=t[0],o=t[1];if(null!=n&&null!=(s=n.preserve)&&s[r])i[r]=o;else if(("string"==typeof o||"number"==typeof o)&&!(null!=n&&null!=(l=n.ignore)&&l[r])){var s,l,c,u=tm(r,null==n?void 0:n.prefix);a[u]="number"!=typeof o||null!=n&&null!=(c=n.unitless)&&c[r]?String(o):"".concat(o,"px"),i[r]="var(".concat(u,")")}}),[i,(r={scope:null==n?void 0:n.scope},Object.keys(a).length?".".concat(t).concat(null!=r&&r.scope?".".concat(r.scope):"","{").concat(Object.entries(a).map(function(e){var t=ew(e,2),n=t[0],r=t[1];return"".concat(n,":").concat(r,";")}).join(""),"}"):"")]},th=eT()?b.useLayoutEffect:b.useEffect;let tg=function(e,t){var n=b.useRef(!0);th(function(){return e(n.current)},t),th(function(){return n.current=!1,function(){n.current=!0}},[])};var tv=e_({},j).useInsertionEffect,ty=tv?function(e,t,n){return tv(function(){return e(),t()},n)}:function(e,t,n){b.useMemo(e,n),tg(function(){return t(!0)},n)},tx=void 0!==e_({},j).useInsertionEffect?function(e){var t=[],n=!1;return b.useEffect(function(){return n=!1,function(){n=!0,t.length&&t.forEach(function(e){return e()})}},e),function(e){n||t.push(e)}}:function(){return function(e){e()}},tb=ed("../../node_modules/.pnpm/@ant-design+cssinjs@1.23.0_react-dom@18.3.1_react@18.3.1/node_modules/@ant-design/cssinjs/es/hooks/useHMR.js");function tj(e,t,n,r,a){var i=b.useContext(e5).cache,o=eQ([e].concat(eh(t))),s=tx([o]);(0,tb.Z)();var l=function(e){i.opUpdate(o,function(t){var r=ew(t||[void 0,void 0],2),a=r[0],i=[void 0===a?0:a,r[1]||n()];return e?e(i):i})};b.useMemo(function(){l()},[o]);var c=i.opGet(o)[1];return ty(function(){null==a||a(c)},function(e){return l(function(t){var n=ew(t,2),r=n[0],i=n[1];return e&&0===r&&(null==a||a(c)),[r+1,i]}),function(){i.opUpdate(o,function(t){var n=ew(t||[],2),a=n[0],l=void 0===a?0:a,c=n[1];return 0==l-1?(s(function(){(e||!i.opGet(o))&&(null==r||r(c,!1))}),null):[l-1,c]})}},[o]),c}var tw={},tS=new Map,tN=function(e,t,n,r){var a=e_(e_({},n.getDerivativeToken(e)),t);return r&&(a=r(a)),a},tk="token";function tC(){return(tC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}let t_={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};var tE="comm",tT="rule",tO="decl",tA=Math.abs,tI=String.fromCharCode;function tP(e,t,n){return e.replace(t,n)}function tM(e,t){return 0|e.charCodeAt(t)}function t$(e,t,n){return e.slice(t,n)}function tL(e){return e.length}function tR(e,t){return t.push(e),e}function tZ(e,t){for(var n="",r=0;r<e.length;r++)n+=t(e[r],r,e,t)||"";return n}function tF(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case"@namespace":case tO:return e.return=e.return||e.value;case tE:return"";case"@keyframes":return e.return=e.value+"{"+tZ(e.children,r)+"}";case tT:if(!tL(e.value=e.props.join(",")))return""}return tL(n=tZ(e.children,r))?e.return=e.value+"{"+n+"}":""}var tz=1,tD=1,tB=0,tH=0,tU=0,tW="";function tq(e,t,n,r,a,i,o,s){return{value:e,root:t,parent:n,type:r,props:a,children:i,line:tz,column:tD,length:o,return:"",siblings:s}}function tG(){return tU=tH<tB?tM(tW,tH++):0,tD++,10===tU&&(tD=1,tz++),tU}function tK(){return tM(tW,tH)}function tV(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function tX(e){var t,n;return(t=tH-1,n=function e(t){for(;tG();)switch(tU){case t:return tH;case 34:case 39:34!==t&&39!==t&&e(tU);break;case 40:41===t&&e(t);break;case 92:tG()}return tH}(91===e?e+2:40===e?e+1:e),t$(tW,t,n)).trim()}function tJ(e,t,n,r,a,i,o,s,l,c,u,d){for(var p=a-1,m=0===a?i:[""],f=m.length,h=0,g=0,v=0;h<r;++h)for(var y=0,x=t$(e,p+1,p=tA(g=o[h])),b=e;y<f;++y)(b=(g>0?m[y]+" "+x:tP(x,/&\f/g,m[y])).trim())&&(l[v++]=b);return tq(e,t,n,0===a?tT:s,l,c,u,d)}function tQ(e,t,n,r,a){return tq(e,t,n,tO,t$(e,0,r),t$(e,r+1,-1),r,a)}var tY="data-ant-cssinjs-cache-path",t0="_FILE_STYLE__",t1=!0,t2="_multi_value_";function t5(e){var t,n,r;return tZ((r=function e(t,n,r,a,i,o,s,l,c){for(var u,d,p,m,f,h,g=0,v=0,y=s,x=0,b=0,j=0,w=1,S=1,N=1,k=0,C="",_=i,E=o,T=a,O=C;S;)switch(j=k,k=tG()){case 40:if(108!=j&&58==tM(O,y-1)){-1!=(f=O+=tP(tX(k),"&","&\f"),h=tA(g?l[g-1]:0),f.indexOf("&\f",h))&&(N=-1);break}case 34:case 39:case 91:O+=tX(k);break;case 9:case 10:case 13:case 32:O+=function(e){for(;tU=tK();)if(tU<33)tG();else break;return tV(e)>2||tV(tU)>3?"":" "}(j);break;case 92:O+=function(e,t){for(var n;--t&&tG()&&!(tU<48)&&!(tU>102)&&(!(tU>57)||!(tU<65))&&(!(tU>70)||!(tU<97)););return n=tH+(t<6&&32==tK()&&32==tG()),t$(tW,e,n)}(tH-1,7);continue;case 47:switch(tK()){case 42:case 47:tR((u=function(e,t){for(;tG();)if(e+tU===57)break;else if(e+tU===84&&47===tK())break;return"/*"+t$(tW,t,tH-1)+"*"+tI(47===e?e:tG())}(tG(),tH),d=n,p=r,m=c,tq(u,d,p,tE,tI(tU),t$(u,2,-2),0,m)),c),(5==tV(j||1)||5==tV(tK()||1))&&tL(O)&&" "!==t$(O,-1,void 0)&&(O+=" ");break;default:O+="/"}break;case 123*w:l[g++]=tL(O)*N;case 125*w:case 59:case 0:switch(k){case 0:case 125:S=0;case 59+v:-1==N&&(O=tP(O,/\f/g,"")),b>0&&(tL(O)-y||0===w&&47===j)&&tR(b>32?tQ(O+";",a,r,y-1,c):tQ(tP(O," ","")+";",a,r,y-2,c),c);break;case 59:O+=";";default:if(tR(T=tJ(O,n,r,g,v,i,l,C,_=[],E=[],y,o),o),123===k)if(0===v)e(O,n,T,T,_,o,y,l,E);else{switch(x){case 99:if(110===tM(O,3))break;case 108:if(97===tM(O,2))break;default:v=0;case 100:case 109:case 115:}v?e(t,T,T,a&&tR(tJ(t,T,T,0,0,i,l,C,i,_=[],y,E),E),i,E,y,l,a?_:E):e(O,T,T,T,[""],E,0,l,E)}}g=v=b=0,w=N=1,C=O="",y=s;break;case 58:y=1+tL(O),b=j;default:if(w<1){if(123==k)--w;else if(125==k&&0==w++&&125==(tU=tH>0?tM(tW,--tH):0,tD--,10===tU&&(tD=1,tz--),tU))continue}switch(O+=tI(k),k*w){case 38:N=v>0?1:(O+="\f",-1);break;case 44:l[g++]=(tL(O)-1)*N,N=1;break;case 64:45===tK()&&(O+=tX(tG())),x=tK(),v=y=tL(C=O+=function(e){for(;!tV(tK());)tG();return t$(tW,e,tH)}(tH)),k++;break;case 45:45===j&&2==tL(O)&&(w=0)}}return o}("",null,null,null,[""],(n=t=e,tz=tD=1,tB=tL(tW=n),tH=0,t=[]),0,[0],t),tW="",r),tF).replace(/\{%%%\:[^;];}/g,";")}function t3(e,t,n){if(!t)return e;var r=".".concat(t),a="low"===n?":where(".concat(r,")"):r;return e.split(",").map(function(e){var t,n=e.trim().split(/\s+/),r=n[0]||"",i=(null==(t=r.match(/^\w+/))?void 0:t[0])||"";return[r="".concat(i).concat(a).concat(r.slice(i.length))].concat(eh(n.slice(1))).join(" ")}).join(",")}var t4=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{root:!0,parentSelectors:[]},a=r.root,i=r.injectHash,o=r.parentSelectors,s=n.hashId,l=n.layer,c=(n.path,n.hashPriority),u=n.transformers,d=void 0===u?[]:u,p=(n.linters,""),m={};function f(t){var r=t.getName(s);if(!m[r]){var a=ew(e(t.style,n,{root:!1,parentSelectors:o}),1)[0];m[r]="@keyframes ".concat(t.getName(s)).concat(a)}}return(function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return t.forEach(function(t){Array.isArray(t)?e(t,n):t&&n.push(t)}),n})(Array.isArray(t)?t:[t]).forEach(function(t){var r="string"!=typeof t||a?t:{};if("string"==typeof r)p+="".concat(r,"\n");else if(r._keyframe)f(r);else{var l=d.reduce(function(e,t){var n;return(null==t||null==(n=t.visit)?void 0:n.call(t,e))||e},r);Object.keys(l).forEach(function(t){var r=l[t];if("object"!==eS(r)||!r||"animationName"===t&&r._keyframe||"object"===eS(r)&&r&&("_skip_check_"in r||t2 in r)){function u(e,t){var n=e.replace(/[A-Z]/g,function(e){return"-".concat(e.toLowerCase())}),r=t;t_[e]||"number"!=typeof r||0===r||(r="".concat(r,"px")),"animationName"===e&&null!=t&&t._keyframe&&(f(t),r=t.getName(s)),p+="".concat(n,":").concat(r,";")}var d,h=null!=(d=null==r?void 0:r.value)?d:r;"object"===eS(r)&&null!=r&&r[t2]&&Array.isArray(h)?h.forEach(function(e){u(t,e)}):u(t,h)}else{var g=!1,v=t.trim(),y=!1;(a||i)&&s?v.startsWith("@")?g=!0:v="&"===v?t3("",s,c):t3(t,s,c):a&&!s&&("&"===v||""===v)&&(v="",y=!0);var x=ew(e(r,n,{root:y,injectHash:g,parentSelectors:[].concat(eh(o),[v])}),2),b=x[0],j=x[1];m=e_(e_({},m),j),p+="".concat(v).concat(b)}})}}),a?l&&(p&&(p="@layer ".concat(l.name," {").concat(p,"}")),l.dependencies&&(m["@layer ".concat(l.name)]=l.dependencies.map(function(e){return"@layer ".concat(e,", ").concat(l.name,";")}).join("\n"))):p="{".concat(p,"}"),[p,m]};function t6(e,t){return eE("".concat(e.join("%")).concat(t))}function t8(){return null}var t9="style";function t7(e,t){var n=e.token,r=e.path,a=e.hashId,i=e.layer,o=e.nonce,s=e.clientOnly,l=e.order,c=void 0===l?0:l,u=b.useContext(e5),d=u.autoClear,p=(u.mock,u.defaultCache),m=u.hashPriority,f=u.container,g=u.ssrInline,v=u.transformers,y=u.linters,x=u.cache,j=u.layer,w=n._tokenKey,S=[w];j&&S.push("layer"),S.push.apply(S,eh(r));var N=tj(t9,S,function(){var e=S.join("|");if(function(e){if(!h&&(h={},eT())){var t,n=document.createElement("div");n.className=tY,n.style.position="fixed",n.style.visibility="hidden",n.style.top="-9999px",document.body.appendChild(n);var r=getComputedStyle(n).content||"";(r=r.replace(/^"/,"").replace(/"$/,"")).split(";").forEach(function(e){var t=ew(e.split(":"),2),n=t[0],r=t[1];h[n]=r});var a=document.querySelector("style[".concat(tY,"]"));a&&(t1=!1,null==(t=a.parentNode)||t.removeChild(a)),document.body.removeChild(n)}return!!h[e]}(e)){var n=ew(function(e){var t=h[e],n=null;if(t&&eT())if(t1)n=t0;else{var r=document.querySelector("style[".concat(e1,'="').concat(h[e],'"]'));r?n=r.innerHTML:delete h[e]}return[n,t]}(e),2),o=n[0],l=n[1];if(o)return[o,w,l,{},s,c]}var u=ew(t4(t(),{hashId:a,hashPriority:m,layer:j?i:void 0,path:r.join("-"),transformers:v,linters:y}),2),d=u[0],p=u[1],f=t5(d),g=t6(S,f);return[f,w,g,p,s,c]},function(e,t){var n=ew(e,3)[2];(t||d)&&tu&&eZ(n,{mark:e1})},function(e){var t=ew(e,4),n=t[0],r=(t[1],t[2]),a=t[3];if(tu&&n!==t0){var i={mark:e1,prepend:!j&&"queue",attachTo:f,priority:c},s="function"==typeof o?o():o;s&&(i.csp={nonce:s});var l=[],u=[];Object.keys(a).forEach(function(e){e.startsWith("@layer")?l.push(e):u.push(e)}),l.forEach(function(e){eF(t5(a[e]),"_layer-".concat(e),e_(e_({},i),{},{prepend:!0}))});var d=eF(n,r,i);d[e2]=x.instanceId,d.setAttribute(e0,w),u.forEach(function(e){eF(t5(a[e]),"_effect-".concat(e),i)})}}),k=ew(N,3),C=k[0],_=k[1],E=k[2];return function(e){var t,n;return t=g&&!tu&&p?b.createElement("style",tC({},(ek(n={},e0,_),ek(n,e1,E),n),{dangerouslySetInnerHTML:{__html:C}})):b.createElement(t8,null),b.createElement(b.Fragment,null,t,e)}}var ne="cssVar";let nt=function(e,t){var n=e.key,r=e.prefix,a=e.unitless,i=e.ignore,o=e.token,s=e.scope,l=void 0===s?"":s,c=(0,b.useContext)(e5),u=c.cache.instanceId,d=c.container,p=o._tokenKey,m=[].concat(eh(e.path),[n,l,p]);return tj(ne,m,function(){var e=ew(tf(t(),n,{prefix:r,unitless:a,ignore:i,scope:l}),2),o=e[0],s=e[1],c=t6(m,s);return[o,s,c,n]},function(e){var t=ew(e,3)[2];tu&&eZ(t,{mark:e1})},function(e){var t=ew(e,3),r=t[1],a=t[2];if(r){var i=eF(r,a,{mark:e1,prepend:"queue",attachTo:d,priority:-999});i[e2]=u,i.setAttribute(e0,n)}})};ek(g={},t9,function(e,t,n){var r=ew(e,6),a=r[0],i=r[1],o=r[2],s=r[3],l=r[4],c=r[5],u=(n||{}).plain;if(l)return null;var d=a,p={"data-rc-order":"prependQueue","data-rc-priority":"".concat(c)};return d=tp(a,i,o,p,u),s&&Object.keys(s).forEach(function(e){if(!t[e]){t[e]=!0;var n=tp(t5(s[e]),i,"_effect-".concat(e),p,u);e.startsWith("@layer")?d=n+d:d+=n}}),[c,o,d]}),ek(g,tk,function(e,t,n){var r=ew(e,5),a=r[2],i=r[3],o=r[4],s=(n||{}).plain;if(!i)return null;var l=a._tokenKey,c=tp(i,o,l,{"data-rc-order":"prependQueue","data-rc-priority":"".concat(-999)},s);return[-999,l,c]}),ek(g,ne,function(e,t,n){var r=ew(e,4),a=r[1],i=r[2],o=r[3],s=(n||{}).plain;if(!a)return null;var l=tp(a,o,i,{"data-rc-order":"prependQueue","data-rc-priority":"".concat(-999)},s);return[-999,i,l]});var nn=function(){function e(t,n){eV(this,e),ek(this,"name",void 0),ek(this,"style",void 0),ek(this,"_keyframe",!0),this.name=t,this.style=n}return eJ(e,[{key:"getName",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e?"".concat(e,"-").concat(this.name):this.name}}]),e}();function nr(e){return e.notSplit=!0,e}nr(["borderTop","borderBottom"]),nr(["borderTop"]),nr(["borderBottom"]),nr(["borderLeft","borderRight"]),nr(["borderLeft"]),nr(["borderRight"]);var na=(0,b.createContext)({});function ni(e,t){for(var n=e,r=0;r<t.length;r+=1){if(null==n)return;n=n[t[r]]}return n}function no(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return t.length&&r&&void 0===n&&!ni(e,t.slice(0,-1))?e:function e(t,n,r,a){if(!n.length)return r;var i,o=eb(n)||em(n)||ef(n)||ej(),s=o[0],l=o.slice(1);return i=t||"number"!=typeof s?Array.isArray(t)?eh(t):e_({},t):[],a&&void 0===r&&1===l.length?delete i[s][l[0]]:i[s]=e(i[s],l,r,a),i}(e,t,n,r)}function ns(e){return Array.isArray(e)?[]:{}}var nl="undefined"==typeof Reflect?Object.keys:Reflect.ownKeys;function nc(){}let nu=b.createContext({}),nd=()=>{let e=()=>{};return e.deprecated=nc,e},np=(0,b.createContext)(void 0);Object.assign({placeholder:"Select date",yearPlaceholder:"Select year",quarterPlaceholder:"Select quarter",monthPlaceholder:"Select month",weekPlaceholder:"Select week",rangePlaceholder:["Start date","End date"],rangeYearPlaceholder:["Start year","End year"],rangeQuarterPlaceholder:["Start quarter","End quarter"],rangeMonthPlaceholder:["Start month","End month"],rangeWeekPlaceholder:["Start week","End week"]},e_(e_({},{yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0}),{},{locale:"en_US",today:"Today",now:"Now",backToToday:"Back to today",ok:"OK",clear:"Clear",week:"Week",month:"Month",year:"Year",timeSelect:"select time",dateSelect:"select date",weekSelect:"Choose a week",monthSelect:"Choose a month",yearSelect:"Choose a year",decadeSelect:"Choose a decade",dateFormat:"M/D/YYYY",dateTimeFormat:"M/D/YYYY HH:mm:ss",previousMonth:"Previous month (PageUp)",nextMonth:"Next month (PageDown)",previousYear:"Last year (Control + left)",nextYear:"Next year (Control + right)",previousDecade:"Last decade",nextDecade:"Next decade",previousCentury:"Last century",nextCentury:"Next century"})),Object.assign({},{placeholder:"Select time",rangePlaceholder:["Start time","End time"]});let nm="${label} is not a valid ${type}",nf={Modal:{okText:"OK",cancelText:"Cancel",justOkText:"OK"},Form:{optional:"(optional)",defaultValidateMessages:{default:"Field validation error for ${label}",required:"Please enter ${label}",enum:"${label} must be one of [${enum}]",whitespace:"${label} cannot be a blank character",date:{format:"${label} date format is invalid",parse:"${label} cannot be converted to a date",invalid:"${label} is an invalid date"},types:{string:nm,method:nm,array:nm,object:nm,number:nm,date:nm,boolean:nm,integer:nm,float:nm,regexp:nm,email:nm,url:nm,hex:nm},string:{len:"${label} must be ${len} characters",min:"${label} must be at least ${min} characters",max:"${label} must be up to ${max} characters",range:"${label} must be between ${min}-${max} characters"},number:{len:"${label} must be equal to ${len}",min:"${label} must be minimum ${min}",max:"${label} must be maximum ${max}",range:"${label} must be between ${min}-${max}"},array:{len:"Must be ${len} ${label}",min:"At least ${min} ${label}",max:"At most ${max} ${label}",range:"The amount of ${label} must be between ${min}-${max}"},pattern:{mismatch:"${label} does not match the pattern ${pattern}"}}}};Object.assign({},nf.Modal);let nh=[],ng=()=>nh.reduce((e,t)=>Object.assign(Object.assign({},e),t),nf.Modal),nv=(0,b.createContext)(void 0),ny=e=>{let{locale:t={},children:n,_ANT_MARK__:r}=e;b.useEffect(()=>(function(e){if(e){let t=Object.assign({},e);return nh.push(t),ng(),()=>{nh=nh.filter(e=>e!==t),ng()}}Object.assign({},nf.Modal)})(null==t?void 0:t.Modal),[t]);let a=b.useMemo(()=>Object.assign(Object.assign({},t),{exist:!0}),[t]);return b.createElement(nv.Provider,{value:a},n)},nx=Math.round;function nb(e,t){let n=e.replace(/^[^(]*\((.*)/,"$1").replace(/\).*/,"").match(/\d*\.?\d+%?/g)||[],r=n.map(e=>parseFloat(e));for(let e=0;e<3;e+=1)r[e]=t(r[e]||0,n[e]||"",e);return n[3]?r[3]=n[3].includes("%")?r[3]/100:r[3]:r[3]=1,r}let nj=(e,t,n)=>0===n?e:e/100;function nw(e,t){let n=t||255;return e>n?n:e<0?0:e}class nS{setR(e){return this._sc("r",e)}setG(e){return this._sc("g",e)}setB(e){return this._sc("b",e)}setA(e){return this._sc("a",e,1)}setHue(e){let t=this.toHsv();return t.h=e,this._c(t)}getLuminance(){function e(e){let t=e/255;return t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}let t=e(this.r);return .2126*t+.7152*e(this.g)+.0722*e(this.b)}getHue(){if(void 0===this._h){let e=this.getMax()-this.getMin();0===e?this._h=0:this._h=nx(60*(this.r===this.getMax()?(this.g-this.b)/e+6*(this.g<this.b):this.g===this.getMax()?(this.b-this.r)/e+2:(this.r-this.g)/e+4))}return this._h}getSaturation(){if(void 0===this._s){let e=this.getMax()-this.getMin();0===e?this._s=0:this._s=e/this.getMax()}return this._s}getLightness(){return void 0===this._l&&(this._l=(this.getMax()+this.getMin())/510),this._l}getValue(){return void 0===this._v&&(this._v=this.getMax()/255),this._v}getBrightness(){return void 0===this._brightness&&(this._brightness=(299*this.r+587*this.g+114*this.b)/1e3),this._brightness}darken(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=this.getHue(),n=this.getSaturation(),r=this.getLightness()-e/100;return r<0&&(r=0),this._c({h:t,s:n,l:r,a:this.a})}lighten(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=this.getHue(),n=this.getSaturation(),r=this.getLightness()+e/100;return r>1&&(r=1),this._c({h:t,s:n,l:r,a:this.a})}mix(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,n=this._c(e),r=t/100,a=e=>(n[e]-this[e])*r+this[e],i={r:nx(a("r")),g:nx(a("g")),b:nx(a("b")),a:nx(100*a("a"))/100};return this._c(i)}tint(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return this.mix({r:255,g:255,b:255,a:1},e)}shade(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return this.mix({r:0,g:0,b:0,a:1},e)}onBackground(e){let t=this._c(e),n=this.a+t.a*(1-this.a),r=e=>nx((this[e]*this.a+t[e]*t.a*(1-this.a))/n);return this._c({r:r("r"),g:r("g"),b:r("b"),a:n})}isDark(){return 128>this.getBrightness()}isLight(){return this.getBrightness()>=128}equals(e){return this.r===e.r&&this.g===e.g&&this.b===e.b&&this.a===e.a}clone(){return this._c(this)}toHexString(){let e="#",t=(this.r||0).toString(16);e+=2===t.length?t:"0"+t;let n=(this.g||0).toString(16);e+=2===n.length?n:"0"+n;let r=(this.b||0).toString(16);if(e+=2===r.length?r:"0"+r,"number"==typeof this.a&&this.a>=0&&this.a<1){let t=nx(255*this.a).toString(16);e+=2===t.length?t:"0"+t}return e}toHsl(){return{h:this.getHue(),s:this.getSaturation(),l:this.getLightness(),a:this.a}}toHslString(){let e=this.getHue(),t=nx(100*this.getSaturation()),n=nx(100*this.getLightness());return 1!==this.a?`hsla(${e},${t}%,${n}%,${this.a})`:`hsl(${e},${t}%,${n}%)`}toHsv(){return{h:this.getHue(),s:this.getSaturation(),v:this.getValue(),a:this.a}}toRgb(){return{r:this.r,g:this.g,b:this.b,a:this.a}}toRgbString(){return 1!==this.a?`rgba(${this.r},${this.g},${this.b},${this.a})`:`rgb(${this.r},${this.g},${this.b})`}toString(){return this.toRgbString()}_sc(e,t,n){let r=this.clone();return r[e]=nw(t,n),r}_c(e){return new this.constructor(e)}getMax(){return void 0===this._max&&(this._max=Math.max(this.r,this.g,this.b)),this._max}getMin(){return void 0===this._min&&(this._min=Math.min(this.r,this.g,this.b)),this._min}fromHexString(e){let t=e.replace("#","");function n(e,n){return parseInt(t[e]+t[n||e],16)}t.length<6?(this.r=n(0),this.g=n(1),this.b=n(2),this.a=t[3]?n(3)/255:1):(this.r=n(0,1),this.g=n(2,3),this.b=n(4,5),this.a=t[6]?n(6,7)/255:1)}fromHsl(e){let{h:t,s:n,l:r,a}=e;if(this._h=t%360,this._s=n,this._l=r,this.a="number"==typeof a?a:1,n<=0){let e=nx(255*r);this.r=e,this.g=e,this.b=e}let i=0,o=0,s=0,l=t/60,c=(1-Math.abs(2*r-1))*n,u=c*(1-Math.abs(l%2-1));l>=0&&l<1?(i=c,o=u):l>=1&&l<2?(i=u,o=c):l>=2&&l<3?(o=c,s=u):l>=3&&l<4?(o=u,s=c):l>=4&&l<5?(i=u,s=c):l>=5&&l<6&&(i=c,s=u);let d=r-c/2;this.r=nx((i+d)*255),this.g=nx((o+d)*255),this.b=nx((s+d)*255)}fromHsv(e){let{h:t,s:n,v:r,a}=e;this._h=t%360,this._s=n,this._v=r,this.a="number"==typeof a?a:1;let i=nx(255*r);if(this.r=i,this.g=i,this.b=i,n<=0)return;let o=t/60,s=Math.floor(o),l=o-s,c=nx(r*(1-n)*255),u=nx(r*(1-n*l)*255),d=nx(r*(1-n*(1-l))*255);switch(s){case 0:this.g=d,this.b=c;break;case 1:this.r=u,this.b=c;break;case 2:this.r=c,this.b=d;break;case 3:this.r=c,this.g=u;break;case 4:this.r=d,this.g=c;break;default:this.g=c,this.b=u}}fromHsvString(e){let t=nb(e,nj);this.fromHsv({h:t[0],s:t[1],v:t[2],a:t[3]})}fromHslString(e){let t=nb(e,nj);this.fromHsl({h:t[0],s:t[1],l:t[2],a:t[3]})}fromRgbString(e){let t=nb(e,(e,t)=>t.includes("%")?nx(e/100*255):e);this.r=t[0],this.g=t[1],this.b=t[2],this.a=t[3]}constructor(e){function t(t){return t[0]in e&&t[1]in e&&t[2]in e}if(ek(this,"isValid",!0),ek(this,"r",0),ek(this,"g",0),ek(this,"b",0),ek(this,"a",1),ek(this,"_h",void 0),ek(this,"_s",void 0),ek(this,"_l",void 0),ek(this,"_v",void 0),ek(this,"_max",void 0),ek(this,"_min",void 0),ek(this,"_brightness",void 0),e)if("string"==typeof e){let t=e.trim();function n(e){return t.startsWith(e)}/^#?[A-F\d]{3,8}$/i.test(t)?this.fromHexString(t):n("rgb")?this.fromRgbString(t):n("hsl")?this.fromHslString(t):(n("hsv")||n("hsb"))&&this.fromHsvString(t)}else if(e instanceof nS)this.r=e.r,this.g=e.g,this.b=e.b,this.a=e.a,this._h=e._h,this._s=e._s,this._l=e._l,this._v=e._v;else if(t("rgb"))this.r=nw(e.r),this.g=nw(e.g),this.b=nw(e.b),this.a="number"==typeof e.a?nw(e.a,1):1;else if(t("hsl"))this.fromHsl(e);else if(t("hsv"))this.fromHsv(e);else throw Error("@ant-design/fast-color: unsupported input "+JSON.stringify(e))}}var nN=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function nk(e,t,n){var r;return(r=Math.round(e.h)>=60&&240>=Math.round(e.h)?n?Math.round(e.h)-2*t:Math.round(e.h)+2*t:n?Math.round(e.h)+2*t:Math.round(e.h)-2*t)<0?r+=360:r>=360&&(r-=360),r}function nC(e,t,n){var r;return 0===e.h&&0===e.s?e.s:((r=n?e.s-.16*t:4===t?e.s+.16:e.s+.05*t)>1&&(r=1),n&&5===t&&r>.1&&(r=.1),r<.06&&(r=.06),Math.round(100*r)/100)}function n_(e,t,n){var r;return Math.round(100*Math.max(0,Math.min(1,n?e.v+.05*t:e.v-.15*t)))/100}function nE(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[],r=new nS(e),a=r.toHsv(),i=5;i>0;i-=1){var o=new nS({h:nk(a,i,!0),s:nC(a,i,!0),v:n_(a,i,!0)});n.push(o)}n.push(r);for(var s=1;s<=4;s+=1){var l=new nS({h:nk(a,s),s:nC(a,s),v:n_(a,s)});n.push(l)}return"dark"===t.theme?nN.map(function(e){var r=e.index,a=e.amount;return new nS(t.backgroundColor||"#141414").mix(n[r],a).toHexString()}):n.map(function(e){return e.toHexString()})}var nT={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},nO=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];nO.primary=nO[5];var nA=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];nA.primary=nA[5];var nI=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];nI.primary=nI[5];var nP=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];nP.primary=nP[5];var nM=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];nM.primary=nM[5];var n$=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];n$.primary=n$[5];var nL=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];nL.primary=nL[5];var nR=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];nR.primary=nR[5];var nZ=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];nZ.primary=nZ[5];var nF=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];nF.primary=nF[5];var nz=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];nz.primary=nz[5];var nD=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];nD.primary=nD[5];var nB=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];nB.primary=nB[5];var nH={red:nO,volcano:nA,orange:nI,gold:nP,yellow:nM,lime:n$,green:nL,cyan:nR,blue:nZ,geekblue:nF,purple:nz,magenta:nD,grey:nB},nU=["#2a1215","#431418","#58181c","#791a1f","#a61d24","#d32029","#e84749","#f37370","#f89f9a","#fac8c3"];nU.primary=nU[5];var nW=["#2b1611","#441d12","#592716","#7c3118","#aa3e19","#d84a1b","#e87040","#f3956a","#f8b692","#fad4bc"];nW.primary=nW[5];var nq=["#2b1d11","#442a11","#593815","#7c4a15","#aa6215","#d87a16","#e89a3c","#f3b765","#f8cf8d","#fae3b7"];nq.primary=nq[5];var nG=["#2b2111","#443111","#594214","#7c5914","#aa7714","#d89614","#e8b339","#f3cc62","#f8df8b","#faedb5"];nG.primary=nG[5];var nK=["#2b2611","#443b11","#595014","#7c6e14","#aa9514","#d8bd14","#e8d639","#f3ea62","#f8f48b","#fafab5"];nK.primary=nK[5];var nV=["#1f2611","#2e3c10","#3e4f13","#536d13","#6f9412","#8bbb11","#a9d134","#c9e75d","#e4f88b","#f0fab5"];nV.primary=nV[5];var nX=["#162312","#1d3712","#274916","#306317","#3c8618","#49aa19","#6abe39","#8fd460","#b2e58b","#d5f2bb"];nX.primary=nX[5];var nJ=["#112123","#113536","#144848","#146262","#138585","#13a8a8","#33bcb7","#58d1c9","#84e2d8","#b2f1e8"];nJ.primary=nJ[5];var nQ=["#111a2c","#112545","#15325b","#15417e","#1554ad","#1668dc","#3c89e8","#65a9f3","#8dc5f8","#b7dcfa"];nQ.primary=nQ[5];var nY=["#131629","#161d40","#1c2755","#203175","#263ea0","#2b4acb","#5273e0","#7f9ef3","#a8c1f8","#d2e0fa"];nY.primary=nY[5];var n0=["#1a1325","#24163a","#301c4d","#3e2069","#51258f","#642ab5","#854eca","#ab7ae0","#cda8f0","#ebd7fa"];n0.primary=n0[5];var n1=["#291321","#40162f","#551c3b","#75204f","#a02669","#cb2b83","#e0529c","#f37fb7","#f8a8cc","#fad2e3"];n1.primary=n1[5];var n2=["#151515","#1f1f1f","#2d2d2d","#393939","#494949","#5a5a5a","#6a6a6a","#7b7b7b","#888888","#969696"];n2.primary=n2[5];let n5={blue:"#1677FF",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#EB2F96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911"},n3=Object.assign(Object.assign({},n5),{colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff4d4f",colorInfo:"#1677ff",colorLink:"",colorTextBase:"",colorBgBase:"",fontFamily:`-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
'Noto Color Emoji'`,fontFamilyCode:"'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInBack:"cubic-bezier(0.71, -0.46, 0.88, 0.6)",motionEaseInQuint:"cubic-bezier(0.755, 0.05, 0.855, 0.06)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:6,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,motion:!0}),n4=e=>{let t=e,n=e,r=e,a=e;return e<6&&e>=5?t=e+1:e<16&&e>=6?t=e+2:e>=16&&(t=16),e<7&&e>=5?n=4:e<8&&e>=7?n=5:e<14&&e>=8?n=6:e<16&&e>=14?n=7:e>=16&&(n=8),e<6&&e>=2?r=1:e>=6&&(r=2),e>4&&e<8?a=4:e>=8&&(a=6),{borderRadius:e,borderRadiusXS:r,borderRadiusSM:n,borderRadiusLG:t,borderRadiusOuter:a}},n6=e=>{let{controlHeight:t}=e;return{controlHeightSM:.75*t,controlHeightXS:.5*t,controlHeightLG:1.25*t}},n8=e=>{let t=function(e){let t=Array.from({length:10}).map((t,n)=>{let r=e*Math.pow(Math.E,(n-1)/5);return 2*Math.floor((n>1?Math.floor(r):Math.ceil(r))/2)});return t[1]=e,t.map(e=>({size:e,lineHeight:(e+8)/e}))}(e),n=t.map(e=>e.size),r=t.map(e=>e.lineHeight),a=n[1],i=n[0],o=n[2],s=r[1],l=r[0],c=r[2];return{fontSizeSM:i,fontSize:a,fontSizeLG:o,fontSizeXL:n[3],fontSizeHeading1:n[6],fontSizeHeading2:n[5],fontSizeHeading3:n[4],fontSizeHeading4:n[3],fontSizeHeading5:n[2],lineHeight:s,lineHeightLG:c,lineHeightSM:l,fontHeight:Math.round(s*a),fontHeightLG:Math.round(c*o),fontHeightSM:Math.round(l*i),lineHeightHeading1:r[6],lineHeightHeading2:r[5],lineHeightHeading3:r[4],lineHeightHeading4:r[3],lineHeightHeading5:r[2]}},n9=(e,t)=>new nS(e).setA(t).toRgbString(),n7=(e,t)=>new nS(e).darken(t).toHexString(),re=e=>{let t=nE(e);return{1:t[0],2:t[1],3:t[2],4:t[3],5:t[4],6:t[5],7:t[6],8:t[4],9:t[5],10:t[6]}},rt=(e,t)=>{let n=e||"#fff",r=t||"#000";return{colorBgBase:n,colorTextBase:r,colorText:n9(r,.88),colorTextSecondary:n9(r,.65),colorTextTertiary:n9(r,.45),colorTextQuaternary:n9(r,.25),colorFill:n9(r,.15),colorFillSecondary:n9(r,.06),colorFillTertiary:n9(r,.04),colorFillQuaternary:n9(r,.02),colorBgSolid:n9(r,1),colorBgSolidHover:n9(r,.75),colorBgSolidActive:n9(r,.95),colorBgLayout:n7(n,4),colorBgContainer:n7(n,0),colorBgElevated:n7(n,0),colorBgSpotlight:n9(r,.85),colorBgBlur:"transparent",colorBorder:n7(n,15),colorBorderSecondary:n7(n,6)}},rn=ta(function(e){nT.pink=nT.magenta,nH.pink=nH.magenta;let t=Object.keys(n5).map(t=>{let n=e[t]===nT[t]?nH[t]:nE(e[t]);return Array.from({length:10},()=>1).reduce((e,r,a)=>(e[`${t}-${a+1}`]=n[a],e[`${t}${a+1}`]=n[a],e),{})}).reduce((e,t)=>e=Object.assign(Object.assign({},e),t),{});return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},e),t),function(e,t){let{generateColorPalettes:n,generateNeutralColorPalettes:r}=t,{colorSuccess:a,colorWarning:i,colorError:o,colorInfo:s,colorPrimary:l,colorBgBase:c,colorTextBase:u}=e,d=n(l),p=n(a),m=n(i),f=n(o),h=n(s),g=r(c,u),v=n(e.colorLink||e.colorInfo),y=new nS(f[1]).mix(new nS(f[3]),50).toHexString();return Object.assign(Object.assign({},g),{colorPrimaryBg:d[1],colorPrimaryBgHover:d[2],colorPrimaryBorder:d[3],colorPrimaryBorderHover:d[4],colorPrimaryHover:d[5],colorPrimary:d[6],colorPrimaryActive:d[7],colorPrimaryTextHover:d[8],colorPrimaryText:d[9],colorPrimaryTextActive:d[10],colorSuccessBg:p[1],colorSuccessBgHover:p[2],colorSuccessBorder:p[3],colorSuccessBorderHover:p[4],colorSuccessHover:p[4],colorSuccess:p[6],colorSuccessActive:p[7],colorSuccessTextHover:p[8],colorSuccessText:p[9],colorSuccessTextActive:p[10],colorErrorBg:f[1],colorErrorBgHover:f[2],colorErrorBgFilledHover:y,colorErrorBgActive:f[3],colorErrorBorder:f[3],colorErrorBorderHover:f[4],colorErrorHover:f[5],colorError:f[6],colorErrorActive:f[7],colorErrorTextHover:f[8],colorErrorText:f[9],colorErrorTextActive:f[10],colorWarningBg:m[1],colorWarningBgHover:m[2],colorWarningBorder:m[3],colorWarningBorderHover:m[4],colorWarningHover:m[4],colorWarning:m[6],colorWarningActive:m[7],colorWarningTextHover:m[8],colorWarningText:m[9],colorWarningTextActive:m[10],colorInfoBg:h[1],colorInfoBgHover:h[2],colorInfoBorder:h[3],colorInfoBorderHover:h[4],colorInfoHover:h[4],colorInfo:h[6],colorInfoActive:h[7],colorInfoTextHover:h[8],colorInfoText:h[9],colorInfoTextActive:h[10],colorLinkHover:v[4],colorLink:v[6],colorLinkActive:v[7],colorBgMask:new nS("#000").setA(.45).toRgbString(),colorWhite:"#fff"})}(e,{generateColorPalettes:re,generateNeutralColorPalettes:rt})),n8(e.fontSize)),function(e){let{sizeUnit:t,sizeStep:n}=e;return{sizeXXL:t*(n+8),sizeXL:t*(n+4),sizeLG:t*(n+2),sizeMD:t*(n+1),sizeMS:t*n,size:t*n,sizeSM:t*(n-1),sizeXS:t*(n-2),sizeXXS:t*(n-3)}}(e)),n6(e)),function(e){let{motionUnit:t,motionBase:n,borderRadius:r,lineWidth:a}=e;return Object.assign({motionDurationFast:`${(n+t).toFixed(1)}s`,motionDurationMid:`${(n+2*t).toFixed(1)}s`,motionDurationSlow:`${(n+3*t).toFixed(1)}s`,lineWidthBold:a+1},n4(r))}(e))}),rr={token:n3,override:{override:n3},hashed:!0},ra=b.createContext(rr),ri=`-ant-${Date.now()}-${Math.random()}`,ro=b.createContext(!1),rs=e=>{let{children:t,disabled:n}=e,r=b.useContext(ro);return b.createElement(ro.Provider,{value:null!=n?n:r},t)},rl=b.createContext(void 0),rc=e=>{let{children:t,size:n}=e,r=b.useContext(rl);return b.createElement(rl.Provider,{value:n||r},t)},{useId:ru}=Object.assign({},j),rd=void 0===ru?()=>"":ru;var rp=ed("../../node_modules/.pnpm/classnames@2.5.1/node_modules/classnames/index.js"),rm=ed.n(rp);function rf(e){return e instanceof HTMLElement||e instanceof SVGElement}var rh=ed("../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/index.js"),rg=Symbol.for("react.element"),rv=Symbol.for("react.transitional.element"),ry=Symbol.for("react.fragment"),rx=Number(b.version.split(".")[0]),rb=function(e,t){"function"==typeof e?e(t):"object"===eS(e)&&e&&"current"in e&&(e.current=t)},rj=function(e){if(!e)return!1;if(rw(e)&&rx>=19)return!0;var t,n,r=(0,rh.isMemo)(e)?e.type.type:e.type;return("function"!=typeof r||!!(null!=(t=r.prototype)&&t.render)||r.$$typeof===rh.ForwardRef)&&("function"!=typeof e||!!(null!=(n=e.prototype)&&n.render)||e.$$typeof===rh.ForwardRef)};function rw(e){return(0,b.isValidElement)(e)&&!(e&&"object"===eS(e)&&(e.$$typeof===rg||e.$$typeof===rv)&&e.type===ry)}var rS=["children"],rN=b.createContext({});function rk(e){var t=e.children,n=ez(e,rS);return b.createElement(rN.Provider,{value:n},t)}var rC=function(e){e6(n,e);var t=e7(n);function n(){return eV(this,n),t.apply(this,arguments)}return eJ(n,[{key:"render",value:function(){return this.props.children}}]),n}(b.Component);function r_(e){var t=b.useRef();return t.current=e,b.useCallback(function(){for(var e,n=arguments.length,r=Array(n),a=0;a<n;a++)r[a]=arguments[a];return null==(e=t.current)?void 0:e.call.apply(e,[t].concat(r))},[])}function rE(e){var t=b.useRef(!1),n=ew(b.useState(e),2),r=n[0],a=n[1];return b.useEffect(function(){return t.current=!1,function(){t.current=!0}},[]),[r,function(e,n){n&&t.current||a(e)}]}var rT="none",rO="appear",rA="enter",rI="leave",rP="none",rM="prepare",r$="start",rL="active",rR="prepared";function rZ(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit".concat(e)]="webkit".concat(t),n["Moz".concat(e)]="moz".concat(t),n["ms".concat(e)]="MS".concat(t),n["O".concat(e)]="o".concat(t.toLowerCase()),n}var rF=(l=eT(),c="undefined"!=typeof window?window:{},u={animationend:rZ("Animation","AnimationEnd"),transitionend:rZ("Transition","TransitionEnd")},l&&("AnimationEvent"in c||delete u.animationend.animation,"TransitionEvent"in c||delete u.transitionend.transition),u),rz={};eT()&&(rz=document.createElement("div").style);var rD={};function rB(e){if(rD[e])return rD[e];var t=rF[e];if(t)for(var n=Object.keys(t),r=n.length,a=0;a<r;a+=1){var i=n[a];if(Object.prototype.hasOwnProperty.call(t,i)&&i in rz)return rD[e]=t[i],rD[e]}return""}var rH=rB("animationend"),rU=rB("transitionend"),rW=!!(rH&&rU),rq=rH||"animationend",rG=rU||"transitionend";function rK(e,t){return e?"object"===eS(e)?e[t.replace(/-\w/g,function(e){return e[1].toUpperCase()})]:"".concat(e,"-").concat(t):null}let rV=function(e){var t=(0,b.useRef)();function n(t){t&&(t.removeEventListener(rG,e),t.removeEventListener(rq,e))}return b.useEffect(function(){return function(){n(t.current)}},[]),[function(r){t.current&&t.current!==r&&n(t.current),r&&r!==t.current&&(r.addEventListener(rG,e),r.addEventListener(rq,e),t.current=r)},n]};var rX=eT()?b.useLayoutEffect:b.useEffect,rJ=function(e){return+setTimeout(e,16)},rQ=function(e){return clearTimeout(e)};"undefined"!=typeof window&&"requestAnimationFrame"in window&&(rJ=function(e){return window.requestAnimationFrame(e)},rQ=function(e){return window.cancelAnimationFrame(e)});var rY=0,r0=new Map,r1=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=rY+=1;return!function t(r){if(0===r)r0.delete(n),e();else{var a=rJ(function(){t(r-1)});r0.set(n,a)}}(t),n};r1.cancel=function(e){var t=r0.get(e);return r0.delete(e),rQ(t)};let r2=function(){var e=b.useRef(null);function t(){r1.cancel(e.current)}return b.useEffect(function(){return function(){t()}},[]),[function n(r){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;t();var i=r1(function(){a<=1?r({isCanceled:function(){return i!==e.current}}):n(r,a-1)});e.current=i},t]};var r5=[rM,r$,rL,"end"],r3=[rM,rR];function r4(e){return e===rL||"end"===e}let r6=function(e,t,n){var r=ew(rE(rP),2),a=r[0],i=r[1],o=ew(r2(),2),s=o[0],l=o[1],c=t?r3:r5;return rX(function(){if(a!==rP&&"end"!==a){var e=c.indexOf(a),t=c[e+1],r=n(a);!1===r?i(t,!0):t&&s(function(e){function n(){e.isCanceled()||i(t,!0)}!0===r?n():Promise.resolve(r).then(n)})}},[e,a]),b.useEffect(function(){return function(){l()}},[]),[function(){i(rM,!0)},a]},r8=(d=rW,"object"===eS(rW)&&(d=rW.transitionSupport),(p=b.forwardRef(function(e,t){var n,r=e.visible,a=void 0===r||r,i=e.removeOnLeave,o=void 0===i||i,s=e.forceRender,l=e.children,c=e.motionName,u=e.leavedClassName,p=e.eventProps,m=b.useContext(rN).motion,f=!!(e.motionName&&d&&!1!==m),h=(0,b.useRef)(),g=(0,b.useRef)(),v=function(e,t,n,r){var a,i,o=r.motionEnter,s=void 0===o||o,l=r.motionAppear,c=void 0===l||l,u=r.motionLeave,d=void 0===u||u,p=r.motionDeadline,m=r.motionLeaveImmediately,f=r.onAppearPrepare,h=r.onEnterPrepare,g=r.onLeavePrepare,v=r.onAppearStart,y=r.onEnterStart,x=r.onLeaveStart,j=r.onAppearActive,w=r.onEnterActive,S=r.onLeaveActive,N=r.onAppearEnd,k=r.onEnterEnd,C=r.onLeaveEnd,_=r.onVisibleChanged,E=ew(rE(),2),T=E[0],O=E[1],A=(a=ew(b.useReducer(function(e){return e+1},0),2)[1],i=b.useRef(rT),[r_(function(){return i.current}),r_(function(e){i.current="function"==typeof e?e(i.current):e,a()})]),I=ew(A,2),P=I[0],M=I[1],$=ew(rE(null),2),L=$[0],R=$[1],Z=P(),F=(0,b.useRef)(!1),z=(0,b.useRef)(null),D=(0,b.useRef)(!1);function B(){M(rT),R(null,!0)}var H=r_(function(e){var t,r=P();if(r!==rT){var a=n();if(!e||e.deadline||e.target===a){var i=D.current;r===rO&&i?t=null==N?void 0:N(a,e):r===rA&&i?t=null==k?void 0:k(a,e):r===rI&&i&&(t=null==C?void 0:C(a,e)),i&&!1!==t&&B()}}}),U=ew(rV(H),1)[0],W=function(e){switch(e){case rO:return ek(ek(ek({},rM,f),r$,v),rL,j);case rA:return ek(ek(ek({},rM,h),r$,y),rL,w);case rI:return ek(ek(ek({},rM,g),r$,x),rL,S);default:return{}}},q=b.useMemo(function(){return W(Z)},[Z]),G=ew(r6(Z,!e,function(e){if(e===rM){var t,r=q[rM];return!!r&&r(n())}return V in q&&R((null==(t=q[V])?void 0:t.call(q,n(),null))||null),V===rL&&Z!==rT&&(U(n()),p>0&&(clearTimeout(z.current),z.current=setTimeout(function(){H({deadline:!0})},p))),V===rR&&B(),!0}),2),K=G[0],V=G[1];D.current=r4(V);var X=(0,b.useRef)(null);rX(function(){if(!F.current||X.current!==t){O(t);var n,r=F.current;F.current=!0,!r&&t&&c&&(n=rO),r&&t&&s&&(n=rA),(r&&!t&&d||!r&&m&&!t&&d)&&(n=rI);var a=W(n);n&&(e||a[rM])?(M(n),K()):M(rT),X.current=t}},[t]),(0,b.useEffect)(function(){(Z!==rO||c)&&(Z!==rA||s)&&(Z!==rI||d)||M(rT)},[c,s,d]),(0,b.useEffect)(function(){return function(){F.current=!1,clearTimeout(z.current)}},[]);var J=b.useRef(!1);(0,b.useEffect)(function(){T&&(J.current=!0),void 0!==T&&Z===rT&&((J.current||T)&&(null==_||_(T)),J.current=!0)},[T,Z]);var Q=L;return q[rM]&&V===r$&&(Q=e_({transition:"none"},Q)),[Z,V,Q,null!=T?T:t]}(f,a,function(){try{var e,t,n,r;return h.current instanceof HTMLElement?h.current:(r=(t=e=g.current)&&"object"===eS(t)&&rf(t.nativeElement)?t.nativeElement:rf(t)?t:null)?r:e instanceof b.Component?null==(n=es.findDOMNode)?void 0:n.call(es,e):null}catch(e){return null}},e),y=ew(v,4),x=y[0],j=y[1],w=y[2],S=y[3],N=b.useRef(S);S&&(N.current=!0);var k=b.useCallback(function(e){h.current=e,rb(t,e)},[t]),C=e_(e_({},p),{},{visible:a});if(l)if(x===rT)_=S?l(e_({},C),k):!o&&N.current&&u?l(e_(e_({},C),{},{className:u}),k):!s&&(o||u)?null:l(e_(e_({},C),{},{style:{display:"none"}}),k);else{j===rM?E="prepare":r4(j)?E="active":j===r$&&(E="start");var _,E,T=rK(c,"".concat(x,"-").concat(E));_=l(e_(e_({},C),{},{className:rm()(rK(c,x),ek(ek({},T,T&&E),c,"string"==typeof c)),style:w}),k)}else _=null;return b.isValidElement(_)&&rj(_)&&(((n=_)&&rw(n)?n.props.propertyIsEnumerable("ref")?n.props.ref:n.ref:null)||(_=b.cloneElement(_,{ref:k}))),b.createElement(rC,{ref:g},_)})).displayName="CSSMotion",p);var r9="keep",r7="remove",ae="removed";function at(e){var t;return e_(e_({},t=e&&"object"===eS(e)&&"key"in e?e:{key:e}),{},{key:String(t.key)})}function an(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.map(at)}var ar=["component","children","onVisibleChanged","onAllRemoved"],aa=["status"],ai=["eventProps","visible","children","motionName","motionAppear","motionEnter","motionLeave","motionLeaveImmediately","motionDeadline","removeOnLeave","leavedClassName","onAppearPrepare","onAppearStart","onAppearActive","onAppearEnd","onEnterStart","onEnterActive","onEnterEnd","onLeaveStart","onLeaveActive","onLeaveEnd"];let ao=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r8,n=function(e){e6(r,e);var n=e7(r);function r(){var e;eV(this,r);for(var t=arguments.length,a=Array(t),i=0;i<t;i++)a[i]=arguments[i];return ek(e3(e=n.call.apply(n,[this].concat(a))),"state",{keyEntities:[]}),ek(e3(e),"removeKey",function(t){e.setState(function(e){return{keyEntities:e.keyEntities.map(function(e){return e.key!==t?e:e_(e_({},e),{},{status:ae})})}},function(){0===e.state.keyEntities.filter(function(e){return e.status!==ae}).length&&e.props.onAllRemoved&&e.props.onAllRemoved()})}),e}return eJ(r,[{key:"render",value:function(){var e=this,n=this.state.keyEntities,r=this.props,a=r.component,i=r.children,o=r.onVisibleChanged,s=(r.onAllRemoved,ez(r,ar)),l=a||b.Fragment,c={};return ai.forEach(function(e){c[e]=s[e],delete s[e]}),delete s.keys,b.createElement(l,s,n.map(function(n,r){var a=n.status,s=ez(n,aa);return b.createElement(t,tC({},c,{key:s.key,visible:"add"===a||a===r9,eventProps:s,onVisibleChanged:function(t){null==o||o(t,{key:s.key}),t||e.removeKey(s.key)}}),function(e,t){return i(e_(e_({},e),{},{index:r}),t)})}))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.keys,r=t.keyEntities;return{keyEntities:(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[],r=0,a=t.length,i=an(e),o=an(t);i.forEach(function(e){for(var t=!1,i=r;i<a;i+=1){var s=o[i];if(s.key===e.key){r<i&&(n=n.concat(o.slice(r,i).map(function(e){return e_(e_({},e),{},{status:"add"})})),r=i),n.push(e_(e_({},s),{},{status:r9})),r+=1,t=!0;break}}t||n.push(e_(e_({},e),{},{status:r7}))}),r<a&&(n=n.concat(o.slice(r).map(function(e){return e_(e_({},e),{},{status:"add"})})));var s={};return n.forEach(function(e){var t=e.key;s[t]=(s[t]||0)+1}),Object.keys(s).filter(function(e){return s[e]>1}).forEach(function(e){(n=n.filter(function(t){var n=t.key,r=t.status;return n!==e||r!==r7})).forEach(function(t){t.key===e&&(t.status=r9)})}),n})(r,an(n)).filter(function(e){var t=r.find(function(t){var n=t.key;return e.key===n});return!t||t.status!==ae||e.status!==r7})}}}]),r}(b.Component);return ek(n,"defaultProps",{component:"div"}),n}(rW);function as(e){return e>=0&&e<=255}let al=function(e,t){let{r:n,g:r,b:a,a:i}=new nS(e).toRgb();if(i<1)return e;let{r:o,g:s,b:l}=new nS(t).toRgb();for(let e=.01;e<=1;e+=.01){let t=Math.round((n-o*(1-e))/e),i=Math.round((r-s*(1-e))/e),c=Math.round((a-l*(1-e))/e);if(as(t)&&as(i)&&as(c))return new nS({r:t,g:i,b:c,a:Math.round(100*e)/100}).toRgbString()}return new nS({r:n,g:r,b:a,a:1}).toRgbString()};var ac=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};function au(e){let{override:t}=e,n=ac(e,["override"]),r=Object.assign({},t);Object.keys(n3).forEach(e=>{delete r[e]});let a=Object.assign(Object.assign({},n),r);return!1===a.motion&&(a.motionDurationFast="0s",a.motionDurationMid="0s",a.motionDurationSlow="0s"),Object.assign(Object.assign(Object.assign({},a),{colorFillContent:a.colorFillSecondary,colorFillContentHover:a.colorFill,colorFillAlter:a.colorFillQuaternary,colorBgContainerDisabled:a.colorFillTertiary,colorBorderBg:a.colorBgContainer,colorSplit:al(a.colorBorderSecondary,a.colorBgContainer),colorTextPlaceholder:a.colorTextQuaternary,colorTextDisabled:a.colorTextQuaternary,colorTextHeading:a.colorText,colorTextLabel:a.colorTextSecondary,colorTextDescription:a.colorTextTertiary,colorTextLightSolid:a.colorWhite,colorHighlight:a.colorError,colorBgTextHover:a.colorFillSecondary,colorBgTextActive:a.colorFill,colorIcon:a.colorTextTertiary,colorIconHover:a.colorText,colorErrorOutline:al(a.colorErrorBg,a.colorBgContainer),colorWarningOutline:al(a.colorWarningBg,a.colorBgContainer),fontSizeIcon:a.fontSizeSM,lineWidthFocus:3*a.lineWidth,lineWidth:a.lineWidth,controlOutlineWidth:2*a.lineWidth,controlInteractiveSize:a.controlHeight/2,controlItemBgHover:a.colorFillTertiary,controlItemBgActive:a.colorPrimaryBg,controlItemBgActiveHover:a.colorPrimaryBgHover,controlItemBgActiveDisabled:a.colorFill,controlTmpOutline:a.colorFillQuaternary,controlOutline:al(a.colorPrimaryBg,a.colorBgContainer),lineType:a.lineType,borderRadius:a.borderRadius,borderRadiusXS:a.borderRadiusXS,borderRadiusSM:a.borderRadiusSM,borderRadiusLG:a.borderRadiusLG,fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:a.sizeXXS,paddingXS:a.sizeXS,paddingSM:a.sizeSM,padding:a.size,paddingMD:a.sizeMD,paddingLG:a.sizeLG,paddingXL:a.sizeXL,paddingContentHorizontalLG:a.sizeLG,paddingContentVerticalLG:a.sizeMS,paddingContentHorizontal:a.sizeMS,paddingContentVertical:a.sizeSM,paddingContentHorizontalSM:a.size,paddingContentVerticalSM:a.sizeXS,marginXXS:a.sizeXXS,marginXS:a.sizeXS,marginSM:a.sizeSM,margin:a.size,marginMD:a.sizeMD,marginLG:a.sizeLG,marginXL:a.sizeXL,marginXXL:a.sizeXXL,boxShadow:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowSecondary:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowTertiary:`
      0 1px 2px 0 rgba(0, 0, 0, 0.03),
      0 1px 6px -1px rgba(0, 0, 0, 0.02),
      0 2px 4px 0 rgba(0, 0, 0, 0.02)
    `,screenXS:480,screenXSMin:480,screenXSMax:575,screenSM:576,screenSMMin:576,screenSMMax:767,screenMD:768,screenMDMin:768,screenMDMax:991,screenLG:992,screenLGMin:992,screenLGMax:1199,screenXL:1200,screenXLMin:1200,screenXLMax:1599,screenXXL:1600,screenXXLMin:1600,boxShadowPopoverArrow:"2px 2px 5px rgba(0, 0, 0, 0.05)",boxShadowCard:`
      0 1px 2px -2px ${new nS("rgba(0, 0, 0, 0.16)").toRgbString()},
      0 3px 6px 0 ${new nS("rgba(0, 0, 0, 0.12)").toRgbString()},
      0 5px 12px 4px ${new nS("rgba(0, 0, 0, 0.09)").toRgbString()}
    `,boxShadowDrawerRight:`
      -6px 0 16px 0 rgba(0, 0, 0, 0.08),
      -3px 0 6px -4px rgba(0, 0, 0, 0.12),
      -9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerLeft:`
      6px 0 16px 0 rgba(0, 0, 0, 0.08),
      3px 0 6px -4px rgba(0, 0, 0, 0.12),
      9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerUp:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerDown:`
      0 -6px 16px 0 rgba(0, 0, 0, 0.08),
      0 -3px 6px -4px rgba(0, 0, 0, 0.12),
      0 -9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)"}),r)}var ad=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};let ap={lineHeight:!0,lineHeightSM:!0,lineHeightLG:!0,lineHeightHeading1:!0,lineHeightHeading2:!0,lineHeightHeading3:!0,lineHeightHeading4:!0,lineHeightHeading5:!0,opacityLoading:!0,fontWeightStrong:!0,zIndexPopupBase:!0,zIndexBase:!0,opacityImage:!0},am={size:!0,sizeSM:!0,sizeLG:!0,sizeMD:!0,sizeXS:!0,sizeXXS:!0,sizeMS:!0,sizeXL:!0,sizeXXL:!0,sizeUnit:!0,sizeStep:!0,motionBase:!0,motionUnit:!0},af={screenXS:!0,screenXSMin:!0,screenXSMax:!0,screenSM:!0,screenSMMin:!0,screenSMMax:!0,screenMD:!0,screenMDMin:!0,screenMDMax:!0,screenLG:!0,screenLGMin:!0,screenLGMax:!0,screenXL:!0,screenXLMin:!0,screenXLMax:!0,screenXXL:!0,screenXXLMin:!0},ah=(e,t,n)=>{let r=n.getDerivativeToken(e),{override:a}=t,i=ad(t,["override"]),o=Object.assign(Object.assign({},r),{override:a});return o=au(o),i&&Object.entries(i).forEach(e=>{let[t,n]=e,{theme:r}=n,a=ad(n,["theme"]),i=a;r&&(i=ah(Object.assign(Object.assign({},o),a),{override:a},r)),o[t]=i}),o};function ag(){let{token:e,hashed:t,theme:n,override:r,cssVar:a}=b.useContext(ra),i=n||rn,[o,s,l]=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=(0,b.useContext)(e5),a=r.cache.instanceId,i=r.container,o=n.salt,s=void 0===o?"":o,l=n.override,c=void 0===l?tw:l,u=n.formatToken,d=n.getComputedToken,p=n.cssVar,m=function(e,t){for(var n=ti,r=0;r<t.length;r+=1){var a=t[r];n.has(a)||n.set(a,new WeakMap),n=n.get(a)}return n.has(to)||n.set(to,e()),n.get(to)}(function(){return Object.assign.apply(Object,[{}].concat(eh(t)))},t),f=tl(m),h=tl(c),g=p?tl(p):"";return tj(tk,[s,e.id,f,h,g],function(){var t,n=d?d(m,c,e):tN(m,c,e,u),r=e_({},n),a="";if(p){var i=ew(tf(n,p.key,{prefix:p.prefix,ignore:p.ignore,unitless:p.unitless,preserve:p.preserve}),2);n=i[0],a=i[1]}var o=tc(n,s);n._tokenKey=o,r._tokenKey=tc(r,s);var l=null!=(t=null==p?void 0:p.key)?t:o;n._themeKey=l,tS.set(l,(tS.get(l)||0)+1);var f="".concat("css","-").concat(eE(o));return n._hashId=f,[n,f,r,a,(null==p?void 0:p.key)||""]},function(e){var t,n,r;t=e[0]._themeKey,tS.set(t,(tS.get(t)||0)-1),r=(n=Array.from(tS.keys())).filter(function(e){return 0>=(tS.get(e)||0)}),n.length-r.length>0&&r.forEach(function(e){"undefined"!=typeof document&&document.querySelectorAll("style[".concat(e0,'="').concat(e,'"]')).forEach(function(e){if(e[e2]===a){var t;null==(t=e.parentNode)||t.removeChild(e)}}),tS.delete(e)})},function(e){var t=ew(e,4),n=t[0],r=t[3];if(p&&r){var o=eF(r,eE("css-variables-".concat(n._themeKey)),{mark:e1,prepend:"queue",attachTo:i,priority:-999});o[e2]=a,o.setAttribute(e0,n._themeKey)}})}(i,[n3,e],{salt:`5.24.8-${t||""}`,override:r,getComputedToken:ah,formatToken:au,cssVar:a&&{prefix:a.prefix,key:a.key,unitless:ap,ignore:am,preserve:af}});return[i,l,t?s:"",o,a]}function av(e){let{children:t}=e,[,n]=ag(),{motion:r}=n,a=b.useRef(!1);return(a.current=a.current||!1===r,a.current)?b.createElement(rk,{motion:r},t):t}let ay=()=>null,ax=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return{boxSizing:"border-box",margin:0,padding:0,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,listStyle:"none",fontFamily:t?"inherit":e.fontFamily}},ab=()=>({display:"inline-flex",alignItems:"center",color:"inherit",fontStyle:"normal",lineHeight:0,textAlign:"center",textTransform:"none",verticalAlign:"-0.125em",textRendering:"optimizeLegibility","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale","> *":{lineHeight:1},svg:{display:"inline-block"}}),aj=e=>({a:{color:e.colorLink,textDecoration:e.linkDecoration,backgroundColor:"transparent",outline:"none",cursor:"pointer",transition:`color ${e.motionDurationSlow}`,"-webkit-text-decoration-skip":"objects","&:hover":{color:e.colorLinkHover},"&:active":{color:e.colorLinkActive},"&:active, &:hover":{textDecoration:e.linkHoverDecoration,outline:0},"&:focus":{textDecoration:e.linkFocusDecoration,outline:0},"&[disabled]":{color:e.colorTextDisabled,cursor:"not-allowed"}}}),aw=e=>({[`.${e}`]:Object.assign(Object.assign({},ab()),{[`.${e} .${e}-icon`]:{display:"block"}})}),aS=(e,t)=>{let[n,r]=ag();return t7({theme:n,token:r,hashId:"",path:["ant-design-icons",e],nonce:()=>null==t?void 0:t.nonce,layer:{name:"antd"}},()=>[aw(e)])};var aN=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};let ak=["getTargetContainer","getPopupContainer","renderEmpty","input","pagination","form","select","button"];function aC(){return r||"ant"}function a_(){return a||ev}let aE=()=>({getPrefixCls:(e,t)=>t||(e?`${aC()}-${e}`:aC()),getIconPrefixCls:a_,getRootPrefixCls:()=>r||aC(),getTheme:()=>i,holderRender:o}),aT=e=>{let{children:t,csp:n,autoInsertSpaceInButton:r,alert:a,anchor:i,form:o,locale:s,componentSize:l,direction:c,space:u,splitter:d,virtual:p,dropdownMatchSelectWidth:m,popupMatchSelectWidth:f,popupOverflow:h,legacyLocale:g,parentContext:v,iconPrefixCls:y,theme:x,componentDisabled:j,segmented:w,statistic:S,spin:N,calendar:k,carousel:C,cascader:_,collapse:E,typography:T,checkbox:O,descriptions:A,divider:I,drawer:P,skeleton:M,steps:$,image:L,layout:R,list:Z,mentions:F,modal:z,progress:D,result:B,slider:H,breadcrumb:U,menu:W,pagination:q,input:G,textArea:K,empty:V,badge:X,radio:J,rate:Q,switch:Y,transfer:ee,avatar:et,message:en,tag:er,table:ea,card:ei,tabs:eo,timeline:es,timePicker:el,upload:ec,notification:eu,tree:ed,colorPicker:ep,datePicker:em,rangePicker:ef,flex:eg,wave:ex,dropdown:eb,warning:ej,tour:ew,tooltip:eN,popover:ek,popconfirm:eC,floatButtonGroup:e_,variant:eE,inputNumber:eT,treeSelect:eO}=e,eA=b.useCallback((t,n)=>{let{prefixCls:r}=e;if(n)return n;let a=r||v.getPrefixCls("");return t?`${a}-${t}`:a},[v.getPrefixCls,e.prefixCls]),eI=y||v.iconPrefixCls||ev,eP=n||v.csp;aS(eI,eP);let eM=function(e,t,n){var r;nd("ConfigProvider");let a=e||{},i=!1!==a.inherit&&t?t:Object.assign(Object.assign({},rr),{hashed:null!=(r=null==t?void 0:t.hashed)?r:rr.hashed,cssVar:null==t?void 0:t.cssVar}),o=rd();return eD(()=>{var r,s;if(!e)return t;let l=Object.assign({},i.components);Object.keys(e.components||{}).forEach(t=>{l[t]=Object.assign(Object.assign({},l[t]),e.components[t])});let c=`css-var-${o.replace(/:/g,"")}`,u=(null!=(r=a.cssVar)?r:i.cssVar)&&Object.assign(Object.assign(Object.assign({prefix:null==n?void 0:n.prefixCls},"object"==typeof i.cssVar?i.cssVar:{}),"object"==typeof a.cssVar?a.cssVar:{}),{key:"object"==typeof a.cssVar&&(null==(s=a.cssVar)?void 0:s.key)||c});return Object.assign(Object.assign(Object.assign({},i),a),{token:Object.assign(Object.assign({},i.token),a.token),components:l,cssVar:u})},[a,i],(e,t)=>e.some((e,n)=>!eK(e,t[n],!0)))}(x,v.theme,{prefixCls:eA("")}),e$={csp:eP,autoInsertSpaceInButton:r,alert:a,anchor:i,locale:s||g,direction:c,space:u,splitter:d,virtual:p,popupMatchSelectWidth:null!=f?f:m,popupOverflow:h,getPrefixCls:eA,iconPrefixCls:eI,theme:eM,segmented:w,statistic:S,spin:N,calendar:k,carousel:C,cascader:_,collapse:E,typography:T,checkbox:O,descriptions:A,divider:I,drawer:P,skeleton:M,steps:$,image:L,input:G,textArea:K,layout:R,list:Z,mentions:F,modal:z,progress:D,result:B,slider:H,breadcrumb:U,menu:W,pagination:q,empty:V,badge:X,radio:J,rate:Q,switch:Y,transfer:ee,avatar:et,message:en,tag:er,table:ea,card:ei,tabs:eo,timeline:es,timePicker:el,upload:ec,notification:eu,tree:ed,colorPicker:ep,datePicker:em,rangePicker:ef,flex:eg,wave:ex,dropdown:eb,warning:ej,tour:ew,tooltip:eN,popover:ek,popconfirm:eC,floatButtonGroup:e_,variant:eE,inputNumber:eT,treeSelect:eO},eL=Object.assign({},v);Object.keys(e$).forEach(e=>{void 0!==e$[e]&&(eL[e]=e$[e])}),ak.forEach(t=>{let n=e[t];n&&(eL[t]=n)}),void 0!==r&&(eL.button=Object.assign({autoInsertSpace:r},eL.button));let eR=eD(()=>eL,eL,(e,t)=>{let n=Object.keys(e),r=Object.keys(t);return n.length!==r.length||n.some(n=>e[n]!==t[n])}),{layer:eZ}=b.useContext(e5),eF=b.useMemo(()=>({prefixCls:eI,csp:eP,layer:eZ?"antd":void 0}),[eI,eP,eZ]),ez=b.createElement(b.Fragment,null,b.createElement(ay,{dropdownMatchSelectWidth:m}),t),eB=b.useMemo(()=>{var e,t,n,r;return function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=ns(t[0]);return t.forEach(function(e){!function t(n,a){var i=new Set(a),o=ni(e,n),s=Array.isArray(o);if(s||"object"===eS(o)&&null!==o&&Object.getPrototypeOf(o)===Object.prototype){if(!i.has(o)){i.add(o);var l=ni(r,n);s?r=no(r,n,[]):l&&"object"===eS(l)||(r=no(r,n,ns(o))),nl(o).forEach(function(e){t([].concat(eh(n),[e]),i)})}}else r=no(r,n,o)}([])}),r}((null==(e=nf.Form)?void 0:e.defaultValidateMessages)||{},(null==(n=null==(t=eR.locale)?void 0:t.Form)?void 0:n.defaultValidateMessages)||{},(null==(r=eR.form)?void 0:r.validateMessages)||{},(null==o?void 0:o.validateMessages)||{})},[eR,null==o?void 0:o.validateMessages]);Object.keys(eB).length>0&&(ez=b.createElement(np.Provider,{value:eB},ez)),s&&(ez=b.createElement(ny,{locale:s,_ANT_MARK__:"internalMark"},ez)),(eI||eP)&&(ez=b.createElement(na.Provider,{value:eF},ez)),l&&(ez=b.createElement(rc,{size:l},ez)),ez=b.createElement(av,null,ez);let eH=b.useMemo(()=>{let e=eM||{},{algorithm:t,token:n,components:r,cssVar:a}=e,i=aN(e,["algorithm","token","components","cssVar"]),o=t&&(!Array.isArray(t)||t.length>0)?ta(t):rn,s={};Object.entries(r||{}).forEach(e=>{let[t,n]=e,r=Object.assign({},n);"algorithm"in r&&(!0===r.algorithm?r.theme=o:(Array.isArray(r.algorithm)||"function"==typeof r.algorithm)&&(r.theme=ta(r.algorithm)),delete r.algorithm),s[t]=r});let l=Object.assign(Object.assign({},n3),n);return Object.assign(Object.assign({},i),{theme:o,token:l,components:s,override:Object.assign({override:l},s),cssVar:a})},[eM]);return x&&(ez=b.createElement(ra.Provider,{value:eH},ez)),eR.warning&&(ez=b.createElement(nu.Provider,{value:eR.warning},ez)),void 0!==j&&(ez=b.createElement(rs,{disabled:j},ez)),b.createElement(ey.Provider,{value:eR},ez)},aO=e=>{let t=b.useContext(ey),n=b.useContext(nv);return b.createElement(aT,Object.assign({parentContext:t,legacyLocale:n},e))};function aA(){aA=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(t,n,r,i){var o,s,l,c,u=Object.create((n&&n.prototype instanceof g?n:g).prototype);return a(u,"_invoke",{value:(o=t,s=r,l=new _(i||[]),c=p,function(t,n){if(c===m)throw Error("Generator is already running");if(c===f){if("throw"===t)throw n;return{value:e,done:!0}}for(l.method=t,l.arg=n;;){var r=l.delegate;if(r){var a=function t(n,r){var a=r.method,i=n.iterator[a];if(i===e)return r.delegate=null,"throw"===a&&n.iterator.return&&(r.method="return",r.arg=e,t(n,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+a+"' method")),h;var o=d(i,n.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,h;var s=o.arg;return s?s.done?(r[n.resultName]=s.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,h):s:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,h)}(r,l);if(a){if(a===h)continue;return a}}if("next"===l.method)l.sent=l._sent=l.arg;else if("throw"===l.method){if(c===p)throw c=f,l.arg;l.dispatchException(l.arg)}else"return"===l.method&&l.abrupt("return",l.arg);c=m;var i=d(o,s,l);if("normal"===i.type){if(c=l.done?f:"suspendedYield",i.arg===h)continue;return{value:i.arg,done:l.done}}"throw"===i.type&&(c=f,l.method="throw",l.arg=i.arg)}})}),u}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var p="suspendedStart",m="executing",f="completed",h={};function g(){}function v(){}function y(){}var x={};c(x,o,function(){return this});var b=Object.getPrototypeOf,j=b&&b(b(E([])));j&&j!==n&&r.call(j,o)&&(x=j);var w=y.prototype=g.prototype=Object.create(x);function S(e){["next","throw","return"].forEach(function(t){c(e,t,function(e){return this._invoke(t,e)})})}function N(e,t){var n;a(this,"_invoke",{value:function(a,i){function o(){return new t(function(n,o){!function n(a,i,o,s){var l=d(e[a],e,i);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==eS(u)&&r.call(u,"__await")?t.resolve(u.__await).then(function(e){n("next",e,o,s)},function(e){n("throw",e,o,s)}):t.resolve(u).then(function(e){c.value=e,o(c)},function(e){return n("throw",e,o,s)})}s(l.arg)}(a,i,n,o)})}return n=n?n.then(o,o):o()}})}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function _(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function E(t){if(t||""===t){var n=t[o];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw TypeError(eS(t)+" is not iterable")}return v.prototype=y,a(w,"constructor",{value:y,configurable:!0}),a(y,"constructor",{value:v,configurable:!0}),v.displayName=c(y,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,c(e,l,"GeneratorFunction")),e.prototype=Object.create(w),e},t.awrap=function(e){return{__await:e}},S(N.prototype),c(N.prototype,s,function(){return this}),t.AsyncIterator=N,t.async=function(e,n,r,a,i){void 0===i&&(i=Promise);var o=new N(u(e,n,r,a),i);return t.isGeneratorFunction(n)?o:o.next().then(function(e){return e.done?e.value:o.next()})},S(w),c(w,l,"Generator"),c(w,o,function(){return this}),c(w,"toString",function(){return"[object Generator]"}),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=E,_.prototype={constructor:_,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(C),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(r,a){return s.type="throw",s.arg=t,n.next=r,a&&(n.method="next",n.arg=e),!!a}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),c=r.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var i=a;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;C(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:E(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),h}},t}function aI(e,t,n,r,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,a)}function aP(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function o(e){aI(i,r,a,o,s,"next",e)}function s(e){aI(i,r,a,o,s,"throw",e)}o(void 0)})}}aO.ConfigContext=ey,aO.SizeContext=rl,aO.config=e=>{let{prefixCls:t,iconPrefixCls:n,theme:s,holderRender:l}=e;if(void 0!==t&&(r=t),void 0!==n&&(a=n),"holderRender"in e&&(o=l),s)if(Object.keys(s).some(e=>e.endsWith("Color"))){let e=function(e,t){let n={},r=(e,t)=>{let n=e.clone();return(n=(null==t?void 0:t(n))||n).toRgbString()},a=(e,t)=>{let a=new nS(e),i=nE(a.toRgbString());n[`${t}-color`]=r(a),n[`${t}-color-disabled`]=i[1],n[`${t}-color-hover`]=i[4],n[`${t}-color-active`]=i[6],n[`${t}-color-outline`]=a.clone().setA(.2).toRgbString(),n[`${t}-color-deprecated-bg`]=i[0],n[`${t}-color-deprecated-border`]=i[2]};if(t.primaryColor){a(t.primaryColor,"primary");let e=new nS(t.primaryColor),i=nE(e.toRgbString());i.forEach((e,t)=>{n[`primary-${t+1}`]=e}),n["primary-color-deprecated-l-35"]=r(e,e=>e.lighten(35)),n["primary-color-deprecated-l-20"]=r(e,e=>e.lighten(20)),n["primary-color-deprecated-t-20"]=r(e,e=>e.tint(20)),n["primary-color-deprecated-t-50"]=r(e,e=>e.tint(50)),n["primary-color-deprecated-f-12"]=r(e,e=>e.setA(.12*e.a));let o=new nS(i[0]);n["primary-color-active-deprecated-f-30"]=r(o,e=>e.setA(.3*e.a)),n["primary-color-active-deprecated-d-02"]=r(o,e=>e.darken(2))}t.successColor&&a(t.successColor,"success"),t.warningColor&&a(t.warningColor,"warning"),t.errorColor&&a(t.errorColor,"error"),t.infoColor&&a(t.infoColor,"info");let i=Object.keys(n).map(t=>`--${e}-${t}: ${n[t]};`);return`
  :root {
    ${i.join("\n")}
  }
  `.trim()}(aC(),s);eT()&&eF(e,`${ri}-dynamic-theme`)}else i=s},aO.useConfig=function(){return{componentDisabled:(0,b.useContext)(ro),componentSize:(0,b.useContext)(rl)}},Object.defineProperty(aO,"SizeContext",{get:()=>rl});var aM=e_({},el),a$=aM.version,aL=aM.render,aR=aM.unmountComponentAtNode;try{Number((a$||"").split(".")[0])>=18&&(v=aM.createRoot)}catch(e){}function aZ(e){var t=aM.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;t&&"object"===eS(t)&&(t.usingClientEntryPoint=e)}var aF="__rc_react_root__";function az(){return(az=aP(aA().mark(function e(t){return aA().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",Promise.resolve().then(function(){var e;null==(e=t[aF])||e.unmount(),delete t[aF]}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function aD(){return(aD=aP(aA().mark(function e(t){return aA().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===v){e.next=2;break}return e.abrupt("return",function(e){return az.apply(this,arguments)}(t));case 2:aR(t);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}let aB=(e,t)=>(!function(e,t){var n;if(v)return aZ(!0),n=t[aF]||v(t),aZ(!1),n.render(e),t[aF]=n;null==aL||aL(e,t)}(e,t),()=>(function(e){return aD.apply(this,arguments)})(t)),aH={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"}}]},name:"check-circle",theme:"filled"};function aU(e){var t;return null==e||null==(t=e.getRootNode)?void 0:t.call(e)}function aW(e){return"object"===eS(e)&&"string"==typeof e.name&&"string"==typeof e.theme&&("object"===eS(e.icon)||"function"==typeof e.icon)}function aq(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(e).reduce(function(t,n){var r=e[n];return"class"===n?(t.className=r,delete t.class):(delete t[n],t[n.replace(/-(.)/g,function(e,t){return t.toUpperCase()})]=r),t},{})}function aG(e){return e?Array.isArray(e)?e:[e]:[]}var aK=function(e){var t=(0,b.useContext)(na),n=t.csp,r=t.prefixCls,a=t.layer,i="\n.anticon {\n  display: inline-flex;\n  align-items: center;\n  color: inherit;\n  font-style: normal;\n  line-height: 0;\n  text-align: center;\n  text-transform: none;\n  vertical-align: -0.125em;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.anticon > * {\n  line-height: 1;\n}\n\n.anticon svg {\n  display: inline-block;\n}\n\n.anticon::before {\n  display: none;\n}\n\n.anticon .anticon-icon {\n  display: block;\n}\n\n.anticon[tabindex] {\n  cursor: pointer;\n}\n\n.anticon-spin::before,\n.anticon-spin {\n  display: inline-block;\n  -webkit-animation: loadingCircle 1s infinite linear;\n  animation: loadingCircle 1s infinite linear;\n}\n\n@-webkit-keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n";r&&(i=i.replace(/anticon/g,r)),a&&(i="@layer ".concat(a," {\n").concat(i,"\n}")),(0,b.useEffect)(function(){var t,r=aU(t=e.current)instanceof ShadowRoot?aU(t):null;eF(i,"@ant-design-icons",{prepend:!a,csp:n,attachTo:r})},[])},aV=["icon","className","onClick","style","primaryColor","secondaryColor"],aX={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1},aJ=function(e){var t,n,r=e.icon,a=e.className,i=e.onClick,o=e.style,s=e.primaryColor,l=e.secondaryColor,c=ez(e,aV),u=b.useRef(),d=aX;if(s&&(d={primaryColor:s,secondaryColor:l||nE(s)[0]}),aK(u),t=aW(r),n="icon should be icon definiton, but got ".concat(r),eG(t,"[@ant-design/icons] ".concat(n)),!aW(r))return null;var p=r;return p&&"function"==typeof p.icon&&(p=e_(e_({},p),{},{icon:p.icon(d.primaryColor,d.secondaryColor)})),function e(t,n,r){return r?b.createElement(t.tag,e_(e_({key:n},aq(t.attrs)),r),(t.children||[]).map(function(r,a){return e(r,"".concat(n,"-").concat(t.tag,"-").concat(a))})):b.createElement(t.tag,e_({key:n},aq(t.attrs)),(t.children||[]).map(function(r,a){return e(r,"".concat(n,"-").concat(t.tag,"-").concat(a))}))}(p.icon,"svg-".concat(p.name),e_(e_({className:a,onClick:i,style:o,"data-icon":p.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},c),{},{ref:u}))};function aQ(e){var t=ew(aG(e),2),n=t[0],r=t[1];return aJ.setTwoToneColors({primaryColor:n,secondaryColor:r})}aJ.displayName="IconReact",aJ.getTwoToneColors=function(){return e_({},aX)},aJ.setTwoToneColors=function(e){var t=e.primaryColor,n=e.secondaryColor;aX.primaryColor=t,aX.secondaryColor=n||nE(t)[0],aX.calculated=!!n};var aY=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];aQ(nZ.primary);var a0=b.forwardRef(function(e,t){var n=e.className,r=e.icon,a=e.spin,i=e.rotate,o=e.tabIndex,s=e.onClick,l=e.twoToneColor,c=ez(e,aY),u=b.useContext(na),d=u.prefixCls,p=void 0===d?"anticon":d,m=u.rootClassName,f=rm()(m,p,ek(ek({},"".concat(p,"-").concat(r.name),!!r.name),"".concat(p,"-spin"),!!a||"loading"===r.name),n),h=o;void 0===h&&s&&(h=-1);var g=ew(aG(l),2),v=g[0],y=g[1];return b.createElement("span",tC({role:"img","aria-label":r.name},c,{ref:t,tabIndex:h,onClick:s,className:f}),b.createElement(aJ,{icon:r,primaryColor:v,secondaryColor:y,style:i?{msTransform:"rotate(".concat(i,"deg)"),transform:"rotate(".concat(i,"deg)")}:void 0}))});a0.displayName="AntdIcon",a0.getTwoToneColor=function(){var e=aJ.getTwoToneColors();return e.calculated?[e.primaryColor,e.secondaryColor]:e.primaryColor},a0.setTwoToneColor=aQ;var a1=b.forwardRef(function(e,t){return b.createElement(a0,tC({},e,{ref:t,icon:aH}))});let a2={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"}}]},name:"close-circle",theme:"filled"};var a5=b.forwardRef(function(e,t){return b.createElement(a0,tC({},e,{ref:t,icon:a2}))});let a3={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"exclamation-circle",theme:"filled"};var a4=b.forwardRef(function(e,t){return b.createElement(a0,tC({},e,{ref:t,icon:a3}))});let a6={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"info-circle",theme:"filled"};var a8=b.forwardRef(function(e,t){return b.createElement(a0,tC({},e,{ref:t,icon:a6}))});let a9={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"}}]},name:"loading",theme:"outlined"};var a7=b.forwardRef(function(e,t){return b.createElement(a0,tC({},e,{ref:t,icon:a9}))}),ie={MAC_ENTER:3,BACKSPACE:8,TAB:9,NUM_CENTER:12,ENTER:13,SHIFT:16,CTRL:17,ALT:18,PAUSE:19,CAPS_LOCK:20,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,PRINT_SCREEN:44,INSERT:45,DELETE:46,ZERO:48,ONE:49,TWO:50,THREE:51,FOUR:52,FIVE:53,SIX:54,SEVEN:55,EIGHT:56,NINE:57,QUESTION_MARK:63,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,META:91,WIN_KEY_RIGHT:92,CONTEXT_MENU:93,NUM_ZERO:96,NUM_ONE:97,NUM_TWO:98,NUM_THREE:99,NUM_FOUR:100,NUM_FIVE:101,NUM_SIX:102,NUM_SEVEN:103,NUM_EIGHT:104,NUM_NINE:105,NUM_MULTIPLY:106,NUM_PLUS:107,NUM_MINUS:109,NUM_PERIOD:110,NUM_DIVISION:111,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,NUMLOCK:144,SEMICOLON:186,DASH:189,EQUALS:187,COMMA:188,PERIOD:190,SLASH:191,APOSTROPHE:192,SINGLE_QUOTE:222,OPEN_SQUARE_BRACKET:219,BACKSLASH:220,CLOSE_SQUARE_BRACKET:221,WIN_KEY:224,MAC_FF_META:224,WIN_IME:229,isTextModifyingKeyEvent:function(e){var t=e.keyCode;if(e.altKey&&!e.ctrlKey||e.metaKey||t>=ie.F1&&t<=ie.F12)return!1;switch(t){case ie.ALT:case ie.CAPS_LOCK:case ie.CONTEXT_MENU:case ie.CTRL:case ie.DOWN:case ie.END:case ie.ESC:case ie.HOME:case ie.INSERT:case ie.LEFT:case ie.MAC_FF_META:case ie.META:case ie.NUMLOCK:case ie.NUM_CENTER:case ie.PAGE_DOWN:case ie.PAGE_UP:case ie.PAUSE:case ie.PRINT_SCREEN:case ie.RIGHT:case ie.SHIFT:case ie.UP:case ie.WIN_KEY:case ie.WIN_KEY_RIGHT:return!1;default:return!0}},isCharacterKey:function(e){if(e>=ie.ZERO&&e<=ie.NINE||e>=ie.NUM_ZERO&&e<=ie.NUM_MULTIPLY||e>=ie.A&&e<=ie.Z||-1!==window.navigator.userAgent.indexOf("WebKit")&&0===e)return!0;switch(e){case ie.SPACE:case ie.QUESTION_MARK:case ie.NUM_PLUS:case ie.NUM_MINUS:case ie.NUM_PERIOD:case ie.NUM_DIVISION:case ie.SEMICOLON:case ie.DASH:case ie.EQUALS:case ie.COMMA:case ie.PERIOD:case ie.SLASH:case ie.APOSTROPHE:case ie.SINGLE_QUOTE:case ie.OPEN_SQUARE_BRACKET:case ie.BACKSLASH:case ie.CLOSE_SQUARE_BRACKET:return!0;default:return!1}}},it="".concat("accept acceptCharset accessKey action allowFullScreen allowTransparency\n    alt async autoComplete autoFocus autoPlay capture cellPadding cellSpacing challenge\n    charSet checked classID className colSpan cols content contentEditable contextMenu\n    controls coords crossOrigin data dateTime default defer dir disabled download draggable\n    encType form formAction formEncType formMethod formNoValidate formTarget frameBorder\n    headers height hidden high href hrefLang htmlFor httpEquiv icon id inputMode integrity\n    is keyParams keyType kind label lang list loop low manifest marginHeight marginWidth max maxLength media\n    mediaGroup method min minLength multiple muted name noValidate nonce open\n    optimum pattern placeholder poster preload radioGroup readOnly rel required\n    reversed role rowSpan rows sandbox scope scoped scrolling seamless selected\n    shape size sizes span spellCheck src srcDoc srcLang srcSet start step style\n    summary tabIndex target title type useMap value width wmode wrap"," ").concat("onCopy onCut onPaste onCompositionEnd onCompositionStart onCompositionUpdate onKeyDown\n    onKeyPress onKeyUp onFocus onBlur onChange onInput onSubmit onClick onContextMenu onDoubleClick\n    onDrag onDragEnd onDragEnter onDragExit onDragLeave onDragOver onDragStart onDrop onMouseDown\n    onMouseEnter onMouseLeave onMouseMove onMouseOut onMouseOver onMouseUp onSelect onTouchCancel\n    onTouchEnd onTouchMove onTouchStart onScroll onWheel onAbort onCanPlay onCanPlayThrough\n    onDurationChange onEmptied onEncrypted onEnded onError onLoadedData onLoadedMetadata\n    onLoadStart onPause onPlay onPlaying onProgress onRateChange onSeeked onSeeking onStalled onSuspend onTimeUpdate onVolumeChange onWaiting onLoad onError").split(/[\s\n]+/);function ir(e,t){return 0===e.indexOf(t)}var ia=b.forwardRef(function(e,t){var n=e.prefixCls,r=e.style,a=e.className,i=e.duration,o=void 0===i?4.5:i,s=e.showProgress,l=e.pauseOnHover,c=void 0===l||l,u=e.eventKey,d=e.content,p=e.closable,m=e.closeIcon,f=void 0===m?"x":m,h=e.props,g=e.onClick,v=e.onNoticeClose,y=e.times,x=e.hovering,j=ew(b.useState(!1),2),w=j[0],S=j[1],N=ew(b.useState(0),2),k=N[0],C=N[1],_=ew(b.useState(0),2),E=_[0],T=_[1],O=x||w,A=o>0&&s,I=function(){v(u)};b.useEffect(function(){if(!O&&o>0){var e=Date.now()-E,t=setTimeout(function(){I()},1e3*o-E);return function(){c&&clearTimeout(t),T(Date.now()-e)}}},[o,O,y]),b.useEffect(function(){if(!O&&A&&(c||0===E)){var e,t=performance.now();return!function n(){cancelAnimationFrame(e),e=requestAnimationFrame(function(e){var r=Math.min((e+E-t)/(1e3*o),1);C(100*r),r<1&&n()})}(),function(){c&&cancelAnimationFrame(e)}}},[o,E,O,A,y]);var P=b.useMemo(function(){return"object"===eS(p)&&null!==p?p:p?{closeIcon:f}:{}},[p,f]),M=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t=!1===n?{aria:!0,data:!0,attr:!0}:!0===n?{aria:!0}:e_({},n);var r={};return Object.keys(e).forEach(function(n){(t.aria&&("role"===n||ir(n,"aria-"))||t.data&&ir(n,"data-")||t.attr&&it.includes(n))&&(r[n]=e[n])}),r}(P,!0),$=100-(!k||k<0?0:k>100?100:k),L="".concat(n,"-notice");return b.createElement("div",tC({},h,{ref:t,className:rm()(L,a,ek({},"".concat(L,"-closable"),p)),style:r,onMouseEnter:function(e){var t;S(!0),null==h||null==(t=h.onMouseEnter)||t.call(h,e)},onMouseLeave:function(e){var t;S(!1),null==h||null==(t=h.onMouseLeave)||t.call(h,e)},onClick:g}),b.createElement("div",{className:"".concat(L,"-content")},d),p&&b.createElement("a",tC({tabIndex:0,className:"".concat(L,"-close"),onKeyDown:function(e){("Enter"===e.key||"Enter"===e.code||e.keyCode===ie.ENTER)&&I()},"aria-label":"Close"},M,{onClick:function(e){e.preventDefault(),e.stopPropagation(),I()}}),P.closeIcon),A&&b.createElement("progress",{className:"".concat(L,"-progress"),max:"100",value:$},$+"%"))}),ii=b.createContext({});let io=function(e){var t=e.children,n=e.classNames;return b.createElement(ii.Provider,{value:{classNames:n}},t)},is=function(e){var t,n,r,a={offset:8,threshold:3,gap:16};return e&&"object"===eS(e)&&(a.offset=null!=(t=e.offset)?t:8,a.threshold=null!=(n=e.threshold)?n:3,a.gap=null!=(r=e.gap)?r:16),[!!e,a]};var il=["className","style","classNames","styles"];let ic=function(e){var t=e.configList,n=e.placement,r=e.prefixCls,a=e.className,i=e.style,o=e.motion,s=e.onAllNoticeRemoved,l=e.onNoticeClose,c=e.stack,u=(0,b.useContext)(ii).classNames,d=(0,b.useRef)({}),p=ew((0,b.useState)(null),2),m=p[0],f=p[1],h=ew((0,b.useState)([]),2),g=h[0],v=h[1],y=t.map(function(e){return{config:e,key:String(e.key)}}),x=ew(is(c),2),j=x[0],w=x[1],S=w.offset,N=w.threshold,k=w.gap,C=j&&(g.length>0||y.length<=N),_="function"==typeof o?o(n):o;return(0,b.useEffect)(function(){j&&g.length>1&&v(function(e){return e.filter(function(e){return y.some(function(t){return e===t.key})})})},[g,y,j]),(0,b.useEffect)(function(){var e,t;j&&d.current[null==(e=y[y.length-1])?void 0:e.key]&&f(d.current[null==(t=y[y.length-1])?void 0:t.key])},[y,j]),b.createElement(ao,tC({key:n,className:rm()(r,"".concat(r,"-").concat(n),null==u?void 0:u.list,a,ek(ek({},"".concat(r,"-stack"),!!j),"".concat(r,"-stack-expanded"),C)),style:i,keys:y,motionAppear:!0},_,{onAllRemoved:function(){s(n)}}),function(e,t){var a=e.config,i=e.className,o=e.style,s=e.index,c=a.key,p=a.times,f=String(c),h=a.className,x=a.style,w=a.classNames,N=a.styles,_=ez(a,il),E=y.findIndex(function(e){return e.key===f}),T={};if(j){var O=y.length-1-(E>-1?E:s-1),A="top"===n||"bottom"===n?"-50%":"0";if(O>0){T.height=C?null==(I=d.current[f])?void 0:I.offsetHeight:null==m?void 0:m.offsetHeight;for(var I,P,M,$,L=0,R=0;R<O;R++)L+=(null==($=d.current[y[y.length-1-R].key])?void 0:$.offsetHeight)+k;var Z=(C?L:O*S)*(n.startsWith("top")?1:-1),F=!C&&null!=m&&m.offsetWidth&&null!=(P=d.current[f])&&P.offsetWidth?((null==m?void 0:m.offsetWidth)-2*S*(O<3?O:3))/(null==(M=d.current[f])?void 0:M.offsetWidth):1;T.transform="translate3d(".concat(A,", ").concat(Z,"px, 0) scaleX(").concat(F,")")}else T.transform="translate3d(".concat(A,", 0, 0)")}return b.createElement("div",{ref:t,className:rm()("".concat(r,"-notice-wrapper"),i,null==w?void 0:w.wrapper),style:e_(e_(e_({},o),T),null==N?void 0:N.wrapper),onMouseEnter:function(){return v(function(e){return e.includes(f)?e:[].concat(eh(e),[f])})},onMouseLeave:function(){return v(function(e){return e.filter(function(e){return e!==f})})}},b.createElement(ia,tC({},_,{ref:function(e){E>-1?d.current[f]=e:delete d.current[f]},prefixCls:r,classNames:w,styles:N,className:rm()(h,null==u?void 0:u.notice),style:x,times:p,key:c,eventKey:c,onNoticeClose:l,hovering:j&&g.length>0})))})};var iu=b.forwardRef(function(e,t){var n=e.prefixCls,r=void 0===n?"rc-notification":n,a=e.container,i=e.motion,o=e.maxCount,s=e.className,l=e.style,c=e.onAllRemoved,u=e.stack,d=e.renderNotifications,p=ew(b.useState([]),2),m=p[0],f=p[1],h=function(e){var t,n=m.find(function(t){return t.key===e});null==n||null==(t=n.onClose)||t.call(n),f(function(t){return t.filter(function(t){return t.key!==e})})};b.useImperativeHandle(t,function(){return{open:function(e){f(function(t){var n,r=eh(t),a=r.findIndex(function(t){return t.key===e.key}),i=e_({},e);return a>=0?(i.times=((null==(n=t[a])?void 0:n.times)||0)+1,r[a]=i):(i.times=0,r.push(i)),o>0&&r.length>o&&(r=r.slice(-o)),r})},close:function(e){h(e)},destroy:function(){f([])}}});var g=ew(b.useState({}),2),v=g[0],y=g[1];b.useEffect(function(){var e={};m.forEach(function(t){var n=t.placement,r=void 0===n?"topRight":n;r&&(e[r]=e[r]||[],e[r].push(t))}),Object.keys(v).forEach(function(t){e[t]=e[t]||[]}),y(e)},[m]);var x=function(e){y(function(t){var n=e_({},t);return(n[e]||[]).length||delete n[e],n})},j=b.useRef(!1);if(b.useEffect(function(){Object.keys(v).length>0?j.current=!0:j.current&&(null==c||c(),j.current=!1)},[v]),!a)return null;var w=Object.keys(v);return(0,es.createPortal)(b.createElement(b.Fragment,null,w.map(function(e){var t=v[e],n=b.createElement(ic,{key:e,configList:t,placement:e,prefixCls:r,className:null==s?void 0:s(e),style:null==l?void 0:l(e),motion:i,onNoticeClose:h,onAllNoticeRemoved:x,stack:u});return d?d(n,{prefixCls:r,key:e}):n})),a)}),id=["getContainer","motion","prefixCls","maxCount","className","style","onAllRemoved","stack","renderNotifications"],ip=function(){return document.body},im=0;let ih=e=>{let[,,,,t]=ag();return t?`${e}-css-var`:""};var ig=eJ(function e(){eV(this,e)}),iv="CALC_UNIT",iy=RegExp(iv,"g");function ix(e){return"number"==typeof e?"".concat(e).concat(iv):e}var ib=function(e){e6(n,e);var t=e7(n);function n(e,r){eV(this,n),ek(e3(a=t.call(this)),"result",""),ek(e3(a),"unitlessCssVar",void 0),ek(e3(a),"lowPriority",void 0);var a,i=eS(e);return a.unitlessCssVar=r,e instanceof n?a.result="(".concat(e.result,")"):"number"===i?a.result=ix(e):"string"===i&&(a.result=e),a}return eJ(n,[{key:"add",value:function(e){return e instanceof n?this.result="".concat(this.result," + ").concat(e.getResult()):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," + ").concat(ix(e))),this.lowPriority=!0,this}},{key:"sub",value:function(e){return e instanceof n?this.result="".concat(this.result," - ").concat(e.getResult()):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," - ").concat(ix(e))),this.lowPriority=!0,this}},{key:"mul",value:function(e){return this.lowPriority&&(this.result="(".concat(this.result,")")),e instanceof n?this.result="".concat(this.result," * ").concat(e.getResult(!0)):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," * ").concat(e)),this.lowPriority=!1,this}},{key:"div",value:function(e){return this.lowPriority&&(this.result="(".concat(this.result,")")),e instanceof n?this.result="".concat(this.result," / ").concat(e.getResult(!0)):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," / ").concat(e)),this.lowPriority=!1,this}},{key:"getResult",value:function(e){return this.lowPriority||e?"(".concat(this.result,")"):this.result}},{key:"equal",value:function(e){var t=this,n=(e||{}).unit,r=!0;return("boolean"==typeof n?r=n:Array.from(this.unitlessCssVar).some(function(e){return t.result.includes(e)})&&(r=!1),this.result=this.result.replace(iy,r?"px":""),void 0!==this.lowPriority)?"calc(".concat(this.result,")"):this.result}}]),n}(ig),ij=function(e){e6(n,e);var t=e7(n);function n(e){var r;return eV(this,n),ek(e3(r=t.call(this)),"result",0),e instanceof n?r.result=e.result:"number"==typeof e&&(r.result=e),r}return eJ(n,[{key:"add",value:function(e){return e instanceof n?this.result+=e.result:"number"==typeof e&&(this.result+=e),this}},{key:"sub",value:function(e){return e instanceof n?this.result-=e.result:"number"==typeof e&&(this.result-=e),this}},{key:"mul",value:function(e){return e instanceof n?this.result*=e.result:"number"==typeof e&&(this.result*=e),this}},{key:"div",value:function(e){return e instanceof n?this.result/=e.result:"number"==typeof e&&(this.result/=e),this}},{key:"equal",value:function(){return this.result}}]),n}(ig);let iw=function(e,t){var n="css"===e?ib:ij;return function(e){return new n(e,t)}},iS=function(e,t){return"".concat([t,e.replace(/([A-Z]+)([A-Z][a-z]+)/g,"$1-$2").replace(/([a-z])([A-Z])/g,"$1-$2")].filter(Boolean).join("-"))},iN=function(e,t,n,r){var a=e_({},t[e]);null!=r&&r.deprecatedTokens&&r.deprecatedTokens.forEach(function(e){var t=ew(e,2),n=t[0],r=t[1];(null!=a&&a[n]||null!=a&&a[r])&&(null!=a[r]||(a[r]=null==a?void 0:a[n]))});var i=e_(e_({},n),a);return Object.keys(i).forEach(function(e){i[e]===t[e]&&delete i[e]}),i};var ik="undefined"!=typeof CSSINJS_STATISTIC,iC=!0;function i_(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(!ik)return Object.assign.apply(Object,[{}].concat(t));iC=!1;var r={};return t.forEach(function(e){"object"===eS(e)&&Object.keys(e).forEach(function(t){Object.defineProperty(r,t,{configurable:!0,enumerable:!0,get:function(){return e[t]}})})}),iC=!0,r}var iE={};function iT(){}let iO=function(e){var t,n=e,r=iT;return ik&&"undefined"!=typeof Proxy&&(t=new Set,n=new Proxy(e,{get:function(e,n){if(iC){var r;null==(r=t)||r.add(n)}return e[n]}}),r=function(e,n){var r;iE[e]={global:Array.from(t),component:e_(e_({},null==(r=iE[e])?void 0:r.component),n)}}),{token:n,keys:t,flush:r}},iA=function(e,t,n){if("function"==typeof n){var r;return n(i_(t,null!=(r=t[e])?r:{}))}return null!=n?n:{}};var iI=new(function(){function e(){eV(this,e),ek(this,"map",new Map),ek(this,"objectIDMap",new WeakMap),ek(this,"nextID",0),ek(this,"lastAccessBeat",new Map),ek(this,"accessBeat",0)}return eJ(e,[{key:"set",value:function(e,t){this.clear();var n=this.getCompositeKey(e);this.map.set(n,t),this.lastAccessBeat.set(n,Date.now())}},{key:"get",value:function(e){var t=this.getCompositeKey(e),n=this.map.get(t);return this.lastAccessBeat.set(t,Date.now()),this.accessBeat+=1,n}},{key:"getCompositeKey",value:function(e){var t=this;return e.map(function(e){return e&&"object"===eS(e)?"obj_".concat(t.getObjectID(e)):"".concat(eS(e),"_").concat(e)}).join("|")}},{key:"getObjectID",value:function(e){if(this.objectIDMap.has(e))return this.objectIDMap.get(e);var t=this.nextID;return this.objectIDMap.set(e,t),this.nextID+=1,t}},{key:"clear",value:function(){var e=this;if(this.accessBeat>1e4){var t=Date.now();this.lastAccessBeat.forEach(function(n,r){t-n>6e5&&(e.map.delete(r),e.lastAccessBeat.delete(r))}),this.accessBeat=0}}}]),e}());let iP=function(){return{}},{genStyleHooks:iM,genComponentStyleHook:i$,genSubStyleComponent:iL}=function(e){var t=e.useCSP,n=void 0===t?iP:t,r=e.useToken,a=e.usePrefix,i=e.getResetStyles,o=e.getCommonStyle,s=e.getCompUnitless;function l(t,s,l){var c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},u=Array.isArray(t)?t:[t,t],d=ew(u,1)[0],p=u.join("-"),m=e.layer||{name:"antd"};return function(e){var t,u,f=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,h=r(),g=h.theme,v=h.realToken,y=h.hashId,x=h.token,j=h.cssVar,w=a(),S=w.rootPrefixCls,N=w.iconPrefixCls,k=n(),C=j?"css":"js",_=(t=function(){var e=new Set;return j&&Object.keys(c.unitless||{}).forEach(function(t){e.add(tm(t,j.prefix)),e.add(tm(t,iS(d,j.prefix)))}),iw(C,e)},u=[C,d,null==j?void 0:j.prefix],b.useMemo(function(){var e=iI.get(u);if(e)return e;var n=t();return iI.set(u,n),n},u)),E="js"===C?{max:Math.max,min:Math.min}:{max:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return"max(".concat(t.map(function(e){return td(e)}).join(","),")")},min:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return"min(".concat(t.map(function(e){return td(e)}).join(","),")")}},T=E.max,O=E.min,A={theme:g,token:x,hashId:y,nonce:function(){return k.nonce},clientOnly:c.clientOnly,layer:m,order:c.order||-999};return"function"==typeof i&&t7(e_(e_({},A),{},{clientOnly:!1,path:["Shared",S]}),function(){return i(x,{prefix:{rootPrefixCls:S,iconPrefixCls:N},csp:k})}),[t7(e_(e_({},A),{},{path:[p,e,N]}),function(){if(!1===c.injectStyle)return[];var t=iO(x),n=t.token,r=t.flush,a=iA(d,v,l),i=".".concat(e),u=iN(d,v,a,{deprecatedTokens:c.deprecatedTokens});j&&a&&"object"===eS(a)&&Object.keys(a).forEach(function(e){a[e]="var(".concat(tm(e,iS(d,j.prefix)),")")});var p=i_(n,{componentCls:i,prefixCls:e,iconCls:".".concat(N),antCls:".".concat(S),calc:_,max:T,min:O},j?a:u),m=s(p,{hashId:y,prefixCls:e,rootPrefixCls:S,iconPrefixCls:N});r(d,u);var h="function"==typeof o?o(p,e,f,c.resetFont):null;return[!1===c.resetStyle?null:h,m]}),y]}}return{genStyleHooks:function(e,t,n,a){var i,o,c,u,d,p,m,f,h,g=Array.isArray(e)?e[0]:e;function v(e){return"".concat(String(g)).concat(e.slice(0,1).toUpperCase()).concat(e.slice(1))}var y=(null==a?void 0:a.unitless)||{},x=e_(e_({},"function"==typeof s?s(e):{}),{},ek({},v("zIndexPopup"),!0));Object.keys(y).forEach(function(e){x[v(e)]=y[e]});var j=e_(e_({},a),{},{unitless:x,prefixToken:v}),w=l(e,t,n,j),S=(i=g,o=n,u=(c=j).unitless,p=void 0===(d=c.injectStyle)||d,m=c.prefixToken,f=c.ignore,h=function(e){var t=e.rootCls,n=e.cssVar,a=void 0===n?{}:n,s=r().realToken;return nt({path:[i],prefix:a.prefix,key:a.key,unitless:u,ignore:f,token:s,scope:t},function(){var e=iA(i,s,o),t=iN(i,s,e,{deprecatedTokens:null==c?void 0:c.deprecatedTokens});return Object.keys(e).forEach(function(e){t[m(e)]=t[e],delete t[e]}),t}),null},function(e){var t=r().cssVar;return[function(n){return p&&t?b.createElement(b.Fragment,null,b.createElement(h,{rootCls:e,cssVar:t,component:i}),n):n},null==t?void 0:t.key]});return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=ew(w(e,t),2)[1],r=ew(S(t),2);return[r[0],n,r[1]]}},genSubStyleComponent:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=l(e,t,n,e_({resetStyle:!1,order:-998},r));return function(e){var t=e.prefixCls,n=e.rootCls,r=void 0===n?t:n;return a(t,r),null}},genComponentStyleHook:l}}({usePrefix:()=>{let{getPrefixCls:e,iconPrefixCls:t}=(0,b.useContext)(ey);return{rootPrefixCls:e(),iconPrefixCls:t}},useToken:()=>{let[e,t,n,r,a]=ag();return{theme:e,realToken:t,hashId:n,token:r,cssVar:a}},useCSP:()=>{let{csp:e}=(0,b.useContext)(ey);return null!=e?e:{}},getResetStyles:(e,t)=>{var n;let r=aj(e);return[r,{"&":r},aw(null!=(n=null==t?void 0:t.prefix.iconPrefixCls)?n:ev)]},getCommonStyle:(e,t,n,r)=>{let a=`[class^="${t}"], [class*=" ${t}"]`,i=n?`.${n}`:a,o={boxSizing:"border-box","&::before, &::after":{boxSizing:"border-box"}},s={};return!1!==r&&(s={fontFamily:e.fontFamily,fontSize:e.fontSize}),{[i]:Object.assign(Object.assign(Object.assign({},s),o),{[a]:o})}},getCompUnitless:()=>ap}),iR=e=>{let{componentCls:t,iconCls:n,boxShadow:r,colorText:a,colorSuccess:i,colorError:o,colorWarning:s,colorInfo:l,fontSizeLG:c,motionEaseInOutCirc:u,motionDurationSlow:d,marginXS:p,paddingXS:m,borderRadiusLG:f,zIndexPopup:h,contentPadding:g,contentBg:v}=e,y=`${t}-notice`,x=new nn("MessageMoveIn",{"0%":{padding:0,transform:"translateY(-100%)",opacity:0},"100%":{padding:m,transform:"translateY(0)",opacity:1}}),b=new nn("MessageMoveOut",{"0%":{maxHeight:e.height,padding:m,opacity:1},"100%":{maxHeight:0,padding:0,opacity:0}}),j={padding:m,textAlign:"center",[`${t}-custom-content`]:{display:"flex",alignItems:"center"},[`${t}-custom-content > ${n}`]:{marginInlineEnd:p,fontSize:c},[`${y}-content`]:{display:"inline-block",padding:g,background:v,borderRadius:f,boxShadow:r,pointerEvents:"all"},[`${t}-success > ${n}`]:{color:i},[`${t}-error > ${n}`]:{color:o},[`${t}-warning > ${n}`]:{color:s},[`${t}-info > ${n},
      ${t}-loading > ${n}`]:{color:l}};return[{[t]:Object.assign(Object.assign({},ax(e)),{color:a,position:"fixed",top:p,width:"100%",pointerEvents:"none",zIndex:h,[`${t}-move-up`]:{animationFillMode:"forwards"},[`
        ${t}-move-up-appear,
        ${t}-move-up-enter
      `]:{animationName:x,animationDuration:d,animationPlayState:"paused",animationTimingFunction:u},[`
        ${t}-move-up-appear${t}-move-up-appear-active,
        ${t}-move-up-enter${t}-move-up-enter-active
      `]:{animationPlayState:"running"},[`${t}-move-up-leave`]:{animationName:b,animationDuration:d,animationPlayState:"paused",animationTimingFunction:u},[`${t}-move-up-leave${t}-move-up-leave-active`]:{animationPlayState:"running"},"&-rtl":{direction:"rtl",span:{direction:"rtl"}}})},{[t]:{[`${y}-wrapper`]:Object.assign({},j)}},{[`${t}-notice-pure-panel`]:Object.assign(Object.assign({},j),{padding:0,textAlign:"start"})}]},iZ=iM("Message",e=>[iR(i_(e,{height:150}))],e=>({zIndexPopup:e.zIndexPopupBase+1e3+10,contentBg:e.colorBgElevated,contentPadding:`${(e.controlHeightLG-e.fontSize*e.lineHeight)/2}px ${e.paddingSM}px`}));var iF=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};let iz={info:b.createElement(a8,null),success:b.createElement(a1,null),error:b.createElement(a5,null),warning:b.createElement(a4,null),loading:b.createElement(a7,null)},iD=e=>{let{prefixCls:t,type:n,icon:r,children:a}=e;return b.createElement("div",{className:rm()(`${t}-custom-content`,`${t}-${n}`)},r||iz[n],b.createElement("span",null,a))},iB={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"}}]},name:"close",theme:"outlined"};var iH=b.forwardRef(function(e,t){return b.createElement(a0,tC({},e,{ref:t,icon:iB}))});function iU(e){let t,n=new Promise(n=>{t=e(()=>{n(!0)})}),r=()=>{null==t||t()};return r.then=(e,t)=>n.then(e,t),r.promise=n,r}var iW=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)0>t.indexOf(r[a])&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};let iq=e=>{let{children:t,prefixCls:n}=e,r=ih(n),[a,i,o]=iZ(n,r);return a(b.createElement(io,{classNames:{list:rm()(i,o,r)}},t))},iG=(e,t)=>{let{prefixCls:n,key:r}=t;return b.createElement(iq,{prefixCls:n,key:r},e)},iK=b.forwardRef((e,t)=>{let{top:n,prefixCls:r,getContainer:a,maxCount:i,duration:o=3,rtl:s,transitionName:l,onAllRemoved:c}=e,{getPrefixCls:u,getPopupContainer:d,message:p,direction:m}=b.useContext(ey),f=r||u("message"),h=b.createElement("span",{className:`${f}-close-x`},b.createElement(iH,{className:`${f}-close-icon`})),[g,v]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.getContainer,n=void 0===t?ip:t,r=e.motion,a=e.prefixCls,i=e.maxCount,o=e.className,s=e.style,l=e.onAllRemoved,c=e.stack,u=e.renderNotifications,d=ez(e,id),p=ew(b.useState(),2),m=p[0],f=p[1],h=b.useRef(),g=b.createElement(iu,{container:m,ref:h,prefixCls:a,motion:r,maxCount:i,className:o,style:s,onAllRemoved:l,stack:c,renderNotifications:u}),v=ew(b.useState([]),2),y=v[0],x=v[1],j=r_(function(e){var t=function(){for(var e={},t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return n.forEach(function(t){t&&Object.keys(t).forEach(function(n){var r=t[n];void 0!==r&&(e[n]=r)})}),e}(d,e);(null===t.key||void 0===t.key)&&(t.key="rc-notification-".concat(im),im+=1),x(function(e){return[].concat(eh(e),[{type:"open",config:t}])})}),w=b.useMemo(function(){return{open:j,close:function(e){x(function(t){return[].concat(eh(t),[{type:"close",key:e}])})},destroy:function(){x(function(e){return[].concat(eh(e),[{type:"destroy"}])})}}},[]);return b.useEffect(function(){f(n())}),b.useEffect(function(){if(h.current&&y.length){var e,t;y.forEach(function(e){switch(e.type){case"open":h.current.open(e.config);break;case"close":h.current.close(e.key);break;case"destroy":h.current.destroy()}}),x(function(n){return e===n&&t||(e=n,t=n.filter(function(e){return!y.includes(e)})),t})}},[y]),[w,g]}({prefixCls:f,style:()=>({left:"50%",transform:"translateX(-50%)",top:null!=n?n:8}),className:()=>rm()({[`${f}-rtl`]:null!=s?s:"rtl"===m}),motion:()=>({motionName:null!=l?l:`${f}-move-up`}),closable:!1,closeIcon:h,duration:o,getContainer:()=>(null==a?void 0:a())||(null==d?void 0:d())||document.body,maxCount:i,onAllRemoved:c,renderNotifications:iG});return b.useImperativeHandle(t,()=>Object.assign(Object.assign({},g),{prefixCls:f,message:p})),v}),iV=0;function iX(e){let t=b.useRef(null);return nd("Message"),[b.useMemo(()=>{let e=e=>{var n;null==(n=t.current)||n.close(e)},n=n=>{if(!t.current){let e=()=>{};return e.then=()=>{},e}let{open:r,prefixCls:a,message:i}=t.current,o=`${a}-notice`,{content:s,icon:l,type:c,key:u,className:d,style:p,onClose:m}=n,f=iW(n,["content","icon","type","key","className","style","onClose"]),h=u;return null==h&&(iV+=1,h=`antd-message-${iV}`),iU(t=>(r(Object.assign(Object.assign({},f),{key:h,content:b.createElement(iD,{prefixCls:a,type:c,icon:l},s),placement:"top",className:rm()(c&&`${o}-${c}`,d,null==i?void 0:i.className),style:Object.assign(Object.assign({},null==i?void 0:i.style),p),onClose:()=>{null==m||m(),t()}})),()=>{e(h)}))},r={open:n,destroy:n=>{var r;void 0!==n?e(n):null==(r=t.current)||r.destroy()}};return["info","success","warning","error","loading"].forEach(e=>{r[e]=(t,r,a)=>{let i,o;return"function"==typeof r?o=r:(i=r,o=a),n(Object.assign(Object.assign({onClose:o,duration:i},t&&"object"==typeof t&&"content"in t?t:{content:t}),{type:e}))}}),r},[]),b.createElement(iK,Object.assign({key:"message-holder"},e,{ref:t}))]}let iJ=null,iQ=e=>e(),iY=[],i0={};function i1(){let{getContainer:e,duration:t,rtl:n,maxCount:r,top:a}=i0,i=(null==e?void 0:e())||document.body;return{getContainer:()=>i,duration:t,rtl:n,maxCount:r,top:a}}let i2=b.forwardRef((e,t)=>{let{messageConfig:n,sync:r}=e,{getPrefixCls:a}=(0,b.useContext)(ey),i=i0.prefixCls||a("message"),o=(0,b.useContext)(eg),[s,l]=iX(Object.assign(Object.assign(Object.assign({},n),{prefixCls:i}),o.message));return b.useImperativeHandle(t,()=>{let e=Object.assign({},s);return Object.keys(e).forEach(t=>{e[t]=function(){return r(),s[t].apply(s,arguments)}}),{instance:e,sync:r}}),l}),i5=b.forwardRef((e,t)=>{let[n,r]=b.useState(i1),a=()=>{r(i1)};b.useEffect(a,[]);let i=aE(),o=i.getRootPrefixCls(),s=i.getIconPrefixCls(),l=i.getTheme(),c=b.createElement(i2,{ref:t,sync:a,messageConfig:n});return b.createElement(aO,{prefixCls:o,iconPrefixCls:s,theme:l},i.holderRender?i.holderRender(c):c)});function i3(){if(!iJ){let e=document.createDocumentFragment(),t={fragment:e};iJ=t,iQ(()=>{aB(b.createElement(i5,{ref:e=>{let{instance:n,sync:r}=e||{};Promise.resolve().then(()=>{!t.instance&&n&&(t.instance=n,t.sync=r,i3())})}}),e)});return}iJ.instance&&(iY.forEach(e=>{let{type:t,skipped:n}=e;if(!n)switch(t){case"open":iQ(()=>{let t=iJ.instance.open(Object.assign(Object.assign({},i0),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":iQ(()=>{null==iJ||iJ.instance.destroy(e.key)});break;default:iQ(()=>{var n;let r=(n=iJ.instance)[t].apply(n,eh(e.args));null==r||r.then(e.resolve),e.setCloseFn(r)})}}),iY=[])}let i4={open:function(e){let t=iU(t=>{let n,r={type:"open",config:e,resolve:t,setCloseFn:e=>{n=e}};return iY.push(r),()=>{n?iQ(()=>{n()}):r.skipped=!0}});return i3(),t},destroy:e=>{iY.push({type:"destroy",key:e}),i3()},config:function(e){i0=Object.assign(Object.assign({},i0),e),iQ(()=>{var e;null==(e=null==iJ?void 0:iJ.sync)||e.call(iJ)})},useMessage:function(e){return iX(e)},_InternalPanelDoNotUseOrYouWillBeFired:e=>{let{prefixCls:t,className:n,type:r,icon:a,content:i}=e,o=iF(e,["prefixCls","className","type","icon","content"]),{getPrefixCls:s}=b.useContext(ey),l=t||s("message"),c=ih(l),[u,d,p]=iZ(l,c);return u(b.createElement(ia,Object.assign({},o,{prefixCls:l,className:rm()(n,d,`${l}-notice-pure-panel`,p,c),eventKey:"pure",duration:null,content:b.createElement(iD,{prefixCls:l,type:r,icon:a},i)})))}};["success","info","warning","error","loading"].forEach(e=>{i4[e]=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];aE();let a=iU(t=>{let r,a={type:e,args:n,resolve:t,setCloseFn:e=>{r=e}};return iY.push(a),()=>{r?iQ(()=>{r()}):a.skipped=!0}});return i3(),a}});class i6 extends Error{constructor(e){super("Unauthorized"),this.name="UnauthorizedError",this.message=e}}let i8=class{async baseRequest(e,t){let n=await fetch(`${this.options.baseURL}${e}`,{...t,headers:{...t.headers,Authorization:`Bearer ${this.options.apiKey}`,Accept:"text/event-stream, application/json"}});if(n.headers.get("X-Version")){let e=n.headers.get("X-Version");e&&e!==M.version&&(M.version=e)}if(401===n.status)throw i4.error("未授权, 请检查你的配置"),new i6("Unauthorized");return n}async jsonRequest(e,t){let n=await this.baseRequest(e,{...t,headers:{...t.headers,"Content-Type":"application/json"}}),r=await n.json();return 0===r.code?r.data:401==r.code?r:void i4.error(r.msg)}async get(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t?`?${new URLSearchParams(t).toString()}`:"";return await this.jsonRequest(`${e}${r}`,{method:"GET",headers:n})}async post(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"POST",body:JSON.stringify(t),headers:n})}async delete(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"DELETE",body:JSON.stringify(t),headers:n})}constructor(e){var t,n;n=void 0,(t="options")in this?Object.defineProperty(this,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):this[t]=n,this.options=e}};function i9(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class i7{updateOptions(e){this.options=e,this.baseRequest=new i8({baseURL:e.apiBase,apiKey:e.apiKey,appId:e.appId})}async getAppInfo(){return this.baseRequest.get("/info")}async getAppMeta(){return this.baseRequest.get("/meta")}getConversationList(e){return this.baseRequest.get("/conversations",{user:this.options.user,limit:((null==e?void 0:e.limit)||100).toString(),appId:this.options.appId})}sendMessage(e){return e.appId=this.options.appId,this.baseRequest.baseRequest("/chat-messages",{method:"POST",body:JSON.stringify(e),headers:{"Content-Type":"application/json"}})}async stopTask(e){return this.baseRequest.post(`/chat-messages/${e}/stop`,{user:this.options.user,appId:this.options.appId})}async uploadFile(e){let t=new FormData;return t.append("file",e),t.append("user",this.options.user),t.append("appId",this.options.appId),this.baseRequest.baseRequest("/files/upload",{method:"POST",body:t}).then(e=>e.json())}async getNextSuggestions(e){return this.baseRequest.get(`/messages/${e.message_id}/suggested`,{appId:this.options.appId,user:this.options.user})}feedbackMessage(e){let{messageId:t,...n}=e;return this.baseRequest.post(`/messages/${t}/feedbacks`,{...n,user:this.options.user,appId:this.options.appId})}async text2Audio(e){return this.baseRequest.baseRequest("/text-to-audio",{method:"POST",body:JSON.stringify({...e,user:this.options.user}),headers:{"Content-Type":"application/json"}})}async audio2Text(e){let t=new FormData;return t.append("file",e),t.append("user",this.options.user),this.baseRequest.baseRequest("/audio-to-text",{method:"POST",body:t}).then(e=>e.json())}constructor(e){i9(this,"options",void 0),i9(this,"baseRequest",void 0),i9(this,"getAppParameters",()=>this.baseRequest.get("/parameters",{appId:this.options.appId})),i9(this,"renameConversation",e=>{let{conversation_id:t,...n}=e;return this.baseRequest.post(`/conversations/${t}/name`,{...n,user:this.options.user,appId:this.options.appId})}),i9(this,"deleteConversation",(e,t)=>this.baseRequest.delete(`/conversations/${e}?appId=${t}`,{user:this.options.user})),i9(this,"getConversationHistory",e=>this.baseRequest.get("/messages",{user:this.options.user,conversation_id:e,appId:this.options.appId})),this.options=e,this.baseRequest=new i8({baseURL:e.apiBase,apiKey:e.apiKey,appId:e.appId})}}let oe=e=>new i7(e);var ot=((m={}).MESSAGE="message",m.AGENT_MESSAGE="agent_message",m.AGENT_THOUGHT="agent_thought",m.MESSAGE_FILE="message_file",m.MESSAGE_END="message_end",m.TTS_MESSAGE="tts_message",m.TTS_MESSAGE_END="tts_message_end",m.MESSAGE_REPLACE="message_replace",m.ERROR="error",m.PING="ping",m.WORKFLOW_STARTED="workflow_started",m.WORKFLOW_FINISHED="workflow_finished",m.WORKFLOW_NODE_STARTED="node_started",m.WORKFLOW_NODE_FINISHED="node_finished",m),on=n(45186),or=n(62472),oa=n(63522),oi=n(44312),oo=n(79016),os=n(707),ol=n(83882),oc=n(17148),ou=n(55879),od=n(87092);function op(e){let{formInstance:t}=e,n=on.Z.useWatch("answerForm.enabled",t);return(0,y.jsxs)(on.Z,{autoComplete:"off",form:t,labelAlign:"left",labelCol:{span:5},initialValues:{"answerForm.enabled":!1},children:[(0,y.jsxs)("div",{className:"text-base mb-3 flex items-center",children:[(0,y.jsx)("div",{className:"h-4 w-1 bg-primary rounded"}),(0,y.jsx)("div",{className:"ml-2 font-semibold",children:"请求配置"})]}),(0,y.jsx)(on.Z.Item,{label:"API Base",name:"apiBase",rules:[{required:!0}],tooltip:"Dify API 的域名+版本号前缀，如 https://api.dify.ai/v1",required:!0,children:(0,y.jsx)(oc.Z,{autoComplete:"new-password",placeholder:"请输入 API BASE"})}),(0,y.jsx)(on.Z.Item,{label:"API Secret",name:"apiKey",tooltip:"Dify App 的 API Secret (以 app- 开头)",rules:[{required:!0}],required:!0,children:(0,y.jsx)(oc.Z.Password,{autoComplete:"new-password",placeholder:"请输入 API Secret"})}),(0,y.jsxs)("div",{className:"text-base mb-3 flex items-center",children:[(0,y.jsx)("div",{className:"h-4 w-1 bg-primary rounded"}),(0,y.jsx)("div",{className:"ml-2 font-semibold",children:"更多配置"})]}),(0,y.jsx)(on.Z.Item,{label:"表单回复",name:"answerForm.enabled",tooltip:"当工作流需要回复表单给用户填写时，建议开启此功能",rules:[{required:!0}],required:!0,children:(0,y.jsx)(od.Z,{placeholder:"请选择",options:[{label:"启用",value:!0},{label:"禁用",value:!1}]})}),n?(0,y.jsx)(on.Z.Item,{label:"提交消息文本",name:"answerForm.feedbackText",tooltip:"当启用表单回复时，用户填写表单并提交后，默认会以用户角色将填写的表单数据作为消息文本发送，如果配置了此字段，将会固定展示配置的字段值",children:(0,y.jsx)(oc.Z,{placeholder:"请输入提交消息文本"})}):null]})}function om(e){var t;let{activeAppId:n,getAppList:r,appListLoading:a,appList:i,onDeleteSuccess:o,...s}=e,{user:l,appService:c}=C(),[u,d]=(0,b.useState)(),[p,m]=(0,b.useState)(!1),[f]=on.Z.useForm(),[h,g]=(0,b.useState)(),v=I(),{t:x}=(0,ou.$G)(),[j,w]=(0,b.useState)(!1),{runAsync:S,loading:N}=(0,ea.Z)(async e=>c.addApp(e),{manual:!0,onSuccess:()=>{m(!1),B.ZP.success(x("app.addAppConfigSuccess")),r()}}),{runAsync:k,loading:_}=(0,ea.Z)(async e=>c.updateApp(e),{manual:!0,onSuccess:()=>{m(!1),B.ZP.success(x("app.updateAppConfigSuccess")),r()}});(0,b.useEffect)(()=>{p||f.resetFields()},[p]);let E=null==i?void 0:i.find(e=>e.id===u);return(0,y.jsxs)(or.Z,{width:700,title:"应用配置管理",...s,children:[(0,y.jsxs)("div",{className:"w-full h-full overflow-hidden flex flex-col",children:[(0,y.jsx)("div",{className:"pb-3 flex-1 overflow-y-auto",children:(0,y.jsx)(V.Z,{spinning:a,children:(0,y.jsx)(oa.Z,{gutter:16*!v,className:"w-full",children:(null==i?void 0:i.length)?null==i?void 0:i.map(e=>{var t;return(0,y.jsx)(oi.Z,{span:v?24:12,children:(0,y.jsxs)("div",{className:"p-3 bg-white mb-3 border border-solid border-gray-200 rounded-lg cursor-pointer hover:border-primary hover:text-primary",onClick:()=>{var t,n;d(e.id),f.setFieldsValue({apiBase:e.requestConfig.apiBase,apiKey:e.requestConfig.apiKey,"answerForm.enabled":(null==(t=e.answerForm)?void 0:t.enabled)||!1,"answerForm.feedbackText":(null==(n=e.answerForm)?void 0:n.feedbackText)||""}),g("edit"),m(!0)},children:[(0,y.jsxs)("div",{className:"w-full flex items-center overflow-hidden",children:[(0,y.jsxs)("div",{className:"flex-1 font-semibold truncate",children:[n===e.id&&"【当前】",e.info.name]}),(0,y.jsx)(oo.Z,{className:"inline-flex items-center",children:(0,y.jsx)(os.Z,{onPopupClick:e=>e.stopPropagation(),cancelText:"取消",okText:"确定",title:"确定删除应用吗？",onConfirm:async()=>{await c.deleteApp(e.id),B.ZP.success("删除应用成功"),r(),null==o||o(e.id)},children:(0,y.jsx)(eo.Z,{onClick:e=>e.stopPropagation(),className:"p-0 text-red-500"})})})]}),(0,y.jsx)("div",{title:e.info.description,className:"truncate text-sm mt-2 text-desc h-6 leading-6",children:e.info.description}),(0,y.jsxs)("div",{className:"mt-3 text-desc truncate",title:e.info.tags.join(", "),children:["标签：",(null==(t=e.info.tags)?void 0:t.length)?e.info.tags.join(", "):(0,y.jsx)(y.Fragment,{children:"无"})]})]})},e.id)}):(0,y.jsx)(ol.Z,{className:"mx-auto",description:"暂无应用"})})})}),(0,y.jsx)(J.ZP,{type:"primary",size:"large",block:!0,onClick:()=>{d(""),g("create"),f.resetFields(),m(!0)},children:"添加应用"})]}),(0,y.jsxs)(or.Z,{width:600,title:`${"create"===h?"添加应用配置":`应用配置详情 - ${null==E?void 0:E.info.name}`}`,open:p,onClose:()=>m(!1),extra:(0,y.jsxs)(oo.Z,{children:[(0,y.jsx)(J.ZP,{onClick:()=>m(!1),children:"取消"}),(0,y.jsx)(J.ZP,{type:"primary",loading:N||_||j,onClick:async()=>{await f.validateFields(),w(!0);try{let e=f.getFieldsValue(),t=null==i?void 0:i.find(e=>e.id===u),n=new i7({user:l,apiBase:e.apiBase,apiKey:e.apiKey}),a={info:await n.getAppInfo(),requestConfig:{apiBase:e.apiBase,apiKey:e.apiKey},answerForm:{enabled:e["answerForm.enabled"],feedbackText:e["answerForm.feedbackText"]}};"edit"===h?await k({id:t.id,...a}):await S({id:Math.random().toString(),...a}),r()}catch(e){console.error("保存应用配置失败",e),B.ZP.error(`保存应用配置失败: ${e}`)}finally{w(!1)}},children:"create"===h?"确定":"更新"})]}),children:["edit"===h?(0,y.jsxs)(on.Z,{labelAlign:"left",labelCol:{span:5},layout:"horizontal",children:[(0,y.jsxs)("div",{className:"text-base mb-3 flex items-center",children:[(0,y.jsx)("div",{className:"h-4 w-1 bg-primary rounded"}),(0,y.jsx)("div",{className:"ml-2 font-semibold",children:"基本信息"})]}),(0,y.jsx)(on.Z.Item,{label:"应用名称",children:(0,y.jsx)(oc.Z,{disabled:!0,value:null==E?void 0:E.info.name})}),(0,y.jsx)(on.Z.Item,{label:"应用描述",children:(0,y.jsx)(oc.Z,{disabled:!0,value:null==E?void 0:E.info.name})}),(0,y.jsx)(on.Z.Item,{label:"应用标签",children:(null==E||null==(t=E.info.tags)?void 0:t.length)?(0,y.jsx)("div",{className:"text-default",children:E.info.tags.join(", ")}):(0,y.jsx)(y.Fragment,{children:"无"})})]}):null,(0,y.jsx)(op,{formInstance:f})]})]})}var of=n(45549),oh=n(52503),og=n(63627),ov=n(81088),oy=n(44416),ox=n(47041),ob=n(79183),oj=n(51850),ow=n(53509),oS=n(8394);let oN=new Map;oN.set("document",["txt","md","mdx","markdown","pdf","html","xlsx","xls","doc","docx","csv","eml","msg","pptx","ppt","xml","epub"]),oN.set("image",["jpg","jpeg","png","gif","webp","svg"]),oN.set("audio",["mp3","m4a","wav","webm","amr"]),oN.set("video",["mp4","mov","mpeg","mpga"]),oN.set("custom",[]);let ok=e=>e.split(".").pop(),oC=e=>{let t=e.split(".").pop(),n=null;return oN.forEach((e,r)=>{e.indexOf(t)>-1&&(n=r)}),n},o_=e=>e>1048576?`${(e/1024/1024).toFixed(2)} MB`:`${(e/1024).toFixed(2)} KB`,oE=e=>{var t;let{content:n,isRequesting:r,onChange:a,onSubmit:i,className:o,onCancel:s,uploadFileApi:l,appParameters:c,onFocus:u}=e,[d,p]=(0,b.useState)(!1),[m,f]=(0,b.useState)([]),[h,g]=(0,b.useState)(new Map),v=(0,b.useMemo)(()=>{if(!(null==c?void 0:c.file_upload.enabled))return[];let e=[];return c.file_upload.allowed_file_types.forEach(t=>{oN.get(t)&&e.push(...oN.get(t)||[])}),e},[null==c?void 0:c.file_upload]),x=async e=>{let t=[...m],n={uid:e.uid,name:e.name,status:"uploading",size:e.size,type:e.type,originFileObj:e},{clear:r}=(()=>{let e=0;f([...t,{...n,percent:e}]);let r=setInterval(()=>{if(e>=99)return void clearInterval(r);e+=1,f([...t,{...n,percent:e}])},100);return{clear:()=>clearInterval(r)}})(),a=await l(e);if((null==a?void 0:a.code)!==0)return void B.ZP.error(null==a?void 0:a.msg);r(),f([...t,{...n,percent:100,status:"done"}]),g(t=>{var n;let r=new Map(t);return r.set(null==e?void 0:e.uid,null==a||null==(n=a.data)?void 0:n.id),r})},j=(0,b.useRef)(null),w=(0,y.jsx)(oj.Z.Header,{title:"上传文件",open:d,onOpenChange:p,styles:{content:{padding:0}},children:(0,y.jsx)(ow.Z,{beforeUpload:async e=>{let t=ok(e.name);return v.length>0&&!v.includes(t)?B.ZP.error(`不支持的文件类型: ${t}`):x(e),!1},items:m,placeholder:e=>"drop"===e?{title:"Drop file here"}:{icon:(0,y.jsx)(ox.Z,{}),title:"点击或拖拽文件到此区域上传",description:(0,y.jsxs)("div",{children:["支持的文件类型：",v.join(", ")]})},getDropContainer:()=>{var e;return null==(e=j.current)?void 0:e.nativeElement},onRemove:e=>{f(t=>t.filter(t=>t.uid!==e.uid))}})});return(0,y.jsx)(oj.Z,{allowSpeech:null==c?void 0:c.speech_to_text.enabled,header:w,value:n,onChange:a,onFocus:u,prefix:(null==c||null==(t=c.file_upload)?void 0:t.enabled)?(0,y.jsx)(oS.Z,{dot:m.length>0&&!d,children:(0,y.jsx)(J.ZP,{onClick:()=>p(!d),icon:(0,y.jsx)(ob.Z,{})})}):null,style:{boxShadow:"0px -2px 12px 4px #efefef"},loading:r,className:o,onSubmit:async e=>{if((null==m?void 0:m.length)&&!m.every(e=>"done"===e.status))return void B.ZP.error("请等待所有文件上传完成");await i(e,{files:(null==m?void 0:m.map(e=>{let t=oC(e.name);return{...e,type:t||"document",transfer_method:"local_file",upload_file_id:h.get(e.uid)}}))||[]}),f([]),p(!1)},onCancel:s})};var oT=n(82120),oO=n(810),oA=n(96002),oI=n(66642),oP=n(83191);n(98647);var oM=n(13224),o$=n(12599),oL=n(6785),oR=n(96858),oZ=n(78869),oF=n(30322),oz=n(92445),oD=(n(27276),n(259)),oB=n(30781),oH=n(45709),oU=n(17118),oW=n(74266),oq=n(73790);s=null,"undefined"!=typeof window&&(s=oB.Z.mermaidAPI);let oG=e=>{let t=new Blob([new TextEncoder().encode(e)],{type:"image/svg+xml;charset=utf-8"});return new Promise((e,n)=>{let r=new FileReader;r.onloadend=()=>e(r.result),r.onerror=n,r.readAsDataURL(t)})},oK=e=>{let{ref:t,...n}=e,[r,a]=(0,b.useState)(null),[i,o]=(0,b.useState)("classic"),l=(0,oH.Z)(n.PrimitiveCode),[c,u]=(0,b.useState)(!0),d=(0,b.useRef)(0),[p,m]=(0,b.useState)(""),[f,h]=(0,b.useState)(""),g=(0,b.useCallback)(async e=>{a(null),u(!0);try{if("undefined"!=typeof window&&s){let t=await s.render("flowchart",e),n=await oG(t.svg.replaceAll("<br>","<br/>"));a(n),u(!1)}}catch(e){l===n.PrimitiveCode&&(u(!1),m(e.message))}},[n.PrimitiveCode]);return(0,b.useEffect)(()=>{"undefined"!=typeof window&&(oB.Z.initialize({startOnLoad:!0,theme:"neutral",look:i,flowchart:{htmlLabels:!0,useMaxWidth:!0}}),g(n.PrimitiveCode))},[i]),(0,b.useEffect)(()=>{d.current&&window.clearTimeout(d.current),d.current=window.setTimeout(()=>{g(n.PrimitiveCode)},300)},[n.PrimitiveCode]),(0,y.jsxs)("div",{ref:t,children:[(0,y.jsx)("div",{className:"msh-segmented msh-segmented-sm css-23bs09 css-var-r1",children:(0,y.jsx)("div",{className:"msh-segmented-group",children:(0,y.jsx)("label",{className:"msh-segmented-item m-2 flex w-[200px] items-center space-x-1",children:(0,y.jsxs)(oq.ZP.Group,{value:i,buttonStyle:"solid",optionType:"button",onChange:e=>{"handDrawn"===e.target.value?o("handDrawn"):o("classic")},children:[(0,y.jsx)(oq.ZP,{value:"classic",children:"经典"}),(0,y.jsx)(oq.ZP,{value:"handDrawn",children:"手绘"})]})})})}),r&&(0,y.jsx)("div",{className:"mermaid object-fit: cover h-auto w-full cursor-pointer",onClick:()=>h(r),children:r&&(0,y.jsx)("img",{src:r,alt:"mermaid_chart"})}),c&&(0,y.jsx)("div",{className:"px-[26px] py-4",children:(0,y.jsx)(oW.Z,{})}),p&&(0,y.jsxs)("div",{className:"px-[26px] py-4",children:[(0,y.jsx)(oU.Z,{className:"h-6 w-6 text-red-500"}),"\xa0",p]})]})};oK.displayName="Flowchart";let oV=e=>{var t;return"string"==typeof e?e.includes("[ENDTHINKFLAG]"):Array.isArray(e)?e.some(e=>oV(e)):null!=e&&null!=(t=e.props)&&!!t.children&&oV(e.props.children)},oX=e=>{var t;return"string"==typeof e?e.replace("[ENDTHINKFLAG]",""):Array.isArray(e)?e.map(e=>oX(e)):(null==e||null==(t=e.props)?void 0:t.children)?b.cloneElement(e,{...e.props,children:oX(e.props.children)}):e},oJ=e=>{let[t]=(0,b.useState)(Date.now()),[n,r]=(0,b.useState)(0),[a,i]=(0,b.useState)(!1),o=(0,b.useRef)();return(0,b.useEffect)(()=>(o.current=setInterval(()=>{a||r(Math.floor((Date.now()-t)/100)/10)},100),()=>{o.current&&clearInterval(o.current)}),[t,a]),(0,b.useEffect)(()=>{oV(e)&&(i(!0),o.current&&clearInterval(o.current))},[e]),{elapsedTime:n,isComplete:a}},oQ=e=>{let{children:t,...n}=e,{elapsedTime:r,isComplete:a}=oJ(t),i=oX(t);return n["data-think"]?(0,y.jsxs)("details",{...!a&&{open:!0},className:"group",children:[(0,y.jsx)("summary",{className:"flex cursor-pointer select-none list-none items-center whitespace-nowrap font-bold text-gray-500",children:(0,y.jsxs)("div",{className:"flex shrink-0 items-center",children:[(0,y.jsx)("svg",{className:"mr-2 h-3 w-3 transition-transform duration-500 group-open:rotate-90",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,y.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),a?`已深度思考(${r.toFixed(1)}s)`:`深度思考中...(${r.toFixed(1)}s)`]})}),(0,y.jsx)("div",{className:"border-l mt-1 rounded-lg border-gray-300 text-gray-500 p-3 bg-gray-50",children:i})]}):(0,y.jsx)("details",{...n,children:t})};var oY=n(69267),o0=n(12737);let o1=e=>{let{content:t}=e,n=(0,b.useRef)(null),[r,a]=(0,b.useState)(""),[i,o]=(0,b.useState)({width:"undefined"!=typeof window?window.innerWidth:0,height:"undefined"!=typeof window?window.innerHeight:0}),s=e=>{let t=new XMLSerializer().serializeToString(e),n=Buffer.from(t).toString("base64");return`data:image/svg+xml;base64,${n}`};return(0,b.useEffect)(()=>{let e=()=>{o({width:window.innerWidth,height:window.innerHeight})};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,b.useEffect)(()=>{if(n.current)try{n.current.innerHTML="";let e=(0,oY.Wj)().addTo(n.current),r=new DOMParser().parseFromString(t,"image/svg+xml").documentElement;if(!(r instanceof SVGElement))throw Error("Invalid SVG content");let i=Number.parseInt(r.getAttribute("width")||"400",10),o=Number.parseInt(r.getAttribute("height")||"600",10);e.viewbox(0,0,i,o),n.current.style.width=`${Math.min(i,298)}px`,e.svg(o0.Z.sanitize(t)).click(()=>{a(s(r))})}catch(e){n.current&&(n.current.innerHTML='<span style="padding: 1rem;">Error rendering SVG. Wait for the image content to complete.</span>')}},[t,i]),(0,y.jsx)(y.Fragment,{children:(0,y.jsx)("div",{ref:n,style:{maxHeight:"80vh",display:"flex",justifyContent:"center",alignItems:"center",cursor:"pointer",wordBreak:"break-word",whiteSpace:"normal",margin:"0 auto"}})})};var o2=n(5914),o5=n(53075),o3=n(92752),o4=n(1274),o6=n(29981),o8=n.n(o6),o9=n(64919),o7=n(56366),se=n(75510);let st=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,se.m6)(o8()(t))};(0,o9.Z)({html:!0,breaks:!0}).use(o4.Z).use(o7.Z,{delimiters:[{left:"\\[",right:"\\]",display:!0},{left:"\\(",right:"\\)",display:!1},{left:"$$",right:"$$",display:!1}]});let sn=e=>{let{isSVG:t,setIsSVG:n}=e;return(0,y.jsx)(J.ZP,{onClick:()=>{n(e=>!e)},children:(0,y.jsx)("div",{className:st("h-4 w-4"),children:(0,y.jsx)(o3.Z,{})})})};var sr=((f=sr||{}).TEXT="text",f.PASSWORD="password",f.EMAIL="email",f.NUMBER="number",f.DATE="date",f.TIME="time",f.DATETIME="datetime",f.CHECKBOX="checkbox",f.SELECT="select",f);let sa=e=>{let{node:t,onSend:n}=e,[r,a]=(0,b.useState)({});(0,b.useEffect)(()=>{let e={};t.children.forEach(t=>{["input","textarea"].includes(t.tagName)&&(e[t.properties.name]=t.properties.value)}),a(e)},[t.children]);let i=e=>{let t={};return e.forEach(e=>{["input","textarea"].includes(e.tagName)&&(t[e.properties.name]=r[e.properties.name])}),t},o=e=>{e.preventDefault();let r=t.properties.dataFormat||"text",a=i(t.children);if("json"===r)console.log("即将发送",r,a),null==n||n(a);else{let e=Object.entries(a).map(e=>{let[t,n]=e;return`${t}: ${n}`}).join("\n");null==n||n(e)}};return(0,y.jsx)("form",{autoComplete:"off",className:"flex flex-col self-stretch pb-3",onSubmit:e=>{e.preventDefault(),e.stopPropagation()},children:t.children.filter(e=>"element"===e.type).map((e,t)=>{var n,i;if("label"===e.tagName)return(0,y.jsx)("label",{htmlFor:e.properties.for,className:"system-md-semibold my-2 text-text-secondary",children:(null==(n=e.children[0])?void 0:n.value)||""},t);if("input"===e.tagName&&Object.values(sr).includes(e.properties.type))return(0,y.jsx)(oc.Z,{type:e.properties.type,name:e.properties.name,placeholder:e.properties.placeholder,value:r[e.properties.name],onChange:t=>{a(n=>({...n,[e.properties.name]:t.target.value}))}},t);if("textarea"===e.tagName)return(0,y.jsx)(oc.Z.TextArea,{name:e.properties.name,placeholder:e.properties.placeholder,value:r[e.properties.name],onChange:t=>{a(n=>({...n,[e.properties.name]:t.target.value}))}},t);if("button"===e.tagName){let n=e.properties.dataVariant;return e.properties.dataSize,(0,y.jsx)(J.ZP,{type:"primary",variant:n,className:"mt-4",onClick:o,children:(null==(i=e.children[0])?void 0:i.value)||""},t)}return(0,y.jsxs)("p",{children:["Unsupported tag: ",e.tagName]},t)})})};sa.displayName="MarkdownForm";let si={sql:"SQL",javascript:"JavaScript",java:"Java",typescript:"TypeScript",vbscript:"VBScript",css:"CSS",html:"HTML",xml:"XML",php:"PHP",python:"Python",yaml:"Yaml",mermaid:"Mermaid",markdown:"MarkDown",makefile:"MakeFile",echarts:"ECharts",shell:"Shell",powershell:"PowerShell",json:"JSON",latex:"Latex",svg:"SVG"},so=e=>e?e in si?si[e]:e.charAt(0).toUpperCase()+e.substring(1):"Plain",ss=e=>{if("string"!=typeof e)return e;let t=/```[\s\S]*?```/g,n=e.match(t)||[],r=e.replace(t,"CODE_BLOCK_PLACEHOLDER");return r=(0,oD.Z)([e=>e.replace(/\\\[(.*?)\\\]/g,(e,t)=>`$$${t}$$`),e=>e.replace(/\\\[([\s\S]*?)\\\]/g,(e,t)=>`$$${t}$$`),e=>e.replace(/\\\((.*?)\\\)/g,(e,t)=>`$$${t}$$`),e=>e.replace(/(^|[^\\])\$(.+?)\$/g,(e,t,n)=>`${t}$${n}$`)])(r),n.forEach(e=>{r=r.replace("CODE_BLOCK_PLACEHOLDER",e)}),r},sl=e=>(0,oD.Z)([e=>e.replace("<think>\n","<details data-think=true>\n"),e=>e.replace("\n</think>","\n[ENDTHINKFLAG]</details>")])(e),sc=(0,b.memo)(e=>{let{inline:t,className:n,children:r,...a}=e,[i,o]=(0,b.useState)(!0),s=/language-(\w+)/.exec(n||""),l=null==s?void 0:s[1],c=so(l||""),u=(0,b.useMemo)(()=>{if("echarts"===l)try{return JSON.parse(String(r).replace(/\n$/,""))}catch(e){}return JSON.parse('{"title":{"text":"ECharts error - Wrong JSON format."}}')},[l,r]),d=(0,b.useMemo)(()=>{let e=String(r).replace(/\n$/,"");return"mermaid"===l&&i?(0,y.jsx)(oK,{PrimitiveCode:e}):"echarts"===l?(0,y.jsx)("div",{style:{minHeight:"350px",minWidth:"100%",overflowX:"scroll"},children:(0,y.jsx)(sh,{children:(0,y.jsx)(oP.Z,{option:u,style:{minWidth:"700px"}})})}):"svg"===l&&i?(0,y.jsx)(sh,{children:(0,y.jsx)(o1,{content:e})}):(0,y.jsx)(oF.Z,{...a,style:oz.Z,customStyle:{paddingLeft:12,borderBottomLeftRadius:"10px",borderBottomRightRadius:"10px",backgroundColor:"var(--color-components-input-bg-normal)"},language:null==s?void 0:s[1],showLineNumbers:!0,PreTag:"div",children:e})},[l,s,a,r,u,i]);return t||!s?(0,y.jsx)("code",{...a,className:n,children:r}):(0,y.jsxs)("div",{className:"relative",children:[(0,y.jsxs)("div",{className:"flex h-8 items-center justify-between rounded-t-[10px] border-b border-divider-subtle bg-components-input-bg-normal p-1 pl-3",children:[(0,y.jsx)("div",{className:"text-gray-700",children:c}),(0,y.jsxs)("div",{className:"flex items-center gap-1",children:[["mermaid","svg"].includes(l)&&(0,y.jsx)(sn,{isSVG:i,setIsSVG:o}),(0,y.jsx)(J.ZP,{children:(0,y.jsx)(o2.Z,{onClick:async()=>{await (0,o5.v)(String(r).replace(/\n$/,"")),B.ZP.success("复制成功")}})})]})]}),d]})});sc.displayName="CodeBlock";let su=(0,b.memo)(e=>{var t;let{node:n}=e,r=(null==(t=n.children[0])?void 0:t.value)||"";return`<script>${r}</script>`});su.displayName="ScriptBlock";let sd=e=>{let{node:t}=e,n=t.children;return n&&n[0]&&"tagName"in n[0]&&"img"===n[0].tagName?(0,y.jsx)(y.Fragment,{children:Array.isArray(e.children)?(0,y.jsx)("p",{children:e.children.slice(1)}):null}):(0,y.jsx)("p",{children:e.children})},sp=e=>{let{src:t}=e;return(0,y.jsx)("img",{src:t})},sm=e=>{var t;let{node:n,...r}=e;return(0,y.jsx)("a",{...r,target:"_blank",className:"cursor-pointer underline !decoration-primary-700 decoration-dashed",children:n.children[0]?null==(t=n.children[0])?void 0:t.value:"Download"})};function sf(e){let{onSubmit:t}=e,n=(0,oD.Z)([sl,ss])(e.markdownText);return(0,y.jsx)("div",{className:"text-default",children:(0,y.jsx)(oI.UG,{remarkPlugins:[oR.Z,[oM.Z,{singleDollarTextMath:!1}],o$.Z],rehypePlugins:[oL.Z,oZ.Z,()=>e=>{let t=e=>{var n;"element"===e.type&&(null==(n=e.properties)?void 0:n.ref)&&delete e.properties.ref,"element"!==e.type||/^[a-z][a-z0-9]*$/i.test(e.tagName)||(e.type="text",e.value=`<${e.tagName}`),e.children&&e.children.forEach(t)};e.children.forEach(t)}],disallowedElements:["iframe","head","html","meta","link","style","body",...e.customDisallowedElements||[]],components:{code:sc,img:sp,a:sm,p:sd,form:e=>(0,y.jsx)(sa,{...e,onSend:e=>{t(JSON.stringify({...e,isFormSubmit:!0}),{inputs:e})}}),script:su,details:oQ},children:n})})}class sh extends b.Component{componentDidCatch(e,t){this.setState({hasError:!0}),console.error(e,t)}render(){return this.state.hasError?(0,y.jsxs)("div",{children:["Oops! An error occurred. This could be due to an ECharts runtime error or invalid SVG content. ",(0,y.jsx)("br",{}),"(see the browser console for more information)"]}):this.props.children}constructor(e){super(e),this.state={hasError:!1}}}var sg=n(34014),sv=n(6997),sy=n(82288);function sx(e){let{text:t}=e;return t?(0,y.jsx)("pre",{className:"!m-0 !p-0 !bg-white !border-none",children:t}):"空"}function sb(e){let{uniqueKey:t,items:n,className:r}=e;if(!(null==n?void 0:n.length))return null;let a=n.map(e=>({title:(0,y.jsx)("div",{className:"text-base",children:e.tool?`已使用 ${e.tool}`:"暂无标题"}),status:"success",icon:(0,y.jsx)(sg.Z,{}),description:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(sy.Z,{className:"mt-3 min-w-chat-card",size:"small",items:[{key:`${t}-tool_input`,label:"输入",children:(0,y.jsx)(sx,{text:e.tool_input})},{key:`${t}-observation`,label:"输出",children:(0,y.jsx)(sx,{text:e.observation})}]}),(0,y.jsx)("pre",{className:"border-none",children:e.thought})]})}));return(null==a?void 0:a.length)?(0,y.jsx)(sv.Z,{className:r,items:a}):null}var sj=n(61124),sw=n(50374),sS=n(42634);function sN(e){let{files:t}=e;return(null==t?void 0:t.length)?t.every(e=>"image"===e.type)?(0,y.jsx)("div",{className:"flex flex-wrap",children:t.map(e=>(0,y.jsx)(sS.TV,{children:(0,y.jsx)(sS.HI,{src:e.url,children:(0,y.jsx)("img",{src:e.url&&(location.origin.includes(".medon.com.cn")||location.origin.includes(".medsci.cn")?location.origin+e.url:"https://ai-base.medon.com.cn"+e.url),alt:e.filename,className:"w-24 h-24 cursor-zoom-in mr-2 rounded-lg",style:{objectFit:"cover"}},e.id)})},e.id))}):(0,y.jsx)(y.Fragment,{children:t.map((e,t)=>{var n;return(0,y.jsxs)("a",{title:"点击下载文件",href:e.url,target:"_blank",className:"p-3 bg-gray-50 rounded-lg w-60 flex items-center cursor-pointer no-underline mb-2",children:["image"===e.type?(0,y.jsx)(sj.Z,{className:"text-3xl text-gray-400 mr-2"}):(0,y.jsx)(sw.Z,{className:"text-3xl text-gray-400 mr-2"}),(0,y.jsxs)("div",{className:"overflow-hidden",children:[(0,y.jsx)("div",{className:"text-default truncate",children:(null==(n=e.filename)?void 0:n.split("_").pop())||e.filename}),e.size?(0,y.jsx)("div",{className:"text-desc truncate",children:o_(e.size)}):null]})]},e.id+t)})}):null}function sk(e){let{items:t}=e;return(null==t?void 0:t.length)?(0,y.jsxs)("div",{className:"pb-3",children:[(0,y.jsxs)("div",{className:"flex items-center text-gray-400",children:[(0,y.jsx)("span",{className:"mr-3 text-sm",children:"引用"}),(0,y.jsx)("div",{className:"flex-1 border-gray-400 border-dashed border-0 border-t h-0"})]}),t.map(e=>(0,y.jsx)("div",{className:"mt-2 truncate",children:(0,y.jsx)("a",{className:"text-gray-600",target:"_blank",href:"javascript:void(0)",title:e.document_name,children:e.document_name})},e.id))]}):null}function sC(e){var t;let{appConfig:n,onSubmit:r,messageItem:{id:a,status:i,error:o,agentThoughts:s,workflows:l,files:c,content:u,retrieverResources:d,role:p}}=e,m=(0,b.useMemo)(()=>{let e=u.startsWith("{")&&u.endsWith("}");if("local"===p||"user"===p&&e){var t,r,a;if((null==(t=n.answerForm)?void 0:t.enabled)&&(null==(r=n.answerForm)?void 0:r.feedbackText))try{return JSON.parse(u).isFormSubmit?null==(a=n.answerForm)?void 0:a.feedbackText:u}catch(e){console.log("computedContent json 解析失败",e)}}return u},[u,null==n?void 0:n.answerForm,p]);return"error"===i?(0,y.jsxs)("p",{className:"text-red-700",children:[(0,y.jsx)(oT.Z,{className:"mr-2"}),(0,y.jsx)("span",{children:o})]}):"success"!==i||u||(null==c?void 0:c.length)||(null==s?void 0:s.length)||(null==l||null==(t=l.nodes)?void 0:t.length)||(null==d?void 0:d.length)?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(sb,{uniqueKey:a,items:s,className:"mt-3"}),(null==c?void 0:c.length)?(0,y.jsx)("div",{className:"mt-3",children:(0,y.jsx)(sN,{files:c})}):null,(0,y.jsx)("div",{className:"local"===p||"user"===p?"":"md:min-w-chat-card",children:(0,y.jsx)(sf,{markdownText:m,onSubmit:r})}),(0,y.jsx)(sk,{items:d})]}):(0,y.jsxs)("p",{className:"text-orange-600",children:[(0,y.jsx)(oT.Z,{className:"mr-2"}),(0,y.jsx)("span",{children:"消息内容为空"}),(0,y.jsx)(oA.Z,{title:"可能是用户在生成内容的过程中点击了停止响应按钮",children:(0,y.jsx)(oO.Z,{className:"ml-2"})})]})}n(70003);var s_=n(53024),sE=n(9702),sT=n(10543),sO=n(89696),sA=n(33331),sI=n(16017);function sP(e){let{icon:t,loading:n=!1,active:r=!1,onClick:a}=e,i=b.cloneElement(t,{className:r?"text-primary":""});return(0,y.jsxs)("div",{className:"relative",children:[(0,y.jsx)(J.ZP,{color:"default",variant:"text",size:"small",icon:i,onClick:a}),(0,y.jsx)(V.Z,{className:"absolute left-0 top-0 w-full h-full",spinning:n})]})}function sM(e){let{messageId:t,messageContent:n,feedback:{rating:r,callback:a},feedbackApi:i,ttsApi:o,ttsConfig:s,onSubmit:l}=e,c="like"===r,u="dislike"===r,[d,p]=(0,sI.Z)({like:!1,dislike:!1}),[m,f]=(0,b.useState)(!1),[h,g]=(0,b.useState)(""),{runAsync:v}=(0,ea.Z)(e=>i({messageId:t.replace("-answer",""),rating:e,content:""}),{manual:!0,onSuccess(){B.ZP.success("操作成功"),null==a||a()},onFinally(){p({like:!1,dislike:!1})}}),x=async e=>{let t=new Audio;t.src=e,t.play(),f(!0),t.addEventListener("ended",()=>{f(!1)})},{runAsync:j,loading:w}=(0,ea.Z)(e=>o({text:e}).then(e=>e.blob()).then(e=>{let t=URL.createObjectURL(e);g(t),x(t)}),{manual:!0}),S=[{icon:(0,y.jsx)(s_.Z,{}),hidden:!1,onClick:()=>{null==l||l(t)}},{icon:(0,y.jsx)(o2.Z,{}),onClick:async()=>{await (0,o5.v)(n),B.ZP.success("复制成功")},active:!1,loading:!1,hidden:!1},{icon:(0,y.jsx)(sE.Z,{}),onClick:()=>{p({like:!0}),v(c?null:"like")},active:c,loading:d.like,hidden:!1},{icon:(0,y.jsx)(sT.Z,{}),onClick:()=>{p({dislike:!0}),v(u?null:"dislike")},active:u,loading:d.dislike,hidden:!1},{icon:m?(0,y.jsx)(sO.Z,{}):(0,y.jsx)(sA.Z,{}),onClick:()=>{h?x(h):j(n)},active:m,loading:w,hidden:!(null==s?void 0:s.enabled)}];return(0,y.jsx)(oo.Z,{children:S.map((e,t)=>!e.hidden&&(0,y.jsx)(sP,{icon:e.icon,onClick:e.onClick,active:e.active,loading:e.loading},t))})}var s$=n(92266),sL=n(21397),sR=n(2016),sZ=n(97004),sF=n(48161),sz=n(71747);let sD=(e,t)=>(0,y.jsxs)(oo.Z,{align:"start",children:[e,(0,y.jsx)("span",{children:t})]}),sB=e=>{var t,n,r,a;let{onPromptItemClick:i,appParameters:o,description:s,appIcon:l,appConfig:c}=e,u=I(),d=(0,b.useMemo)(()=>(sD((0,y.jsx)(s$.Z,{style:{color:"#FF4D4F"}}),"Hot Topics"),sD((0,y.jsx)(sL.Z,{style:{color:"#1890FF"}}),"Design Guide"),sR.Z,sZ.Z,sF.Z,null==o?void 0:o.suggested_questions)?[{key:"remote",label:sD((0,y.jsx)(s$.Z,{style:{color:"#FF4D4F"}}),"Hot Topics"),description:"What are you interested in?",children:o.suggested_questions.map((e,t)=>({key:"index-"+t,description:e}))}]:[],[u]);return(0,y.jsx)("div",{className:"flex justify-center w-full px-3 box-border mx-auto",children:(0,y.jsxs)(oo.Z,{direction:"vertical",className:"pt-8 w-full md:!w-3/4",children:[(0,y.jsx)(sz.Z,{variant:"borderless",icon:l||"https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp",title:(null==(t=c.info)?void 0:t.name)||"Hello, I'm MedSci xAI",description:s||"MedSci xAI"}),(null==(n=d[0].children)?void 0:n.length)||(null==o?void 0:o.opening_statement)?(0,y.jsx)(oy.Z,{title:null==o?void 0:o.opening_statement,vertical:u,items:d,styles:{list:{width:"100%",display:(null==(a=d[0])||null==(r=a.children)?void 0:r.length)?"":"none"},item:u?{width:"100%"}:{flex:1}},onItemClick:i}):null]})})};var sH=n(7712),sU=n(19175),sW=n(23421),sq=e=>{var t;let{messageItems:n,isRequesting:r,nextSuggestions:a,onPromptsItemClick:i,onSubmit:o,onCancel:s,conversationId:l,feedbackCallback:c,difyApi:u,appParameters:d,appConfig:p,onFocus:m}=e;console.log("Chatbox",p);let f=(0,F.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),h=f?f[1]:"",[g,v]=(0,b.useState)(""),x=I(),j=H.Z.get("userInfo"),[w,S]=(0,b.useState)(j&&JSON.parse(j).avatar||"https://img.medsci.cn/web/img/user_icon.png"),N={ai:{placement:"start",avatar:x?void 0:{icon:(0,y.jsx)("img",{src:h.includes("novax")||h.includes("elavax")?null==p||null==(t=p.info)?void 0:t.appIcon:"https://static.medsci.cn/ai-write/robot-08128bd4.png",alt:"robot"}),style:{background:"#fde3cf"}},style:x?void 0:{maxWidth:"calc(100% - 44px)"}},user:{placement:"end",avatar:x?void 0:{icon:w?(0,y.jsx)("img",{src:w,alt:"avatar",onError:()=>{S("https://img.medsci.cn/web/img/user_icon.png")}}):(0,y.jsx)(og.Z,{}),style:{background:w?"":"#87d068",border:"none"}},style:x?void 0:{maxWidth:"calc(100% - 44px)",marginLeft:"44px"}}},k=(0,b.useMemo)(()=>{let e=new Map;return null==n?void 0:n.map(t=>{var n;return"user"===t.role&&e.set(t.id,t),{key:`${t.id}-${t.role}`,content:t.content,messageRender:()=>(0,y.jsx)(sC,{appConfig:p,onSubmit:o,messageItem:t}),role:"local"===t.role?"user":t.role,footer:"ai"===t.role&&(0,y.jsxs)("div",{className:"flex items-center",children:[(0,y.jsx)(sM,{ttsConfig:null==d?void 0:d.text_to_speech,feedbackApi:e=>u.feedbackMessage(e),ttsApi:e=>u.text2Audio(e),messageId:t.id,messageContent:t.content,feedback:{rating:null==(n=t.feedback)?void 0:n.rating,callback:()=>{null==c||c(l)}},onSubmit:n=>{let r=e.get(t.id);n&&r&&o(r.content,{files:(e=>{if(e)return e.map(e=>{var t;return{name:(null==(t=e.filename)?void 0:t.split("_").pop())||e.filename,url:e.url,transfer_method:e.transfer_method,type:e.type,upload_file_id:e.upload_file_id}})})(r.files)})}}),t.created_at&&(0,y.jsxs)("div",{className:"ml-3 text-sm text-desc",children:["回复时间：",t.created_at]})]})}})},[n,l,u,c,p,o]),C=(0,b.useRef)(null),_=(0,b.useDeferredValue)(k);return new URLSearchParams(window.top.location.search),(0,b.useEffect)(()=>{C.current&&C.current.scrollTo({behavior:"smooth",top:C.current.scrollHeight})},[_]),(0,y.jsx)("div",{className:"w-full h-full overflow-hidden my-0 mx-auto box-border flex flex-col gap-4 relative bg-white",children:(0,y.jsxs)("div",{className:"w-full h-full overflow-auto pt-4 pb-48",ref:C,children:[!(null==k?void 0:k.length)&&O(l)&&"medsci-ask"!=h&&(0,y.jsx)(sB,{appParameters:d,onPromptItemClick:i,description:p.info.description,appIcon:p.info.appIcon,appConfig:p}),(0,y.jsx)(ov.Z.List,{items:k,roles:N,className:"flex-1 w-full md:!w-3/4 mx-auto px-3 md:px-0 box-border"}),(0,y.jsxs)("div",{className:"absolute bottom-0 bg-white w-full md:!w-3/4 left-1/2",style:{transform:"translateX(-50%)"},children:[(0,y.jsx)(oy.Z,{className:"text-default p-3 bg-transparent",items:null==a?void 0:a.map((e,t)=>({key:t.toString(),description:e})),onItemClick:i}),(0,y.jsx)("div",{className:"px-3",children:(0,y.jsx)(oE,{appParameters:d,content:g,onChange:e=>v(e),onSubmit:(e,t)=>{e&&(o(e,t),v(""))},isRequesting:r,className:"w-full",uploadFileApi:async function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=await u.uploadFile(...t);return 0!=r.code?(B.ZP.error(r.msg),Promise.reject()):u.uploadFile(...t)},onCancel:s,onFocus:m})}),(0,y.jsx)("div",{className:"text-gray-400 text-sm text-center h-8 leading-8",children:"内容由 AI 生成, 仅供参考"})]})]})})},sG=e=>{let{deleteConversationPromise:t,renameConversationPromise:n,items:r,activeKey:a,onActiveChange:i,onItemsChange:o,refreshItems:s,appConfig:l,onchangeModal2Open:c}=e,u=(0,F.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),d=u?u[1]:"";H.Z.get("userInfo");let[p]=on.Z.useForm(),m=async e=>{if(O(e))null==o||o(r.filter(t=>t.key!==e));else{var n;await t(e,null==l||null==(n=l.info)?void 0:n.appId),s()}B.ZP.success("删除成功"),a===e&&(null==i||i(""))},f=e=>{p.setFieldsValue({name:e.label}),sW.Z.confirm({destroyOnClose:!0,title:"会话重命名",content:(0,y.jsx)(on.Z,{form:p,className:"mt-3",children:(0,y.jsx)(on.Z.Item,{name:"name",children:(0,y.jsx)(oc.Z,{placeholder:"请输入"})})}),onOk:async()=>{await p.validateFields();let t=await p.validateFields();await n(e.key,t.name),B.ZP.success("会话重命名成功"),s()}})};return(0,y.jsx)(sU.Z,{items:r,activeKey:a,onActiveChange:i,menu:e=>({items:[{label:"重命名",key:"rename",icon:(0,y.jsx)(sH.Z,{}),disabled:O(e.key)},d.includes("novax")||d.includes("elavax")?{label:"删除",key:"delete",icon:(0,y.jsx)(eo.Z,{}),danger:!0}:null],onClick:async t=>{switch(t.domEvent.stopPropagation(),t.key){case"delete":await m(e.key);break;case"rename":f(e)}}})})},sK=n(10965),sV=n(95686),sX=n(39016),sJ=n(16483),sQ=n.n(sJ),sY=n(88627);let s0="ai.medsci.cn"===window.location.hostname?"https://ai.medsci.cn":"https://ai.medon.com.cn",s1=new D({baseURL:s0}),s2=class extends _{async getApps(){return await s1.get("/apps")}async getApp(e,t){try{let n=await s1.get("/dev-api/ai-base/index/getAppByUuid",{appUuid:e},t);if(n.data)return{id:n.data.appUuid,info:{name:n.data.appName,description:n.data.appDescription,appUser:n.data.appUser,appId:n.data.dAppUuid,appType:n.data.appType,appIcon:n.data.appIcon,feeTypes:n.data.feeTypes,tags:[],appUuid:n.data.appUuid},requestConfig:{apiBase:`${s0}/dev-api/ai-base/v1`,apiKey:""},answerForm:{enabled:!1}};return void console.warn("No data found for app with id:",e)}catch(e){console.error("Failed to fetch app:",e);return}}async addApp(e){return s1.post("/apps",e)}async updateApp(e){return s1.put(`/apps/${e.id}`,e)}async deleteApp(e){await s1.delete(`/apps/${e}`)}},s5="新对话";function s3(e){let t=(0,b.useRef)(e);return t.current=e,t}var s4=n(80900),s6=n(70699),s8=n(24923);n(77345);let s9=e=>{let{latestProps:t,latestState:n,appParameters:r,getNextSuggestions:a,filesRef:i,abortRef:o,getConversationMessages:s,onConversationIdChange:l,difyApi:c}=e,{user:u}=C(),[d,p]=(0,b.useState)(""),[m]=(0,s4.Z)({request:async(m,f)=>{let{message:h}=m,{onSuccess:g,onUpdate:v,onError:y}=f,x=await c.sendMessage({inputs:(null==h?void 0:h.inputs)||n.current.inputParams,conversation_id:O(t.current.conversationId)?void 0:t.current.conversationId,files:i.current||[],user:u,response_mode:"streaming",query:null==h?void 0:h.content,requestId:"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}),appUuid:e.appUuid}),b="",j=[],w={},S=[];if(200!==x.status){let e=x.statusText||"请求对话接口失败";B.ZP.error(e),o.current=()=>{y({name:x.status.toString(),message:e})},o.current();return}let N=(0,s6.Z)({readableStream:x.body}).getReader();for(o.current=()=>{null==N||N.cancel(),y({name:"abort",message:"用户已取消"})};N;){let{value:e,done:t}=await N.read();if(t){g({content:b,files:j,workflows:w,agentThoughts:S});break}if(e)if(e.data){let t={};try{t=JSON.parse(e.data)}catch(e){console.error("解析 JSON 失败",e)}t.task_id&&t.task_id!==d&&p(t.task_id),console.log("parsedData",t,ot),t.event===ot.MESSAGE_END&&(g({content:b,files:j,workflows:w,agentThoughts:S}),s(t.conversation_id),l(t.conversation_id),(null==r?void 0:r.suggested_questions_after_answer.enabled)&&a(t.message_id));let n=t.data;if(t.event===ot.WORKFLOW_STARTED)w.status="running",w.nodes=[],v({content:b,files:j,workflows:w,agentThoughts:S});else if(t.event===ot.WORKFLOW_FINISHED)console.log("工作流结束",t),w.status="finished",v({content:b,files:j,workflows:w,agentThoughts:S});else if(t.event===ot.WORKFLOW_NODE_STARTED)w.nodes=[...w.nodes||[],{id:n.id,status:"running",type:n.node_type,title:n.title}],v({content:b,files:j,workflows:w,agentThoughts:S});else if(t.event===ot.WORKFLOW_NODE_FINISHED){var k;w.nodes=null==(k=w.nodes)?void 0:k.map(e=>e.id===n.id?{...e,status:"success",inputs:n.inputs,outputs:n.outputs,process_data:n.process_data,elapsed_time:n.elapsed_time,execution_metadata:n.execution_metadata}:e),v({content:b,files:j,workflows:w,agentThoughts:S})}if(t.event===ot.MESSAGE_FILE&&v({content:b+=`<img src=""${t.url} />`,files:j,workflows:w,agentThoughts:S}),t.event===ot.MESSAGE&&v({content:b+=t.answer,files:j,workflows:w,agentThoughts:S}),t.event===ot.ERROR&&(console.log("错误",t),y({name:`${t.status}: ${t.code}`,message:t.message}),B.ZP.error(t.message)),t.event===ot.AGENT_MESSAGE){let e=S[S.length-1];if(!e)continue;{let n=t.answer;e.thought+=n}v({content:b,files:j,workflows:w,agentThoughts:S})}if(t.event===ot.AGENT_THOUGHT){let e=S.findIndex(e=>e.position===t.position),n={conversation_id:t.conversation_id,id:t.id,task_id:t.task_id,position:t.position,tool:t.tool,tool_input:t.tool_input,observation:t.observation,message_files:t.message_files,message_id:t.message_id};-1!==e?S[e]=n:S.push(n),v({content:b,files:j,workflows:w,agentThoughts:S})}}else{console.log("没有数据",e);continue}}}}),{onRequest:f,messages:h,setMessages:g}=(0,s8.Z)({agent:m});return{agent:m,onRequest:f,messages:h,setMessages:g,currentTaskId:d}};var s7=n(31896),le=n(57948),lt=n(33327);function ln(e){let{info:t}=e;return(0,y.jsx)("div",{className:"text-default",children:(0,y.jsxs)("div",{className:"flex items-center justify-center flex-col",children:[(0,y.jsx)(le.Z,{className:"text-2xl text-primary"}),(0,y.jsx)("div",{className:"text-2xl font-bold mt-3",children:t.name}),(0,y.jsx)("div",{className:"text-desc text-base max-w-96 mt-3 text-center",children:t.description}),t.tags?(0,y.jsx)("div",{className:"mt-3 text-center",children:t.tags.map(e=>(0,y.jsx)(lt.Z,{className:"mb-2",children:e},e))}):null]})})}let lr=e=>{let{formFilled:t,onStartConversation:n,user_input_form:r,appInfo:a,conversationId:i}=e,o=(0,F.k6)(),{appUuid:s}=(0,F.UO)(),l=new URLSearchParams(window.top.location.search),[c,u]=(0,b.useState)([]),d=(0,b.useRef)(new URLSearchParams(l)),{mode:p}=C(),[m]=on.Z.useForm(),[f,h]=(0,b.useState)({}),g=o.location.pathname.match(/^\/ai-chat\/([^/]+)$/),v=g?g[1]:"";return(0,b.useEffect)(()=>{m.resetFields()},[i]),(0,b.useEffect)(()=>{if(!t&&(null==r?void 0:r.length)||u([]),u((null==r?void 0:r.map(e=>{if(e["text-input"]){var t;let n=e["text-input"],r={type:"input",label:n.label,name:n.variable},a=new URLSearchParams(null==(t=top)?void 0:t.location.search).get(n.variable);return a&&(m.setFieldValue(n.variable,a),f[n.variable]=a,d.current.delete(n.variable)),n.required&&(r.required=!0,r.rules=[{required:!0,message:"请输入"}]),r}return{}}))||[]),l.size!==d.current.size){d.current.has("isNewCvst")&&d.current.delete("isNewCvst");let e=d.current.size?`?${d.current.toString()}`:"";"multiApp"===p?n(f):o.push(`/ai-chat${e}`)}},[r]),(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center -mt-5",children:(0,y.jsx)("div",{className:"max-w-[80vw] w-3/5  px-10 rounded-3xl bg-gray-100 box-border",children:a&&!("medsci-ask"==v||l.get("fromPlatform"))&&(null==r?void 0:r.length)?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(ln,{info:a}),!t&&(null==r?void 0:r.length)?(0,y.jsx)(on.Z,{form:m,className:"mt-6",labelCol:{span:5},children:c.map(e=>(0,y.jsx)(on.Z.Item,{name:e.name,label:e.label,required:e.required,rules:e.rules,children:"input"===e.type?(0,y.jsx)(oc.Z,{placeholder:"请输入"}):"select"===e.type?(0,y.jsx)(od.Z,{placeholder:"请选择"}):"不支持的控件类型"},e.name))}):null,(0,y.jsx)("div",{className:"mt-3 w-full flex justify-center",children:(0,y.jsx)(J.ZP,{type:"primary",icon:(0,y.jsx)(s7.Z,{}),onClick:async()=>{await m.validateFields(),n(m.getFieldsValue())},children:"开始对话"})})]}):!("medsci-ask"==v||l.get("fromPlatform"))&&(null==r?void 0:r.length)?(0,y.jsx)(ol.Z,{description:"请先配置 Dify 应用"}):null})})};function la(e){let t=(0,F.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),n=t?t[1]:"";return(0,y.jsx)("div",{className:"flex h-full items-center flex-[1.5] overflow-hidden justify-center text-primary font-semibold",children:(0,y.jsx)("div",{className:"flex items-center rounded-3xl shadow-md py-1 px-2 text-sm bg-white",children:n.includes("novax")||n.includes("elavax")?(0,y.jsx)("div",{className:"flex items-center",children:e.children}):(0,y.jsx)("a",{href:top.location.href,title:e.children,children:e.children})})})}let li=e=>{var t,n;let{centerChildren:r,showSubscribe:a,subStatusDetail:i,selectedAppId:o,appList:s,appConfig:l}=e,{mode:c}=C(),u=Y.get("userInfo"),d=u&&(null==(t=JSON.parse(u))?void 0:t.avatar)?JSON.parse(u).avatar:"https://img.medsci.cn/web/img/user_icon.png",p=(0,F.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),m=p?p[1]:"",[f,h]=(0,b.useState)(!1);location.origin.includes("medsci.cn")||location.origin.includes("medon.com.cn");let g=e=>{e.target.src="https://img.medsci.cn/web/img/user_icon.png"},v=()=>Y.get("ai_apps_lang")?Y.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,x=async()=>{h(!1),Y.remove("userInfo",{domain:".medon.com.cn"}),Y.remove("userInfo",{domain:".medsci.cn"}),Y.remove("userInfo",{domain:"localhost"}),Y.remove("yudaoToken",{domain:"ai.medon.com.cn"}),Y.remove("yudaoToken",{domain:"ai.medsci.cn"}),Y.remove("yudaoToken",{domain:".medon.com.cn"}),Y.remove("yudaoToken",{domain:".medsci.cn"}),Y.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("conversation"),window.location.origin.includes("medsci.cn")?window.top.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.top.location.href:window.top.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.top.location.href};return(0,y.jsxs)("div",{className:"h-12 !leading-[3rem] px-4 text-base top-0 z-20 bg-white w-full shadow-sm font-semibold justify-between flex items-center box-border",children:[(0,y.jsx)("div",{className:"flex-1 overflow-hidden flex items-center justify-center",children:r}),"zh-CN"==v()&&!m.includes("novax")&&!m.includes("elavax")&&(0,y.jsx)("div",{onClick:()=>a(),className:"px-[15px] py-[4px] flex items-center h-[28px] rounded border-none mr-[8px] text-xs text-[#614018]",style:{backgroundImage:"linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"},children:"免费"==i.packageType?"升级订阅":"连续包月"==i.packageType||"连续包年"==i.packageType?"修改订阅":"订阅"}),m.includes("novax")||m.includes("elavax")?(0,y.jsx)(la,{children:o?(0,y.jsx)(y.Fragment,{children:(0,y.jsx)(ei.Z,{className:"mr-2 ml-2",arrow:!0,placement:"bottom",trigger:["click"],menu:{style:{},selectedKeys:[o],items:[...(null==s?void 0:s.map(e=>{let t=o===e.id;return{key:e.id,label:(0,y.jsx)("div",{className:t?"text-primary":"text-default",children:null==e?void 0:e.info.name}),onClick:()=>{if(m.includes("novax")||m.includes("elavax")){window.top.location.href=location.origin+(window.top.location.href.includes("ai-chat")?"/ai-chat/":"/chat/")+`${null==e?void 0:e.info.appNameEn}`;return}},icon:(0,y.jsx)(en.Z,{})}}))||[]]},children:(0,y.jsxs)("div",{className:"cursor-pointer flex items-center",children:[(0,y.jsx)("span",{className:"cursor-pointer w-[75px] inline-block whitespace-nowrap overflow-hidden text-ellipsis",children:null==l||null==(n=l.info)?void 0:n.name}),(0,y.jsx)(er.Z,{className:"ml-1"})]})})}):null}):null,(0,y.jsx)("div",{className:"px-[15px] py-[4px] mr-[8px] h-[28px] flex items-center",style:{background:"#f1f5f9"},children:(0,y.jsx)("a",{style:{borderRadius:"4px",fontSize:"12px",color:"#666",lineHeight:"1"},className:"backImg ",href:location.origin.includes(".medon.com.cn")?"https://ai.medon.com.cn/"+v():location.origin.includes(".medsci.cn")?"https://ai.medsci.cn/"+v():"/"+v(),target:"_top",children:"返回首页"})}),!u&&(0,y.jsx)("div",{className:"hover:cursor-pointer",onClick:()=>{let e=v();if(e&&"zh-CN"!==e)window.top.location.href=location.origin+"/"+e+"/login";else{var t,n;null==(t=(n=window).addLoginDom)||t.call(n)}},children:"登录"}),(0,y.jsx)(sK.Z,{placement:"bottomLeft",trigger:"hover",arrow:f,overlayStyle:{width:300,height:163},content:(0,y.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",position:"relative",paddingBottom:"40px"},children:[(0,y.jsx)("a",{className:"exit text-right w-full text-[#333333]",onClick:x,children:"退出"}),(0,y.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},className:"iconHeader bg-write",children:[u&&JSON.parse(u||"").userId&&(0,y.jsx)("img",{src:d,onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),style:{width:"60px",height:"60px"},onError:g,alt:"avatar"}),(0,y.jsx)("span",{className:"account",children:u&&JSON.parse(u||"").userName})]})]}),children:(0,y.jsx)("a",{href:"#",children:(0,y.jsx)("div",{className:"img-area  leading-none",children:u&&JSON.parse(u||"").userId&&(d?(0,y.jsx)("img",{src:d,onError:g,onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),style:{width:"32px",height:"32px"},alt:"avatar"}):(0,y.jsx)(og.Z,{}))})})})]})};var lo=n(69935);let ls=e=>{var t,n,r;let{userInfo:a,currentItem:i,onClose:o,appConfig:s}=e;a=JSON.parse(a)||{};let[l,c]=(0,b.useState)(!1),[u,d]=(0,b.useState)({}),[p,m]=(0,b.useState)(null),[f,h]=(0,b.useState)(""),[g,v]=(0,b.useState)(a.avatar||"https://img.medsci.cn/web/img/user_icon.png"),[x,j]=(0,b.useState)(window.innerWidth>768),[w,S]=(0,b.useState)(null),[N,k]=(0,b.useState)(null),C=e=>({"tool.Support_Alipay_Payment":"支持支付宝支付","tool.Meisi_Account":"梅斯账号","tool.Please_activate_after_reading_and_agreeing_to_the_agreement":"请阅读并同意协议后激活","tool.Free_Trial":"免费试用","market.subscribe":"订阅","tool.Month":"月","tool.Quarter":"季","tool.Year":"年","tool.sS":"订阅成功"})[e]||e,_=new K;window.location.origin.includes("medon.com.cn")||window.location.origin.includes("medsci.cn");let E=()=>{j(window.innerWidth>768)},T=(e,t)=>{d(e),m(t),(null==e?void 0:e.coinType)=="人民币"&&0!==e.feePrice&&I(e,s.id)},O=e=>{let t=setInterval(async()=>{var n;let r=await _.getSubOrder({piId:`${e}`},{Authorization:`Bearer ${H.Z.get("yudaoToken")}`});(null==r||null==(n=r.data)?void 0:n.payStatus)==="PAID"&&(window.location.reload(),clearInterval(t))},2e3);k(t)},A=()=>H.Z.get("ai_apps_lang")?H.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,I=async(e,t)=>{if(!(null==e?void 0:e.coinType))return void B.ZP.warning("请选择订阅服务周期");let n=A();if(a.userId){let n={appUuid:t||"",priceId:e.priceId,monthNum:e.monthNum,packageKey:e.packageKey,packageType:e.type};try{c(!0);let t=await _.createSubscription(n,{Authorization:`Bearer ${H.Z.get("yudaoToken")}`});if((null==t?void 0:t.code)==0)if(c(!1),"人民币"==e.coinType&&0!==e.feePrice){let e=t.data,n=window.location.origin,r=(n.includes(".medsci.cn")||n.includes(".medon.com.cn"),`${n}/payLink/${encodeURIComponent(e)}`);h(r),S(JSON.parse(e).piId),N&&clearInterval(N),O(JSON.parse(e).piId)}else B.ZP.success(C("tool.sS")),setTimeout(()=>{window.top.location.href=t.data},1e3)}catch(e){c(!1),console.error(e)}}else n&&"zh-CN"!==n?window.top.location.href=location.origin+"/"+n+"/login":console.log("Trigger login")};return(0,b.useEffect)(()=>{var e;return"写作"===i.appType&&localStorage.setItem(`appWrite-${s.id}`,JSON.stringify({appUuid:s.id,directoryMd:i.directoryMd})),E(),window.addEventListener("resize",E),x&&(null==(e=i.feeTypes)?void 0:e.length)===1&&T(i.feeTypes[0],0),()=>{window.removeEventListener("resize",E),N&&clearInterval(N)}},[i,x]),(0,y.jsx)("div",{id:"app",children:(0,y.jsxs)("div",{className:"scale",children:[(0,y.jsxs)("div",{className:"micro_header",children:[(0,y.jsx)("div",{className:"micro_left",children:(0,y.jsxs)("div",{className:"avatar",children:[(0,y.jsx)("img",{src:g,onError:()=>{v("https://img.medsci.cn/web/img/user_icon.png")},alt:""}),(0,y.jsx)("span",{className:"t1",children:a.realName||a.userName})]})}),(0,y.jsx)("div",{className:"micro_right",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png",alt:"",onClick:()=>{N&&clearInterval(N),null==o||o(!1)}})})]}),(0,y.jsxs)("div",{className:"micro_main",children:[(0,y.jsx)("div",{className:"micro_main_top",children:(0,y.jsx)("div",{className:"micro_main-sp",children:(0,y.jsx)("div",{className:"micro_main_temp",children:((null==(t=i.feeTypes[0])?void 0:t.coinType)=="美元"||(null==(n=i.feeTypes)?void 0:n.length)>1)&&(0,y.jsx)("div",{className:"swiper-vip",children:null==(r=i.feeTypes)?void 0:r.map((e,t)=>(0,y.jsxs)("div",{className:"swiper-vip-item",onClick:()=>T(e,t),children:[(0,y.jsx)("div",{className:"newer",style:{left:t%4==0&&0!==t?"6px":"-1px"}}),(0,y.jsxs)("div",{className:`swiper-vip-item-child ${p===t?"sactvie":""}`,children:[(0,y.jsx)("div",{className:"title",children:e.type}),(0,y.jsxs)("div",{className:"pricePc",children:[(0,y.jsx)("span",{children:"人民币"===e.coinType?"\xa5":"$"})," ",e.feePrice]})]})]},t))})})})}),(0,y.jsx)("div",{className:"micro_main_middle",children:(0,y.jsxs)("div",{className:"micro_main_middle_banner",children:[(0,y.jsxs)("div",{className:"micro_main_middle_title",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png",alt:""}),i.name]}),(0,y.jsx)("div",{className:"micro_main_middle_content",children:i.description})]})}),u.coinType&&0!==u.feePrice&&(null==u?void 0:u.coinType)=="人民币"&&(0,y.jsx)("div",{className:"micro_main_bottom",children:(0,y.jsx)("div",{className:"micro_pay",children:(0,y.jsxs)("div",{className:"micro_pay_right",children:[l&&(0,y.jsx)("div",{className:"noQrCode"}),!l&&f&&(0,y.jsx)("div",{className:"qr-code",children:(0,y.jsx)(lo.Q,{value:f,size:131,fgColor:"#000",level:"L"})}),(0,y.jsxs)("div",{className:"price",children:[(0,y.jsxs)("div",{className:"micro_way",children:[(0,y.jsx)("div",{className:"box",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})}),(0,y.jsx)("span",{children:C("tool.Support_Alipay_Payment")})]}),(0,y.jsxs)("span",{className:"t1",children:[C("tool.Support_Alipay_Payment"),(0,y.jsx)("span",{className:"bd",children:u.feePrice}),"人民币"===u.coinType?"\xa5":"$","/",3===u.monthNum?C("tool.Quarter"):12===u.monthNum?C("tool.Year"):C("tool.Month")]}),(0,y.jsxs)("span",{className:"t2",children:[C("tool.Meisi_Account"),"：",a.userName]}),(0,y.jsxs)("span",{className:"t3",onClick:()=>{window.open("https://www.medsci.cn/about/index.do?id=27")},children:[C("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"),(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})]})]})]})})}),u.coinType&&0===u.feePrice&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>I(u,s.id),children:C("tool.Free_Trial")})}),u.coinType&&u.feePrice>0&&(null==u?void 0:u.coinType)=="美元"&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>I(u,s.id),children:C("market.subscribe")})})]})]})})};var ll=n(69922);let lc=e=>{var t;let{userInfo:n={},currentItem:r={},onClose:a}=e,[i,o]=(0,b.useState)(!0),[s,l]=(0,b.useState)(!0),[c,u]=(0,b.useState)(!1),[d,p]=(0,b.useState)({}),[m,f]=(0,b.useState)(!1),[h,g]=(0,b.useState)(!1),[v,x]=(0,b.useState)(!1),[j,w]=(0,b.useState)(!1),[S,N]=(0,b.useState)((null==n?void 0:n.avatar)||"https://img.medsci.cn/web/img/user_icon.png"),k=(0,b.useRef)(null),C=new K;window.location.origin.includes("medon.com.cn")||window.location.origin.includes("medsci.cn"),(0,b.useEffect)(()=>{var e,t;let a=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${a}px`),"写作"===r.appType&&localStorage.setItem(`appWrite-${r.appUuid}`,JSON.stringify({appUuid:r.appUuid,directoryMd:r.directoryMd})),(null==(e=r.feeTypes)?void 0:e.length)===1&&r.feeTypes[0].feePrice>=0&&p(r.feeTypes[0]),(null==(t=JSON.parse(n))?void 0:t.userId)&&u(!0),"medsci"===new URLSearchParams(window.location.search).get("source")&&g(!0)},[r,n]);let _=()=>navigator.userAgent.includes("medsci_app"),E=()=>H.Z.get("ai_apps_lang")?H.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,T=()=>{let e=E();if(e&&"zh-CN"!==e)window.top.location.href=location.origin+"/"+e+"/login";else{var t,n;null==(t=(n=window).addLoginDom)||t.call(n)}},O=e=>{p(e)},A=async(e,t,r)=>{var a;if(!v&&r){w(!0),setTimeout(()=>w(!1),500);return}let i=E();if(null==(a=JSON.parse(n))?void 0:a.userId){let n={appUuid:t||"",priceId:e.priceId,monthNum:e.monthNum,packageKey:e.packageKey,packageType:e.type};try{let t=await C.createSubscription(n,{Authorization:`Bearer ${H.Z.get("yudaoToken")}`});if(0==t.code)if("人民币"==e.coinType&&0!==e.feePrice){let e=await C.createAliSub(JSON.parse(t.data));e&&0==e.code&&(window.top.location.href=e.data)}else B.ZP.success("订阅成功"),setTimeout(()=>{window.top.location.href=t.data},1e3)}catch(e){B.ZP.error("订阅失败")}}else i&&"zh-CN"!==i?window.top.location.href=location.origin+"/"+i+"/login":T()};return(0,y.jsxs)("div",{className:`vip ${_()?"sp":""}`,children:[!_()&&(0,y.jsx)("div",{className:"vip-head text-center py-2 text-lg font-medium text-gray-800",children:r.name}),(0,y.jsxs)("div",{className:"vip-introduce bg-gray-900 pt-9 pb-4 relative",children:[(0,y.jsx)("img",{className:"crown absolute right-1 top-0 h-9 object-contain",src:"https://static.medsci.cn/public-image/ms-image/1dcd7d10-58af-11ec-8e2f-1389d01aad85_crown.png",alt:""}),(0,y.jsx)("div",{className:"box mx-3 flex items-center justify-between bg-[url('https://static.medsci.cn/public-image/ms-image/21ad8b80-58b1-11ec-8e2f-1389d01aad85_vip-h5.png')] bg-cover p-3 rounded",children:c?(0,y.jsxs)("div",{className:"box-left flex items-center",children:[(0,y.jsx)("img",{className:"avatar w-8 h-8 rounded-full mr-3",src:S,alt:"",onError:()=>{N("https://img.medsci.cn/web/img/user_icon.png")}}),(0,y.jsx)("div",{className:"box-word",children:(0,y.jsx)("span",{className:"t1 text-lg text-[#58342c]",children:JSON.parse(n).realName||JSON.parse(n).userName})})]}):(0,y.jsxs)("div",{className:"box-left-1 flex items-center",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/web/img/user_icon.png",alt:"",className:"w-8 h-8 rounded-full mr-3"}),(0,y.jsxs)("div",{className:"left2",children:[(0,y.jsx)("span",{className:"t1 text-lg text-[#58342c] cursor-pointer",onClick:T,children:"立即登录"}),(0,y.jsx)("span",{className:"t2 text-sm text-[#7f5947] mt-1",children:"请登录后购买"})]})]})})]}),(0,y.jsxs)("div",{className:"vip-main bg-white rounded-t-3xl -mt-14 z-10 pb-24",children:[(0,y.jsx)("div",{className:"vip-one pl-3",children:(0,y.jsx)("div",{className:"big overflow-auto mr-3",children:(0,y.jsx)("ul",{ref:k,className:"flex mt-2 mb-1",children:null==(t=r.feeTypes)?void 0:t.map((e,t)=>(0,y.jsxs)("li",{className:`min-w-[135px] w-[135px] bg-white border border-gray-300 rounded-lg mr-3 text-center cursor-pointer ${e.type===d.type?"sactvie":""}`,onClick:()=>O(e),children:[(0,y.jsx)("div",{className:"title text-center ellipsis-2-lines mx-auto mt-6 max-w-[102.5px] text-sm font-medium text-gray-800",children:e.type}),(0,y.jsxs)("div",{className:"prices text-2xl font-bold text-orange-400 mt-2 mb-6",children:[(0,y.jsx)("span",{className:"text-lg",children:"人民币"===e.coinType?"\xa5":"$"}),e.feePrice]}),e.originalPrice&&(0,y.jsxs)("div",{className:"isfava relative text-xs text-gray-500 line-through",children:["人民币"===e.coinType?"\xa5":"$",e.feePrice]})]},t))})})}),(0,y.jsx)("div",{className:"vip-two border border-gray-200 rounded m-3 p-2",children:(0,y.jsxs)("div",{className:"vip-two_banner bg-[url('https://img.medsci.cn/202503/48a49f899b4544e39a864d1821b3555a-lRXRIQx3q1VB.png')] bg-cover p-3",children:[(0,y.jsxs)("div",{className:"vip-two_title flex items-center text-sm font-medium text-orange-600",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png",alt:"",className:"w-2 h-2 mx-1"}),r.name]}),(0,y.jsx)("div",{className:"vip-two_content bg-white p-3 text-sm text-gray-700 leading-5 max-h-[calc(var(--vh)*20)] overflow-auto rounded",children:r.description})]})}),d.feePrice>0&&(null==d?void 0:d.coinType)=="人民币"&&(0,y.jsx)("div",{className:"vip-three mt-3",children:(0,y.jsxs)("div",{className:`pay mx-3 p-4 rounded-lg bg-gradient-to-b from-[#fffaf6] to-[#fff8f0] ${m?"h-28":""}`,children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:"",className:"w-15 mb-3"}),(0,y.jsxs)("div",{className:"item flex justify-between items-center bg-white border border-orange-300 rounded p-2 mb-3 cursor-pointer",onClick:()=>{o(!1),l(!0)},children:[(0,y.jsxs)("div",{className:"item-left flex items-center",children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:"",className:"w-5 h-5 mr-2"}),(0,y.jsx)("span",{className:"text-sm text-gray-800",children:"支付宝支付"})]}),(0,y.jsx)("div",{className:"item-right w-5 h-5",children:s&&(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:"",className:"w-full"})})]})]})})]}),d.feePrice>=0&&(0,y.jsx)("div",{className:"vip-pay btns fixed bottom-0 w-full bg-white shadow-lg flex justify-around items-center h-24 z-20",children:0!==d.feePrice&&(null==d?void 0:d.coinType)=="人民币"?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{className:"pay-left ml-2",children:[(0,y.jsx)("div",{className:"t1 text-sm font-medium text-orange-400 mb-1",children:r.name}),(0,y.jsxs)("div",{className:`t2 flex items-start text-xs text-gray-500 ${j?"shake":""}`,children:[(0,y.jsx)(ll.Z,{checked:v,onChange:e=>{x(e.target.checked)},style:{fontSize:"14px"},className:"mr-1"}),(0,y.jsxs)("span",{onClick:()=>{let e="https://www.medsci.cn/about/index.do?id=27";_()?window.top.location.href=e:window.open(e)},className:"cursor-pointer",children:["请在阅读并同意",(0,y.jsx)("span",{className:"text-blue-500",children:"协议"}),"后开通"]})]})]}),(0,y.jsx)("div",{className:"pay-right mr-2 w-48 h-12 bg-gradient-to-r from-[#fde39b] to-[#fbd786] rounded-full flex items-center justify-center cursor-pointer",onClick:()=>A(d,r.appUuid,"ali"),children:(0,y.jsxs)("span",{className:"text-sm text-[#614018]",children:[d.feePrice,"元确认协议并支付"]})})]}):(0,y.jsx)(J.ZP,{type:"primary",className:"w-48 h-12 rounded-full",onClick:()=>A(d,r.appUuid,""),children:0===d.feePrice?"免费试用":"订阅"})})]})},lu=e=>{var t,n,r,a,i,o;let{userInfo:s,currentItem:l,onClose:c,appConfig:u,subStatusDetail:d}=e;s=s&&JSON.parse(s);let[p,m]=(0,b.useState)(!1),[f,h]=(0,b.useState)({}),[g,v]=(0,b.useState)(null),[x,j]=(0,b.useState)(""),[w,S]=(0,b.useState)(0),[N,k]=(0,b.useState)((null==s?void 0:s.avatar)||"https://img.medsci.cn/web/img/user_icon.png"),[C,_]=(0,b.useState)(window.innerWidth>768),[E,T]=(0,b.useState)(null),[O,A]=(0,b.useState)(null);(0,b.useEffect)(()=>{var e;l="zh-CN"==F()?d:l,(null==d||null==(e=d.feeTypes)?void 0:e.length)>0&&"zh-CN"==F()&&(null==d||d.feeTypes.forEach((e,t)=>{d.packageType==e.type&&(v(t),h(e))}))},[]);let I=e=>({"tool.Support_Alipay_Payment":"支持支付宝支付","tool.Meisi_Account":"梅斯账号","tool.Please_activate_after_reading_and_agreeing_to_the_agreement":"请阅读并同意协议后激活","tool.Free_Trial":"免费试用","market.subscribe":"订阅","tool.Month":"月","tool.Quarter":"季","tool.Year":"年","tool.sS":"订阅成功"})[e]||e,P=new K;window.location.origin.includes("medon.com.cn")||window.location.origin.includes("medsci.cn");let M=()=>{window.open("https://www.medsci.cn/about/index.do?id=27")},$=()=>{_(window.innerWidth>768)},L=()=>{O&&clearInterval(O),null==c||c(!1)},R=(e,t)=>{h(e),v(t),(null==e?void 0:e.coinType)=="人民币"&&0!==e.feePrice&&D(e,u.id)},Z=e=>{let t=setInterval(async()=>{var n;let r=await P.getSubOrder({piId:`${e}`},{Authorization:`Bearer ${H.Z.get("yudaoToken")}`});(null==r||null==(n=r.data)?void 0:n.payStatus)=="PAID"&&(window.location.reload(),clearInterval(t))},2e3);A(t)},F=()=>H.Z.get("ai_apps_lang")?H.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,z=async()=>{let e=await P.freeLimit({},{Authorization:`Bearer ${H.Z.get("yudaoToken")}`});S(null==e?void 0:e.data)},D=async(e,t)=>{if(!(null==e?void 0:e.coinType))return void B.ZP.warning("请选择订阅服务周期");let n=F();if(null==s?void 0:s.userId){let n={appUuid:t,priceId:e.priceId,monthNum:e.monthNum,packageKey:e.packageKey,packageType:e.type};try{m(!0);let t=await P.createSubscription(n,{Authorization:`Bearer ${H.Z.get("yudaoToken")}`});if((null==t?void 0:t.code)==0)if(m(!1),"人民币"==e.coinType&&0!==e.feePrice){let e=t.data,n=window.location.origin,r=(n.includes(".medsci.cn")||n.includes(".medon.com.cn"),`${n}/payLink/${encodeURIComponent(e)}`);j(r),T(JSON.parse(e).piId),Z(JSON.parse(e).piId)}else B.ZP.success(I("tool.sS")),setTimeout(()=>{window.top.location.href=t.data},1e3)}catch(e){m(!1),console.error(e)}}else if(n&&"zh-CN"!=n)window.top.location.href=location.origin+"/"+n+"/login";else{var r,a;null==(r=(a=window).addLoginDom)||r.call(a)}};return(0,b.useEffect)(()=>{var e;return z(),"写作"==l.appType&&localStorage.setItem(`appWrite-${u.id}`,JSON.stringify({appUuid:u.id,directoryMd:l.directoryMd})),$(),window.addEventListener("resize",$),C&&(null==(e=l.feeTypes)?void 0:e.length)==1&&R(l.feeTypes[0],0),()=>{window.removeEventListener("resize",$),O&&clearInterval(O)}},[l,C]),(0,y.jsx)("div",{id:"app",children:(0,y.jsxs)("div",{className:"scale",children:[(0,y.jsxs)("div",{className:"micro_header",children:[(0,y.jsx)("div",{className:"micro_left",children:(0,y.jsxs)("div",{className:"avatar",children:[(0,y.jsx)("img",{src:N,onError:()=>{k("https://img.medsci.cn/web/img/user_icon.png")},alt:""}),(0,y.jsx)("span",{className:"t1",children:(null==s?void 0:s.realName)||(null==s?void 0:s.userName)})]})}),(0,y.jsx)("div",{className:"micro_right",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png",alt:"",onClick:L})})]}),(0,y.jsxs)("div",{className:"micro_main",children:[(0,y.jsx)("div",{className:"micro_main_top",children:(0,y.jsx)("div",{className:"micro_main-sp",children:(0,y.jsxs)("div",{className:"micro_main_temp",children:[((null==(t=l.feeTypes[0])?void 0:t.coinType)=="美元"||(null==(n=l.feeTypes)?void 0:n.length)>1)&&"zh-CN"==F()&&(0,y.jsx)("div",{className:"swiper-vip",children:null==(r=l.feeTypes)?void 0:r.map((e,t)=>(0,y.jsxs)("div",{className:`swiper-vip-item ${(null==d?void 0:d.packageType)=="连续包月"&&"免费"==e.type||(null==d?void 0:d.packageType)=="连续包月"&&"连续包年"==e.type||(null==d?void 0:d.packageType)=="连续包年"&&"免费"==e.type||(null==d?void 0:d.packageType)=="连续包年"&&"连续包月"==e.type?"noClick":""}`,onClick:()=>R(e,t),children:[(0,y.jsx)("div",{className:"newer",style:{left:t%4==0&&0!==t?"6px":"-1px"}}),(0,y.jsxs)("div",{className:`swiper-vip-item-child ${g==t?"sactvie":""}`,children:[(0,y.jsx)("div",{className:"title",children:e.type}),(0,y.jsxs)("div",{className:"pricePc",children:[(0,y.jsx)("span",{children:"人民币"==e.coinType?"\xa5":"$"})," ",e.feePrice]})]})]},t))}),((null==(a=l.feeTypes[0])?void 0:a.coinType)=="美元"||(null==(i=l.feeTypes)?void 0:i.length)>1)&&"zh-CN"!=F()&&(0,y.jsx)("div",{className:"swiper-vip",children:null==(o=l.feeTypes)?void 0:o.map((e,t)=>(0,y.jsxs)("div",{className:"swiper-vip-item",onClick:()=>R(e,t),children:[(0,y.jsx)("div",{className:"newer",style:{left:t%4==0&&0!==t?"6px":"-1px"}}),(0,y.jsxs)("div",{className:`swiper-vip-item-child ${g==t?"sactvie":""}`,children:[(0,y.jsx)("div",{className:"title",children:e.type}),(0,y.jsxs)("div",{className:"pricePc",children:[(0,y.jsx)("span",{children:"人民币"==e.coinType?"\xa5":"$"})," ",e.feePrice]})]})]},t))})]})})}),F()&&"zh-CN"==F()?(0,y.jsx)("div",{className:"micro_main_middle",children:(0,y.jsxs)("div",{className:"micro_main_middle_banner",children:[(0,y.jsxs)("div",{className:"micro_main_middle_title",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"}),"梅斯小智 订阅说明"]}),(0,y.jsxs)("div",{className:"micro_main_middle_content",children:[(0,y.jsxs)("div",{children:["免费：每个自然月内，每个智能体的使用上限",w,"次。次月开始重新计次。"]}),(0,y.jsx)("div",{children:"连续包月：订阅之日起一个月内，每个智能体不限使用次数。"}),(0,y.jsx)("div",{children:"连续包年：订阅之日起一年内，每个智能体不限使用次数"})]})]})}):(0,y.jsx)("div",{className:"micro_main_middle",children:(0,y.jsxs)("div",{className:"micro_main_middle_banner",children:[(0,y.jsxs)("div",{className:"micro_main_middle_title",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png",alt:""}),l.name]}),(0,y.jsx)("div",{className:"micro_main_middle_content",children:l.description})]})}),F()&&"zh-CN"==F()?(0,y.jsxs)("div",{className:"micro_main_bottom onborder",children:[("1"==d.subStatus||"3"==d.subStatus)&&f.type==d.packageType&&(0,y.jsxs)("div",{className:"result",children:[d.subAt," 已订阅"]}),"1"==d.subStatus&&"免费"==d.packageType&&"免费"==f.type&&(0,y.jsx)("div",{className:"result",children:"免费使用中…"}),"3"==d.subStatus&&(0,y.jsxs)("div",{className:"result",children:[d.unSubAt," 取消订阅"]}),"3"==d.subStatus&&(0,y.jsxs)("div",{className:"result",children:["您的订阅可使用至 ",d.expireAt]}),"连续包月"==d.packageType&&"1"==d.subStatus&&(0,y.jsx)("div",{className:"result",children:"连续包月中…"}),"连续包年"==d.packageType&&"1"==d.subStatus&&(0,y.jsx)("div",{className:"result",children:"连续包年中…"}),(0,y.jsxs)("div",{className:"btns",children:["连续包月"==d.packageType&&"1"==d.subStatus?(0,y.jsx)("button",{className:"cursor-pointer",onClick:()=>{sY.Vq.confirm({title:"提示",message:`取消包月在${d.expireAt}号生效，再次使用需要重新订阅。是否确认取消？`,confirmButtonColor:"#D7813F",onCancel:()=>{},onConfirm:async()=>{await P.cancelSubscription({},{Authorization:`Bearer ${H.Z.get("yudaoToken")}`}),L()}})},children:"取消包月"}):null,"连续包年"==d.packageType&&"1"==d.subStatus?(0,y.jsx)("button",{className:"cursor-pointer",onClick:()=>{sY.Vq.confirm({title:"提示",message:`取消包月在${d.expireAt}号生次生效，再次使用需要重新订阅。是否确认取消？`,confirmButtonColor:"#D7813F",onCancel:()=>{L()},onConfirm:async()=>{await P.cancelSubscription({},{Authorization:`Bearer ${H.Z.get("yudaoToken")}`}),L()}})},children:"取消包年"}):null]}),(0,y.jsx)("div",{className:"micro_pay",children:f.feePrice>0&&"免费"!=f.type&&"0"==d.subStatus||"免费"!=f.type&&"2"==d.subStatus||"免费"==d.packageType&&"免费"!=f.type?(0,y.jsx)("div",{className:"micro_pay",children:(0,y.jsxs)("div",{className:"micro_pay_right",children:[p&&(0,y.jsx)("div",{className:"noQrCode"}),!p&&x&&(0,y.jsx)("div",{className:"qr-code",children:(0,y.jsx)(lo.Q,{value:x,size:131,fgColor:"#000",level:"L"})}),(0,y.jsxs)("div",{className:"price",children:[(0,y.jsxs)("div",{className:"micro_way",children:[(0,y.jsx)("div",{className:"box",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})}),(0,y.jsx)("span",{children:I("tool.Support_Alipay_Payment")})]}),(0,y.jsxs)("span",{className:"t1",children:[I("tool.Support_Alipay_Payment"),(0,y.jsx)("span",{className:"bd",children:f.feePrice}),"人民币"==f.coinType?"\xa5":"$","/",3==f.monthNum?I("tool.Quarter"):12==f.monthNum?I("tool.Year"):I("tool.Month")]}),(0,y.jsxs)("span",{className:"t2",children:[I("tool.Meisi_Account"),"：",null==s?void 0:s.userName]}),(0,y.jsxs)("span",{className:"t3",onClick:M,children:[I("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"),(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})]})]})]})}):null})]}):null,f.coinType&&0!==f.feePrice&&"zh-CN"!=F()&&(null==f?void 0:f.coinType)=="人民币"&&(0,y.jsx)("div",{className:"micro_main_bottom",children:(0,y.jsx)("div",{className:"micro_pay",children:(0,y.jsxs)("div",{className:"micro_pay_right",children:[p&&(0,y.jsx)("div",{className:"noQrCode"}),!p&&x&&(0,y.jsx)("div",{className:"qr-code",children:(0,y.jsx)(lo.Q,{value:x,size:131,fgColor:"#000",level:"L"})}),(0,y.jsxs)("div",{className:"price",children:[(0,y.jsxs)("div",{className:"micro_way",children:[(0,y.jsx)("div",{className:"box",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})}),(0,y.jsx)("span",{children:I("tool.Support_Alipay_Payment")})]}),(0,y.jsxs)("span",{className:"t1",children:[I("tool.Support_Alipay_Payment"),(0,y.jsx)("span",{className:"bd",children:f.feePrice}),"人民币"==f.coinType?"\xa5":"$","/",3==f.monthNum?I("tool.Quarter"):12==f.monthNum?I("tool.Year"):I("tool.Month")]}),(0,y.jsxs)("span",{className:"t2",children:[I("tool.Meisi_Account"),"：",null==s?void 0:s.userName]}),(0,y.jsxs)("span",{className:"t3",onClick:M,children:[I("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"),(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})]})]})]})})}),f.coinType&&"zh-CN"==F()&&0==f.feePrice&&("0"==d.subStatus||"2"==d.subStatus)&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>D(f,u.id),children:I("tool.Free_Trial")})}),f.coinType&&"zh-CN"!=F()&&0==f.feePrice&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>D(f,u.id),children:I("tool.Free_Trial")})}),f.coinType&&f.feePrice>0&&(null==f?void 0:f.coinType)=="美元"&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>D(f,u.id),children:I("market.subscribe")})})]})]})})},ld=e=>{var t,n;let{userInfo:r,currentItem:a={},onClose:i,subStatusDetail:o={}}=e,[s,l]=(0,b.useState)(!0),[c,u]=(0,b.useState)(!0),[d,p]=(0,b.useState)(!1),[m,f]=(0,b.useState)({}),[h,g]=(0,b.useState)(!1),[v,x]=(0,b.useState)(!1),[j,w]=(0,b.useState)(!1),[S,N]=(0,b.useState)(!1),[k,C]=(0,b.useState)(0),[_,E]=(0,b.useState)((null==r?void 0:r.avatar)||"https://img.medsci.cn/web/img/user_icon.png"),T=(0,b.useRef)(null),O=new K;window.location.origin.includes("medon.com.cn")||window.location.origin.includes("medsci.cn"),(0,b.useEffect)(()=>{var e,t;L();let n=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${n}px`),"写作"==a.appType&&localStorage.setItem(`appWrite-${a.appUuid}`,JSON.stringify({appUuid:a.appUuid,directoryMd:a.directoryMd})),(null==(e=a.feeTypes)?void 0:e.length)==1&&a.feeTypes[0].feePrice>=0&&f(a.feeTypes[0]),r&&(null==(t=JSON.parse(r))?void 0:t.userId)&&p(!0),"medsci"==new URLSearchParams(window.top.location.search).get("source")&&x(!0)},[a,r]),(0,b.useEffect)(()=>{var e;a.feeTypes="zh-CN"==I()?null==o?void 0:o.feeTypes:a.feeTypes,(null==o||null==(e=o.feeTypes)?void 0:e.length)>0&&"zh-CN"==I()&&(null==o||o.feeTypes.forEach((e,t)=>{(null==o?void 0:o.packageType)==e.type&&f(e)}))},[]);let A=()=>navigator.userAgent.includes("medsci_app"),I=()=>H.Z.get("ai_apps_lang")?H.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,P=()=>{let e=I();if(e&&"zh-CN"!=e)window.top.location.href=location.origin+"/"+e+"/login";else{var t,n;null==(t=(n=window).addLoginDom)||t.call(n)}},M=()=>{l(!1),u(!0)},$=()=>{let e="https://www.medsci.cn/about/index.do?id=27";A()?window.top.location.href=e:window.open(e)},L=async()=>{let e=await O.freeLimit({},{Authorization:`Bearer ${H.Z.get("yudaoToken")}`});C(null==e?void 0:e.data)},R=e=>{f(e)},Z=e=>{w(e.target.checked)},F=async(e,t,n)=>{var a;if(!j&&n){N(!0),setTimeout(()=>N(!1),500);return}let i=I();if(r&&(null==(a=JSON.parse(r))?void 0:a.userId)){let n={appUuid:t,priceId:e.priceId,monthNum:e.monthNum,packageKey:e.packageKey,packageType:e.type};try{let t=await O.createSubscription(n,{Authorization:`Bearer ${H.Z.get("yudaoToken")}`});if(0==t.code)if("人民币"==e.coinType&&0!==e.feePrice){let e=await O.createAliSub(JSON.parse(t.data));e&&0==e.code&&(window.top.location.href=e.data)}else setTimeout(()=>{window.top.location.href=t.data},1e3)}catch(e){B.ZP.error("订阅失败")}}else i&&"zh-CN"!=i?window.top.location.href=location.origin+"/"+i+"/login":P()};return(0,y.jsxs)("div",{className:`vip ${A()?"sp":""}`,children:[!A()&&(0,y.jsx)("div",{className:"vip-head text-center py-2 text-lg font-medium text-gray-800",children:a.name}),(0,y.jsxs)("div",{className:"vip-introduce bg-gray-900 pt-9 pb-4 relative",children:[(0,y.jsx)("img",{className:"crown absolute right-1 top-0 h-9 object-contain",src:"https://static.medsci.cn/public-image/ms-image/1dcd7d10-58af-11ec-8e2f-1389d01aad85_crown.png",alt:""}),(0,y.jsx)("div",{className:"box mx-3 flex items-center justify-between bg-[url('https://static.medsci.cn/public-image/ms-image/21ad8b80-58b1-11ec-8e2f-1389d01aad85_vip-h5.png')] bg-cover p-3 rounded",children:d?(0,y.jsxs)("div",{className:"box-left flex items-center",children:[(0,y.jsx)("img",{className:"avatar w-8 h-8 rounded-full mr-3",src:_,alt:"",onError:()=>{E("https://img.medsci.cn/web/img/user_icon.png")}}),(0,y.jsx)("div",{className:"box-word",children:(0,y.jsx)("span",{className:"t1 text-lg text-[#58342c]",children:JSON.parse(r).realName||JSON.parse(r).userName})})]}):(0,y.jsxs)("div",{className:"box-left-1 flex items-center",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/web/img/user_icon.png",alt:"",className:"w-8 h-8 rounded-full mr-3"}),(0,y.jsxs)("div",{className:"left2",children:[(0,y.jsx)("span",{className:"t1 text-lg text-[#58342c] cursor-pointer",onClick:P,children:"立即登录"}),(0,y.jsx)("span",{className:"t2 text-sm text-[#7f5947] mt-1",children:"请登录后购买"})]})]})})]}),(0,y.jsxs)("div",{style:{paddingBottom:"zh-CN"==I()&&("3"==o.subStatus||"1"==o.subStatus&&"免费"==o.packageType)&&m.type==o.packageType?"0":""},className:"vip-main bg-white rounded-t-3xl -mt-14 z-10 ",children:[(0,y.jsx)("div",{className:"vip-one pl-3",children:(0,y.jsx)("div",{className:"big overflow-auto mr-3",children:"zh-CN"==I()?(0,y.jsx)("ul",{ref:T,className:"flex mt-2 mb-1",children:null==(t=a.feeTypes)?void 0:t.map((e,t)=>(0,y.jsxs)("li",{className:`min-w-[135px] w-[135px] bg-white border border-gray-300 rounded-lg mr-3 text-center cursor-pointer ${e.type==m.type?"sactvie":""} ${(null==o?void 0:o.packageType)=="连续包月"&&"免费"==e.type||(null==o?void 0:o.packageType)=="连续包月"&&"连续包年"==e.type||(null==o?void 0:o.packageType)=="连续包年"&&"免费"==e.type||(null==o?void 0:o.packageType)=="连续包年"&&"连续包月"==e.type?"noClick":""}`,onClick:()=>R(e),children:[(0,y.jsx)("div",{className:"title text-center ellipsis-2-lines mx-auto mt-6 max-w-[102.5px] text-sm font-medium text-gray-800",children:e.type}),(0,y.jsxs)("div",{className:"prices text-2xl font-bold text-orange-400 mt-2 mb-6",children:[(0,y.jsx)("span",{className:"text-lg",children:"人民币"==e.coinType?"\xa5":"$"}),e.feePrice]}),e.originalPrice&&(0,y.jsxs)("div",{className:"isfava relative text-xs text-gray-500 line-through",children:["人民币"==e.coinType?"\xa5":"$",e.feePrice]})]},t))}):(0,y.jsx)("ul",{ref:T,className:"flex mt-2 mb-1",children:null==(n=a.feeTypes)?void 0:n.map((e,t)=>(0,y.jsxs)("li",{className:`min-w-[135px] w-[135px] bg-white border border-gray-300 rounded-lg mr-3 text-center cursor-pointer ${e.type==m.type?"sactvie":""}`,onClick:()=>R(e),children:[(0,y.jsx)("div",{className:"title text-center ellipsis-2-lines mx-auto mt-6 max-w-[102.5px] text-sm font-medium text-gray-800",children:e.type}),(0,y.jsxs)("div",{className:"prices text-2xl font-bold text-orange-400 mt-2 mb-6",children:[(0,y.jsx)("span",{className:"text-lg",children:"人民币"==e.coinType?"\xa5":"$"}),e.feePrice]}),e.originalPrice&&(0,y.jsxs)("div",{className:"isfava relative text-xs text-gray-500 line-through",children:["人民币"==e.coinType?"\xa5":"$",e.feePrice]})]},t))})})}),"zh-CN"==I()?(0,y.jsxs)("div",{children:[(0,y.jsx)("div",{className:"vip-two",style:{paddingBottom:"0"},children:(0,y.jsxs)("div",{className:"vip-two_banner",children:[(0,y.jsxs)("div",{className:"vip-two_title",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"}),"梅斯小智 订阅说明"]}),(0,y.jsxs)("div",{className:"vip-two_content",children:[(0,y.jsxs)("div",{children:["免费：每个自然月内，每个智能体的使用上限",k,"次。次月开始重新计次。"]}),(0,y.jsx)("div",{children:"连续包月：订阅之日起一个月内，每个智能体不限使用次数。"}),(0,y.jsx)("div",{children:"连续包年：订阅之日起一年内，每个智能体不限使用次数"})]})]})}),((null==o?void 0:o.subStatus)=="1"||(null==o?void 0:o.subStatus)=="3")&&m.type==(null==o?void 0:o.packageType)&&(0,y.jsxs)("div",{style:{textAlign:"center",fontSize:"14px"},children:[null==o?void 0:o.subAt," 已订阅"]}),(null==o?void 0:o.subStatus)=="1"&&(null==o?void 0:o.packageType)=="免费"&&"免费"==m.type&&(0,y.jsx)("div",{style:{textAlign:"center",fontSize:"14px"},children:"免费使用中…"}),(null==o?void 0:o.subStatus)=="3"&&m.type==o.packageType&&(0,y.jsxs)("div",{style:{textAlign:"center",fontSize:"14px"},children:[null==o?void 0:o.unSubAt," 取消订阅"]}),(null==o?void 0:o.subStatus)=="3"&&m.type==o.packageType&&(0,y.jsxs)("div",{style:{textAlign:"center",fontSize:"14px"},children:["您的订阅可使用至 ",null==o?void 0:o.expireAt]}),(null==o?void 0:o.packageType)=="连续包月"&&(null==o?void 0:o.subStatus)=="1",(null==o?void 0:o.packageType)=="连续包月"&&(null==o?void 0:o.subStatus)=="1"?(0,y.jsx)("div",{style:{textAlign:"center",fontSize:"14px"},children:"连续包月中…"}):null,(null==o?void 0:o.packageType)=="连续包年"&&(null==o?void 0:o.subStatus)=="1"&&(0,y.jsx)("div",{style:{textAlign:"center",fontSize:"14px"},children:"连续包年中…"})]}):(0,y.jsx)("div",{className:"vip-two border border-gray-200 rounded m-3 p-2",children:(0,y.jsxs)("div",{className:"vip-two_banner bg-[url('https://img.medsci.cn/202503/48a49f899b4544e39a864d1821b3555a-lRXRIQx3q1VB.png')] bg-cover p-3",children:[(0,y.jsxs)("div",{className:"vip-two_title flex items-center text-sm font-medium text-orange-600",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png",alt:"",className:"w-2 h-2 mx-1"}),a.name]}),(0,y.jsx)("div",{className:"vip-two_content bg-white p-3 text-sm text-gray-700 leading-5 max-h-[calc(var(--vh)*20)] overflow-auto rounded",children:a.description})]})}),"zh-CN"==I()&&(0,y.jsxs)("div",{className:`vip-pay btns ${"连续包月"==o.packageType&&"1"==o.subStatus||"连续包年"==o.packageType&&"1"==o.subStatus?"":"CN_btns"}`,children:[(null==o?void 0:o.packageType)=="连续包月"&&(null==o?void 0:o.subStatus)=="1"&&(0,y.jsx)(J.ZP,{onClick:()=>{sY.Vq.confirm({title:"提示",message:`取消包月在${null==o?void 0:o.expireAt}号生效，再次使用需要重新订阅。是否确认取消？`,confirmButtonColor:"#D7813F",onCancel:()=>{},onConfirm:async()=>{await O.cancelSubscription({},{Authorization:`Bearer ${H.Z.get("yudaoToken")}`}),i()}})},type:"primary",children:"取消包月"}),(null==o?void 0:o.packageType)=="连续包年"&&(null==o?void 0:o.subStatus)=="1"&&(0,y.jsx)(J.ZP,{onClick:()=>{sY.Vq.confirm({title:"提示",message:`取消包月在${null==o?void 0:o.expireAt}号生次生效，再次使用需要重新订阅。是否确认取消？`,confirmButtonColor:"#D7813F",onCancel:()=>{},onConfirm:async()=>{await O.cancelSubscription({},{Authorization:`Bearer ${H.Z.get("yudaoToken")}`}),i()}})},type:"primary",children:"取消包年"})]}),m.feePrice>0&&(null==m?void 0:m.coinType)=="人民币"&&"zh-CN"==I()&&"免费"!=m.type&&(null==o?void 0:o.subStatus)=="0"||"免费"!=m.type&&(null==o?void 0:o.subStatus)=="2"||(null==o?void 0:o.packageType)=="免费"&&"免费"!=m.type&&(0,y.jsx)("div",{className:"vip-three mt-3",children:(0,y.jsxs)("div",{className:`pay mx-3 p-4 rounded-lg bg-gradient-to-b from-[#fffaf6] to-[#fff8f0] ${h?"h-28":""}`,children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:"",className:"w-15 mb-3"}),(0,y.jsxs)("div",{className:"item flex justify-between items-center bg-white border border-orange-300 rounded p-2 mb-3 cursor-pointer",onClick:M,children:[(0,y.jsxs)("div",{className:"item-left flex items-center",children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:"",className:"w-5 h-5 mr-2"}),(0,y.jsx)("span",{className:"text-sm text-gray-800",children:"支付宝支付"})]}),(0,y.jsx)("div",{className:"item-right w-5 h-5",children:c&&(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:"",className:"w-full"})})]})]})}),m.feePrice>0&&(null==m?void 0:m.coinType)=="人民币"&&"zh-CN"!=I()&&(0,y.jsx)("div",{className:"vip-three mt-3",children:(0,y.jsxs)("div",{className:`pay mx-3 p-4 rounded-lg bg-gradient-to-b from-[#fffaf6] to-[#fff8f0] ${h?"h-28":""}`,children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:"",className:"w-15 mb-3"}),(0,y.jsxs)("div",{className:"item flex justify-between items-center bg-white border border-orange-300 rounded p-2 mb-3 cursor-pointer",onClick:M,children:[(0,y.jsxs)("div",{className:"item-left flex items-center",children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:"",className:"w-5 h-5 mr-2"}),(0,y.jsx)("span",{className:"text-sm text-gray-800",children:"支付宝支付"})]}),(0,y.jsx)("div",{className:"item-right w-5 h-5",children:c&&(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:"",className:"w-full"})})]})]})})]}),m.feePrice>=0&&"zh-CN"==I()&&("免费"==m.type&&"0"==o.subStatus||"免费"!=m.type&&"0"==o.subStatus||"免费"!=m.type&&"2"==o.subStatus||"免费"==o.packageType&&"免费"!=m.type)?(0,y.jsx)("div",{className:`vip-pay btns fixed bottom-0 w-full bg-white shadow-lg flex justify-around items-center h-24 z-20  ${((null==o?void 0:o.subStatus)=="1"||(null==o?void 0:o.subStatus)=="3")&&"免费"==m.type?"CN_btns":""}`,children:m.feePrice>0?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{className:"pay-left ml-2",children:[(0,y.jsx)("div",{className:"t1 text-sm font-medium text-orange-400 mb-1",children:a.name}),(0,y.jsxs)("div",{className:`t2 flex items-start text-xs text-gray-500 ${S?"shake":""}`,children:[(0,y.jsx)(ll.Z,{checked:j,onChange:Z,style:{fontSize:"14px"},className:"mr-1"}),(0,y.jsxs)("span",{onClick:$,className:"cursor-pointer",children:["请在阅读并同意",(0,y.jsx)("span",{className:"text-blue-500",children:"协议"}),"后开通"]})]})]}),(0,y.jsx)("div",{className:"pay-right mr-2 w-48 h-12 bg-gradient-to-r from-[#fde39b] to-[#fbd786] rounded-full flex items-center justify-center cursor-pointer",onClick:()=>F(m,a.appUuid,"ali"),children:(0,y.jsxs)("span",{className:"text-sm text-[#614018]",children:[m.feePrice,"元确认协议并支付"]})})]}):(0,y.jsx)(J.ZP,{className:"w-48 h-12 rounded-full",onClick:()=>F(m,a.appUuid,""),children:0==m.feePrice?"免费试用":"订阅"})}):null,m.feePrice>=0&&"zh-CN"!=I()?(0,y.jsx)("div",{className:"vip-pay btns fixed bottom-0 w-full bg-white shadow-lg flex justify-around items-center h-24 z-20",children:m.feePrice>0?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{className:"pay-left ml-2",children:[(0,y.jsx)("div",{className:"t1 text-sm font-medium text-orange-400 mb-1",children:a.name}),(0,y.jsxs)("div",{className:`t2 flex items-start text-xs text-gray-500 ${S?"shake":""}`,children:[(0,y.jsx)(ll.Z,{checked:j,onChange:Z,style:{fontSize:"14px"},className:"mr-1"}),(0,y.jsxs)("span",{onClick:$,className:"cursor-pointer",children:["请在阅读并同意",(0,y.jsx)("span",{className:"text-blue-500",children:"协议"}),"后开通"]})]})]}),(0,y.jsx)("div",{className:"pay-right mr-2 w-48 h-12 bg-gradient-to-r from-[#fde39b] to-[#fbd786] rounded-full flex items-center justify-center cursor-pointer",onClick:()=>F(m,a.appUuid,"ali"),children:(0,y.jsxs)("span",{className:"text-sm text-[#614018]",children:[m.feePrice,"元确认协议并支付"]})})]}):(0,y.jsx)(J.ZP,{type:"primary",className:"w-48 h-12 rounded-full",onClick:()=>F(m,a.appUuid,""),children:0==m.feePrice?"免费试用":"订阅"})}):null]})};function lp(e){var t,n,r,a,i,o,s,l,c;let{appConfig:u,appInfo:d,appParameters:p,difyApi:m,conversationId:f,conversationItems:h,conversationName:g,onConversationIdChange:v,conversationListLoading:x,onAddConversation:j,onItemsChange:w,conversationItemsChangeCallback:S,appConfigLoading:N,handleStartConfig:k,inputParams:C,setInputParams:_,modal2OpenF:E,onClose:T,onsubmit:A,subStatusDetails:P,changeSubStatusDetails:M,selectedAppId:$,appList:L}=e,R=(0,F.k6)(),Z=new s2,z=new K,D=new URLSearchParams(window.top.location.search),U=I(),W=R.location.pathname.match(/^\/ai-chat\/([^/]+)$/),q=W?W[1]:"",G=(0,b.useRef)(()=>{});(0,b.useEffect)(()=>()=>{G.current()},[]),(0,b.useEffect)(()=>{ex(E)},[E]);let[X,Q]=(0,b.useState)(!1),[Y,ee]=(0,b.useState)([]),[et,en]=(0,b.useState)([]),er=s3({conversationId:f}),ea=s3({inputParams:C}),ei=(0,b.useRef)([]),eo=async e=>{en((await m.getNextSuggestions({message_id:e})).data)},es=async e=>{var t,n,r,a,i;if(!e||O(e))return;let o=await m.getConversationHistory(e);if(!(null==o||null==(t=o.data)?void 0:t.length))return;let s=[];(null==o||null==(n=o.data)?void 0:n.length)&&(null==(r=Object.values(null==(i=o.data)||null==(a=i[0])?void 0:a.inputs))?void 0:r.length)&&_({article_id:D.get("article_id"),qa_id:D.get("qa_id")}),o.data.forEach(e=>{let t=sQ()(1e3*e.created_at).format("YYYY-MM-DD HH:mm:ss");s.push({id:e.id,content:e.query,status:"success",isHistory:!0,files:e.message_files,role:"user",created_at:t},{id:e.id,content:e.answer,status:"error"===e.status?e.status:"success",error:e.error||"",isHistory:!0,feedback:e.feedback,agentThoughts:e.agent_thoughts||[],retrieverResources:e.retriever_resources||[],role:"ai",created_at:t})}),ed([]),ee(s),(null==s?void 0:s.length)&&(null==p?void 0:p.suggested_questions_after_answer.enabled)&&eo(s[s.length-1].id)},{agent:el,onRequest:ec,messages:eu,setMessages:ed,currentTaskId:ep}=s9({latestProps:er,latestState:ea,filesRef:ei,getNextSuggestions:eo,appParameters:p,abortRef:G,getConversationMessages:es,onConversationIdChange:v,difyApi:m,appUuid:(null==u?void 0:u.id)||""}),em=async()=>{f&&!O(f)&&await es(f),Q(!1)};(0,b.useEffect)(()=>{Q(!0),ed([]),en([]),ee([]),em(),O(f)&&("medsci-ask"!=q&&_({}),D.get("article_id")&&(_({article_id:D.get("article_id"),qa_id:D.get("qa_id")}),z.qaList({articleId:D.get("article_id")||"",encryptionId:D.get("qa_id")||""},{Authorization:`Bearer ${H.Z.get("yudaoToken")}`,Accept:"application/json"}).then(e=>{var t;A(eg((null==e||null==(t=e.data[0])?void 0:t.question)||""))})))},[f]);let ef=async e=>{var t;let n=ev();if("nologin"==m.options.user)return console.log("未登录 onFocus",n),n&&"zh-CN"!=n?window.top.location.href=location.origin+"/"+n+"/login":window.addLoginDom(),!1;if(q.includes("elavax-pro")||q.includes("novax-pro")){let t=await z.bindAppUser({appUuid:(null==u?void 0:u.id)||"",appNameEn:q},{Authorization:`Bearer ${H.Z.get("yudaoToken")}`,Accept:"application/json"});if((null==t?void 0:t.data.remainNum)!=0)return ec({content:e.data.description}),!1}let r=W?W[1]:"",a=await Z.getApp(r,{Authorization:`Bearer ${H.Z.get("yudaoToken")}`,Accept:"application/json"});if(!(null==a?void 0:a.info.appUser)||(null==a||null==(t=a.info.appUser)?void 0:t.status)==2)return document.getElementsByTagName("textarea")[0].blur(),ex(!0),!1;ec({content:e.data.description})},eh=(0,b.useMemo)(()=>{var e,t;return!((null==p||null==(e=p.user_input_form)?void 0:e.length)&&(!f||O(f)))||(null==p||null==(t=p.user_input_form)?void 0:t.every(e=>!!C[e["text-input"].variable]))||!1},[p,C,f]),eg=(e,t)=>{if(document.getElementsByTagName("textarea").length>0&&document.getElementsByTagName("textarea")[0].blur(),!eh){let e=(null==p?void 0:p.user_input_form.filter(e=>{let t=e["text-input"];return!C[t.variable]&&t.required}).map(e=>e["text-input"].label))||[];B.ZP.error(`${e.join("、")}不能为空`);return}ei.current=(null==t?void 0:t.files)||[],ec({content:e,files:null==t?void 0:t.files})},ev=()=>H.Z.get("ai_apps_lang")?H.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,[ey,ex]=(0,b.useState)(!1),eb=e=>{ex(e),T(e)},ej=async()=>{var e;let t=ev();if("nologin"==m.options.user)return console.log("未登录 onFocus",t),t&&"zh-CN"!=t?top.location.href=location.origin+"/"+t+"/login":window.addLoginDom(),!1;if(q.includes("elavax-pro")||q.includes("novax-pro")){let e=await z.bindAppUser({appUuid:(null==u?void 0:u.id)||"",appNameEn:q},{Authorization:`Bearer ${H.Z.get("yudaoToken")}`,Accept:"application/json"});if((null==e?void 0:e.data.remainNum)!=0)return!1}let n=W?W[1]:"",r=await Z.getApp(n,{Authorization:`Bearer ${H.Z.get("yudaoToken")}`,Accept:"application/json"});return!!(null==r?void 0:r.info.appUser)&&(null==r||null==(e=r.info)?void 0:e.appUser.status)!=2||(document.getElementsByTagName("textarea")[0].blur(),ex(!0),!1)},ew=(0,b.useMemo)(()=>eu.map(e=>({id:e.id,status:e.status,error:e.message.error||"",workflows:e.message.workflows,agentThoughts:e.message.agentThoughts,retrieverResources:e.message.retrieverResources,files:e.message.files,content:e.message.content,role:"local"===e.status?"user":"ai"})),[eu]),eS=(0,y.jsx)(sK.Z,{trigger:["click"],content:(0,y.jsxs)("div",{className:"w-60",children:[(0,y.jsx)("div",{className:"text-base font-semibold",children:"对话列表"}),(0,y.jsx)(V.Z,{spinning:x,children:(null==h?void 0:h.length)?(0,y.jsx)(sG,{renameConversationPromise:(e,t)=>null==m?void 0:m.renameConversation({conversation_id:e,name:t}),appConfig:u,deleteConversationPromise:null==m?void 0:m.deleteConversation,items:h,activeKey:f,onActiveChange:v,onItemsChange:w,refreshItems:S,onchangeModal2Open:e=>ex(e)}):(0,y.jsx)(ol.Z,{description:"暂无会话"})}),(0,y.jsx)(J.ZP,{className:"mt-3",onClick:j,block:!0,type:"primary",children:"新增对话"})]}),placement:U?"bottom":"bottomLeft",children:(0,y.jsxs)("div",{className:"flex w-full  items-center justify-start",children:[(0,y.jsx)(sX.Z,{className:"mr-3 cursor-pointer"}),(0,y.jsx)("span",{className:"truncate max-w-[80%]",children:g||s5})]})});return x||N?(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):u?(0,y.jsxs)("div",{className:"flex h-screen flex-col overflow-hidden flex-1",children:[!U&&ey?(0,y.jsx)(sW.Z,{centered:!0,open:ey,onOk:()=>ex(!1),onCancel:()=>ex(!1),children:(null==u?void 0:u.id)&&((null==u||null==(n=u.info)||null==(t=n.appNameEn)?void 0:t.includes("elavax"))||(null==u||null==(a=u.info)||null==(r=a.appNameEn)?void 0:r.includes("novax")))?(0,y.jsx)(ls,{onClose:eb,appConfig:u,currentItem:null==u?void 0:u.info,userInfo:H.Z.get("userInfo"),subStatusDetail:P}):(0,y.jsx)(lu,{onClose:eb,appConfig:u,currentItem:null==u?void 0:u.info,userInfo:H.Z.get("userInfo"),subStatusDetail:P})}):null,U&&ey?(0,y.jsx)(sY.GI,{visible:ey,style:{width:"100%",height:"90%"},position:"bottom",closeable:!0,round:!0,onClose:()=>eb(!1),children:(null==u?void 0:u.id)&&((null==u||null==(o=u.info)||null==(i=o.appNameEn)?void 0:i.includes("elavax"))||(null==u||null==(l=u.info)||null==(s=l.appNameEn)?void 0:s.includes("novax")))?(0,y.jsx)(lc,{onClose:()=>eb(!1),currentItem:null==u?void 0:u.info,userInfo:H.Z.get("userInfo"),subStatusDetail:P}):(0,y.jsx)(ld,{onClose:()=>eb(!1),currentItem:null==u?void 0:u.info,userInfo:H.Z.get("userInfo"),subStatusDetail:P})}):null,U?(0,y.jsx)(li,{subStatusDetail:P,appConfig:u,appList:L,selectedAppId:$,showSubscribe:()=>{M(),ex(!0)},centerChildren:eS}):null,(0,y.jsxs)("div",{className:"flex-1 overflow-hidden relative",children:[X?(0,y.jsx)("div",{className:"absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):null,h.length?f&&eh?(0,y.jsx)(sq,{appConfig:u,conversationId:f,appParameters:p,nextSuggestions:et,messageItems:[...Y,...ew],isRequesting:el.isRequesting(),onPromptsItemClick:ef,onSubmit:eg,onCancel:async()=>{G.current(),ep&&(await m.stopTask(ep),es(f))},feedbackApi:m.feedbackMessage,feedbackCallback:e=>{es(e)},uploadFileApi:m.uploadFile,difyApi:m,onFocus:ej}):(null==p||null==(c=p.user_input_form)?void 0:c.length)?(0,y.jsx)(lr,{conversationId:f,formFilled:eh,onStartConversation:e=>{_(e),f||"medsci-ask"==q||j()},appInfo:d,user_input_form:null==p?void 0:p.user_input_form}):(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):(0,y.jsx)(lr,{conversationId:f,formFilled:eh,onStartConversation:e=>{_(e),f||j()},appInfo:d,user_input_form:null==p?void 0:p.user_input_form})]})]}):(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(ol.Z,{description:"请先配置 Dify 应用",children:(0,y.jsx)(J.ZP,{type:"primary",onClick:k,children:"开始配置"})})})}var lm=n(33405);let{Option:lf}=od.Z,lh=[{code:"zh-CN",name:"简体中文",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"zh-TW",name:"繁體中文",flag:"\uD83C\uDDF9\uD83C\uDDFC"},{code:"en",name:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{code:"vi",name:"Tiếng Việt",flag:"\uD83C\uDDFB\uD83C\uDDF3"},{code:"es",name:"Espa\xf1ol",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"ar",name:"العربية",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"id",name:"Bahasa Indonesia",flag:"\uD83C\uDDEE\uD83C\uDDE9"},{code:"pt",name:"Portugu\xeas",flag:"\uD83C\uDDE7\uD83C\uDDF7"},{code:"ja",name:"日本語",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"ko",name:"한국어",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"ms",name:"Bahasa Melayu",flag:"\uD83C\uDDF2\uD83C\uDDFE"}],lg=e=>{let{className:t,size:n="middle",showIcon:r=!0}=e,{i18n:a}=(0,ou.$G)();return(0,y.jsx)(od.Z,{value:a.language,onChange:e=>{a.changeLanguage(e)},className:t,size:n,style:{minWidth:120},suffixIcon:r?(0,y.jsx)(lm.Z,{}):void 0,popupMatchSelectWidth:!1,children:lh.map(e=>(0,y.jsxs)(lf,{value:e.code,children:[(0,y.jsx)("span",{style:{marginRight:8},children:e.flag}),e.name]},e.code))})},lv=(e,t)=>{let[n]=(0,b.useState)(new Map);return(0,b.useEffect)(()=>{let r=new Map(e.map(e=>[e[t],e]));n.clear(),r.forEach((e,t)=>n.set(t,e))},[e,t,n]),n},ly=n.p+"static/image/kefu.1767c230.png",lx=n.p+"static/image/qrcode.74f61641.png",lb=()=>{let[e,t]=(0,b.useState)(!1),[n,r]=(0,b.useState)(!1),[a,i]=(0,b.useState)(!1);return(0,b.useEffect)(()=>{let e=()=>{let e=window.innerWidth<=768;i(e),e?t(!0):t(!1),r(!1)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,y.jsxs)("div",{className:`assistant-container ${e?"is-collapsed":""}`,children:[(0,y.jsx)("div",{className:"assistant-icon",onClick:()=>{let n=!e;t(n),n?setTimeout(()=>{r(!1)},300):r(!0)},children:(0,y.jsx)("img",{src:ly,className:"fas fa-user-astronaut",alt:"客服"})}),(0,y.jsxs)("div",{className:`qr-codes ${n?"is-visible":""}`,children:[(0,y.jsx)("img",{src:lx,alt:"QR Code"}),"扫码添加小助手"]})]})},lj=(0,sV.kc)(e=>{let{token:t,css:n}=e;return{layout:n`
			font-family: AlibabaPuHuiTi, ${t.fontFamily}, sans-serif;
		`,menu:n`
			background: ${t.colorBgLayout}80;
		`}}),lw=e=>{let[t,n]=(0,b.useState)(!1),r=(0,F.k6)(),a=new s2,{extComponents:i,appConfig:o,useAppInit:s,renderCenterTitle:l,handleStartConfig:c,initLoading:u,selectedAppId:d,appList:p}=e,{...m}=C(),{user:f}=m,{t:h}=(0,ou.$G)(),{styles:g}=lj(),[v]=(0,b.useState)(oe({user:f,apiBase:"",apiKey:"",appId:""})),x=new URLSearchParams(window.top.location.search),[j,w]=(0,b.useState)([]),S=lv(j,"key"),[N,k]=(0,b.useState)(!1),[_,E]=(0,b.useState)(),[T,O]=(0,b.useState)(),[A,I]=(0,b.useState)(),[P,M]=(0,b.useState)(!1),[$,L]=(0,b.useState)({}),[R,Z]=(0,b.useState)({}),z=r.location.pathname.match(/^\/ai-chat\/([^/]+)$/),D=z?z[1]:"",[H,U]=(0,b.useState)(null),W=()=>{var e;if(null==A||null==(e=A.user_input_form)?void 0:e.length){let e={...$};null==A||A.user_input_form.forEach(t=>{e[t["text-input"].variable]=void 0}),L(e)}},q=async()=>{O(void 0),v&&(M(!0),O({name:o.info.name,description:o.info.description,tags:[]}),I(await v.getAppParameters()),M(!1))};s(v,()=>{q().then(()=>{G().then(()=>{("medsci-ask"==D||x.get("fromPlatform"))&&Q()})}),W(),E(void 0)});let G=async()=>{k(!0);try{var e,t,n;let r=`temp_${Math.random()}`,a=[{key:r,label:s5}];if(en){let n=await (null==v?void 0:v.getConversationList());(null==n?void 0:n.code)==401&&(Y.remove("userInfo",{domain:".medon.com.cn"}),Y.remove("userInfo",{domain:".medsci.cn"}),Y.remove("userInfo",{domain:"localhost"}),Y.remove("yudaoToken",{domain:"ai.medon.com.cn"}),Y.remove("yudaoToken",{domain:"ai.medsci.cn"}),Y.remove("yudaoToken",{domain:".medon.com.cn"}),Y.remove("yudaoToken",{domain:".medsci.cn"}),Y.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("conversation"),window.location.origin.includes("medsci.cn")?window.top.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.top.location.href:window.top.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.top.location.href),a=(null==n||null==(e=n.data)?void 0:e.length)?null==n||null==(t=n.data)?void 0:t.map(e=>({key:e.id,label:e.name})):[{key:r,label:s5}]}w(a),"medsci-ask"==D||x.get("fromPlatform")||E(null==(n=a[0])?void 0:n.key)}catch(e){console.error(e),B.ZP.error(`${h("common.getConversationListFailed")}: ${e}`)}finally{k(!1)}},X=()=>Y.get("ai_apps_lang")?Y.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,Q=async()=>{var e,t;let i=new K,s=r.location.pathname.match(/^\/ai-chat\/([^/]+)$/),l=s?s[1]:"",c=X();if("nologin"==f)return void(c&&"zh-CN"!=c?window.top.location.href=location.origin+"/"+c+"/login":window.addLoginDom());let u=`temp_${Math.random()}`;if(w(e=>[{key:u,label:s5},...e]),l.includes("elavax-pro")||l.includes("novax-pro")){let e=await i.bindAppUser({appUuid:(null==o?void 0:o.id)||"",appNameEn:l},{Authorization:`Bearer ${Y.get("yudaoToken")}`,Accept:"application/json"});if((null==e?void 0:e.data.remainNum)!=0)return E(u),!1}let d=s?s[1]:"",p=await a.getApp(d,{Authorization:`Bearer ${Y.get("yudaoToken")}`,Accept:"application/json"});if(!p.info.appUser||(null==p||null==(e=p.info.appUser)?void 0:e.status)==2){(null==(t=document.getElementsByTagName("textarea"))?void 0:t.length)&&document.getElementsByTagName("textarea")[0].blur(),n(!0);return}E(u)};(0,b.useEffect)(()=>{ei()},[]),(0,b.useEffect)(()=>{_&&!S.has(_)&&G()},[_]);let ee=(0,b.useMemo)(()=>{var e;return(null==(e=j.find(e=>e.key===_))?void 0:e.label)||s5},[j,_]);(0,b.useEffect)(()=>{o||(w([]),O(void 0),E(""),W())},[o]);let en=Y.get("userInfo"),er=en&&JSON.parse(en).avatar?JSON.parse(en).avatar:"https://img.medsci.cn/web/img/user_icon.png",ea=e=>{e.target.src="https://img.medsci.cn/web/img/user_icon.png"},ei=async()=>{let e=new K;Z((await e.getPackageByKey({},{Authorization:`Bearer ${Y.get("yudaoToken")}`,Accept:"application/json"})).data)},[eo,es]=(0,b.useState)(!1),el=async()=>{es(!1),Y.remove("userInfo",{domain:".medon.com.cn"}),Y.remove("userInfo",{domain:".medsci.cn"}),Y.remove("userInfo",{domain:"localhost"}),localStorage.removeItem("hasuraToken"),Y.remove("yudaoToken",{domain:"ai.medon.com.cn"}),Y.remove("yudaoToken",{domain:"ai.medsci.cn"}),Y.remove("yudaoToken",{domain:".medon.com.cn"}),Y.remove("yudaoToken",{domain:".medsci.cn"}),Y.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("conversation"),window.location.origin.includes("medsci.cn")?window.top.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.top.location.href:window.top.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.top.location.href};return(0,y.jsxs)(oh.ZP,{theme:{token:{colorPrimary:"#1669ee",colorText:"#333"}},children:[(0,y.jsxs)("div",{className:`w-full h-screen ${g.layout} flex flex-col overflow-hidden bg-[#eff0f5]`,children:[(0,y.jsx)(lb,{}),(0,y.jsxs)("div",{className:"hidden md:!flex items-center justify-between px-6",children:[(0,y.jsx)("div",{className:`flex-1 overflow-hidden ${o?"":"shadow-sm"}`,children:(0,y.jsx)(et,{hideGithubIcon:!0})}),(0,y.jsx)(la,{children:D.includes("novax")||D.includes("elavax")?l&&T?l({name:T.name,description:T.description,tags:T.tags||[],appUser:null,appId:"",appType:"",appIcon:"",feeTypes:[]}):null:o?o.info.name:""}),(0,y.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,y.jsxs)("div",{className:"flex items-center justify-end text-sm",children:["zh-CN"==X()&&!D.includes("novax")&&!D.includes("elavax")&&(0,y.jsx)("div",{className:"cursor-pointer px-[15px] py-[4px] flex items-center h-[28px] rounded border-none text-xs mr-[8px] text-[#614018]",style:{backgroundImage:"linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"},onClick:()=>{ei(),n(!0)},children:R.packageType==h("payment.free")?h("payment.upgradeSubscription"):R.packageType==h("payment.monthlySubscription")||R.packageType==h("payment.yearlySubscription")?h("payment.modifySubscription"):h("payment.subscribe")}),(0,y.jsx)(lg,{size:"small",className:"mr-3"}),(0,y.jsx)("a",{style:{borderRadius:"4px",background:"#f1f5f9",padding:"6px 10px",fontSize:"12px",color:"#666",marginRight:"23px"},className:"backImg",href:location.origin.includes(".medon.com.cn")?"https://ai.medon.com.cn/"+X():location.origin.includes(".medsci.cn")?"https://ai.medsci.cn/"+X():"/"+X(),target:"_top",children:h("common.goHome")}),!en&&(0,y.jsx)("div",{className:"hover:cursor-pointer",onClick:()=>{let e=X();if(e&&"zh-CN"!==e)window.top.location.href=location.origin+"/"+e+"/login";else{var t,n;null==(t=(n=window).addLoginDom)||t.call(n)}},children:h("common.login")}),(0,y.jsx)(sK.Z,{placement:"bottomLeft",trigger:"hover",arrow:eo,overlayStyle:{width:350,paddingBottom:40},content:(0,y.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",position:"relative",paddingBottom:"40px"},children:[(0,y.jsx)("a",{className:"exit text-right w-full text-[#333333]",onClick:el,children:h("common.logout")}),(0,y.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},className:"iconHeader bg-write",children:[en&&JSON.parse(en||"").userId&&(0,y.jsx)("img",{src:er,onMouseEnter:()=>es(!0),onMouseLeave:()=>es(!1),style:{width:"60px",height:"60px"},onError:ea,alt:"avatar"}),(0,y.jsx)("span",{className:"account",children:en&&JSON.parse(en||"").userName})]})]}),children:(0,y.jsx)("a",{href:"#",children:(0,y.jsx)("div",{className:"img-area",children:en&&JSON.parse(en||"").userId&&(0,y.jsx)("img",{src:er,onMouseEnter:()=>es(!0),onMouseLeave:()=>es(!1),style:{width:"30px",height:"32px"},onError:ea,alt:"avatar"})})})})]})})]}),(0,y.jsx)("div",{className:"flex-1 overflow-hidden flex rounded-3xl bg-white",children:P||u?(0,y.jsx)("div",{className:"absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):o?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{className:`${g.menu} hidden md:!flex w-72 h-full flex-col`,children:[o?(0,y.jsx)(J.ZP,{onClick:()=>{W(),Q()},className:"h-10 leading-10 border border-solid border-gray-200 w-[calc(100%-24px)] mt-3 mx-3 text-default",icon:(0,y.jsx)(of.Z,{}),children:h("common.newConversation")}):null,(0,y.jsx)("div",{className:"px-3",children:(0,y.jsx)(V.Z,{spinning:N,children:(null==j?void 0:j.length)?(0,y.jsx)(sG,{renameConversationPromise:(e,t)=>null==v?void 0:v.renameConversation({conversation_id:e,name:t}),deleteConversationPromise:null==v?void 0:v.deleteConversation,items:j,activeKey:_,onActiveChange:e=>{W(),E(e)},onItemsChange:w,refreshItems:G,appConfig:o,onchangeModal2Open:e=>n(e)}):(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(ol.Z,{className:"pt-6",description:h("common.noSessions")})})})})]}),(0,y.jsx)("div",{className:"flex-1 min-w-0 flex flex-col overflow-hidden",children:ee?(0,y.jsx)(lp,{inputParams:$,setInputParams:L,resetFormValues:W,appConfig:o,appConfigLoading:P,appInfo:T,difyApi:v,conversationId:_,conversationName:ee,conversationItems:j,onConversationIdChange:E,appParameters:A,conversationListLoading:N,onAddConversation:Q,onItemsChange:w,conversationItemsChangeCallback:G,modal2OpenF:t,onsubmit:e=>U(e),subStatusDetails:R,changeSubStatusDetails:()=>{ei()},onClose:e=>{n(e)},selectedAppId:d,appList:p}):""})]}):(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(ol.Z,{description:h("common.noDifyAppConfig"),className:"text-base",children:(0,y.jsx)(J.ZP,{size:"large",type:"primary",onClick:c,children:h("common.startConfig")})})})})]}),i]})},lS=()=>{let{setCurrentAppConfig:e,...t}=C(),{user:n,appService:r,enableSetting:a}=t,i=(0,F.k6)(),o=new K,[s,l]=(0,b.useState)(""),[c,u]=(0,b.useState)(!1),[d,p]=(0,b.useState)(!1),{appId:m}=(0,F.UO)(),f=Y.get("userInfo"),[h,g]=(0,b.useState)(null),v=new URLSearchParams(window.top.location.search),x=i.location.pathname.match(/^\/ai-chat\/([^/]+)$/),j=x?x[1]:"",[w,S]=(0,b.useState)("nologin");(0,b.useEffect)(()=>{l(m)},[m]);let N="ai.medsci.cn"===window.location.hostname?"https://ai.medsci.cn":"https://ai.medon.com.cn",{runAsync:k,data:_,loading:E}=(0,ea.Z)(async()=>{let e=i.location.pathname.match(/^\/ai-chat\/([^/]+)$/),t=e?e[1]:"";if(t)if(t.includes("elavax")||t.includes("novax")){let e=await o.getAppByConfigKey({configKey:t.includes("elavax")?"elavax_apps":"novax_apps"},{Authorization:`Bearer ${Y.get("yudaoToken")}`,Accept:"application/json"});return null==e?void 0:e.data.map(e=>({id:e.appUuid,info:{name:e.appName,description:e.appDescription,appUser:e.appUser,appId:e.dAppUuid,appType:e.appType,appIcon:e.appIcon,feeTypes:e.feeTypes,tags:[],appUuid:e.appUuid,appNameEn:e.appNameEn},requestConfig:{apiKey:Y.get("yudaoToken"),apiBase:`${N}/dev-api/ai-base/v1`},answerForm:{enabled:!1}}))}else{let e=await r.getApp(t,{Authorization:`Bearer ${Y.get("yudaoToken")}`,Accept:"application/json"});if(e.requestConfig.apiKey=Y.get("yudaoToken"),console.log(e),e&&"问答"===e.info.appType)return[e]}return i.replace("/"),[]},{manual:!0,onSuccess:e=>{if(O&&!(null==e?void 0:e.length))return i.replace("/apps"),Promise.resolve([]);if(m)l(m);else if(!s&&(null==e?void 0:e.length)){if(1==e.length&&!j.includes("novax")&&!j.includes("elavax")){var t;l((null==(t=e[0])?void 0:t.id)||"")}(j.includes("novax")||j.includes("elavax"))&&(j.includes("novax")||j.includes("elavax"))&&e.forEach(e=>{e.info.appNameEn==j&&l((null==e?void 0:e.id)||"")})}p(!1)},onError:e=>{B.ZP.error(`获取应用列表失败: ${e}`),console.error(e),p(!1)}}),T=(0,b.useMemo)(()=>null==_?void 0:_.find(e=>e.id===s),[_,s]),O=I();return((0,$.Z)(async()=>{if(f){let e=new K;if(Y.get("yudaoToken")&&"medsci-ask"!=j||v.get("fromPlatform"));else{S(JSON.parse(f).userName);let{data:t}=await e.getAiWriteToken({userId:JSON.parse(f).userId,userName:JSON.parse(f).userName,realName:JSON.parse(f).realName,avatar:JSON.parse(f).avatar,plaintextUserId:JSON.parse(f).plaintextUserId,mobile:JSON.parse(f).mobile,email:JSON.parse(f).email,fromPlatform:"medsci-ask"==j||v.get("fromPlatform")?"medsci":"",appUuid:j})||{};(null==t?void 0:t.token)?(Y.set("yudaoToken",t.token),localStorage.setItem("hasuraToken",t.htoken),localStorage.setItem("openid",t.openid),localStorage.setItem("socialUserId",t.socialUserId),localStorage.setItem("socialType",t.socialType)):console.error("登录失败: 未返回 token")}}k()}),d)?(0,y.jsx)("div",{className:"absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):T?(0,y.jsx)(lw,{useAppInit:(t,r)=>{(0,b.useEffect)(()=>{let a=null==_?void 0:_.find(e=>e.id===s);a&&(t.updateOptions({appId:a.info.appId,user:n,...a.requestConfig}),e(a),r())},[s])},appConfig:T,initLoading:d,handleStartConfig:()=>{a&&u(!0)},selectedAppId:s,appList:_,extComponents:(0,y.jsx)(y.Fragment,{children:(0,y.jsx)(om,{open:c,onClose:()=>u(!1),activeAppId:s,appList:_,getAppList:k,appListLoading:E,onDeleteSuccess:e=>{e===s&&l("")}})}),renderCenterTitle:()=>{var e;return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(en.Z,{className:"mr-2"}),s?(0,y.jsx)(y.Fragment,{children:(0,y.jsx)(ei.Z,{arrow:!0,placement:"bottom",trigger:["click"],menu:{style:{},selectedKeys:[s],items:[...(null==_?void 0:_.map(e=>{let t=s===e.id;return{key:e.id,label:(0,y.jsx)("div",{className:t?"text-primary":"text-default",children:null==e?void 0:e.info.name}),onClick:()=>{if(j.includes("novax")||j.includes("elavax")){window.top.location.href=location.origin+(window.top.location.href.includes("ai-chat")?"/ai-chat/":"/chat/")+`${null==e?void 0:e.info.appNameEn}`;return}},icon:(0,y.jsx)(en.Z,{})}}))||[]]},children:(0,y.jsxs)("div",{className:"cursor-pointer",children:[(0,y.jsx)("span",{className:"cursor-pointer",children:null==T||null==(e=T.info)?void 0:e.name}),(0,y.jsx)(er.Z,{className:"ml-1"})]})})}):null]})}}):null},lN=()=>{let e=C(),{user:t,appConfig:n}=e;return(0,y.jsx)(lw,{initLoading:!1,handleStartConfig:()=>{},useAppInit:(n,r)=>{let a=async()=>{n.updateOptions({user:t,apiBase:e.appConfig.requestConfig.apiBase,apiKey:e.appConfig.requestConfig.apiKey}),r()};(0,$.Z)(()=>{a()})},appConfig:n,renderCenterTitle:e=>(0,y.jsx)(y.Fragment,{children:null==e?void 0:e.name})})};function lk(){let{user:e,mode:t}=C();return e?"singleApp"===t?(0,y.jsx)(lN,{}):(0,y.jsx)(lS,{}):(0,y.jsx)("div",{className:"w-screen h-screen flex flex-col items-center justify-center",children:(0,y.jsxs)("div",{className:"absolute flex-col w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:[(0,y.jsx)(et,{hideGithubIcon:!0}),(0,y.jsx)("div",{children:"授权登录中..."}),(0,y.jsx)("div",{className:"mt-6",children:(0,y.jsx)(V.Z,{spinning:!0})})]})})}var lC=n(49021),l_=n(94448),lE=JSON.parse('{"common":{"login":"登录","logout":"退出","goHome":"返回首页","newConversation":"新增对话","noSessions":"暂无会话","startConfig":"开始配置","noDifyAppConfig":"暂无 Dify 应用配置","getConversationListFailed":"获取会话列表失败"},"app":{"addAppConfigSuccess":"添加应用配置成功","updateAppConfigSuccess":"更新应用配置成功"},"payment":{"subscribe":"订阅","free":"免费","monthlySubscription":"连续包月","yearlySubscription":"连续包年","upgradeSubscription":"升级订阅","modifySubscription":"修改订阅"}}');lC.ZP.use(l_.Z).use(ou.Db).init({resources:{"zh-CN":{translation:lE},"zh-TW":{translation:{}},en:{translation:{}},vi:{translation:{}},es:{translation:{}},ar:{translation:{}},id:{translation:{}},pt:{translation:{}},ja:{translation:{}},ko:{translation:{}},ms:{translation:{}}},fallbackLng:"zh-CN",debug:!1,interpolation:{escapeValue:!1},detection:{order:["cookie","localStorage","navigator","htmlTag"],caches:["cookie","localStorage"],cookieMinutes:160,cookieDomain:".medsci.cn"}}),function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:A;(0,T.P)(e)}();let lT=[{path:"/:appUuid",component:()=>(0,y.jsx)(lk,{})}],lO=document.getElementById("root");Y.removeInit("yudaoToken"),lO&&x.createRoot(lO).render((0,y.jsx)(function(){let[e,t]=(0,b.useState)("nologin"),[n,r]=(0,b.useState)(null),a=new URLSearchParams(window.top.location.search),i=(0,F.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),o=i?i[1]:"";return(0,$.Z)(()=>{let e=new K,t=Z.get("userInfo");if(t)if(r(JSON.parse(t)),Z.get("yudaoToken")&&"medsci-ask"!=o||!a.get("fromPlatform"));else{let n=JSON.parse(t);e.getAiWriteToken({userId:n.userId,userName:n.userName,realName:n.realName,avatar:n.avatar,plaintextUserId:n.plaintextUserId,mobile:n.mobile,email:n.email,fromPlatform:"medsci-ask"==o?"medsci":null,appUuid:o}).then(e=>{let{data:t}=e;(null==t?void 0:t.token)?(Z.set("yudaoToken",t.token),localStorage.setItem("hasuraToken",t.htoken),localStorage.setItem("openid",t.openid),localStorage.setItem("socialUserId",t.socialUserId),localStorage.setItem("socialType",t.socialType)):console.error("登录失败: 未返回 token")})}}),(0,b.useEffect)(()=>{if(n){let e=new K,r=Z.get("yudaoToken");t(n.userName),r&&"medsci-ask"!=o||a.get("fromPlatform")||e.getAiWriteToken({userId:n.userId,userName:n.userName,realName:n.realName,avatar:n.avatar,plaintextUserId:n.plaintextUserId,mobile:n.mobile,email:n.email,fromPlatform:"medsci-ask"==o||a.get("fromPlatform")?"medsci":null,appUuid:o}).then(e=>{let{data:t}=e||{};(null==t?void 0:t.token)?(Z.set("yudaoToken",t.token),localStorage.setItem("hasuraToken",t.htoken),localStorage.setItem("openid",t.openid),localStorage.setItem("socialUserId",t.socialUserId),localStorage.setItem("socialType",t.socialType)):console.error("登录失败: 未返回 token")})}},[n]),(0,b.useEffect)(()=>{e&&console.log("Updated userId====",e)},[e]),(0,b.useEffect)(()=>{Z.set("ai_apps_lang",Z.get("ai_apps_lang")?Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language)},[]),(0,y.jsx)(F.VK,{basename:"",routes:lT,children:(0,y.jsx)(k,{value:{mode:"multiApp",user:e,appService:new s2,enableSetting:!1},children:(0,y.jsx)(F.AW,{})})})},{}))}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a||"object"==typeof r&&r&&(4&a&&r.__esModule||16&a&&"function"==typeof r.then))return r;var i=Object.create(null);n.r(i);var o={};e=e||[null,t({}),t([]),t(t)];for(var s=2&a&&r;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach(e=>{o[e]=()=>r[e]});return o.default=()=>r,n.d(i,o),i}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/async/"+e+"."+({103:"89ed92cf",113:"e6e53918",139:"fab3c4a9",174:"1a50f52e",18:"cc888385",194:"44058ca1",205:"45736c37",208:"ddb6c055",219:"9951a8b3",254:"fcfa37af",33:"d3140ddd",354:"2ecd0882",360:"f01ca2dd",406:"bf0604ad",469:"fd1e1bbe",476:"198a7d91",503:"d03a97e9",516:"e942ecc7",541:"e01b9e80",584:"7293f8f7",66:"28c91132",672:"696afbca",714:"272bc6bb",733:"0cc83532",751:"d1496734",774:"ca6d64f3",796:"e7c58007",8:"faf715eb",828:"253e6351",836:"41e72eca",854:"f9b7e535",857:"79a042c5",867:"514b7df5",951:"1bfd7079",953:"2ffeeb4d",957:"2743cee0",976:"3df1c38e"})[e]+".js",n.miniCssF=e=>""+e+".css",n.h=()=>"731a21f56c24c46b",n.g=(()=>{if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}})(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="dify-chat-web:";n.l=function(r,a,i,o){if(e[r])return void e[r].push(a);if(void 0!==i)for(var s,l,c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+i){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+i),s.src=r),e[r]=[a];var p=function(t,n){s.onerror=s.onload=null,clearTimeout(m);var a=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),a&&a.forEach(function(e){return e(n)}),t)return t(n)},m=setTimeout(p.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=p.bind(null,s.onerror),s.onload=p.bind(null,s.onload),l&&document.head.appendChild(s)}})(),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e=[];n.O=(t,r,a,i)=>{if(r){i=i||0;for(var o=e.length;o>0&&e[o-1][2]>i;o--)e[o]=e[o-1];e[o]=[r,a,i];return}for(var s=1/0,o=0;o<e.length;o++){for(var[r,a,i]=e[o],l=!0,c=0;c<r.length;c++)(!1&i||s>=i)&&Object.keys(n.O).every(e=>n.O[e](r[c]))?r.splice(c--,1):(l=!1,i<s&&(s=i));if(l){e.splice(o--,1);var u=a();void 0!==u&&(t=u)}}return t}})(),n.p="/ai-chat/",n.rv=()=>"1.3.5",(()=>{var e={980:0};n.f.j=function(t,r){var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var i=new Promise((n,r)=>a=e[t]=[n,r]);r.push(a[2]=i);var o=n.p+n.u(t),s=Error();n.l(o,function(r){if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var i=r&&("load"===r.type?"missing":r.type),o=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+i+": "+o+")",s.name="ChunkLoadError",s.type=i,s.request=o,a[1](s)}},"chunk-"+t,t)}},n.O.j=t=>0===e[t];var t=(t,r)=>{var a,i,[o,s,l]=r,c=0;if(o.some(t=>0!==e[t])){for(a in s)n.o(s,a)&&(n.m[a]=s[a]);if(l)var u=l(n)}for(t&&t(r);c<o.length;c++)i=o[c],n.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return n.O(u)},r=self.webpackChunkdify_chat_web=self.webpackChunkdify_chat_web||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.ruid="bundler=rspack@1.3.5";var r=n.O(void 0,["361","749"],function(){return n(58779)});r=n.O(r)})();