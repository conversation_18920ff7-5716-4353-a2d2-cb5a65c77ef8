/*! For license information please see index.e6cc8d2b.js.LICENSE.txt */
(()=>{"use strict";var e={58779:function(e,t,n){let a,i,r,o,s;var l,c,u,d,p,m,f,g,h,v,y=n(52676),b=n(38751),x=n(75271),S=n.t(x,2);let w={mode:"multiApp",user:"",appService:{},enableSetting:!0},j={mode:"singleApp",user:"",appConfig:{requestConfig:{apiBase:"",apiKey:""},answerForm:{enabled:!1,feedbackText:""}}},k=x.createContext(w),C=k.Provider,N=()=>{let[e,t]=(0,x.useState)({}),n=(0,x.useContext)(k),{mode:a}=n;return{currentAppConfig:e,setCurrentAppConfig:t,..."multiApp"===a?w:j,...n}};class A{}class T{}var _=n(20274);let E=e=>!!e&&e.startsWith("temp"),I={sm:0,md:768,lg:1024,xl:1280,"2xl":1536},O=()=>{let{sm:e,md:t}=(0,_.Z)();return!!e&&!t},P="DIFY_CHAT__DIFY_VERSION",M={get version(){return localStorage.getItem(P)||""},set version(version){localStorage.setItem(P,version)}};var L=n(72422),D=n(96038);let F={".medsci.cn":".medsci.cn",".medon.com.cn":".medon.com.cn",localhost:"localhost"},$={set:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{expires:a=365,domain:i,...r}=n,o=i;if(!o){let e=Object.keys(F).find(e=>location.origin.includes(e));e&&(o=F[e])}let s={expires:a,...o?{domain:o}:{},...r};D.Z.set(e,t,s)},get:e=>{let t=D.Z.get(e);try{return t||null}catch{return t}}};var R=n(75727),Z=n(40833);class U{async baseRequest(e,t){return await fetch(`${this.options.baseURL}${e}`,{...t,headers:{...t.headers,"Content-Type":"application/json",Accept:" application/json"}})}async jsonRequest(e,t){return(await this.baseRequest(e,{...t,headers:{...t.headers,"Content-Type":"application/json"}})).json()}async get(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=t&&Object.keys(t).length>0?`?${new URLSearchParams(t).toString()}`:"";return await this.jsonRequest(`${e}${a}`,{method:"GET",headers:n})}async post(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"POST",body:JSON.stringify(t),headers:n})}async put(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"PUT",body:JSON.stringify(t),headers:n})}async delete(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.baseRequest(e,{method:"DELETE",body:JSON.stringify(t),headers:{...n,"Content-Type":"application/json"}})}constructor(e){(0,Z._)(this,"options",void 0),this.options=e}}var z=n(94234),B=n(21256);let H=window.location.hostname,G=new U({baseURL:"ai.medsci.cn"===H?"https://ai.medsci.cn/dev-api/ai-base":"ai.medon.com.cn"===H?"https://ai.medon.com.cn/dev-api/ai-base":"/ai-base"}),q=()=>B.Z.get("ai_apps_lang")?B.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,K=e=>{if(401===e.code){B.Z.remove("userInfo",{domain:".medon.com.cn"}),B.Z.remove("userInfo",{domain:".medsci.cn"}),B.Z.remove("userInfo",{domain:"localhost"}),B.Z.remove("yudaoToken",{domain:"ai.medon.com.cn"}),B.Z.remove("yudaoToken",{domain:"ai.medsci.cn"}),B.Z.remove("yudaoToken",{domain:".medon.com.cn"}),B.Z.remove("yudaoToken",{domain:".medsci.cn"}),B.Z.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("conversation"),localStorage.removeItem("hasuraToken"),localStorage.removeItem("socialUserId"),localStorage.removeItem("socialType"),localStorage.removeItem("openid");let r=q();if(r&&"zh-CN"!=r){var t,n,a,i;window.top.location.href=(null==(t=location)?void 0:t.origin.includes("medon.com.cn"))||(null==(n=location)?void 0:n.origin.includes("medsci.cn"))?`${null==(a=location)?void 0:a.origin}${r?"/"+r:""}/login`:`${null==(i=location)?void 0:i.origin}${r?"/"+r:""}/login`}else window.addLoginDom();return e}return 0!==e.code?(z.ZP.open({type:"error",content:e.msg}),e):0===e.code?e:void 0},W=class extends T{async createSubscription(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return K(await G.post("/appUser/createSubscription",e,t))}async getAiWriteToken(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return K(await G.post("/index/getAiWriteToken",e,t))}async getSubOrder(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return K(await G.get("/index/getSubOrder",e,t))}async createAliSub(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return K(await G.get("/index/createAliSub",e,t))}async freeLimit(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return K(await G.get("/index/free-limit",e,t))}async getPackageByKey(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return K(await G.get("/index/getPackageByKey",e,t))}async cancelSubscription(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return K(await G.post("/appUser/cancelSubscription?appUuid=",{},e))}async getAppList(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return K(await G.post("/index/getAppList",e,t))}async getAppByConfigKey(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return K(await G.post("/index/getAppByConfigKey",e,t))}async bindAppUser(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return K(await G.post(`/appUser/bindAppUser?appUuid=${e.appUuid}&appNameEn=${e.appNameEn}`,{},t))}async qaList(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return K(await G.post("/index/qa-list",e,t))}};var V=n(50742),X=n(91107),J=n(91904);n.p;let Y={".medsci.cn":".medsci.cn",".medon.com.cn":".medon.com.cn",localhost:"localhost"},Q={set:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{expires:a=365,domain:i,...r}=n,o=i;if(!o){let e=Object.keys(Y).find(e=>location.origin.includes(e));e&&(o=Y[e])}let s={expires:a,...o?{domain:o}:{},...r};B.Z.set(e,t,s)},get:e=>{let t=B.Z.get(e);try{return t||null}catch{return t}},remove:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{domain:n,...a}=t;B.Z.remove(e,{...a,domain:n||Y[window.location.host],path:"/"})},removeInit:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{domain:n,...a}=t;B.Z.remove(e,{domain:void 0})}},ee=()=>Q.get("ai_apps_lang")?Q.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,et=e=>{let{hideGithubIcon:t}=e,n=(0,R.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),a=n?n[1]:"",i=location.origin.includes("medsci.cn")?"https://ai.medsci.cn/"+ee():location.origin.includes("medon.com.cn")?"https://ai.medon.com.cn/"+ee():"http://localhost:3000/"+ee(),r=a.includes("novax")||a.includes("elavax")?a.includes("novax")?location.origin+"/novax":a.includes("elavax")?location.origin+"/elavax":"":i;return(0,y.jsxs)("div",{className:"flex h-16 items-center justify-start py-0 box-border",children:[(0,y.jsx)("div",{className:"h-full flex items-center flex-1 overflow-hidden",children:(0,y.jsx)("a",{href:r,target:"__top",children:(0,y.jsx)("img",{className:`h-6 inline-block hover:cursor-pointer ${a.includes("novax")||a.includes("elavax")?"!h-10":""}`,src:a.includes("novax")||a.includes("elavax")?a.includes("novax")?"https://img.medsci.cn/202506/a75d550504434d22aacaefcc951bc9ec-PBB8bYHyNwIQ.png":a.includes("elavax")?"https://img.medsci.cn/202506/06e46058edd34eea9ae96580a46bf327-FP4DdQMkcnma.png":"":"https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png",draggable:!1,alt:"logo"})})}),!t&&(0,y.jsx)(J.ZP,{type:"link",href:"https://github.com/lexmin0412/dify-chat",target:"_blank",className:"px-0",children:(0,y.jsx)(X.Z,{className:"text-lg cursor-pointer text-default"})})]})};var en=n(3692),ea=n(86910),ei=n(48415),er=n(96949),eo=n(16615),es=n(30967),el=n.t(es,2),ec={"../../node_modules/.pnpm/@ant-design+cssinjs@1.23.0_react-dom@18.3.1_react@18.3.1/node_modules/@ant-design/cssinjs/es/hooks/useHMR.js":function(e,t,n){n.d(t,{Z:()=>a}),e=n.hmd(e);let a=function(){return!1}},"../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/cjs/react-is.development.js":function(e,t){},"../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/cjs/react-is.production.min.js":function(e,t){var n=Symbol.for("react.element"),a=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),c=Symbol.for("react.server_context"),u=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),m=Symbol.for("react.memo"),f=Symbol.for("react.lazy");Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),t.ForwardRef=u,t.isMemo=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case i:case o:case r:case d:case p:return e;default:switch(e=e&&e.$$typeof){case c:case l:case u:case f:case m:case s:return e;default:return t}}case a:return t}}}(e)===m}},"../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/index.js":function(e,t,n){e.exports=n("../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/cjs/react-is.production.min.js")},"../../node_modules/.pnpm/classnames@2.5.1/node_modules/classnames/index.js":function(e){var t={}.hasOwnProperty;function n(){for(var e="",i=0;i<arguments.length;i++){var r=arguments[i];r&&(e=a(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return n.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var i="";for(var r in e)t.call(e,r)&&e[r]&&(i=a(i,r));return i}(r)))}return e}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(n.default=n,e.exports=n):"function"==typeof define&&"object"==typeof define.amd&&define.amd?define("classnames",[],function(){return n}):window.classNames=n}},eu={};function ed(e){var t=eu[e];if(void 0!==t)return t.exports;var n=eu[e]={id:e,loaded:!1,exports:{}};return ec[e](n,n.exports,ed),n.loaded=!0,n.exports}function ep(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}function em(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function ef(e,t){if(e){if("string"==typeof e)return ep(e,t);var n=({}).toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ep(e,t):void 0}}function eg(e){return function(e){if(Array.isArray(e))return ep(e)}(e)||em(e)||ef(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}ed.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return ed.d(t,{a:t}),t},ed.d=(e,t)=>{for(var n in t)ed.o(t,n)&&!ed.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},ed.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),ed.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);let eh=x.createContext({}),ev="anticon",ey=x.createContext({getPrefixCls:(e,t)=>t||(e?`ant-${e}`:"ant"),iconPrefixCls:ev}),{Consumer:eb}=ey;function ex(e){if(Array.isArray(e))return e}function eS(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ew(e,t){return ex(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,i,r,o,s=[],l=!0,c=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=r.call(n)).done)&&(s.push(a.value),s.length!==t);l=!0);}catch(e){c=!0,i=e}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(c)throw i}}return s}}(e,t)||ef(e,t)||eS()}function ej(e){return(ej="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ek(e){var t=function(e,t){if("object"!=ej(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=ej(a))return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ej(t)?t:t+""}function eC(e,t,n){return(t=ek(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function eN(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,a)}return n}function eA(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?eN(Object(n),!0).forEach(function(t){eC(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):eN(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}let eT=function(e){for(var t,n=0,a=0,i=e.length;i>=4;++a,i-=4)t=(65535&(t=255&e.charCodeAt(a)|(255&e.charCodeAt(++a))<<8|(255&e.charCodeAt(++a))<<16|(255&e.charCodeAt(++a))<<24))*0x5bd1e995+((t>>>16)*59797<<16),t^=t>>>24,n=(65535&t)*0x5bd1e995+((t>>>16)*59797<<16)^(65535&n)*0x5bd1e995+((n>>>16)*59797<<16);switch(i){case 3:n^=(255&e.charCodeAt(a+2))<<16;case 2:n^=(255&e.charCodeAt(a+1))<<8;case 1:n^=255&e.charCodeAt(a),n=(65535&n)*0x5bd1e995+((n>>>16)*59797<<16)}return n^=n>>>13,(((n=(65535&n)*0x5bd1e995+((n>>>16)*59797<<16))^n>>>15)>>>0).toString(36)};function e_(){return!!("undefined"!=typeof window&&window.document&&window.document.createElement)}var eE="data-rc-order",eI="data-rc-priority",eO=new Map;function eP(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.mark;return t?t.startsWith("data-")?t:"data-".concat(t):"rc-util-key"}function eM(e){return e.attachTo?e.attachTo:document.querySelector("head")||document.body}function eL(e){return Array.from((eO.get(e)||e).children).filter(function(e){return"STYLE"===e.tagName})}function eD(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e_())return null;var n=t.csp,a=t.prepend,i=t.priority,r=void 0===i?0:i,o="queue"===a?"prependQueue":a?"prepend":"append",s="prependQueue"===o,l=document.createElement("style");l.setAttribute(eE,o),s&&r&&l.setAttribute(eI,"".concat(r)),null!=n&&n.nonce&&(l.nonce=null==n?void 0:n.nonce),l.innerHTML=e;var c=eM(t),u=c.firstChild;if(a){if(s){var d=(t.styles||eL(c)).filter(function(e){return!!["prepend","prependQueue"].includes(e.getAttribute(eE))&&r>=Number(e.getAttribute(eI)||0)});if(d.length)return c.insertBefore(l,d[d.length-1].nextSibling),l}c.insertBefore(l,u)}else c.appendChild(l);return l}function eF(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=eM(t);return(t.styles||eL(n)).find(function(n){return n.getAttribute(eP(t))===e})}function e$(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=eF(e,t);n&&eM(t).removeChild(n)}function eR(e,t){var n,a,i,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=eM(r),s=eL(o),l=eA(eA({},r),{},{styles:s}),c=eO.get(o);if(!c||!function(e,t){if(!e)return!1;if(e.contains)return e.contains(t);for(var n=t;n;){if(n===e)return!0;n=n.parentNode}return!1}(document,c)){var u=eD("",l),d=u.parentNode;eO.set(o,d),o.removeChild(u)}var p=eF(t,l);if(p)return null!=(n=l.csp)&&n.nonce&&p.nonce!==(null==(a=l.csp)?void 0:a.nonce)&&(p.nonce=null==(i=l.csp)?void 0:i.nonce),p.innerHTML!==e&&(p.innerHTML=e),p;var m=eD(e,l);return m.setAttribute(eP(l),t),m}function eZ(e,t){if(null==e)return{};var n,a,i=function(e,t){if(null==e)return{};var n={};for(var a in e)if(({}).hasOwnProperty.call(e,a)){if(-1!==t.indexOf(a))continue;n[a]=e[a]}return n}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(a=0;a<r.length;a++)n=r[a],-1===t.indexOf(n)&&({}).propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function eU(e,t,n){var a=x.useRef({});return(!("value"in a.current)||n(a.current.condition,t))&&(a.current.value=e(),a.current.condition=t),a.current.value}var ez={},eB=[];function eH(e,t){}function eG(e,t){}function eq(e,t,n){t||ez[n]||(e(!1,n),ez[n]=!0)}function eK(e,t){eq(eH,e,t)}eK.preMessage=function(e){eB.push(e)},eK.resetWarned=function(){ez={}},eK.noteOnce=function(e,t){eq(eG,e,t)};let eW=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=new Set;return function e(t,i){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,o=a.has(t);if(eK(!o,"Warning: There may be circular references"),o)return!1;if(t===i)return!0;if(n&&r>1)return!1;a.add(t);var s=r+1;if(Array.isArray(t)){if(!Array.isArray(i)||t.length!==i.length)return!1;for(var l=0;l<t.length;l++)if(!e(t[l],i[l],s))return!1;return!0}if(t&&i&&"object"===ej(t)&&"object"===ej(i)){var c=Object.keys(t);return c.length===Object.keys(i).length&&c.every(function(n){return e(t[n],i[n],s)})}return!1}(e,t)};function eV(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function eX(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,ek(a.key),a)}}function eJ(e,t,n){return t&&eX(e.prototype,t),n&&eX(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function eY(e){return e.join("%")}var eQ=function(){function e(t){eV(this,e),eC(this,"instanceId",void 0),eC(this,"cache",new Map),this.instanceId=t}return eJ(e,[{key:"get",value:function(e){return this.opGet(eY(e))}},{key:"opGet",value:function(e){return this.cache.get(e)||null}},{key:"update",value:function(e,t){return this.opUpdate(eY(e),t)}},{key:"opUpdate",value:function(e,t){var n=t(this.cache.get(e));null===n?this.cache.delete(e):this.cache.set(e,n)}}]),e}(),e0="data-token-hash",e1="data-css-hash",e2="__cssinjs_instance__",e5=x.createContext({hashPriority:"low",cache:function(){var e=Math.random().toString(12).slice(2);if("undefined"!=typeof document&&document.head&&document.body){var t=document.body.querySelectorAll("style[".concat(e1,"]"))||[],n=document.head.firstChild;Array.from(t).forEach(function(t){t[e2]=t[e2]||e,t[e2]===e&&document.head.insertBefore(t,n)});var a={};Array.from(document.querySelectorAll("style[".concat(e1,"]"))).forEach(function(t){var n,i=t.getAttribute(e1);a[i]?t[e2]===e&&(null==(n=t.parentNode)||n.removeChild(t)):a[i]=!0})}return new eQ(e)}(),defaultCache:!0});function e4(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function e3(e,t){return(e3=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function e6(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&e3(e,t)}function e8(e){return(e8=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function e9(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(e9=function(){return!!e})()}function e7(e){var t=e9();return function(){var n,a=e8(e);n=t?Reflect.construct(a,arguments,e8(this).constructor):a.apply(this,arguments);if(n&&("object"==ej(n)||"function"==typeof n))return n;if(void 0!==n)throw TypeError("Derived constructors may only return object or undefined");return e4(this)}}var te=function(){function e(){eV(this,e),eC(this,"cache",void 0),eC(this,"keys",void 0),eC(this,"cacheCallTimes",void 0),this.cache=new Map,this.keys=[],this.cacheCallTimes=0}return eJ(e,[{key:"size",value:function(){return this.keys.length}},{key:"internalGet",value:function(e){var t,n,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i={map:this.cache};return e.forEach(function(e){if(i){var t;i=null==(t=i)||null==(t=t.map)?void 0:t.get(e)}else i=void 0}),null!=(t=i)&&t.value&&a&&(i.value[1]=this.cacheCallTimes++),null==(n=i)?void 0:n.value}},{key:"get",value:function(e){var t;return null==(t=this.internalGet(e,!0))?void 0:t[0]}},{key:"has",value:function(e){return!!this.internalGet(e)}},{key:"set",value:function(t,n){var a=this;if(!this.has(t)){if(this.size()+1>e.MAX_CACHE_SIZE+e.MAX_CACHE_OFFSET){var i=this.keys.reduce(function(e,t){var n=ew(e,2)[1];return a.internalGet(t)[1]<n?[t,a.internalGet(t)[1]]:e},[this.keys[0],this.cacheCallTimes]),r=ew(i,1)[0];this.delete(r)}this.keys.push(t)}var o=this.cache;t.forEach(function(e,i){if(i===t.length-1)o.set(e,{value:[n,a.cacheCallTimes++]});else{var r=o.get(e);r?r.map||(r.map=new Map):o.set(e,{map:new Map}),o=o.get(e).map}})}},{key:"deleteByPath",value:function(e,t){var n,a=e.get(t[0]);if(1===t.length)return a.map?e.set(t[0],{map:a.map}):e.delete(t[0]),null==(n=a.value)?void 0:n[0];var i=this.deleteByPath(a.map,t.slice(1));return a.map&&0!==a.map.size||a.value||e.delete(t[0]),i}},{key:"delete",value:function(e){if(this.has(e))return this.keys=this.keys.filter(function(t){return!function(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(t,e)}),this.deleteByPath(this.cache,e)}}]),e}();eC(te,"MAX_CACHE_SIZE",20),eC(te,"MAX_CACHE_OFFSET",5);var tt=0,tn=function(){function e(t){eV(this,e),eC(this,"derivatives",void 0),eC(this,"id",void 0),this.derivatives=Array.isArray(t)?t:[t],this.id=tt,0===t.length&&t.length,tt+=1}return eJ(e,[{key:"getDerivativeToken",value:function(e){return this.derivatives.reduce(function(t,n){return n(e,t)},void 0)}}]),e}(),ta=new te;function ti(e){var t=Array.isArray(e)?e:[e];return ta.has(t)||ta.set(t,new tn(t)),ta.get(t)}var tr=new WeakMap,to={},ts=new WeakMap;function tl(e){var t=ts.get(e)||"";return t||(Object.keys(e).forEach(function(n){var a=e[n];t+=n,a instanceof tn?t+=a.id:a&&"object"===ej(a)?t+=tl(a):t+=a}),t=eT(t),ts.set(e,t)),t}function tc(e,t){return eT("".concat(t,"_").concat(tl(e)))}"random-".concat(Date.now(),"-").concat(Math.random()).replace(/\./g,"");var tu=e_();function td(e){return"number"==typeof e?"".concat(e,"px"):e}function tp(e,t,n){var a,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(r)return e;var o=eA(eA({},i),{},(eC(a={},e0,t),eC(a,e1,n),a)),s=Object.keys(o).map(function(e){var t=o[e];return t?"".concat(e,'="').concat(t,'"'):null}).filter(function(e){return e}).join(" ");return"<style ".concat(s,">").concat(e,"</style>")}var tm=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"--".concat(t?"".concat(t,"-"):"").concat(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").replace(/([A-Z]+)([A-Z][a-z0-9]+)/g,"$1-$2").replace(/([a-z])([A-Z0-9])/g,"$1-$2").toLowerCase()},tf=function(e,t,n){var a,i={},r={};return Object.entries(e).forEach(function(e){var t=ew(e,2),a=t[0],o=t[1];if(null!=n&&null!=(s=n.preserve)&&s[a])r[a]=o;else if(("string"==typeof o||"number"==typeof o)&&!(null!=n&&null!=(l=n.ignore)&&l[a])){var s,l,c,u=tm(a,null==n?void 0:n.prefix);i[u]="number"!=typeof o||null!=n&&null!=(c=n.unitless)&&c[a]?String(o):"".concat(o,"px"),r[a]="var(".concat(u,")")}}),[r,(a={scope:null==n?void 0:n.scope},Object.keys(i).length?".".concat(t).concat(null!=a&&a.scope?".".concat(a.scope):"","{").concat(Object.entries(i).map(function(e){var t=ew(e,2),n=t[0],a=t[1];return"".concat(n,":").concat(a,";")}).join(""),"}"):"")]},tg=e_()?x.useLayoutEffect:x.useEffect;let th=function(e,t){var n=x.useRef(!0);tg(function(){return e(n.current)},t),tg(function(){return n.current=!1,function(){n.current=!0}},[])};var tv=eA({},S).useInsertionEffect,ty=tv?function(e,t,n){return tv(function(){return e(),t()},n)}:function(e,t,n){x.useMemo(e,n),th(function(){return t(!0)},n)},tb=void 0!==eA({},S).useInsertionEffect?function(e){var t=[],n=!1;return x.useEffect(function(){return n=!1,function(){n=!0,t.length&&t.forEach(function(e){return e()})}},e),function(e){n||t.push(e)}}:function(){return function(e){e()}},tx=ed("../../node_modules/.pnpm/@ant-design+cssinjs@1.23.0_react-dom@18.3.1_react@18.3.1/node_modules/@ant-design/cssinjs/es/hooks/useHMR.js");function tS(e,t,n,a,i){var r=x.useContext(e5).cache,o=eY([e].concat(eg(t))),s=tb([o]);(0,tx.Z)();var l=function(e){r.opUpdate(o,function(t){var a=ew(t||[void 0,void 0],2),i=a[0],r=[void 0===i?0:i,a[1]||n()];return e?e(r):r})};x.useMemo(function(){l()},[o]);var c=r.opGet(o)[1];return ty(function(){null==i||i(c)},function(e){return l(function(t){var n=ew(t,2),a=n[0],r=n[1];return e&&0===a&&(null==i||i(c)),[a+1,r]}),function(){r.opUpdate(o,function(t){var n=ew(t||[],2),i=n[0],l=void 0===i?0:i,c=n[1];return 0==l-1?(s(function(){(e||!r.opGet(o))&&(null==a||a(c,!1))}),null):[l-1,c]})}},[o]),c}var tw={},tj=new Map,tk=function(e,t,n,a){var i=eA(eA({},n.getDerivativeToken(e)),t);return a&&(i=a(i)),i},tC="token";function tN(){return(tN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(null,arguments)}let tA={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};var tT="comm",t_="rule",tE="decl",tI=Math.abs,tO=String.fromCharCode;function tP(e,t,n){return e.replace(t,n)}function tM(e,t){return 0|e.charCodeAt(t)}function tL(e,t,n){return e.slice(t,n)}function tD(e){return e.length}function tF(e,t){return t.push(e),e}function t$(e,t){for(var n="",a=0;a<e.length;a++)n+=t(e[a],a,e,t)||"";return n}function tR(e,t,n,a){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case"@namespace":case tE:return e.return=e.return||e.value;case tT:return"";case"@keyframes":return e.return=e.value+"{"+t$(e.children,a)+"}";case t_:if(!tD(e.value=e.props.join(",")))return""}return tD(n=t$(e.children,a))?e.return=e.value+"{"+n+"}":""}var tZ=1,tU=1,tz=0,tB=0,tH=0,tG="";function tq(e,t,n,a,i,r,o,s){return{value:e,root:t,parent:n,type:a,props:i,children:r,line:tZ,column:tU,length:o,return:"",siblings:s}}function tK(){return tH=tB<tz?tM(tG,tB++):0,tU++,10===tH&&(tU=1,tZ++),tH}function tW(){return tM(tG,tB)}function tV(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function tX(e){var t,n;return(t=tB-1,n=function e(t){for(;tK();)switch(tH){case t:return tB;case 34:case 39:34!==t&&39!==t&&e(tH);break;case 40:41===t&&e(t);break;case 92:tK()}return tB}(91===e?e+2:40===e?e+1:e),tL(tG,t,n)).trim()}function tJ(e,t,n,a,i,r,o,s,l,c,u,d){for(var p=i-1,m=0===i?r:[""],f=m.length,g=0,h=0,v=0;g<a;++g)for(var y=0,b=tL(e,p+1,p=tI(h=o[g])),x=e;y<f;++y)(x=(h>0?m[y]+" "+b:tP(b,/&\f/g,m[y])).trim())&&(l[v++]=x);return tq(e,t,n,0===i?t_:s,l,c,u,d)}function tY(e,t,n,a,i){return tq(e,t,n,tE,tL(e,0,a),tL(e,a+1,-1),a,i)}var tQ="data-ant-cssinjs-cache-path",t0="_FILE_STYLE__",t1=!0,t2="_multi_value_";function t5(e){var t,n,a;return t$((a=function e(t,n,a,i,r,o,s,l,c){for(var u,d,p,m,f,g,h=0,v=0,y=s,b=0,x=0,S=0,w=1,j=1,k=1,C=0,N="",A=r,T=o,_=i,E=N;j;)switch(S=C,C=tK()){case 40:if(108!=S&&58==tM(E,y-1)){-1!=(f=E+=tP(tX(C),"&","&\f"),g=tI(h?l[h-1]:0),f.indexOf("&\f",g))&&(k=-1);break}case 34:case 39:case 91:E+=tX(C);break;case 9:case 10:case 13:case 32:E+=function(e){for(;tH=tW();)if(tH<33)tK();else break;return tV(e)>2||tV(tH)>3?"":" "}(S);break;case 92:E+=function(e,t){for(var n;--t&&tK()&&!(tH<48)&&!(tH>102)&&(!(tH>57)||!(tH<65))&&(!(tH>70)||!(tH<97)););return n=tB+(t<6&&32==tW()&&32==tK()),tL(tG,e,n)}(tB-1,7);continue;case 47:switch(tW()){case 42:case 47:tF((u=function(e,t){for(;tK();)if(e+tH===57)break;else if(e+tH===84&&47===tW())break;return"/*"+tL(tG,t,tB-1)+"*"+tO(47===e?e:tK())}(tK(),tB),d=n,p=a,m=c,tq(u,d,p,tT,tO(tH),tL(u,2,-2),0,m)),c),(5==tV(S||1)||5==tV(tW()||1))&&tD(E)&&" "!==tL(E,-1,void 0)&&(E+=" ");break;default:E+="/"}break;case 123*w:l[h++]=tD(E)*k;case 125*w:case 59:case 0:switch(C){case 0:case 125:j=0;case 59+v:-1==k&&(E=tP(E,/\f/g,"")),x>0&&(tD(E)-y||0===w&&47===S)&&tF(x>32?tY(E+";",i,a,y-1,c):tY(tP(E," ","")+";",i,a,y-2,c),c);break;case 59:E+=";";default:if(tF(_=tJ(E,n,a,h,v,r,l,N,A=[],T=[],y,o),o),123===C)if(0===v)e(E,n,_,_,A,o,y,l,T);else{switch(b){case 99:if(110===tM(E,3))break;case 108:if(97===tM(E,2))break;default:v=0;case 100:case 109:case 115:}v?e(t,_,_,i&&tF(tJ(t,_,_,0,0,r,l,N,r,A=[],y,T),T),r,T,y,l,i?A:T):e(E,_,_,_,[""],T,0,l,T)}}h=v=x=0,w=k=1,N=E="",y=s;break;case 58:y=1+tD(E),x=S;default:if(w<1){if(123==C)--w;else if(125==C&&0==w++&&125==(tH=tB>0?tM(tG,--tB):0,tU--,10===tH&&(tU=1,tZ--),tH))continue}switch(E+=tO(C),C*w){case 38:k=v>0?1:(E+="\f",-1);break;case 44:l[h++]=(tD(E)-1)*k,k=1;break;case 64:45===tW()&&(E+=tX(tK())),b=tW(),v=y=tD(N=E+=function(e){for(;!tV(tW());)tK();return tL(tG,e,tB)}(tB)),C++;break;case 45:45===S&&2==tD(E)&&(w=0)}}return o}("",null,null,null,[""],(n=t=e,tZ=tU=1,tz=tD(tG=n),tB=0,t=[]),0,[0],t),tG="",a),tR).replace(/\{%%%\:[^;];}/g,";")}function t4(e,t,n){if(!t)return e;var a=".".concat(t),i="low"===n?":where(".concat(a,")"):a;return e.split(",").map(function(e){var t,n=e.trim().split(/\s+/),a=n[0]||"",r=(null==(t=a.match(/^\w+/))?void 0:t[0])||"";return[a="".concat(r).concat(i).concat(a.slice(r.length))].concat(eg(n.slice(1))).join(" ")}).join(",")}var t3=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{root:!0,parentSelectors:[]},i=a.root,r=a.injectHash,o=a.parentSelectors,s=n.hashId,l=n.layer,c=(n.path,n.hashPriority),u=n.transformers,d=void 0===u?[]:u,p=(n.linters,""),m={};function f(t){var a=t.getName(s);if(!m[a]){var i=ew(e(t.style,n,{root:!1,parentSelectors:o}),1)[0];m[a]="@keyframes ".concat(t.getName(s)).concat(i)}}return(function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return t.forEach(function(t){Array.isArray(t)?e(t,n):t&&n.push(t)}),n})(Array.isArray(t)?t:[t]).forEach(function(t){var a="string"!=typeof t||i?t:{};if("string"==typeof a)p+="".concat(a,"\n");else if(a._keyframe)f(a);else{var l=d.reduce(function(e,t){var n;return(null==t||null==(n=t.visit)?void 0:n.call(t,e))||e},a);Object.keys(l).forEach(function(t){var a=l[t];if("object"!==ej(a)||!a||"animationName"===t&&a._keyframe||"object"===ej(a)&&a&&("_skip_check_"in a||t2 in a)){function u(e,t){var n=e.replace(/[A-Z]/g,function(e){return"-".concat(e.toLowerCase())}),a=t;tA[e]||"number"!=typeof a||0===a||(a="".concat(a,"px")),"animationName"===e&&null!=t&&t._keyframe&&(f(t),a=t.getName(s)),p+="".concat(n,":").concat(a,";")}var d,g=null!=(d=null==a?void 0:a.value)?d:a;"object"===ej(a)&&null!=a&&a[t2]&&Array.isArray(g)?g.forEach(function(e){u(t,e)}):u(t,g)}else{var h=!1,v=t.trim(),y=!1;(i||r)&&s?v.startsWith("@")?h=!0:v="&"===v?t4("",s,c):t4(t,s,c):i&&!s&&("&"===v||""===v)&&(v="",y=!0);var b=ew(e(a,n,{root:y,injectHash:h,parentSelectors:[].concat(eg(o),[v])}),2),x=b[0],S=b[1];m=eA(eA({},m),S),p+="".concat(v).concat(x)}})}}),i?l&&(p&&(p="@layer ".concat(l.name," {").concat(p,"}")),l.dependencies&&(m["@layer ".concat(l.name)]=l.dependencies.map(function(e){return"@layer ".concat(e,", ").concat(l.name,";")}).join("\n"))):p="{".concat(p,"}"),[p,m]};function t6(e,t){return eT("".concat(e.join("%")).concat(t))}function t8(){return null}var t9="style";function t7(e,t){var n=e.token,a=e.path,i=e.hashId,r=e.layer,o=e.nonce,s=e.clientOnly,l=e.order,c=void 0===l?0:l,u=x.useContext(e5),d=u.autoClear,p=(u.mock,u.defaultCache),m=u.hashPriority,f=u.container,h=u.ssrInline,v=u.transformers,y=u.linters,b=u.cache,S=u.layer,w=n._tokenKey,j=[w];S&&j.push("layer"),j.push.apply(j,eg(a));var k=tS(t9,j,function(){var e=j.join("|");if(function(e){if(!g&&(g={},e_())){var t,n=document.createElement("div");n.className=tQ,n.style.position="fixed",n.style.visibility="hidden",n.style.top="-9999px",document.body.appendChild(n);var a=getComputedStyle(n).content||"";(a=a.replace(/^"/,"").replace(/"$/,"")).split(";").forEach(function(e){var t=ew(e.split(":"),2),n=t[0],a=t[1];g[n]=a});var i=document.querySelector("style[".concat(tQ,"]"));i&&(t1=!1,null==(t=i.parentNode)||t.removeChild(i)),document.body.removeChild(n)}return!!g[e]}(e)){var n=ew(function(e){var t=g[e],n=null;if(t&&e_())if(t1)n=t0;else{var a=document.querySelector("style[".concat(e1,'="').concat(g[e],'"]'));a?n=a.innerHTML:delete g[e]}return[n,t]}(e),2),o=n[0],l=n[1];if(o)return[o,w,l,{},s,c]}var u=ew(t3(t(),{hashId:i,hashPriority:m,layer:S?r:void 0,path:a.join("-"),transformers:v,linters:y}),2),d=u[0],p=u[1],f=t5(d),h=t6(j,f);return[f,w,h,p,s,c]},function(e,t){var n=ew(e,3)[2];(t||d)&&tu&&e$(n,{mark:e1})},function(e){var t=ew(e,4),n=t[0],a=(t[1],t[2]),i=t[3];if(tu&&n!==t0){var r={mark:e1,prepend:!S&&"queue",attachTo:f,priority:c},s="function"==typeof o?o():o;s&&(r.csp={nonce:s});var l=[],u=[];Object.keys(i).forEach(function(e){e.startsWith("@layer")?l.push(e):u.push(e)}),l.forEach(function(e){eR(t5(i[e]),"_layer-".concat(e),eA(eA({},r),{},{prepend:!0}))});var d=eR(n,a,r);d[e2]=b.instanceId,d.setAttribute(e0,w),u.forEach(function(e){eR(t5(i[e]),"_effect-".concat(e),r)})}}),C=ew(k,3),N=C[0],A=C[1],T=C[2];return function(e){var t,n;return t=h&&!tu&&p?x.createElement("style",tN({},(eC(n={},e0,A),eC(n,e1,T),n),{dangerouslySetInnerHTML:{__html:N}})):x.createElement(t8,null),x.createElement(x.Fragment,null,t,e)}}var ne="cssVar";let nt=function(e,t){var n=e.key,a=e.prefix,i=e.unitless,r=e.ignore,o=e.token,s=e.scope,l=void 0===s?"":s,c=(0,x.useContext)(e5),u=c.cache.instanceId,d=c.container,p=o._tokenKey,m=[].concat(eg(e.path),[n,l,p]);return tS(ne,m,function(){var e=ew(tf(t(),n,{prefix:a,unitless:i,ignore:r,scope:l}),2),o=e[0],s=e[1],c=t6(m,s);return[o,s,c,n]},function(e){var t=ew(e,3)[2];tu&&e$(t,{mark:e1})},function(e){var t=ew(e,3),a=t[1],i=t[2];if(a){var r=eR(a,i,{mark:e1,prepend:"queue",attachTo:d,priority:-999});r[e2]=u,r.setAttribute(e0,n)}})};eC(h={},t9,function(e,t,n){var a=ew(e,6),i=a[0],r=a[1],o=a[2],s=a[3],l=a[4],c=a[5],u=(n||{}).plain;if(l)return null;var d=i,p={"data-rc-order":"prependQueue","data-rc-priority":"".concat(c)};return d=tp(i,r,o,p,u),s&&Object.keys(s).forEach(function(e){if(!t[e]){t[e]=!0;var n=tp(t5(s[e]),r,"_effect-".concat(e),p,u);e.startsWith("@layer")?d=n+d:d+=n}}),[c,o,d]}),eC(h,tC,function(e,t,n){var a=ew(e,5),i=a[2],r=a[3],o=a[4],s=(n||{}).plain;if(!r)return null;var l=i._tokenKey,c=tp(r,o,l,{"data-rc-order":"prependQueue","data-rc-priority":"".concat(-999)},s);return[-999,l,c]}),eC(h,ne,function(e,t,n){var a=ew(e,4),i=a[1],r=a[2],o=a[3],s=(n||{}).plain;if(!i)return null;var l=tp(i,o,r,{"data-rc-order":"prependQueue","data-rc-priority":"".concat(-999)},s);return[-999,r,l]});var nn=function(){function e(t,n){eV(this,e),eC(this,"name",void 0),eC(this,"style",void 0),eC(this,"_keyframe",!0),this.name=t,this.style=n}return eJ(e,[{key:"getName",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e?"".concat(e,"-").concat(this.name):this.name}}]),e}();function na(e){return e.notSplit=!0,e}na(["borderTop","borderBottom"]),na(["borderTop"]),na(["borderBottom"]),na(["borderLeft","borderRight"]),na(["borderLeft"]),na(["borderRight"]);var ni=(0,x.createContext)({});function nr(e,t){for(var n=e,a=0;a<t.length;a+=1){if(null==n)return;n=n[t[a]]}return n}function no(e,t,n){var a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return t.length&&a&&void 0===n&&!nr(e,t.slice(0,-1))?e:function e(t,n,a,i){if(!n.length)return a;var r,o=ex(n)||em(n)||ef(n)||eS(),s=o[0],l=o.slice(1);return r=t||"number"!=typeof s?Array.isArray(t)?eg(t):eA({},t):[],i&&void 0===a&&1===l.length?delete r[s][l[0]]:r[s]=e(r[s],l,a,i),r}(e,t,n,a)}function ns(e){return Array.isArray(e)?[]:{}}var nl="undefined"==typeof Reflect?Object.keys:Reflect.ownKeys;function nc(){}let nu=x.createContext({}),nd=()=>{let e=()=>{};return e.deprecated=nc,e},np=(0,x.createContext)(void 0);Object.assign({placeholder:"Select date",yearPlaceholder:"Select year",quarterPlaceholder:"Select quarter",monthPlaceholder:"Select month",weekPlaceholder:"Select week",rangePlaceholder:["Start date","End date"],rangeYearPlaceholder:["Start year","End year"],rangeQuarterPlaceholder:["Start quarter","End quarter"],rangeMonthPlaceholder:["Start month","End month"],rangeWeekPlaceholder:["Start week","End week"]},eA(eA({},{yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0}),{},{locale:"en_US",today:"Today",now:"Now",backToToday:"Back to today",ok:"OK",clear:"Clear",week:"Week",month:"Month",year:"Year",timeSelect:"select time",dateSelect:"select date",weekSelect:"Choose a week",monthSelect:"Choose a month",yearSelect:"Choose a year",decadeSelect:"Choose a decade",dateFormat:"M/D/YYYY",dateTimeFormat:"M/D/YYYY HH:mm:ss",previousMonth:"Previous month (PageUp)",nextMonth:"Next month (PageDown)",previousYear:"Last year (Control + left)",nextYear:"Next year (Control + right)",previousDecade:"Last decade",nextDecade:"Next decade",previousCentury:"Last century",nextCentury:"Next century"})),Object.assign({},{placeholder:"Select time",rangePlaceholder:["Start time","End time"]});let nm="${label} is not a valid ${type}",nf={Modal:{okText:"OK",cancelText:"Cancel",justOkText:"OK"},Form:{optional:"(optional)",defaultValidateMessages:{default:"Field validation error for ${label}",required:"Please enter ${label}",enum:"${label} must be one of [${enum}]",whitespace:"${label} cannot be a blank character",date:{format:"${label} date format is invalid",parse:"${label} cannot be converted to a date",invalid:"${label} is an invalid date"},types:{string:nm,method:nm,array:nm,object:nm,number:nm,date:nm,boolean:nm,integer:nm,float:nm,regexp:nm,email:nm,url:nm,hex:nm},string:{len:"${label} must be ${len} characters",min:"${label} must be at least ${min} characters",max:"${label} must be up to ${max} characters",range:"${label} must be between ${min}-${max} characters"},number:{len:"${label} must be equal to ${len}",min:"${label} must be minimum ${min}",max:"${label} must be maximum ${max}",range:"${label} must be between ${min}-${max}"},array:{len:"Must be ${len} ${label}",min:"At least ${min} ${label}",max:"At most ${max} ${label}",range:"The amount of ${label} must be between ${min}-${max}"},pattern:{mismatch:"${label} does not match the pattern ${pattern}"}}}};Object.assign({},nf.Modal);let ng=[],nh=()=>ng.reduce((e,t)=>Object.assign(Object.assign({},e),t),nf.Modal),nv=(0,x.createContext)(void 0),ny=e=>{let{locale:t={},children:n,_ANT_MARK__:a}=e;x.useEffect(()=>(function(e){if(e){let t=Object.assign({},e);return ng.push(t),nh(),()=>{ng=ng.filter(e=>e!==t),nh()}}Object.assign({},nf.Modal)})(null==t?void 0:t.Modal),[t]);let i=x.useMemo(()=>Object.assign(Object.assign({},t),{exist:!0}),[t]);return x.createElement(nv.Provider,{value:i},n)},nb=Math.round;function nx(e,t){let n=e.replace(/^[^(]*\((.*)/,"$1").replace(/\).*/,"").match(/\d*\.?\d+%?/g)||[],a=n.map(e=>parseFloat(e));for(let e=0;e<3;e+=1)a[e]=t(a[e]||0,n[e]||"",e);return n[3]?a[3]=n[3].includes("%")?a[3]/100:a[3]:a[3]=1,a}let nS=(e,t,n)=>0===n?e:e/100;function nw(e,t){let n=t||255;return e>n?n:e<0?0:e}class nj{setR(e){return this._sc("r",e)}setG(e){return this._sc("g",e)}setB(e){return this._sc("b",e)}setA(e){return this._sc("a",e,1)}setHue(e){let t=this.toHsv();return t.h=e,this._c(t)}getLuminance(){function e(e){let t=e/255;return t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}let t=e(this.r);return .2126*t+.7152*e(this.g)+.0722*e(this.b)}getHue(){if(void 0===this._h){let e=this.getMax()-this.getMin();0===e?this._h=0:this._h=nb(60*(this.r===this.getMax()?(this.g-this.b)/e+6*(this.g<this.b):this.g===this.getMax()?(this.b-this.r)/e+2:(this.r-this.g)/e+4))}return this._h}getSaturation(){if(void 0===this._s){let e=this.getMax()-this.getMin();0===e?this._s=0:this._s=e/this.getMax()}return this._s}getLightness(){return void 0===this._l&&(this._l=(this.getMax()+this.getMin())/510),this._l}getValue(){return void 0===this._v&&(this._v=this.getMax()/255),this._v}getBrightness(){return void 0===this._brightness&&(this._brightness=(299*this.r+587*this.g+114*this.b)/1e3),this._brightness}darken(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=this.getHue(),n=this.getSaturation(),a=this.getLightness()-e/100;return a<0&&(a=0),this._c({h:t,s:n,l:a,a:this.a})}lighten(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=this.getHue(),n=this.getSaturation(),a=this.getLightness()+e/100;return a>1&&(a=1),this._c({h:t,s:n,l:a,a:this.a})}mix(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,n=this._c(e),a=t/100,i=e=>(n[e]-this[e])*a+this[e],r={r:nb(i("r")),g:nb(i("g")),b:nb(i("b")),a:nb(100*i("a"))/100};return this._c(r)}tint(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return this.mix({r:255,g:255,b:255,a:1},e)}shade(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return this.mix({r:0,g:0,b:0,a:1},e)}onBackground(e){let t=this._c(e),n=this.a+t.a*(1-this.a),a=e=>nb((this[e]*this.a+t[e]*t.a*(1-this.a))/n);return this._c({r:a("r"),g:a("g"),b:a("b"),a:n})}isDark(){return 128>this.getBrightness()}isLight(){return this.getBrightness()>=128}equals(e){return this.r===e.r&&this.g===e.g&&this.b===e.b&&this.a===e.a}clone(){return this._c(this)}toHexString(){let e="#",t=(this.r||0).toString(16);e+=2===t.length?t:"0"+t;let n=(this.g||0).toString(16);e+=2===n.length?n:"0"+n;let a=(this.b||0).toString(16);if(e+=2===a.length?a:"0"+a,"number"==typeof this.a&&this.a>=0&&this.a<1){let t=nb(255*this.a).toString(16);e+=2===t.length?t:"0"+t}return e}toHsl(){return{h:this.getHue(),s:this.getSaturation(),l:this.getLightness(),a:this.a}}toHslString(){let e=this.getHue(),t=nb(100*this.getSaturation()),n=nb(100*this.getLightness());return 1!==this.a?`hsla(${e},${t}%,${n}%,${this.a})`:`hsl(${e},${t}%,${n}%)`}toHsv(){return{h:this.getHue(),s:this.getSaturation(),v:this.getValue(),a:this.a}}toRgb(){return{r:this.r,g:this.g,b:this.b,a:this.a}}toRgbString(){return 1!==this.a?`rgba(${this.r},${this.g},${this.b},${this.a})`:`rgb(${this.r},${this.g},${this.b})`}toString(){return this.toRgbString()}_sc(e,t,n){let a=this.clone();return a[e]=nw(t,n),a}_c(e){return new this.constructor(e)}getMax(){return void 0===this._max&&(this._max=Math.max(this.r,this.g,this.b)),this._max}getMin(){return void 0===this._min&&(this._min=Math.min(this.r,this.g,this.b)),this._min}fromHexString(e){let t=e.replace("#","");function n(e,n){return parseInt(t[e]+t[n||e],16)}t.length<6?(this.r=n(0),this.g=n(1),this.b=n(2),this.a=t[3]?n(3)/255:1):(this.r=n(0,1),this.g=n(2,3),this.b=n(4,5),this.a=t[6]?n(6,7)/255:1)}fromHsl(e){let{h:t,s:n,l:a,a:i}=e;if(this._h=t%360,this._s=n,this._l=a,this.a="number"==typeof i?i:1,n<=0){let e=nb(255*a);this.r=e,this.g=e,this.b=e}let r=0,o=0,s=0,l=t/60,c=(1-Math.abs(2*a-1))*n,u=c*(1-Math.abs(l%2-1));l>=0&&l<1?(r=c,o=u):l>=1&&l<2?(r=u,o=c):l>=2&&l<3?(o=c,s=u):l>=3&&l<4?(o=u,s=c):l>=4&&l<5?(r=u,s=c):l>=5&&l<6&&(r=c,s=u);let d=a-c/2;this.r=nb((r+d)*255),this.g=nb((o+d)*255),this.b=nb((s+d)*255)}fromHsv(e){let{h:t,s:n,v:a,a:i}=e;this._h=t%360,this._s=n,this._v=a,this.a="number"==typeof i?i:1;let r=nb(255*a);if(this.r=r,this.g=r,this.b=r,n<=0)return;let o=t/60,s=Math.floor(o),l=o-s,c=nb(a*(1-n)*255),u=nb(a*(1-n*l)*255),d=nb(a*(1-n*(1-l))*255);switch(s){case 0:this.g=d,this.b=c;break;case 1:this.r=u,this.b=c;break;case 2:this.r=c,this.b=d;break;case 3:this.r=c,this.g=u;break;case 4:this.r=d,this.g=c;break;default:this.g=c,this.b=u}}fromHsvString(e){let t=nx(e,nS);this.fromHsv({h:t[0],s:t[1],v:t[2],a:t[3]})}fromHslString(e){let t=nx(e,nS);this.fromHsl({h:t[0],s:t[1],l:t[2],a:t[3]})}fromRgbString(e){let t=nx(e,(e,t)=>t.includes("%")?nb(e/100*255):e);this.r=t[0],this.g=t[1],this.b=t[2],this.a=t[3]}constructor(e){function t(t){return t[0]in e&&t[1]in e&&t[2]in e}if(eC(this,"isValid",!0),eC(this,"r",0),eC(this,"g",0),eC(this,"b",0),eC(this,"a",1),eC(this,"_h",void 0),eC(this,"_s",void 0),eC(this,"_l",void 0),eC(this,"_v",void 0),eC(this,"_max",void 0),eC(this,"_min",void 0),eC(this,"_brightness",void 0),e)if("string"==typeof e){let t=e.trim();function n(e){return t.startsWith(e)}/^#?[A-F\d]{3,8}$/i.test(t)?this.fromHexString(t):n("rgb")?this.fromRgbString(t):n("hsl")?this.fromHslString(t):(n("hsv")||n("hsb"))&&this.fromHsvString(t)}else if(e instanceof nj)this.r=e.r,this.g=e.g,this.b=e.b,this.a=e.a,this._h=e._h,this._s=e._s,this._l=e._l,this._v=e._v;else if(t("rgb"))this.r=nw(e.r),this.g=nw(e.g),this.b=nw(e.b),this.a="number"==typeof e.a?nw(e.a,1):1;else if(t("hsl"))this.fromHsl(e);else if(t("hsv"))this.fromHsv(e);else throw Error("@ant-design/fast-color: unsupported input "+JSON.stringify(e))}}var nk=[{index:7,amount:15},{index:6,amount:25},{index:5,amount:30},{index:5,amount:45},{index:5,amount:65},{index:5,amount:85},{index:4,amount:90},{index:3,amount:95},{index:2,amount:97},{index:1,amount:98}];function nC(e,t,n){var a;return(a=Math.round(e.h)>=60&&240>=Math.round(e.h)?n?Math.round(e.h)-2*t:Math.round(e.h)+2*t:n?Math.round(e.h)+2*t:Math.round(e.h)-2*t)<0?a+=360:a>=360&&(a-=360),a}function nN(e,t,n){var a;return 0===e.h&&0===e.s?e.s:((a=n?e.s-.16*t:4===t?e.s+.16:e.s+.05*t)>1&&(a=1),n&&5===t&&a>.1&&(a=.1),a<.06&&(a=.06),Math.round(100*a)/100)}function nA(e,t,n){var a;return Math.round(100*Math.max(0,Math.min(1,n?e.v+.05*t:e.v-.15*t)))/100}function nT(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[],a=new nj(e),i=a.toHsv(),r=5;r>0;r-=1){var o=new nj({h:nC(i,r,!0),s:nN(i,r,!0),v:nA(i,r,!0)});n.push(o)}n.push(a);for(var s=1;s<=4;s+=1){var l=new nj({h:nC(i,s),s:nN(i,s),v:nA(i,s)});n.push(l)}return"dark"===t.theme?nk.map(function(e){var a=e.index,i=e.amount;return new nj(t.backgroundColor||"#141414").mix(n[a],i).toHexString()}):n.map(function(e){return e.toHexString()})}var n_={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},nE=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];nE.primary=nE[5];var nI=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];nI.primary=nI[5];var nO=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];nO.primary=nO[5];var nP=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];nP.primary=nP[5];var nM=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];nM.primary=nM[5];var nL=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];nL.primary=nL[5];var nD=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];nD.primary=nD[5];var nF=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];nF.primary=nF[5];var n$=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];n$.primary=n$[5];var nR=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];nR.primary=nR[5];var nZ=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];nZ.primary=nZ[5];var nU=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];nU.primary=nU[5];var nz=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];nz.primary=nz[5];var nB={red:nE,volcano:nI,orange:nO,gold:nP,yellow:nM,lime:nL,green:nD,cyan:nF,blue:n$,geekblue:nR,purple:nZ,magenta:nU,grey:nz},nH=["#2a1215","#431418","#58181c","#791a1f","#a61d24","#d32029","#e84749","#f37370","#f89f9a","#fac8c3"];nH.primary=nH[5];var nG=["#2b1611","#441d12","#592716","#7c3118","#aa3e19","#d84a1b","#e87040","#f3956a","#f8b692","#fad4bc"];nG.primary=nG[5];var nq=["#2b1d11","#442a11","#593815","#7c4a15","#aa6215","#d87a16","#e89a3c","#f3b765","#f8cf8d","#fae3b7"];nq.primary=nq[5];var nK=["#2b2111","#443111","#594214","#7c5914","#aa7714","#d89614","#e8b339","#f3cc62","#f8df8b","#faedb5"];nK.primary=nK[5];var nW=["#2b2611","#443b11","#595014","#7c6e14","#aa9514","#d8bd14","#e8d639","#f3ea62","#f8f48b","#fafab5"];nW.primary=nW[5];var nV=["#1f2611","#2e3c10","#3e4f13","#536d13","#6f9412","#8bbb11","#a9d134","#c9e75d","#e4f88b","#f0fab5"];nV.primary=nV[5];var nX=["#162312","#1d3712","#274916","#306317","#3c8618","#49aa19","#6abe39","#8fd460","#b2e58b","#d5f2bb"];nX.primary=nX[5];var nJ=["#112123","#113536","#144848","#146262","#138585","#13a8a8","#33bcb7","#58d1c9","#84e2d8","#b2f1e8"];nJ.primary=nJ[5];var nY=["#111a2c","#112545","#15325b","#15417e","#1554ad","#1668dc","#3c89e8","#65a9f3","#8dc5f8","#b7dcfa"];nY.primary=nY[5];var nQ=["#131629","#161d40","#1c2755","#203175","#263ea0","#2b4acb","#5273e0","#7f9ef3","#a8c1f8","#d2e0fa"];nQ.primary=nQ[5];var n0=["#1a1325","#24163a","#301c4d","#3e2069","#51258f","#642ab5","#854eca","#ab7ae0","#cda8f0","#ebd7fa"];n0.primary=n0[5];var n1=["#291321","#40162f","#551c3b","#75204f","#a02669","#cb2b83","#e0529c","#f37fb7","#f8a8cc","#fad2e3"];n1.primary=n1[5];var n2=["#151515","#1f1f1f","#2d2d2d","#393939","#494949","#5a5a5a","#6a6a6a","#7b7b7b","#888888","#969696"];n2.primary=n2[5];let n5={blue:"#1677FF",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#EB2F96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911"},n4=Object.assign(Object.assign({},n5),{colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff4d4f",colorInfo:"#1677ff",colorLink:"",colorTextBase:"",colorBgBase:"",fontFamily:`-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
'Noto Color Emoji'`,fontFamilyCode:"'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInBack:"cubic-bezier(0.71, -0.46, 0.88, 0.6)",motionEaseInQuint:"cubic-bezier(0.755, 0.05, 0.855, 0.06)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:6,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,motion:!0}),n3=e=>{let t=e,n=e,a=e,i=e;return e<6&&e>=5?t=e+1:e<16&&e>=6?t=e+2:e>=16&&(t=16),e<7&&e>=5?n=4:e<8&&e>=7?n=5:e<14&&e>=8?n=6:e<16&&e>=14?n=7:e>=16&&(n=8),e<6&&e>=2?a=1:e>=6&&(a=2),e>4&&e<8?i=4:e>=8&&(i=6),{borderRadius:e,borderRadiusXS:a,borderRadiusSM:n,borderRadiusLG:t,borderRadiusOuter:i}},n6=e=>{let{controlHeight:t}=e;return{controlHeightSM:.75*t,controlHeightXS:.5*t,controlHeightLG:1.25*t}},n8=e=>{let t=function(e){let t=Array.from({length:10}).map((t,n)=>{let a=e*Math.pow(Math.E,(n-1)/5);return 2*Math.floor((n>1?Math.floor(a):Math.ceil(a))/2)});return t[1]=e,t.map(e=>({size:e,lineHeight:(e+8)/e}))}(e),n=t.map(e=>e.size),a=t.map(e=>e.lineHeight),i=n[1],r=n[0],o=n[2],s=a[1],l=a[0],c=a[2];return{fontSizeSM:r,fontSize:i,fontSizeLG:o,fontSizeXL:n[3],fontSizeHeading1:n[6],fontSizeHeading2:n[5],fontSizeHeading3:n[4],fontSizeHeading4:n[3],fontSizeHeading5:n[2],lineHeight:s,lineHeightLG:c,lineHeightSM:l,fontHeight:Math.round(s*i),fontHeightLG:Math.round(c*o),fontHeightSM:Math.round(l*r),lineHeightHeading1:a[6],lineHeightHeading2:a[5],lineHeightHeading3:a[4],lineHeightHeading4:a[3],lineHeightHeading5:a[2]}},n9=(e,t)=>new nj(e).setA(t).toRgbString(),n7=(e,t)=>new nj(e).darken(t).toHexString(),ae=e=>{let t=nT(e);return{1:t[0],2:t[1],3:t[2],4:t[3],5:t[4],6:t[5],7:t[6],8:t[4],9:t[5],10:t[6]}},at=(e,t)=>{let n=e||"#fff",a=t||"#000";return{colorBgBase:n,colorTextBase:a,colorText:n9(a,.88),colorTextSecondary:n9(a,.65),colorTextTertiary:n9(a,.45),colorTextQuaternary:n9(a,.25),colorFill:n9(a,.15),colorFillSecondary:n9(a,.06),colorFillTertiary:n9(a,.04),colorFillQuaternary:n9(a,.02),colorBgSolid:n9(a,1),colorBgSolidHover:n9(a,.75),colorBgSolidActive:n9(a,.95),colorBgLayout:n7(n,4),colorBgContainer:n7(n,0),colorBgElevated:n7(n,0),colorBgSpotlight:n9(a,.85),colorBgBlur:"transparent",colorBorder:n7(n,15),colorBorderSecondary:n7(n,6)}},an=ti(function(e){n_.pink=n_.magenta,nB.pink=nB.magenta;let t=Object.keys(n5).map(t=>{let n=e[t]===n_[t]?nB[t]:nT(e[t]);return Array.from({length:10},()=>1).reduce((e,a,i)=>(e[`${t}-${i+1}`]=n[i],e[`${t}${i+1}`]=n[i],e),{})}).reduce((e,t)=>e=Object.assign(Object.assign({},e),t),{});return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},e),t),function(e,t){let{generateColorPalettes:n,generateNeutralColorPalettes:a}=t,{colorSuccess:i,colorWarning:r,colorError:o,colorInfo:s,colorPrimary:l,colorBgBase:c,colorTextBase:u}=e,d=n(l),p=n(i),m=n(r),f=n(o),g=n(s),h=a(c,u),v=n(e.colorLink||e.colorInfo),y=new nj(f[1]).mix(new nj(f[3]),50).toHexString();return Object.assign(Object.assign({},h),{colorPrimaryBg:d[1],colorPrimaryBgHover:d[2],colorPrimaryBorder:d[3],colorPrimaryBorderHover:d[4],colorPrimaryHover:d[5],colorPrimary:d[6],colorPrimaryActive:d[7],colorPrimaryTextHover:d[8],colorPrimaryText:d[9],colorPrimaryTextActive:d[10],colorSuccessBg:p[1],colorSuccessBgHover:p[2],colorSuccessBorder:p[3],colorSuccessBorderHover:p[4],colorSuccessHover:p[4],colorSuccess:p[6],colorSuccessActive:p[7],colorSuccessTextHover:p[8],colorSuccessText:p[9],colorSuccessTextActive:p[10],colorErrorBg:f[1],colorErrorBgHover:f[2],colorErrorBgFilledHover:y,colorErrorBgActive:f[3],colorErrorBorder:f[3],colorErrorBorderHover:f[4],colorErrorHover:f[5],colorError:f[6],colorErrorActive:f[7],colorErrorTextHover:f[8],colorErrorText:f[9],colorErrorTextActive:f[10],colorWarningBg:m[1],colorWarningBgHover:m[2],colorWarningBorder:m[3],colorWarningBorderHover:m[4],colorWarningHover:m[4],colorWarning:m[6],colorWarningActive:m[7],colorWarningTextHover:m[8],colorWarningText:m[9],colorWarningTextActive:m[10],colorInfoBg:g[1],colorInfoBgHover:g[2],colorInfoBorder:g[3],colorInfoBorderHover:g[4],colorInfoHover:g[4],colorInfo:g[6],colorInfoActive:g[7],colorInfoTextHover:g[8],colorInfoText:g[9],colorInfoTextActive:g[10],colorLinkHover:v[4],colorLink:v[6],colorLinkActive:v[7],colorBgMask:new nj("#000").setA(.45).toRgbString(),colorWhite:"#fff"})}(e,{generateColorPalettes:ae,generateNeutralColorPalettes:at})),n8(e.fontSize)),function(e){let{sizeUnit:t,sizeStep:n}=e;return{sizeXXL:t*(n+8),sizeXL:t*(n+4),sizeLG:t*(n+2),sizeMD:t*(n+1),sizeMS:t*n,size:t*n,sizeSM:t*(n-1),sizeXS:t*(n-2),sizeXXS:t*(n-3)}}(e)),n6(e)),function(e){let{motionUnit:t,motionBase:n,borderRadius:a,lineWidth:i}=e;return Object.assign({motionDurationFast:`${(n+t).toFixed(1)}s`,motionDurationMid:`${(n+2*t).toFixed(1)}s`,motionDurationSlow:`${(n+3*t).toFixed(1)}s`,lineWidthBold:i+1},n3(a))}(e))}),aa={token:n4,override:{override:n4},hashed:!0},ai=x.createContext(aa),ar=`-ant-${Date.now()}-${Math.random()}`,ao=x.createContext(!1),as=e=>{let{children:t,disabled:n}=e,a=x.useContext(ao);return x.createElement(ao.Provider,{value:null!=n?n:a},t)},al=x.createContext(void 0),ac=e=>{let{children:t,size:n}=e,a=x.useContext(al);return x.createElement(al.Provider,{value:n||a},t)},{useId:au}=Object.assign({},S),ad=void 0===au?()=>"":au;var ap=ed("../../node_modules/.pnpm/classnames@2.5.1/node_modules/classnames/index.js"),am=ed.n(ap);function af(e){return e instanceof HTMLElement||e instanceof SVGElement}var ag=ed("../../node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/index.js"),ah=Symbol.for("react.element"),av=Symbol.for("react.transitional.element"),ay=Symbol.for("react.fragment"),ab=Number(x.version.split(".")[0]),ax=function(e,t){"function"==typeof e?e(t):"object"===ej(e)&&e&&"current"in e&&(e.current=t)},aS=function(e){if(!e)return!1;if(aw(e)&&ab>=19)return!0;var t,n,a=(0,ag.isMemo)(e)?e.type.type:e.type;return("function"!=typeof a||!!(null!=(t=a.prototype)&&t.render)||a.$$typeof===ag.ForwardRef)&&("function"!=typeof e||!!(null!=(n=e.prototype)&&n.render)||e.$$typeof===ag.ForwardRef)};function aw(e){return(0,x.isValidElement)(e)&&!(e&&"object"===ej(e)&&(e.$$typeof===ah||e.$$typeof===av)&&e.type===ay)}var aj=["children"],ak=x.createContext({});function aC(e){var t=e.children,n=eZ(e,aj);return x.createElement(ak.Provider,{value:n},t)}var aN=function(e){e6(n,e);var t=e7(n);function n(){return eV(this,n),t.apply(this,arguments)}return eJ(n,[{key:"render",value:function(){return this.props.children}}]),n}(x.Component);function aA(e){var t=x.useRef();return t.current=e,x.useCallback(function(){for(var e,n=arguments.length,a=Array(n),i=0;i<n;i++)a[i]=arguments[i];return null==(e=t.current)?void 0:e.call.apply(e,[t].concat(a))},[])}function aT(e){var t=x.useRef(!1),n=ew(x.useState(e),2),a=n[0],i=n[1];return x.useEffect(function(){return t.current=!1,function(){t.current=!0}},[]),[a,function(e,n){n&&t.current||i(e)}]}var a_="none",aE="appear",aI="enter",aO="leave",aP="none",aM="prepare",aL="start",aD="active",aF="prepared";function a$(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit".concat(e)]="webkit".concat(t),n["Moz".concat(e)]="moz".concat(t),n["ms".concat(e)]="MS".concat(t),n["O".concat(e)]="o".concat(t.toLowerCase()),n}var aR=(l=e_(),c="undefined"!=typeof window?window:{},u={animationend:a$("Animation","AnimationEnd"),transitionend:a$("Transition","TransitionEnd")},l&&("AnimationEvent"in c||delete u.animationend.animation,"TransitionEvent"in c||delete u.transitionend.transition),u),aZ={};e_()&&(aZ=document.createElement("div").style);var aU={};function az(e){if(aU[e])return aU[e];var t=aR[e];if(t)for(var n=Object.keys(t),a=n.length,i=0;i<a;i+=1){var r=n[i];if(Object.prototype.hasOwnProperty.call(t,r)&&r in aZ)return aU[e]=t[r],aU[e]}return""}var aB=az("animationend"),aH=az("transitionend"),aG=!!(aB&&aH),aq=aB||"animationend",aK=aH||"transitionend";function aW(e,t){return e?"object"===ej(e)?e[t.replace(/-\w/g,function(e){return e[1].toUpperCase()})]:"".concat(e,"-").concat(t):null}let aV=function(e){var t=(0,x.useRef)();function n(t){t&&(t.removeEventListener(aK,e),t.removeEventListener(aq,e))}return x.useEffect(function(){return function(){n(t.current)}},[]),[function(a){t.current&&t.current!==a&&n(t.current),a&&a!==t.current&&(a.addEventListener(aK,e),a.addEventListener(aq,e),t.current=a)},n]};var aX=e_()?x.useLayoutEffect:x.useEffect,aJ=function(e){return+setTimeout(e,16)},aY=function(e){return clearTimeout(e)};"undefined"!=typeof window&&"requestAnimationFrame"in window&&(aJ=function(e){return window.requestAnimationFrame(e)},aY=function(e){return window.cancelAnimationFrame(e)});var aQ=0,a0=new Map,a1=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=aQ+=1;return!function t(a){if(0===a)a0.delete(n),e();else{var i=aJ(function(){t(a-1)});a0.set(n,i)}}(t),n};a1.cancel=function(e){var t=a0.get(e);return a0.delete(e),aY(t)};let a2=function(){var e=x.useRef(null);function t(){a1.cancel(e.current)}return x.useEffect(function(){return function(){t()}},[]),[function n(a){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;t();var r=a1(function(){i<=1?a({isCanceled:function(){return r!==e.current}}):n(a,i-1)});e.current=r},t]};var a5=[aM,aL,aD,"end"],a4=[aM,aF];function a3(e){return e===aD||"end"===e}let a6=function(e,t,n){var a=ew(aT(aP),2),i=a[0],r=a[1],o=ew(a2(),2),s=o[0],l=o[1],c=t?a4:a5;return aX(function(){if(i!==aP&&"end"!==i){var e=c.indexOf(i),t=c[e+1],a=n(i);!1===a?r(t,!0):t&&s(function(e){function n(){e.isCanceled()||r(t,!0)}!0===a?n():Promise.resolve(a).then(n)})}},[e,i]),x.useEffect(function(){return function(){l()}},[]),[function(){r(aM,!0)},i]},a8=(d=aG,"object"===ej(aG)&&(d=aG.transitionSupport),(p=x.forwardRef(function(e,t){var n,a=e.visible,i=void 0===a||a,r=e.removeOnLeave,o=void 0===r||r,s=e.forceRender,l=e.children,c=e.motionName,u=e.leavedClassName,p=e.eventProps,m=x.useContext(ak).motion,f=!!(e.motionName&&d&&!1!==m),g=(0,x.useRef)(),h=(0,x.useRef)(),v=function(e,t,n,a){var i,r,o=a.motionEnter,s=void 0===o||o,l=a.motionAppear,c=void 0===l||l,u=a.motionLeave,d=void 0===u||u,p=a.motionDeadline,m=a.motionLeaveImmediately,f=a.onAppearPrepare,g=a.onEnterPrepare,h=a.onLeavePrepare,v=a.onAppearStart,y=a.onEnterStart,b=a.onLeaveStart,S=a.onAppearActive,w=a.onEnterActive,j=a.onLeaveActive,k=a.onAppearEnd,C=a.onEnterEnd,N=a.onLeaveEnd,A=a.onVisibleChanged,T=ew(aT(),2),_=T[0],E=T[1],I=(i=ew(x.useReducer(function(e){return e+1},0),2)[1],r=x.useRef(a_),[aA(function(){return r.current}),aA(function(e){r.current="function"==typeof e?e(r.current):e,i()})]),O=ew(I,2),P=O[0],M=O[1],L=ew(aT(null),2),D=L[0],F=L[1],$=P(),R=(0,x.useRef)(!1),Z=(0,x.useRef)(null),U=(0,x.useRef)(!1);function z(){M(a_),F(null,!0)}var B=aA(function(e){var t,a=P();if(a!==a_){var i=n();if(!e||e.deadline||e.target===i){var r=U.current;a===aE&&r?t=null==k?void 0:k(i,e):a===aI&&r?t=null==C?void 0:C(i,e):a===aO&&r&&(t=null==N?void 0:N(i,e)),r&&!1!==t&&z()}}}),H=ew(aV(B),1)[0],G=function(e){switch(e){case aE:return eC(eC(eC({},aM,f),aL,v),aD,S);case aI:return eC(eC(eC({},aM,g),aL,y),aD,w);case aO:return eC(eC(eC({},aM,h),aL,b),aD,j);default:return{}}},q=x.useMemo(function(){return G($)},[$]),K=ew(a6($,!e,function(e){if(e===aM){var t,a=q[aM];return!!a&&a(n())}return V in q&&F((null==(t=q[V])?void 0:t.call(q,n(),null))||null),V===aD&&$!==a_&&(H(n()),p>0&&(clearTimeout(Z.current),Z.current=setTimeout(function(){B({deadline:!0})},p))),V===aF&&z(),!0}),2),W=K[0],V=K[1];U.current=a3(V);var X=(0,x.useRef)(null);aX(function(){if(!R.current||X.current!==t){E(t);var n,a=R.current;R.current=!0,!a&&t&&c&&(n=aE),a&&t&&s&&(n=aI),(a&&!t&&d||!a&&m&&!t&&d)&&(n=aO);var i=G(n);n&&(e||i[aM])?(M(n),W()):M(a_),X.current=t}},[t]),(0,x.useEffect)(function(){($!==aE||c)&&($!==aI||s)&&($!==aO||d)||M(a_)},[c,s,d]),(0,x.useEffect)(function(){return function(){R.current=!1,clearTimeout(Z.current)}},[]);var J=x.useRef(!1);(0,x.useEffect)(function(){_&&(J.current=!0),void 0!==_&&$===a_&&((J.current||_)&&(null==A||A(_)),J.current=!0)},[_,$]);var Y=D;return q[aM]&&V===aL&&(Y=eA({transition:"none"},Y)),[$,V,Y,null!=_?_:t]}(f,i,function(){try{var e,t,n,a;return g.current instanceof HTMLElement?g.current:(a=(t=e=h.current)&&"object"===ej(t)&&af(t.nativeElement)?t.nativeElement:af(t)?t:null)?a:e instanceof x.Component?null==(n=es.findDOMNode)?void 0:n.call(es,e):null}catch(e){return null}},e),y=ew(v,4),b=y[0],S=y[1],w=y[2],j=y[3],k=x.useRef(j);j&&(k.current=!0);var C=x.useCallback(function(e){g.current=e,ax(t,e)},[t]),N=eA(eA({},p),{},{visible:i});if(l)if(b===a_)A=j?l(eA({},N),C):!o&&k.current&&u?l(eA(eA({},N),{},{className:u}),C):!s&&(o||u)?null:l(eA(eA({},N),{},{style:{display:"none"}}),C);else{S===aM?T="prepare":a3(S)?T="active":S===aL&&(T="start");var A,T,_=aW(c,"".concat(b,"-").concat(T));A=l(eA(eA({},N),{},{className:am()(aW(c,b),eC(eC({},_,_&&T),c,"string"==typeof c)),style:w}),C)}else A=null;return x.isValidElement(A)&&aS(A)&&(((n=A)&&aw(n)?n.props.propertyIsEnumerable("ref")?n.props.ref:n.ref:null)||(A=x.cloneElement(A,{ref:C}))),x.createElement(aN,{ref:h},A)})).displayName="CSSMotion",p);var a9="keep",a7="remove",ie="removed";function it(e){var t;return eA(eA({},t=e&&"object"===ej(e)&&"key"in e?e:{key:e}),{},{key:String(t.key)})}function ia(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.map(it)}var ii=["component","children","onVisibleChanged","onAllRemoved"],ir=["status"],io=["eventProps","visible","children","motionName","motionAppear","motionEnter","motionLeave","motionLeaveImmediately","motionDeadline","removeOnLeave","leavedClassName","onAppearPrepare","onAppearStart","onAppearActive","onAppearEnd","onEnterStart","onEnterActive","onEnterEnd","onLeaveStart","onLeaveActive","onLeaveEnd"];let is=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a8,n=function(e){e6(a,e);var n=e7(a);function a(){var e;eV(this,a);for(var t=arguments.length,i=Array(t),r=0;r<t;r++)i[r]=arguments[r];return eC(e4(e=n.call.apply(n,[this].concat(i))),"state",{keyEntities:[]}),eC(e4(e),"removeKey",function(t){e.setState(function(e){return{keyEntities:e.keyEntities.map(function(e){return e.key!==t?e:eA(eA({},e),{},{status:ie})})}},function(){0===e.state.keyEntities.filter(function(e){return e.status!==ie}).length&&e.props.onAllRemoved&&e.props.onAllRemoved()})}),e}return eJ(a,[{key:"render",value:function(){var e=this,n=this.state.keyEntities,a=this.props,i=a.component,r=a.children,o=a.onVisibleChanged,s=(a.onAllRemoved,eZ(a,ii)),l=i||x.Fragment,c={};return io.forEach(function(e){c[e]=s[e],delete s[e]}),delete s.keys,x.createElement(l,s,n.map(function(n,a){var i=n.status,s=eZ(n,ir);return x.createElement(t,tN({},c,{key:s.key,visible:"add"===i||i===a9,eventProps:s,onVisibleChanged:function(t){null==o||o(t,{key:s.key}),t||e.removeKey(s.key)}}),function(e,t){return r(eA(eA({},e),{},{index:a}),t)})}))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.keys,a=t.keyEntities;return{keyEntities:(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[],a=0,i=t.length,r=ia(e),o=ia(t);r.forEach(function(e){for(var t=!1,r=a;r<i;r+=1){var s=o[r];if(s.key===e.key){a<r&&(n=n.concat(o.slice(a,r).map(function(e){return eA(eA({},e),{},{status:"add"})})),a=r),n.push(eA(eA({},s),{},{status:a9})),a+=1,t=!0;break}}t||n.push(eA(eA({},e),{},{status:a7}))}),a<i&&(n=n.concat(o.slice(a).map(function(e){return eA(eA({},e),{},{status:"add"})})));var s={};return n.forEach(function(e){var t=e.key;s[t]=(s[t]||0)+1}),Object.keys(s).filter(function(e){return s[e]>1}).forEach(function(e){(n=n.filter(function(t){var n=t.key,a=t.status;return n!==e||a!==a7})).forEach(function(t){t.key===e&&(t.status=a9)})}),n})(a,ia(n)).filter(function(e){var t=a.find(function(t){var n=t.key;return e.key===n});return!t||t.status!==ie||e.status!==a7})}}}]),a}(x.Component);return eC(n,"defaultProps",{component:"div"}),n}(aG);function il(e){return e>=0&&e<=255}let ic=function(e,t){let{r:n,g:a,b:i,a:r}=new nj(e).toRgb();if(r<1)return e;let{r:o,g:s,b:l}=new nj(t).toRgb();for(let e=.01;e<=1;e+=.01){let t=Math.round((n-o*(1-e))/e),r=Math.round((a-s*(1-e))/e),c=Math.round((i-l*(1-e))/e);if(il(t)&&il(r)&&il(c))return new nj({r:t,g:r,b:c,a:Math.round(100*e)/100}).toRgbString()}return new nj({r:n,g:a,b:i,a:1}).toRgbString()};var iu=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,a=Object.getOwnPropertySymbols(e);i<a.length;i++)0>t.indexOf(a[i])&&Object.prototype.propertyIsEnumerable.call(e,a[i])&&(n[a[i]]=e[a[i]]);return n};function id(e){let{override:t}=e,n=iu(e,["override"]),a=Object.assign({},t);Object.keys(n4).forEach(e=>{delete a[e]});let i=Object.assign(Object.assign({},n),a);return!1===i.motion&&(i.motionDurationFast="0s",i.motionDurationMid="0s",i.motionDurationSlow="0s"),Object.assign(Object.assign(Object.assign({},i),{colorFillContent:i.colorFillSecondary,colorFillContentHover:i.colorFill,colorFillAlter:i.colorFillQuaternary,colorBgContainerDisabled:i.colorFillTertiary,colorBorderBg:i.colorBgContainer,colorSplit:ic(i.colorBorderSecondary,i.colorBgContainer),colorTextPlaceholder:i.colorTextQuaternary,colorTextDisabled:i.colorTextQuaternary,colorTextHeading:i.colorText,colorTextLabel:i.colorTextSecondary,colorTextDescription:i.colorTextTertiary,colorTextLightSolid:i.colorWhite,colorHighlight:i.colorError,colorBgTextHover:i.colorFillSecondary,colorBgTextActive:i.colorFill,colorIcon:i.colorTextTertiary,colorIconHover:i.colorText,colorErrorOutline:ic(i.colorErrorBg,i.colorBgContainer),colorWarningOutline:ic(i.colorWarningBg,i.colorBgContainer),fontSizeIcon:i.fontSizeSM,lineWidthFocus:3*i.lineWidth,lineWidth:i.lineWidth,controlOutlineWidth:2*i.lineWidth,controlInteractiveSize:i.controlHeight/2,controlItemBgHover:i.colorFillTertiary,controlItemBgActive:i.colorPrimaryBg,controlItemBgActiveHover:i.colorPrimaryBgHover,controlItemBgActiveDisabled:i.colorFill,controlTmpOutline:i.colorFillQuaternary,controlOutline:ic(i.colorPrimaryBg,i.colorBgContainer),lineType:i.lineType,borderRadius:i.borderRadius,borderRadiusXS:i.borderRadiusXS,borderRadiusSM:i.borderRadiusSM,borderRadiusLG:i.borderRadiusLG,fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:i.sizeXXS,paddingXS:i.sizeXS,paddingSM:i.sizeSM,padding:i.size,paddingMD:i.sizeMD,paddingLG:i.sizeLG,paddingXL:i.sizeXL,paddingContentHorizontalLG:i.sizeLG,paddingContentVerticalLG:i.sizeMS,paddingContentHorizontal:i.sizeMS,paddingContentVertical:i.sizeSM,paddingContentHorizontalSM:i.size,paddingContentVerticalSM:i.sizeXS,marginXXS:i.sizeXXS,marginXS:i.sizeXS,marginSM:i.sizeSM,margin:i.size,marginMD:i.sizeMD,marginLG:i.sizeLG,marginXL:i.sizeXL,marginXXL:i.sizeXXL,boxShadow:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowSecondary:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowTertiary:`
      0 1px 2px 0 rgba(0, 0, 0, 0.03),
      0 1px 6px -1px rgba(0, 0, 0, 0.02),
      0 2px 4px 0 rgba(0, 0, 0, 0.02)
    `,screenXS:480,screenXSMin:480,screenXSMax:575,screenSM:576,screenSMMin:576,screenSMMax:767,screenMD:768,screenMDMin:768,screenMDMax:991,screenLG:992,screenLGMin:992,screenLGMax:1199,screenXL:1200,screenXLMin:1200,screenXLMax:1599,screenXXL:1600,screenXXLMin:1600,boxShadowPopoverArrow:"2px 2px 5px rgba(0, 0, 0, 0.05)",boxShadowCard:`
      0 1px 2px -2px ${new nj("rgba(0, 0, 0, 0.16)").toRgbString()},
      0 3px 6px 0 ${new nj("rgba(0, 0, 0, 0.12)").toRgbString()},
      0 5px 12px 4px ${new nj("rgba(0, 0, 0, 0.09)").toRgbString()}
    `,boxShadowDrawerRight:`
      -6px 0 16px 0 rgba(0, 0, 0, 0.08),
      -3px 0 6px -4px rgba(0, 0, 0, 0.12),
      -9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerLeft:`
      6px 0 16px 0 rgba(0, 0, 0, 0.08),
      3px 0 6px -4px rgba(0, 0, 0, 0.12),
      9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerUp:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerDown:`
      0 -6px 16px 0 rgba(0, 0, 0, 0.08),
      0 -3px 6px -4px rgba(0, 0, 0, 0.12),
      0 -9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)"}),a)}var ip=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,a=Object.getOwnPropertySymbols(e);i<a.length;i++)0>t.indexOf(a[i])&&Object.prototype.propertyIsEnumerable.call(e,a[i])&&(n[a[i]]=e[a[i]]);return n};let im={lineHeight:!0,lineHeightSM:!0,lineHeightLG:!0,lineHeightHeading1:!0,lineHeightHeading2:!0,lineHeightHeading3:!0,lineHeightHeading4:!0,lineHeightHeading5:!0,opacityLoading:!0,fontWeightStrong:!0,zIndexPopupBase:!0,zIndexBase:!0,opacityImage:!0},ig={size:!0,sizeSM:!0,sizeLG:!0,sizeMD:!0,sizeXS:!0,sizeXXS:!0,sizeMS:!0,sizeXL:!0,sizeXXL:!0,sizeUnit:!0,sizeStep:!0,motionBase:!0,motionUnit:!0},ih={screenXS:!0,screenXSMin:!0,screenXSMax:!0,screenSM:!0,screenSMMin:!0,screenSMMax:!0,screenMD:!0,screenMDMin:!0,screenMDMax:!0,screenLG:!0,screenLGMin:!0,screenLGMax:!0,screenXL:!0,screenXLMin:!0,screenXLMax:!0,screenXXL:!0,screenXXLMin:!0},iv=(e,t,n)=>{let a=n.getDerivativeToken(e),{override:i}=t,r=ip(t,["override"]),o=Object.assign(Object.assign({},a),{override:i});return o=id(o),r&&Object.entries(r).forEach(e=>{let[t,n]=e,{theme:a}=n,i=ip(n,["theme"]),r=i;a&&(r=iv(Object.assign(Object.assign({},o),i),{override:i},a)),o[t]=r}),o};function iy(){let{token:e,hashed:t,theme:n,override:a,cssVar:i}=x.useContext(ai),r=n||an,[o,s,l]=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=(0,x.useContext)(e5),i=a.cache.instanceId,r=a.container,o=n.salt,s=void 0===o?"":o,l=n.override,c=void 0===l?tw:l,u=n.formatToken,d=n.getComputedToken,p=n.cssVar,m=function(e,t){for(var n=tr,a=0;a<t.length;a+=1){var i=t[a];n.has(i)||n.set(i,new WeakMap),n=n.get(i)}return n.has(to)||n.set(to,e()),n.get(to)}(function(){return Object.assign.apply(Object,[{}].concat(eg(t)))},t),f=tl(m),g=tl(c),h=p?tl(p):"";return tS(tC,[s,e.id,f,g,h],function(){var t,n=d?d(m,c,e):tk(m,c,e,u),a=eA({},n),i="";if(p){var r=ew(tf(n,p.key,{prefix:p.prefix,ignore:p.ignore,unitless:p.unitless,preserve:p.preserve}),2);n=r[0],i=r[1]}var o=tc(n,s);n._tokenKey=o,a._tokenKey=tc(a,s);var l=null!=(t=null==p?void 0:p.key)?t:o;n._themeKey=l,tj.set(l,(tj.get(l)||0)+1);var f="".concat("css","-").concat(eT(o));return n._hashId=f,[n,f,a,i,(null==p?void 0:p.key)||""]},function(e){var t,n,a;t=e[0]._themeKey,tj.set(t,(tj.get(t)||0)-1),a=(n=Array.from(tj.keys())).filter(function(e){return 0>=(tj.get(e)||0)}),n.length-a.length>0&&a.forEach(function(e){"undefined"!=typeof document&&document.querySelectorAll("style[".concat(e0,'="').concat(e,'"]')).forEach(function(e){if(e[e2]===i){var t;null==(t=e.parentNode)||t.removeChild(e)}}),tj.delete(e)})},function(e){var t=ew(e,4),n=t[0],a=t[3];if(p&&a){var o=eR(a,eT("css-variables-".concat(n._themeKey)),{mark:e1,prepend:"queue",attachTo:r,priority:-999});o[e2]=i,o.setAttribute(e0,n._themeKey)}})}(r,[n4,e],{salt:`5.24.8-${t||""}`,override:a,getComputedToken:iv,formatToken:id,cssVar:i&&{prefix:i.prefix,key:i.key,unitless:im,ignore:ig,preserve:ih}});return[r,l,t?s:"",o,i]}function ib(e){let{children:t}=e,[,n]=iy(),{motion:a}=n,i=x.useRef(!1);return(i.current=i.current||!1===a,i.current)?x.createElement(aC,{motion:a},t):t}let ix=()=>null,iS=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return{boxSizing:"border-box",margin:0,padding:0,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,listStyle:"none",fontFamily:t?"inherit":e.fontFamily}},iw=()=>({display:"inline-flex",alignItems:"center",color:"inherit",fontStyle:"normal",lineHeight:0,textAlign:"center",textTransform:"none",verticalAlign:"-0.125em",textRendering:"optimizeLegibility","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale","> *":{lineHeight:1},svg:{display:"inline-block"}}),ij=e=>({a:{color:e.colorLink,textDecoration:e.linkDecoration,backgroundColor:"transparent",outline:"none",cursor:"pointer",transition:`color ${e.motionDurationSlow}`,"-webkit-text-decoration-skip":"objects","&:hover":{color:e.colorLinkHover},"&:active":{color:e.colorLinkActive},"&:active, &:hover":{textDecoration:e.linkHoverDecoration,outline:0},"&:focus":{textDecoration:e.linkFocusDecoration,outline:0},"&[disabled]":{color:e.colorTextDisabled,cursor:"not-allowed"}}}),ik=e=>({[`.${e}`]:Object.assign(Object.assign({},iw()),{[`.${e} .${e}-icon`]:{display:"block"}})}),iC=(e,t)=>{let[n,a]=iy();return t7({theme:n,token:a,hashId:"",path:["ant-design-icons",e],nonce:()=>null==t?void 0:t.nonce,layer:{name:"antd"}},()=>[ik(e)])};var iN=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,a=Object.getOwnPropertySymbols(e);i<a.length;i++)0>t.indexOf(a[i])&&Object.prototype.propertyIsEnumerable.call(e,a[i])&&(n[a[i]]=e[a[i]]);return n};let iA=["getTargetContainer","getPopupContainer","renderEmpty","input","pagination","form","select","button"];function iT(){return a||"ant"}function i_(){return i||ev}let iE=()=>({getPrefixCls:(e,t)=>t||(e?`${iT()}-${e}`:iT()),getIconPrefixCls:i_,getRootPrefixCls:()=>a||iT(),getTheme:()=>r,holderRender:o}),iI=e=>{let{children:t,csp:n,autoInsertSpaceInButton:a,alert:i,anchor:r,form:o,locale:s,componentSize:l,direction:c,space:u,splitter:d,virtual:p,dropdownMatchSelectWidth:m,popupMatchSelectWidth:f,popupOverflow:g,legacyLocale:h,parentContext:v,iconPrefixCls:y,theme:b,componentDisabled:S,segmented:w,statistic:j,spin:k,calendar:C,carousel:N,cascader:A,collapse:T,typography:_,checkbox:E,descriptions:I,divider:O,drawer:P,skeleton:M,steps:L,image:D,layout:F,list:$,mentions:R,modal:Z,progress:U,result:z,slider:B,breadcrumb:H,menu:G,pagination:q,input:K,textArea:W,empty:V,badge:X,radio:J,rate:Y,switch:Q,transfer:ee,avatar:et,message:en,tag:ea,table:ei,card:er,tabs:eo,timeline:es,timePicker:el,upload:ec,notification:eu,tree:ed,colorPicker:ep,datePicker:em,rangePicker:ef,flex:eh,wave:eb,dropdown:ex,warning:eS,tour:ew,tooltip:ek,popover:eC,popconfirm:eN,floatButtonGroup:eA,variant:eT,inputNumber:e_,treeSelect:eE}=e,eI=x.useCallback((t,n)=>{let{prefixCls:a}=e;if(n)return n;let i=a||v.getPrefixCls("");return t?`${i}-${t}`:i},[v.getPrefixCls,e.prefixCls]),eO=y||v.iconPrefixCls||ev,eP=n||v.csp;iC(eO,eP);let eM=function(e,t,n){var a;nd("ConfigProvider");let i=e||{},r=!1!==i.inherit&&t?t:Object.assign(Object.assign({},aa),{hashed:null!=(a=null==t?void 0:t.hashed)?a:aa.hashed,cssVar:null==t?void 0:t.cssVar}),o=ad();return eU(()=>{var a,s;if(!e)return t;let l=Object.assign({},r.components);Object.keys(e.components||{}).forEach(t=>{l[t]=Object.assign(Object.assign({},l[t]),e.components[t])});let c=`css-var-${o.replace(/:/g,"")}`,u=(null!=(a=i.cssVar)?a:r.cssVar)&&Object.assign(Object.assign(Object.assign({prefix:null==n?void 0:n.prefixCls},"object"==typeof r.cssVar?r.cssVar:{}),"object"==typeof i.cssVar?i.cssVar:{}),{key:"object"==typeof i.cssVar&&(null==(s=i.cssVar)?void 0:s.key)||c});return Object.assign(Object.assign(Object.assign({},r),i),{token:Object.assign(Object.assign({},r.token),i.token),components:l,cssVar:u})},[i,r],(e,t)=>e.some((e,n)=>!eW(e,t[n],!0)))}(b,v.theme,{prefixCls:eI("")}),eL={csp:eP,autoInsertSpaceInButton:a,alert:i,anchor:r,locale:s||h,direction:c,space:u,splitter:d,virtual:p,popupMatchSelectWidth:null!=f?f:m,popupOverflow:g,getPrefixCls:eI,iconPrefixCls:eO,theme:eM,segmented:w,statistic:j,spin:k,calendar:C,carousel:N,cascader:A,collapse:T,typography:_,checkbox:E,descriptions:I,divider:O,drawer:P,skeleton:M,steps:L,image:D,input:K,textArea:W,layout:F,list:$,mentions:R,modal:Z,progress:U,result:z,slider:B,breadcrumb:H,menu:G,pagination:q,empty:V,badge:X,radio:J,rate:Y,switch:Q,transfer:ee,avatar:et,message:en,tag:ea,table:ei,card:er,tabs:eo,timeline:es,timePicker:el,upload:ec,notification:eu,tree:ed,colorPicker:ep,datePicker:em,rangePicker:ef,flex:eh,wave:eb,dropdown:ex,warning:eS,tour:ew,tooltip:ek,popover:eC,popconfirm:eN,floatButtonGroup:eA,variant:eT,inputNumber:e_,treeSelect:eE},eD=Object.assign({},v);Object.keys(eL).forEach(e=>{void 0!==eL[e]&&(eD[e]=eL[e])}),iA.forEach(t=>{let n=e[t];n&&(eD[t]=n)}),void 0!==a&&(eD.button=Object.assign({autoInsertSpace:a},eD.button));let eF=eU(()=>eD,eD,(e,t)=>{let n=Object.keys(e),a=Object.keys(t);return n.length!==a.length||n.some(n=>e[n]!==t[n])}),{layer:e$}=x.useContext(e5),eR=x.useMemo(()=>({prefixCls:eO,csp:eP,layer:e$?"antd":void 0}),[eO,eP,e$]),eZ=x.createElement(x.Fragment,null,x.createElement(ix,{dropdownMatchSelectWidth:m}),t),ez=x.useMemo(()=>{var e,t,n,a;return function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var a=ns(t[0]);return t.forEach(function(e){!function t(n,i){var r=new Set(i),o=nr(e,n),s=Array.isArray(o);if(s||"object"===ej(o)&&null!==o&&Object.getPrototypeOf(o)===Object.prototype){if(!r.has(o)){r.add(o);var l=nr(a,n);s?a=no(a,n,[]):l&&"object"===ej(l)||(a=no(a,n,ns(o))),nl(o).forEach(function(e){t([].concat(eg(n),[e]),r)})}}else a=no(a,n,o)}([])}),a}((null==(e=nf.Form)?void 0:e.defaultValidateMessages)||{},(null==(n=null==(t=eF.locale)?void 0:t.Form)?void 0:n.defaultValidateMessages)||{},(null==(a=eF.form)?void 0:a.validateMessages)||{},(null==o?void 0:o.validateMessages)||{})},[eF,null==o?void 0:o.validateMessages]);Object.keys(ez).length>0&&(eZ=x.createElement(np.Provider,{value:ez},eZ)),s&&(eZ=x.createElement(ny,{locale:s,_ANT_MARK__:"internalMark"},eZ)),(eO||eP)&&(eZ=x.createElement(ni.Provider,{value:eR},eZ)),l&&(eZ=x.createElement(ac,{size:l},eZ)),eZ=x.createElement(ib,null,eZ);let eB=x.useMemo(()=>{let e=eM||{},{algorithm:t,token:n,components:a,cssVar:i}=e,r=iN(e,["algorithm","token","components","cssVar"]),o=t&&(!Array.isArray(t)||t.length>0)?ti(t):an,s={};Object.entries(a||{}).forEach(e=>{let[t,n]=e,a=Object.assign({},n);"algorithm"in a&&(!0===a.algorithm?a.theme=o:(Array.isArray(a.algorithm)||"function"==typeof a.algorithm)&&(a.theme=ti(a.algorithm)),delete a.algorithm),s[t]=a});let l=Object.assign(Object.assign({},n4),n);return Object.assign(Object.assign({},r),{theme:o,token:l,components:s,override:Object.assign({override:l},s),cssVar:i})},[eM]);return b&&(eZ=x.createElement(ai.Provider,{value:eB},eZ)),eF.warning&&(eZ=x.createElement(nu.Provider,{value:eF.warning},eZ)),void 0!==S&&(eZ=x.createElement(as,{disabled:S},eZ)),x.createElement(ey.Provider,{value:eF},eZ)},iO=e=>{let t=x.useContext(ey),n=x.useContext(nv);return x.createElement(iI,Object.assign({parentContext:t,legacyLocale:n},e))};function iP(){iP=function(){return t};var e,t={},n=Object.prototype,a=n.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",s=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(t,n,a,r){var o,s,l,c,u=Object.create((n&&n.prototype instanceof h?n:h).prototype);return i(u,"_invoke",{value:(o=t,s=a,l=new A(r||[]),c=p,function(t,n){if(c===m)throw Error("Generator is already running");if(c===f){if("throw"===t)throw n;return{value:e,done:!0}}for(l.method=t,l.arg=n;;){var a=l.delegate;if(a){var i=function t(n,a){var i=a.method,r=n.iterator[i];if(r===e)return a.delegate=null,"throw"===i&&n.iterator.return&&(a.method="return",a.arg=e,t(n,a),"throw"===a.method)||"return"!==i&&(a.method="throw",a.arg=TypeError("The iterator does not provide a '"+i+"' method")),g;var o=d(r,n.iterator,a.arg);if("throw"===o.type)return a.method="throw",a.arg=o.arg,a.delegate=null,g;var s=o.arg;return s?s.done?(a[n.resultName]=s.value,a.next=n.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,g):s:(a.method="throw",a.arg=TypeError("iterator result is not an object"),a.delegate=null,g)}(a,l);if(i){if(i===g)continue;return i}}if("next"===l.method)l.sent=l._sent=l.arg;else if("throw"===l.method){if(c===p)throw c=f,l.arg;l.dispatchException(l.arg)}else"return"===l.method&&l.abrupt("return",l.arg);c=m;var r=d(o,s,l);if("normal"===r.type){if(c=l.done?f:"suspendedYield",r.arg===g)continue;return{value:r.arg,done:l.done}}"throw"===r.type&&(c=f,l.method="throw",l.arg=r.arg)}})}),u}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var p="suspendedStart",m="executing",f="completed",g={};function h(){}function v(){}function y(){}var b={};c(b,o,function(){return this});var x=Object.getPrototypeOf,S=x&&x(x(T([])));S&&S!==n&&a.call(S,o)&&(b=S);var w=y.prototype=h.prototype=Object.create(b);function j(e){["next","throw","return"].forEach(function(t){c(e,t,function(e){return this._invoke(t,e)})})}function k(e,t){var n;i(this,"_invoke",{value:function(i,r){function o(){return new t(function(n,o){!function n(i,r,o,s){var l=d(e[i],e,r);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==ej(u)&&a.call(u,"__await")?t.resolve(u.__await).then(function(e){n("next",e,o,s)},function(e){n("throw",e,o,s)}):t.resolve(u).then(function(e){c.value=e,o(c)},function(e){return n("throw",e,o,s)})}s(l.arg)}(i,r,n,o)})}return n=n?n.then(o,o):o()}})}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function A(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function T(t){if(t||""===t){var n=t[o];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,r=function n(){for(;++i<t.length;)if(a.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return r.next=r}}throw TypeError(ej(t)+" is not iterable")}return v.prototype=y,i(w,"constructor",{value:y,configurable:!0}),i(y,"constructor",{value:v,configurable:!0}),v.displayName=c(y,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,c(e,l,"GeneratorFunction")),e.prototype=Object.create(w),e},t.awrap=function(e){return{__await:e}},j(k.prototype),c(k.prototype,s,function(){return this}),t.AsyncIterator=k,t.async=function(e,n,a,i,r){void 0===r&&(r=Promise);var o=new k(u(e,n,a,i),r);return t.isGeneratorFunction(n)?o:o.next().then(function(e){return e.done?e.value:o.next()})},j(w),c(w,l,"Generator"),c(w,o,function(){return this}),c(w,"toString",function(){return"[object Generator]"}),t.keys=function(e){var t=Object(e),n=[];for(var a in t)n.push(a);return n.reverse(),function e(){for(;n.length;){var a=n.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},t.values=T,A.prototype={constructor:A,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function i(a,i){return s.type="throw",s.arg=t,n.next=a,i&&(n.method="next",n.arg=e),!!i}for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],s=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var l=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&a.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var r=i;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var o=r?r.completion:{};return o.type=e,o.arg=t,r?(this.method="next",this.next=r.finallyLoc,g):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),N(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var a=n.completion;if("throw"===a.type){var i=a.arg;N(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,a){return this.delegate={iterator:T(t),resultName:n,nextLoc:a},"next"===this.method&&(this.arg=e),g}},t}function iM(e,t,n,a,i,r,o){try{var s=e[r](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(a,i)}function iL(e){return function(){var t=this,n=arguments;return new Promise(function(a,i){var r=e.apply(t,n);function o(e){iM(r,a,i,o,s,"next",e)}function s(e){iM(r,a,i,o,s,"throw",e)}o(void 0)})}}iO.ConfigContext=ey,iO.SizeContext=al,iO.config=e=>{let{prefixCls:t,iconPrefixCls:n,theme:s,holderRender:l}=e;if(void 0!==t&&(a=t),void 0!==n&&(i=n),"holderRender"in e&&(o=l),s)if(Object.keys(s).some(e=>e.endsWith("Color"))){let e=function(e,t){let n={},a=(e,t)=>{let n=e.clone();return(n=(null==t?void 0:t(n))||n).toRgbString()},i=(e,t)=>{let i=new nj(e),r=nT(i.toRgbString());n[`${t}-color`]=a(i),n[`${t}-color-disabled`]=r[1],n[`${t}-color-hover`]=r[4],n[`${t}-color-active`]=r[6],n[`${t}-color-outline`]=i.clone().setA(.2).toRgbString(),n[`${t}-color-deprecated-bg`]=r[0],n[`${t}-color-deprecated-border`]=r[2]};if(t.primaryColor){i(t.primaryColor,"primary");let e=new nj(t.primaryColor),r=nT(e.toRgbString());r.forEach((e,t)=>{n[`primary-${t+1}`]=e}),n["primary-color-deprecated-l-35"]=a(e,e=>e.lighten(35)),n["primary-color-deprecated-l-20"]=a(e,e=>e.lighten(20)),n["primary-color-deprecated-t-20"]=a(e,e=>e.tint(20)),n["primary-color-deprecated-t-50"]=a(e,e=>e.tint(50)),n["primary-color-deprecated-f-12"]=a(e,e=>e.setA(.12*e.a));let o=new nj(r[0]);n["primary-color-active-deprecated-f-30"]=a(o,e=>e.setA(.3*e.a)),n["primary-color-active-deprecated-d-02"]=a(o,e=>e.darken(2))}t.successColor&&i(t.successColor,"success"),t.warningColor&&i(t.warningColor,"warning"),t.errorColor&&i(t.errorColor,"error"),t.infoColor&&i(t.infoColor,"info");let r=Object.keys(n).map(t=>`--${e}-${t}: ${n[t]};`);return`
  :root {
    ${r.join("\n")}
  }
  `.trim()}(iT(),s);e_()&&eR(e,`${ar}-dynamic-theme`)}else r=s},iO.useConfig=function(){return{componentDisabled:(0,x.useContext)(ao),componentSize:(0,x.useContext)(al)}},Object.defineProperty(iO,"SizeContext",{get:()=>al});var iD=eA({},el),iF=iD.version,i$=iD.render,iR=iD.unmountComponentAtNode;try{Number((iF||"").split(".")[0])>=18&&(v=iD.createRoot)}catch(e){}function iZ(e){var t=iD.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;t&&"object"===ej(t)&&(t.usingClientEntryPoint=e)}var iU="__rc_react_root__";function iz(){return(iz=iL(iP().mark(function e(t){return iP().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",Promise.resolve().then(function(){var e;null==(e=t[iU])||e.unmount(),delete t[iU]}));case 1:case"end":return e.stop()}},e)}))).apply(this,arguments)}function iB(){return(iB=iL(iP().mark(function e(t){return iP().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0===v){e.next=2;break}return e.abrupt("return",function(e){return iz.apply(this,arguments)}(t));case 2:iR(t);case 3:case"end":return e.stop()}},e)}))).apply(this,arguments)}let iH=(e,t)=>(!function(e,t){var n;if(v)return iZ(!0),n=t[iU]||v(t),iZ(!1),n.render(e),t[iU]=n;null==i$||i$(e,t)}(e,t),()=>(function(e){return iB.apply(this,arguments)})(t)),iG={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"}}]},name:"check-circle",theme:"filled"};function iq(e){var t;return null==e||null==(t=e.getRootNode)?void 0:t.call(e)}function iK(e){return"object"===ej(e)&&"string"==typeof e.name&&"string"==typeof e.theme&&("object"===ej(e.icon)||"function"==typeof e.icon)}function iW(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(e).reduce(function(t,n){var a=e[n];return"class"===n?(t.className=a,delete t.class):(delete t[n],t[n.replace(/-(.)/g,function(e,t){return t.toUpperCase()})]=a),t},{})}function iV(e){return e?Array.isArray(e)?e:[e]:[]}var iX=function(e){var t=(0,x.useContext)(ni),n=t.csp,a=t.prefixCls,i=t.layer,r="\n.anticon {\n  display: inline-flex;\n  align-items: center;\n  color: inherit;\n  font-style: normal;\n  line-height: 0;\n  text-align: center;\n  text-transform: none;\n  vertical-align: -0.125em;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.anticon > * {\n  line-height: 1;\n}\n\n.anticon svg {\n  display: inline-block;\n}\n\n.anticon::before {\n  display: none;\n}\n\n.anticon .anticon-icon {\n  display: block;\n}\n\n.anticon[tabindex] {\n  cursor: pointer;\n}\n\n.anticon-spin::before,\n.anticon-spin {\n  display: inline-block;\n  -webkit-animation: loadingCircle 1s infinite linear;\n  animation: loadingCircle 1s infinite linear;\n}\n\n@-webkit-keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n";a&&(r=r.replace(/anticon/g,a)),i&&(r="@layer ".concat(i," {\n").concat(r,"\n}")),(0,x.useEffect)(function(){var t,a=iq(t=e.current)instanceof ShadowRoot?iq(t):null;eR(r,"@ant-design-icons",{prepend:!i,csp:n,attachTo:a})},[])},iJ=["icon","className","onClick","style","primaryColor","secondaryColor"],iY={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1},iQ=function(e){var t,n,a=e.icon,i=e.className,r=e.onClick,o=e.style,s=e.primaryColor,l=e.secondaryColor,c=eZ(e,iJ),u=x.useRef(),d=iY;if(s&&(d={primaryColor:s,secondaryColor:l||nT(s)[0]}),iX(u),t=iK(a),n="icon should be icon definiton, but got ".concat(a),eK(t,"[@ant-design/icons] ".concat(n)),!iK(a))return null;var p=a;return p&&"function"==typeof p.icon&&(p=eA(eA({},p),{},{icon:p.icon(d.primaryColor,d.secondaryColor)})),function e(t,n,a){return a?x.createElement(t.tag,eA(eA({key:n},iW(t.attrs)),a),(t.children||[]).map(function(a,i){return e(a,"".concat(n,"-").concat(t.tag,"-").concat(i))})):x.createElement(t.tag,eA({key:n},iW(t.attrs)),(t.children||[]).map(function(a,i){return e(a,"".concat(n,"-").concat(t.tag,"-").concat(i))}))}(p.icon,"svg-".concat(p.name),eA(eA({className:i,onClick:r,style:o,"data-icon":p.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},c),{},{ref:u}))};function i0(e){var t=ew(iV(e),2),n=t[0],a=t[1];return iQ.setTwoToneColors({primaryColor:n,secondaryColor:a})}iQ.displayName="IconReact",iQ.getTwoToneColors=function(){return eA({},iY)},iQ.setTwoToneColors=function(e){var t=e.primaryColor,n=e.secondaryColor;iY.primaryColor=t,iY.secondaryColor=n||nT(t)[0],iY.calculated=!!n};var i1=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];i0(n$.primary);var i2=x.forwardRef(function(e,t){var n=e.className,a=e.icon,i=e.spin,r=e.rotate,o=e.tabIndex,s=e.onClick,l=e.twoToneColor,c=eZ(e,i1),u=x.useContext(ni),d=u.prefixCls,p=void 0===d?"anticon":d,m=u.rootClassName,f=am()(m,p,eC(eC({},"".concat(p,"-").concat(a.name),!!a.name),"".concat(p,"-spin"),!!i||"loading"===a.name),n),g=o;void 0===g&&s&&(g=-1);var h=ew(iV(l),2),v=h[0],y=h[1];return x.createElement("span",tN({role:"img","aria-label":a.name},c,{ref:t,tabIndex:g,onClick:s,className:f}),x.createElement(iQ,{icon:a,primaryColor:v,secondaryColor:y,style:r?{msTransform:"rotate(".concat(r,"deg)"),transform:"rotate(".concat(r,"deg)")}:void 0}))});i2.displayName="AntdIcon",i2.getTwoToneColor=function(){var e=iQ.getTwoToneColors();return e.calculated?[e.primaryColor,e.secondaryColor]:e.primaryColor},i2.setTwoToneColor=i0;var i5=x.forwardRef(function(e,t){return x.createElement(i2,tN({},e,{ref:t,icon:iG}))});let i4={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"}}]},name:"close-circle",theme:"filled"};var i3=x.forwardRef(function(e,t){return x.createElement(i2,tN({},e,{ref:t,icon:i4}))});let i6={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"exclamation-circle",theme:"filled"};var i8=x.forwardRef(function(e,t){return x.createElement(i2,tN({},e,{ref:t,icon:i6}))});let i9={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"info-circle",theme:"filled"};var i7=x.forwardRef(function(e,t){return x.createElement(i2,tN({},e,{ref:t,icon:i9}))});let re={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"}}]},name:"loading",theme:"outlined"};var rt=x.forwardRef(function(e,t){return x.createElement(i2,tN({},e,{ref:t,icon:re}))}),rn={MAC_ENTER:3,BACKSPACE:8,TAB:9,NUM_CENTER:12,ENTER:13,SHIFT:16,CTRL:17,ALT:18,PAUSE:19,CAPS_LOCK:20,ESC:27,SPACE:32,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,LEFT:37,UP:38,RIGHT:39,DOWN:40,PRINT_SCREEN:44,INSERT:45,DELETE:46,ZERO:48,ONE:49,TWO:50,THREE:51,FOUR:52,FIVE:53,SIX:54,SEVEN:55,EIGHT:56,NINE:57,QUESTION_MARK:63,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,META:91,WIN_KEY_RIGHT:92,CONTEXT_MENU:93,NUM_ZERO:96,NUM_ONE:97,NUM_TWO:98,NUM_THREE:99,NUM_FOUR:100,NUM_FIVE:101,NUM_SIX:102,NUM_SEVEN:103,NUM_EIGHT:104,NUM_NINE:105,NUM_MULTIPLY:106,NUM_PLUS:107,NUM_MINUS:109,NUM_PERIOD:110,NUM_DIVISION:111,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,NUMLOCK:144,SEMICOLON:186,DASH:189,EQUALS:187,COMMA:188,PERIOD:190,SLASH:191,APOSTROPHE:192,SINGLE_QUOTE:222,OPEN_SQUARE_BRACKET:219,BACKSLASH:220,CLOSE_SQUARE_BRACKET:221,WIN_KEY:224,MAC_FF_META:224,WIN_IME:229,isTextModifyingKeyEvent:function(e){var t=e.keyCode;if(e.altKey&&!e.ctrlKey||e.metaKey||t>=rn.F1&&t<=rn.F12)return!1;switch(t){case rn.ALT:case rn.CAPS_LOCK:case rn.CONTEXT_MENU:case rn.CTRL:case rn.DOWN:case rn.END:case rn.ESC:case rn.HOME:case rn.INSERT:case rn.LEFT:case rn.MAC_FF_META:case rn.META:case rn.NUMLOCK:case rn.NUM_CENTER:case rn.PAGE_DOWN:case rn.PAGE_UP:case rn.PAUSE:case rn.PRINT_SCREEN:case rn.RIGHT:case rn.SHIFT:case rn.UP:case rn.WIN_KEY:case rn.WIN_KEY_RIGHT:return!1;default:return!0}},isCharacterKey:function(e){if(e>=rn.ZERO&&e<=rn.NINE||e>=rn.NUM_ZERO&&e<=rn.NUM_MULTIPLY||e>=rn.A&&e<=rn.Z||-1!==window.navigator.userAgent.indexOf("WebKit")&&0===e)return!0;switch(e){case rn.SPACE:case rn.QUESTION_MARK:case rn.NUM_PLUS:case rn.NUM_MINUS:case rn.NUM_PERIOD:case rn.NUM_DIVISION:case rn.SEMICOLON:case rn.DASH:case rn.EQUALS:case rn.COMMA:case rn.PERIOD:case rn.SLASH:case rn.APOSTROPHE:case rn.SINGLE_QUOTE:case rn.OPEN_SQUARE_BRACKET:case rn.BACKSLASH:case rn.CLOSE_SQUARE_BRACKET:return!0;default:return!1}}},ra="".concat("accept acceptCharset accessKey action allowFullScreen allowTransparency\n    alt async autoComplete autoFocus autoPlay capture cellPadding cellSpacing challenge\n    charSet checked classID className colSpan cols content contentEditable contextMenu\n    controls coords crossOrigin data dateTime default defer dir disabled download draggable\n    encType form formAction formEncType formMethod formNoValidate formTarget frameBorder\n    headers height hidden high href hrefLang htmlFor httpEquiv icon id inputMode integrity\n    is keyParams keyType kind label lang list loop low manifest marginHeight marginWidth max maxLength media\n    mediaGroup method min minLength multiple muted name noValidate nonce open\n    optimum pattern placeholder poster preload radioGroup readOnly rel required\n    reversed role rowSpan rows sandbox scope scoped scrolling seamless selected\n    shape size sizes span spellCheck src srcDoc srcLang srcSet start step style\n    summary tabIndex target title type useMap value width wmode wrap"," ").concat("onCopy onCut onPaste onCompositionEnd onCompositionStart onCompositionUpdate onKeyDown\n    onKeyPress onKeyUp onFocus onBlur onChange onInput onSubmit onClick onContextMenu onDoubleClick\n    onDrag onDragEnd onDragEnter onDragExit onDragLeave onDragOver onDragStart onDrop onMouseDown\n    onMouseEnter onMouseLeave onMouseMove onMouseOut onMouseOver onMouseUp onSelect onTouchCancel\n    onTouchEnd onTouchMove onTouchStart onScroll onWheel onAbort onCanPlay onCanPlayThrough\n    onDurationChange onEmptied onEncrypted onEnded onError onLoadedData onLoadedMetadata\n    onLoadStart onPause onPlay onPlaying onProgress onRateChange onSeeked onSeeking onStalled onSuspend onTimeUpdate onVolumeChange onWaiting onLoad onError").split(/[\s\n]+/);function ri(e,t){return 0===e.indexOf(t)}var rr=x.forwardRef(function(e,t){var n=e.prefixCls,a=e.style,i=e.className,r=e.duration,o=void 0===r?4.5:r,s=e.showProgress,l=e.pauseOnHover,c=void 0===l||l,u=e.eventKey,d=e.content,p=e.closable,m=e.closeIcon,f=void 0===m?"x":m,g=e.props,h=e.onClick,v=e.onNoticeClose,y=e.times,b=e.hovering,S=ew(x.useState(!1),2),w=S[0],j=S[1],k=ew(x.useState(0),2),C=k[0],N=k[1],A=ew(x.useState(0),2),T=A[0],_=A[1],E=b||w,I=o>0&&s,O=function(){v(u)};x.useEffect(function(){if(!E&&o>0){var e=Date.now()-T,t=setTimeout(function(){O()},1e3*o-T);return function(){c&&clearTimeout(t),_(Date.now()-e)}}},[o,E,y]),x.useEffect(function(){if(!E&&I&&(c||0===T)){var e,t=performance.now();return!function n(){cancelAnimationFrame(e),e=requestAnimationFrame(function(e){var a=Math.min((e+T-t)/(1e3*o),1);N(100*a),a<1&&n()})}(),function(){c&&cancelAnimationFrame(e)}}},[o,T,E,I,y]);var P=x.useMemo(function(){return"object"===ej(p)&&null!==p?p:p?{closeIcon:f}:{}},[p,f]),M=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t=!1===n?{aria:!0,data:!0,attr:!0}:!0===n?{aria:!0}:eA({},n);var a={};return Object.keys(e).forEach(function(n){(t.aria&&("role"===n||ri(n,"aria-"))||t.data&&ri(n,"data-")||t.attr&&ra.includes(n))&&(a[n]=e[n])}),a}(P,!0),L=100-(!C||C<0?0:C>100?100:C),D="".concat(n,"-notice");return x.createElement("div",tN({},g,{ref:t,className:am()(D,i,eC({},"".concat(D,"-closable"),p)),style:a,onMouseEnter:function(e){var t;j(!0),null==g||null==(t=g.onMouseEnter)||t.call(g,e)},onMouseLeave:function(e){var t;j(!1),null==g||null==(t=g.onMouseLeave)||t.call(g,e)},onClick:h}),x.createElement("div",{className:"".concat(D,"-content")},d),p&&x.createElement("a",tN({tabIndex:0,className:"".concat(D,"-close"),onKeyDown:function(e){("Enter"===e.key||"Enter"===e.code||e.keyCode===rn.ENTER)&&O()},"aria-label":"Close"},M,{onClick:function(e){e.preventDefault(),e.stopPropagation(),O()}}),P.closeIcon),I&&x.createElement("progress",{className:"".concat(D,"-progress"),max:"100",value:L},L+"%"))}),ro=x.createContext({});let rs=function(e){var t=e.children,n=e.classNames;return x.createElement(ro.Provider,{value:{classNames:n}},t)},rl=function(e){var t,n,a,i={offset:8,threshold:3,gap:16};return e&&"object"===ej(e)&&(i.offset=null!=(t=e.offset)?t:8,i.threshold=null!=(n=e.threshold)?n:3,i.gap=null!=(a=e.gap)?a:16),[!!e,i]};var rc=["className","style","classNames","styles"];let ru=function(e){var t=e.configList,n=e.placement,a=e.prefixCls,i=e.className,r=e.style,o=e.motion,s=e.onAllNoticeRemoved,l=e.onNoticeClose,c=e.stack,u=(0,x.useContext)(ro).classNames,d=(0,x.useRef)({}),p=ew((0,x.useState)(null),2),m=p[0],f=p[1],g=ew((0,x.useState)([]),2),h=g[0],v=g[1],y=t.map(function(e){return{config:e,key:String(e.key)}}),b=ew(rl(c),2),S=b[0],w=b[1],j=w.offset,k=w.threshold,C=w.gap,N=S&&(h.length>0||y.length<=k),A="function"==typeof o?o(n):o;return(0,x.useEffect)(function(){S&&h.length>1&&v(function(e){return e.filter(function(e){return y.some(function(t){return e===t.key})})})},[h,y,S]),(0,x.useEffect)(function(){var e,t;S&&d.current[null==(e=y[y.length-1])?void 0:e.key]&&f(d.current[null==(t=y[y.length-1])?void 0:t.key])},[y,S]),x.createElement(is,tN({key:n,className:am()(a,"".concat(a,"-").concat(n),null==u?void 0:u.list,i,eC(eC({},"".concat(a,"-stack"),!!S),"".concat(a,"-stack-expanded"),N)),style:r,keys:y,motionAppear:!0},A,{onAllRemoved:function(){s(n)}}),function(e,t){var i=e.config,r=e.className,o=e.style,s=e.index,c=i.key,p=i.times,f=String(c),g=i.className,b=i.style,w=i.classNames,k=i.styles,A=eZ(i,rc),T=y.findIndex(function(e){return e.key===f}),_={};if(S){var E=y.length-1-(T>-1?T:s-1),I="top"===n||"bottom"===n?"-50%":"0";if(E>0){_.height=N?null==(O=d.current[f])?void 0:O.offsetHeight:null==m?void 0:m.offsetHeight;for(var O,P,M,L,D=0,F=0;F<E;F++)D+=(null==(L=d.current[y[y.length-1-F].key])?void 0:L.offsetHeight)+C;var $=(N?D:E*j)*(n.startsWith("top")?1:-1),R=!N&&null!=m&&m.offsetWidth&&null!=(P=d.current[f])&&P.offsetWidth?((null==m?void 0:m.offsetWidth)-2*j*(E<3?E:3))/(null==(M=d.current[f])?void 0:M.offsetWidth):1;_.transform="translate3d(".concat(I,", ").concat($,"px, 0) scaleX(").concat(R,")")}else _.transform="translate3d(".concat(I,", 0, 0)")}return x.createElement("div",{ref:t,className:am()("".concat(a,"-notice-wrapper"),r,null==w?void 0:w.wrapper),style:eA(eA(eA({},o),_),null==k?void 0:k.wrapper),onMouseEnter:function(){return v(function(e){return e.includes(f)?e:[].concat(eg(e),[f])})},onMouseLeave:function(){return v(function(e){return e.filter(function(e){return e!==f})})}},x.createElement(rr,tN({},A,{ref:function(e){T>-1?d.current[f]=e:delete d.current[f]},prefixCls:a,classNames:w,styles:k,className:am()(g,null==u?void 0:u.notice),style:b,times:p,key:c,eventKey:c,onNoticeClose:l,hovering:S&&h.length>0})))})};var rd=x.forwardRef(function(e,t){var n=e.prefixCls,a=void 0===n?"rc-notification":n,i=e.container,r=e.motion,o=e.maxCount,s=e.className,l=e.style,c=e.onAllRemoved,u=e.stack,d=e.renderNotifications,p=ew(x.useState([]),2),m=p[0],f=p[1],g=function(e){var t,n=m.find(function(t){return t.key===e});null==n||null==(t=n.onClose)||t.call(n),f(function(t){return t.filter(function(t){return t.key!==e})})};x.useImperativeHandle(t,function(){return{open:function(e){f(function(t){var n,a=eg(t),i=a.findIndex(function(t){return t.key===e.key}),r=eA({},e);return i>=0?(r.times=((null==(n=t[i])?void 0:n.times)||0)+1,a[i]=r):(r.times=0,a.push(r)),o>0&&a.length>o&&(a=a.slice(-o)),a})},close:function(e){g(e)},destroy:function(){f([])}}});var h=ew(x.useState({}),2),v=h[0],y=h[1];x.useEffect(function(){var e={};m.forEach(function(t){var n=t.placement,a=void 0===n?"topRight":n;a&&(e[a]=e[a]||[],e[a].push(t))}),Object.keys(v).forEach(function(t){e[t]=e[t]||[]}),y(e)},[m]);var b=function(e){y(function(t){var n=eA({},t);return(n[e]||[]).length||delete n[e],n})},S=x.useRef(!1);if(x.useEffect(function(){Object.keys(v).length>0?S.current=!0:S.current&&(null==c||c(),S.current=!1)},[v]),!i)return null;var w=Object.keys(v);return(0,es.createPortal)(x.createElement(x.Fragment,null,w.map(function(e){var t=v[e],n=x.createElement(ru,{key:e,configList:t,placement:e,prefixCls:a,className:null==s?void 0:s(e),style:null==l?void 0:l(e),motion:r,onNoticeClose:g,onAllNoticeRemoved:b,stack:u});return d?d(n,{prefixCls:a,key:e}):n})),i)}),rp=["getContainer","motion","prefixCls","maxCount","className","style","onAllRemoved","stack","renderNotifications"],rm=function(){return document.body},rf=0;let rg=e=>{let[,,,,t]=iy();return t?`${e}-css-var`:""};var rh=eJ(function e(){eV(this,e)}),rv="CALC_UNIT",ry=RegExp(rv,"g");function rb(e){return"number"==typeof e?"".concat(e).concat(rv):e}var rx=function(e){e6(n,e);var t=e7(n);function n(e,a){eV(this,n),eC(e4(i=t.call(this)),"result",""),eC(e4(i),"unitlessCssVar",void 0),eC(e4(i),"lowPriority",void 0);var i,r=ej(e);return i.unitlessCssVar=a,e instanceof n?i.result="(".concat(e.result,")"):"number"===r?i.result=rb(e):"string"===r&&(i.result=e),i}return eJ(n,[{key:"add",value:function(e){return e instanceof n?this.result="".concat(this.result," + ").concat(e.getResult()):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," + ").concat(rb(e))),this.lowPriority=!0,this}},{key:"sub",value:function(e){return e instanceof n?this.result="".concat(this.result," - ").concat(e.getResult()):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," - ").concat(rb(e))),this.lowPriority=!0,this}},{key:"mul",value:function(e){return this.lowPriority&&(this.result="(".concat(this.result,")")),e instanceof n?this.result="".concat(this.result," * ").concat(e.getResult(!0)):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," * ").concat(e)),this.lowPriority=!1,this}},{key:"div",value:function(e){return this.lowPriority&&(this.result="(".concat(this.result,")")),e instanceof n?this.result="".concat(this.result," / ").concat(e.getResult(!0)):("number"==typeof e||"string"==typeof e)&&(this.result="".concat(this.result," / ").concat(e)),this.lowPriority=!1,this}},{key:"getResult",value:function(e){return this.lowPriority||e?"(".concat(this.result,")"):this.result}},{key:"equal",value:function(e){var t=this,n=(e||{}).unit,a=!0;return("boolean"==typeof n?a=n:Array.from(this.unitlessCssVar).some(function(e){return t.result.includes(e)})&&(a=!1),this.result=this.result.replace(ry,a?"px":""),void 0!==this.lowPriority)?"calc(".concat(this.result,")"):this.result}}]),n}(rh),rS=function(e){e6(n,e);var t=e7(n);function n(e){var a;return eV(this,n),eC(e4(a=t.call(this)),"result",0),e instanceof n?a.result=e.result:"number"==typeof e&&(a.result=e),a}return eJ(n,[{key:"add",value:function(e){return e instanceof n?this.result+=e.result:"number"==typeof e&&(this.result+=e),this}},{key:"sub",value:function(e){return e instanceof n?this.result-=e.result:"number"==typeof e&&(this.result-=e),this}},{key:"mul",value:function(e){return e instanceof n?this.result*=e.result:"number"==typeof e&&(this.result*=e),this}},{key:"div",value:function(e){return e instanceof n?this.result/=e.result:"number"==typeof e&&(this.result/=e),this}},{key:"equal",value:function(){return this.result}}]),n}(rh);let rw=function(e,t){var n="css"===e?rx:rS;return function(e){return new n(e,t)}},rj=function(e,t){return"".concat([t,e.replace(/([A-Z]+)([A-Z][a-z]+)/g,"$1-$2").replace(/([a-z])([A-Z])/g,"$1-$2")].filter(Boolean).join("-"))},rk=function(e,t,n,a){var i=eA({},t[e]);null!=a&&a.deprecatedTokens&&a.deprecatedTokens.forEach(function(e){var t=ew(e,2),n=t[0],a=t[1];(null!=i&&i[n]||null!=i&&i[a])&&(null!=i[a]||(i[a]=null==i?void 0:i[n]))});var r=eA(eA({},n),i);return Object.keys(r).forEach(function(e){r[e]===t[e]&&delete r[e]}),r};var rC="undefined"!=typeof CSSINJS_STATISTIC,rN=!0;function rA(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(!rC)return Object.assign.apply(Object,[{}].concat(t));rN=!1;var a={};return t.forEach(function(e){"object"===ej(e)&&Object.keys(e).forEach(function(t){Object.defineProperty(a,t,{configurable:!0,enumerable:!0,get:function(){return e[t]}})})}),rN=!0,a}var rT={};function r_(){}let rE=function(e){var t,n=e,a=r_;return rC&&"undefined"!=typeof Proxy&&(t=new Set,n=new Proxy(e,{get:function(e,n){if(rN){var a;null==(a=t)||a.add(n)}return e[n]}}),a=function(e,n){var a;rT[e]={global:Array.from(t),component:eA(eA({},null==(a=rT[e])?void 0:a.component),n)}}),{token:n,keys:t,flush:a}},rI=function(e,t,n){if("function"==typeof n){var a;return n(rA(t,null!=(a=t[e])?a:{}))}return null!=n?n:{}};var rO=new(function(){function e(){eV(this,e),eC(this,"map",new Map),eC(this,"objectIDMap",new WeakMap),eC(this,"nextID",0),eC(this,"lastAccessBeat",new Map),eC(this,"accessBeat",0)}return eJ(e,[{key:"set",value:function(e,t){this.clear();var n=this.getCompositeKey(e);this.map.set(n,t),this.lastAccessBeat.set(n,Date.now())}},{key:"get",value:function(e){var t=this.getCompositeKey(e),n=this.map.get(t);return this.lastAccessBeat.set(t,Date.now()),this.accessBeat+=1,n}},{key:"getCompositeKey",value:function(e){var t=this;return e.map(function(e){return e&&"object"===ej(e)?"obj_".concat(t.getObjectID(e)):"".concat(ej(e),"_").concat(e)}).join("|")}},{key:"getObjectID",value:function(e){if(this.objectIDMap.has(e))return this.objectIDMap.get(e);var t=this.nextID;return this.objectIDMap.set(e,t),this.nextID+=1,t}},{key:"clear",value:function(){var e=this;if(this.accessBeat>1e4){var t=Date.now();this.lastAccessBeat.forEach(function(n,a){t-n>6e5&&(e.map.delete(a),e.lastAccessBeat.delete(a))}),this.accessBeat=0}}}]),e}());let rP=function(){return{}},{genStyleHooks:rM,genComponentStyleHook:rL,genSubStyleComponent:rD}=function(e){var t=e.useCSP,n=void 0===t?rP:t,a=e.useToken,i=e.usePrefix,r=e.getResetStyles,o=e.getCommonStyle,s=e.getCompUnitless;function l(t,s,l){var c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},u=Array.isArray(t)?t:[t,t],d=ew(u,1)[0],p=u.join("-"),m=e.layer||{name:"antd"};return function(e){var t,u,f=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,g=a(),h=g.theme,v=g.realToken,y=g.hashId,b=g.token,S=g.cssVar,w=i(),j=w.rootPrefixCls,k=w.iconPrefixCls,C=n(),N=S?"css":"js",A=(t=function(){var e=new Set;return S&&Object.keys(c.unitless||{}).forEach(function(t){e.add(tm(t,S.prefix)),e.add(tm(t,rj(d,S.prefix)))}),rw(N,e)},u=[N,d,null==S?void 0:S.prefix],x.useMemo(function(){var e=rO.get(u);if(e)return e;var n=t();return rO.set(u,n),n},u)),T="js"===N?{max:Math.max,min:Math.min}:{max:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return"max(".concat(t.map(function(e){return td(e)}).join(","),")")},min:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return"min(".concat(t.map(function(e){return td(e)}).join(","),")")}},_=T.max,E=T.min,I={theme:h,token:b,hashId:y,nonce:function(){return C.nonce},clientOnly:c.clientOnly,layer:m,order:c.order||-999};return"function"==typeof r&&t7(eA(eA({},I),{},{clientOnly:!1,path:["Shared",j]}),function(){return r(b,{prefix:{rootPrefixCls:j,iconPrefixCls:k},csp:C})}),[t7(eA(eA({},I),{},{path:[p,e,k]}),function(){if(!1===c.injectStyle)return[];var t=rE(b),n=t.token,a=t.flush,i=rI(d,v,l),r=".".concat(e),u=rk(d,v,i,{deprecatedTokens:c.deprecatedTokens});S&&i&&"object"===ej(i)&&Object.keys(i).forEach(function(e){i[e]="var(".concat(tm(e,rj(d,S.prefix)),")")});var p=rA(n,{componentCls:r,prefixCls:e,iconCls:".".concat(k),antCls:".".concat(j),calc:A,max:_,min:E},S?i:u),m=s(p,{hashId:y,prefixCls:e,rootPrefixCls:j,iconPrefixCls:k});a(d,u);var g="function"==typeof o?o(p,e,f,c.resetFont):null;return[!1===c.resetStyle?null:g,m]}),y]}}return{genStyleHooks:function(e,t,n,i){var r,o,c,u,d,p,m,f,g,h=Array.isArray(e)?e[0]:e;function v(e){return"".concat(String(h)).concat(e.slice(0,1).toUpperCase()).concat(e.slice(1))}var y=(null==i?void 0:i.unitless)||{},b=eA(eA({},"function"==typeof s?s(e):{}),{},eC({},v("zIndexPopup"),!0));Object.keys(y).forEach(function(e){b[v(e)]=y[e]});var S=eA(eA({},i),{},{unitless:b,prefixToken:v}),w=l(e,t,n,S),j=(r=h,o=n,u=(c=S).unitless,p=void 0===(d=c.injectStyle)||d,m=c.prefixToken,f=c.ignore,g=function(e){var t=e.rootCls,n=e.cssVar,i=void 0===n?{}:n,s=a().realToken;return nt({path:[r],prefix:i.prefix,key:i.key,unitless:u,ignore:f,token:s,scope:t},function(){var e=rI(r,s,o),t=rk(r,s,e,{deprecatedTokens:null==c?void 0:c.deprecatedTokens});return Object.keys(e).forEach(function(e){t[m(e)]=t[e],delete t[e]}),t}),null},function(e){var t=a().cssVar;return[function(n){return p&&t?x.createElement(x.Fragment,null,x.createElement(g,{rootCls:e,cssVar:t,component:r}),n):n},null==t?void 0:t.key]});return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=ew(w(e,t),2)[1],a=ew(j(t),2);return[a[0],n,a[1]]}},genSubStyleComponent:function(e,t,n){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=l(e,t,n,eA({resetStyle:!1,order:-998},a));return function(e){var t=e.prefixCls,n=e.rootCls,a=void 0===n?t:n;return i(t,a),null}},genComponentStyleHook:l}}({usePrefix:()=>{let{getPrefixCls:e,iconPrefixCls:t}=(0,x.useContext)(ey);return{rootPrefixCls:e(),iconPrefixCls:t}},useToken:()=>{let[e,t,n,a,i]=iy();return{theme:e,realToken:t,hashId:n,token:a,cssVar:i}},useCSP:()=>{let{csp:e}=(0,x.useContext)(ey);return null!=e?e:{}},getResetStyles:(e,t)=>{var n;let a=ij(e);return[a,{"&":a},ik(null!=(n=null==t?void 0:t.prefix.iconPrefixCls)?n:ev)]},getCommonStyle:(e,t,n,a)=>{let i=`[class^="${t}"], [class*=" ${t}"]`,r=n?`.${n}`:i,o={boxSizing:"border-box","&::before, &::after":{boxSizing:"border-box"}},s={};return!1!==a&&(s={fontFamily:e.fontFamily,fontSize:e.fontSize}),{[r]:Object.assign(Object.assign(Object.assign({},s),o),{[i]:o})}},getCompUnitless:()=>im}),rF=e=>{let{componentCls:t,iconCls:n,boxShadow:a,colorText:i,colorSuccess:r,colorError:o,colorWarning:s,colorInfo:l,fontSizeLG:c,motionEaseInOutCirc:u,motionDurationSlow:d,marginXS:p,paddingXS:m,borderRadiusLG:f,zIndexPopup:g,contentPadding:h,contentBg:v}=e,y=`${t}-notice`,b=new nn("MessageMoveIn",{"0%":{padding:0,transform:"translateY(-100%)",opacity:0},"100%":{padding:m,transform:"translateY(0)",opacity:1}}),x=new nn("MessageMoveOut",{"0%":{maxHeight:e.height,padding:m,opacity:1},"100%":{maxHeight:0,padding:0,opacity:0}}),S={padding:m,textAlign:"center",[`${t}-custom-content`]:{display:"flex",alignItems:"center"},[`${t}-custom-content > ${n}`]:{marginInlineEnd:p,fontSize:c},[`${y}-content`]:{display:"inline-block",padding:h,background:v,borderRadius:f,boxShadow:a,pointerEvents:"all"},[`${t}-success > ${n}`]:{color:r},[`${t}-error > ${n}`]:{color:o},[`${t}-warning > ${n}`]:{color:s},[`${t}-info > ${n},
      ${t}-loading > ${n}`]:{color:l}};return[{[t]:Object.assign(Object.assign({},iS(e)),{color:i,position:"fixed",top:p,width:"100%",pointerEvents:"none",zIndex:g,[`${t}-move-up`]:{animationFillMode:"forwards"},[`
        ${t}-move-up-appear,
        ${t}-move-up-enter
      `]:{animationName:b,animationDuration:d,animationPlayState:"paused",animationTimingFunction:u},[`
        ${t}-move-up-appear${t}-move-up-appear-active,
        ${t}-move-up-enter${t}-move-up-enter-active
      `]:{animationPlayState:"running"},[`${t}-move-up-leave`]:{animationName:x,animationDuration:d,animationPlayState:"paused",animationTimingFunction:u},[`${t}-move-up-leave${t}-move-up-leave-active`]:{animationPlayState:"running"},"&-rtl":{direction:"rtl",span:{direction:"rtl"}}})},{[t]:{[`${y}-wrapper`]:Object.assign({},S)}},{[`${t}-notice-pure-panel`]:Object.assign(Object.assign({},S),{padding:0,textAlign:"start"})}]},r$=rM("Message",e=>[rF(rA(e,{height:150}))],e=>({zIndexPopup:e.zIndexPopupBase+1e3+10,contentBg:e.colorBgElevated,contentPadding:`${(e.controlHeightLG-e.fontSize*e.lineHeight)/2}px ${e.paddingSM}px`}));var rR=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,a=Object.getOwnPropertySymbols(e);i<a.length;i++)0>t.indexOf(a[i])&&Object.prototype.propertyIsEnumerable.call(e,a[i])&&(n[a[i]]=e[a[i]]);return n};let rZ={info:x.createElement(i7,null),success:x.createElement(i5,null),error:x.createElement(i3,null),warning:x.createElement(i8,null),loading:x.createElement(rt,null)},rU=e=>{let{prefixCls:t,type:n,icon:a,children:i}=e;return x.createElement("div",{className:am()(`${t}-custom-content`,`${t}-${n}`)},a||rZ[n],x.createElement("span",null,i))},rz={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"}}]},name:"close",theme:"outlined"};var rB=x.forwardRef(function(e,t){return x.createElement(i2,tN({},e,{ref:t,icon:rz}))});function rH(e){let t,n=new Promise(n=>{t=e(()=>{n(!0)})}),a=()=>{null==t||t()};return a.then=(e,t)=>n.then(e,t),a.promise=n,a}var rG=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,a=Object.getOwnPropertySymbols(e);i<a.length;i++)0>t.indexOf(a[i])&&Object.prototype.propertyIsEnumerable.call(e,a[i])&&(n[a[i]]=e[a[i]]);return n};let rq=e=>{let{children:t,prefixCls:n}=e,a=rg(n),[i,r,o]=r$(n,a);return i(x.createElement(rs,{classNames:{list:am()(r,o,a)}},t))},rK=(e,t)=>{let{prefixCls:n,key:a}=t;return x.createElement(rq,{prefixCls:n,key:a},e)},rW=x.forwardRef((e,t)=>{let{top:n,prefixCls:a,getContainer:i,maxCount:r,duration:o=3,rtl:s,transitionName:l,onAllRemoved:c}=e,{getPrefixCls:u,getPopupContainer:d,message:p,direction:m}=x.useContext(ey),f=a||u("message"),g=x.createElement("span",{className:`${f}-close-x`},x.createElement(rB,{className:`${f}-close-icon`})),[h,v]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.getContainer,n=void 0===t?rm:t,a=e.motion,i=e.prefixCls,r=e.maxCount,o=e.className,s=e.style,l=e.onAllRemoved,c=e.stack,u=e.renderNotifications,d=eZ(e,rp),p=ew(x.useState(),2),m=p[0],f=p[1],g=x.useRef(),h=x.createElement(rd,{container:m,ref:g,prefixCls:i,motion:a,maxCount:r,className:o,style:s,onAllRemoved:l,stack:c,renderNotifications:u}),v=ew(x.useState([]),2),y=v[0],b=v[1],S=aA(function(e){var t=function(){for(var e={},t=arguments.length,n=Array(t),a=0;a<t;a++)n[a]=arguments[a];return n.forEach(function(t){t&&Object.keys(t).forEach(function(n){var a=t[n];void 0!==a&&(e[n]=a)})}),e}(d,e);(null===t.key||void 0===t.key)&&(t.key="rc-notification-".concat(rf),rf+=1),b(function(e){return[].concat(eg(e),[{type:"open",config:t}])})}),w=x.useMemo(function(){return{open:S,close:function(e){b(function(t){return[].concat(eg(t),[{type:"close",key:e}])})},destroy:function(){b(function(e){return[].concat(eg(e),[{type:"destroy"}])})}}},[]);return x.useEffect(function(){f(n())}),x.useEffect(function(){if(g.current&&y.length){var e,t;y.forEach(function(e){switch(e.type){case"open":g.current.open(e.config);break;case"close":g.current.close(e.key);break;case"destroy":g.current.destroy()}}),b(function(n){return e===n&&t||(e=n,t=n.filter(function(e){return!y.includes(e)})),t})}},[y]),[w,h]}({prefixCls:f,style:()=>({left:"50%",transform:"translateX(-50%)",top:null!=n?n:8}),className:()=>am()({[`${f}-rtl`]:null!=s?s:"rtl"===m}),motion:()=>({motionName:null!=l?l:`${f}-move-up`}),closable:!1,closeIcon:g,duration:o,getContainer:()=>(null==i?void 0:i())||(null==d?void 0:d())||document.body,maxCount:r,onAllRemoved:c,renderNotifications:rK});return x.useImperativeHandle(t,()=>Object.assign(Object.assign({},h),{prefixCls:f,message:p})),v}),rV=0;function rX(e){let t=x.useRef(null);return nd("Message"),[x.useMemo(()=>{let e=e=>{var n;null==(n=t.current)||n.close(e)},n=n=>{if(!t.current){let e=()=>{};return e.then=()=>{},e}let{open:a,prefixCls:i,message:r}=t.current,o=`${i}-notice`,{content:s,icon:l,type:c,key:u,className:d,style:p,onClose:m}=n,f=rG(n,["content","icon","type","key","className","style","onClose"]),g=u;return null==g&&(rV+=1,g=`antd-message-${rV}`),rH(t=>(a(Object.assign(Object.assign({},f),{key:g,content:x.createElement(rU,{prefixCls:i,type:c,icon:l},s),placement:"top",className:am()(c&&`${o}-${c}`,d,null==r?void 0:r.className),style:Object.assign(Object.assign({},null==r?void 0:r.style),p),onClose:()=>{null==m||m(),t()}})),()=>{e(g)}))},a={open:n,destroy:n=>{var a;void 0!==n?e(n):null==(a=t.current)||a.destroy()}};return["info","success","warning","error","loading"].forEach(e=>{a[e]=(t,a,i)=>{let r,o;return"function"==typeof a?o=a:(r=a,o=i),n(Object.assign(Object.assign({onClose:o,duration:r},t&&"object"==typeof t&&"content"in t?t:{content:t}),{type:e}))}}),a},[]),x.createElement(rW,Object.assign({key:"message-holder"},e,{ref:t}))]}let rJ=null,rY=e=>e(),rQ=[],r0={};function r1(){let{getContainer:e,duration:t,rtl:n,maxCount:a,top:i}=r0,r=(null==e?void 0:e())||document.body;return{getContainer:()=>r,duration:t,rtl:n,maxCount:a,top:i}}let r2=x.forwardRef((e,t)=>{let{messageConfig:n,sync:a}=e,{getPrefixCls:i}=(0,x.useContext)(ey),r=r0.prefixCls||i("message"),o=(0,x.useContext)(eh),[s,l]=rX(Object.assign(Object.assign(Object.assign({},n),{prefixCls:r}),o.message));return x.useImperativeHandle(t,()=>{let e=Object.assign({},s);return Object.keys(e).forEach(t=>{e[t]=function(){return a(),s[t].apply(s,arguments)}}),{instance:e,sync:a}}),l}),r5=x.forwardRef((e,t)=>{let[n,a]=x.useState(r1),i=()=>{a(r1)};x.useEffect(i,[]);let r=iE(),o=r.getRootPrefixCls(),s=r.getIconPrefixCls(),l=r.getTheme(),c=x.createElement(r2,{ref:t,sync:i,messageConfig:n});return x.createElement(iO,{prefixCls:o,iconPrefixCls:s,theme:l},r.holderRender?r.holderRender(c):c)});function r4(){if(!rJ){let e=document.createDocumentFragment(),t={fragment:e};rJ=t,rY(()=>{iH(x.createElement(r5,{ref:e=>{let{instance:n,sync:a}=e||{};Promise.resolve().then(()=>{!t.instance&&n&&(t.instance=n,t.sync=a,r4())})}}),e)});return}rJ.instance&&(rQ.forEach(e=>{let{type:t,skipped:n}=e;if(!n)switch(t){case"open":rY(()=>{let t=rJ.instance.open(Object.assign(Object.assign({},r0),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":rY(()=>{null==rJ||rJ.instance.destroy(e.key)});break;default:rY(()=>{var n;let a=(n=rJ.instance)[t].apply(n,eg(e.args));null==a||a.then(e.resolve),e.setCloseFn(a)})}}),rQ=[])}let r3={open:function(e){let t=rH(t=>{let n,a={type:"open",config:e,resolve:t,setCloseFn:e=>{n=e}};return rQ.push(a),()=>{n?rY(()=>{n()}):a.skipped=!0}});return r4(),t},destroy:e=>{rQ.push({type:"destroy",key:e}),r4()},config:function(e){r0=Object.assign(Object.assign({},r0),e),rY(()=>{var e;null==(e=null==rJ?void 0:rJ.sync)||e.call(rJ)})},useMessage:function(e){return rX(e)},_InternalPanelDoNotUseOrYouWillBeFired:e=>{let{prefixCls:t,className:n,type:a,icon:i,content:r}=e,o=rR(e,["prefixCls","className","type","icon","content"]),{getPrefixCls:s}=x.useContext(ey),l=t||s("message"),c=rg(l),[u,d,p]=r$(l,c);return u(x.createElement(rr,Object.assign({},o,{prefixCls:l,className:am()(n,d,`${l}-notice-pure-panel`,p,c),eventKey:"pure",duration:null,content:x.createElement(rU,{prefixCls:l,type:a,icon:i},r)})))}};["success","info","warning","error","loading"].forEach(e=>{r3[e]=function(){for(var t=arguments.length,n=Array(t),a=0;a<t;a++)n[a]=arguments[a];iE();let i=rH(t=>{let a,i={type:e,args:n,resolve:t,setCloseFn:e=>{a=e}};return rQ.push(i),()=>{a?rY(()=>{a()}):i.skipped=!0}});return r4(),i}});class r6 extends Error{constructor(e){super("Unauthorized"),this.name="UnauthorizedError",this.message=e}}let r8=class{async baseRequest(e,t){let n=await fetch(`${this.options.baseURL}${e}`,{...t,headers:{...t.headers,Authorization:`Bearer ${this.options.apiKey}`,Accept:"text/event-stream, application/json"}});if(n.headers.get("X-Version")){let e=n.headers.get("X-Version");e&&e!==M.version&&(M.version=e)}if(401===n.status)throw r3.error("未授权, 请检查你的配置"),new r6("Unauthorized");return n}async jsonRequest(e,t){let n=await this.baseRequest(e,{...t,headers:{...t.headers,"Content-Type":"application/json"}}),a=await n.json();return 0===a.code?a.data:401==a.code?a:void r3.error(a.msg)}async get(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=t?`?${new URLSearchParams(t).toString()}`:"";return await this.jsonRequest(`${e}${a}`,{method:"GET",headers:n})}async post(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"POST",body:JSON.stringify(t),headers:n})}async delete(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return await this.jsonRequest(e,{method:"DELETE",body:JSON.stringify(t),headers:n})}constructor(e){var t,n;n=void 0,(t="options")in this?Object.defineProperty(this,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):this[t]=n,this.options=e}};function r9(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class r7{updateOptions(e){this.options=e,this.baseRequest=new r8({baseURL:e.apiBase,apiKey:e.apiKey,appId:e.appId})}async getAppInfo(){return this.baseRequest.get("/info")}async getAppMeta(){return this.baseRequest.get("/meta")}getConversationList(e){return this.baseRequest.get("/conversations",{user:this.options.user,limit:((null==e?void 0:e.limit)||100).toString(),appId:this.options.appId})}sendMessage(e){return e.appId=this.options.appId,this.baseRequest.baseRequest("/chat-messages",{method:"POST",body:JSON.stringify(e),headers:{"Content-Type":"application/json"}})}async stopTask(e){return this.baseRequest.post(`/chat-messages/${e}/stop`,{user:this.options.user,appId:this.options.appId})}async uploadFile(e){let t=new FormData;return t.append("file",e),t.append("user",this.options.user),t.append("appId",this.options.appId),this.baseRequest.baseRequest("/files/upload",{method:"POST",body:t}).then(e=>e.json())}async getNextSuggestions(e){return this.baseRequest.get(`/messages/${e.message_id}/suggested`,{appId:this.options.appId,user:this.options.user})}feedbackMessage(e){let{messageId:t,...n}=e;return this.baseRequest.post(`/messages/${t}/feedbacks`,{...n,user:this.options.user,appId:this.options.appId})}async text2Audio(e){return this.baseRequest.baseRequest("/text-to-audio",{method:"POST",body:JSON.stringify({...e,user:this.options.user}),headers:{"Content-Type":"application/json"}})}async audio2Text(e){let t=new FormData;return t.append("file",e),t.append("user",this.options.user),this.baseRequest.baseRequest("/audio-to-text",{method:"POST",body:t}).then(e=>e.json())}constructor(e){r9(this,"options",void 0),r9(this,"baseRequest",void 0),r9(this,"getAppParameters",()=>this.baseRequest.get("/parameters",{appId:this.options.appId})),r9(this,"renameConversation",e=>{let{conversation_id:t,...n}=e;return this.baseRequest.post(`/conversations/${t}/name`,{...n,user:this.options.user,appId:this.options.appId})}),r9(this,"deleteConversation",(e,t)=>this.baseRequest.delete(`/conversations/${e}?appId=${t}`,{user:this.options.user})),r9(this,"getConversationHistory",e=>this.baseRequest.get("/messages",{user:this.options.user,conversation_id:e,appId:this.options.appId})),this.options=e,this.baseRequest=new r8({baseURL:e.apiBase,apiKey:e.apiKey,appId:e.appId})}}let oe=e=>new r7(e);var ot=((m={}).MESSAGE="message",m.AGENT_MESSAGE="agent_message",m.AGENT_THOUGHT="agent_thought",m.MESSAGE_FILE="message_file",m.MESSAGE_END="message_end",m.TTS_MESSAGE="tts_message",m.TTS_MESSAGE_END="tts_message_end",m.MESSAGE_REPLACE="message_replace",m.ERROR="error",m.PING="ping",m.WORKFLOW_STARTED="workflow_started",m.WORKFLOW_FINISHED="workflow_finished",m.WORKFLOW_NODE_STARTED="node_started",m.WORKFLOW_NODE_FINISHED="node_finished",m),on=n(45186),oa=n(62472),oi=n(63522),or=n(44312),oo=n(79016),os=n(707),ol=n(83882),oc=n(17148),ou=n(55879),od=n(87092);function op(e){let{formInstance:t}=e,n=on.Z.useWatch("answerForm.enabled",t);return(0,y.jsxs)(on.Z,{autoComplete:"off",form:t,labelAlign:"left",labelCol:{span:5},initialValues:{"answerForm.enabled":!1},children:[(0,y.jsxs)("div",{className:"text-base mb-3 flex items-center",children:[(0,y.jsx)("div",{className:"h-4 w-1 bg-primary rounded"}),(0,y.jsx)("div",{className:"ml-2 font-semibold",children:"请求配置"})]}),(0,y.jsx)(on.Z.Item,{label:"API Base",name:"apiBase",rules:[{required:!0}],tooltip:"Dify API 的域名+版本号前缀，如 https://api.dify.ai/v1",required:!0,children:(0,y.jsx)(oc.Z,{autoComplete:"new-password",placeholder:"请输入 API BASE"})}),(0,y.jsx)(on.Z.Item,{label:"API Secret",name:"apiKey",tooltip:"Dify App 的 API Secret (以 app- 开头)",rules:[{required:!0}],required:!0,children:(0,y.jsx)(oc.Z.Password,{autoComplete:"new-password",placeholder:"请输入 API Secret"})}),(0,y.jsxs)("div",{className:"text-base mb-3 flex items-center",children:[(0,y.jsx)("div",{className:"h-4 w-1 bg-primary rounded"}),(0,y.jsx)("div",{className:"ml-2 font-semibold",children:"更多配置"})]}),(0,y.jsx)(on.Z.Item,{label:"表单回复",name:"answerForm.enabled",tooltip:"当工作流需要回复表单给用户填写时，建议开启此功能",rules:[{required:!0}],required:!0,children:(0,y.jsx)(od.Z,{placeholder:"请选择",options:[{label:"启用",value:!0},{label:"禁用",value:!1}]})}),n?(0,y.jsx)(on.Z.Item,{label:"提交消息文本",name:"answerForm.feedbackText",tooltip:"当启用表单回复时，用户填写表单并提交后，默认会以用户角色将填写的表单数据作为消息文本发送，如果配置了此字段，将会固定展示配置的字段值",children:(0,y.jsx)(oc.Z,{placeholder:"请输入提交消息文本"})}):null]})}function om(e){var t;let{activeAppId:n,getAppList:a,appListLoading:i,appList:r,onDeleteSuccess:o,...s}=e,{user:l,appService:c}=N(),[u,d]=(0,x.useState)(),[p,m]=(0,x.useState)(!1),[f]=on.Z.useForm(),[g,h]=(0,x.useState)(),v=O(),{t:b}=(0,ou.$G)(),[S,w]=(0,x.useState)(!1),{runAsync:j,loading:k}=(0,ei.Z)(async e=>c.addApp(e),{manual:!0,onSuccess:()=>{m(!1),z.ZP.success(b("app.addAppConfigSuccess")),a()}}),{runAsync:C,loading:A}=(0,ei.Z)(async e=>c.updateApp(e),{manual:!0,onSuccess:()=>{m(!1),z.ZP.success(b("app.updateAppConfigSuccess")),a()}});(0,x.useEffect)(()=>{p||f.resetFields()},[p]);let T=null==r?void 0:r.find(e=>e.id===u);return(0,y.jsxs)(oa.Z,{width:700,title:b("app.management"),...s,children:[(0,y.jsxs)("div",{className:"w-full h-full overflow-hidden flex flex-col",children:[(0,y.jsx)("div",{className:"pb-3 flex-1 overflow-y-auto",children:(0,y.jsx)(V.Z,{spinning:i,children:(0,y.jsx)(oi.Z,{gutter:16*!v,className:"w-full",children:(null==r?void 0:r.length)?null==r?void 0:r.map(e=>{var t;return(0,y.jsx)(or.Z,{span:v?24:12,children:(0,y.jsxs)("div",{className:"p-3 bg-white mb-3 border border-solid border-gray-200 rounded-lg cursor-pointer hover:border-primary hover:text-primary",onClick:()=>{var t,n;d(e.id),f.setFieldsValue({apiBase:e.requestConfig.apiBase,apiKey:e.requestConfig.apiKey,"answerForm.enabled":(null==(t=e.answerForm)?void 0:t.enabled)||!1,"answerForm.feedbackText":(null==(n=e.answerForm)?void 0:n.feedbackText)||""}),h("edit"),m(!0)},children:[(0,y.jsxs)("div",{className:"w-full flex items-center overflow-hidden",children:[(0,y.jsxs)("div",{className:"flex-1 font-semibold truncate",children:[n===e.id&&"【当前】",e.info.name]}),(0,y.jsx)(oo.Z,{className:"inline-flex items-center",children:(0,y.jsx)(os.Z,{onPopupClick:e=>e.stopPropagation(),cancelText:"取消",okText:"确定",title:"确定删除应用吗？",onConfirm:async()=>{await c.deleteApp(e.id),z.ZP.success("删除应用成功"),a(),null==o||o(e.id)},children:(0,y.jsx)(eo.Z,{onClick:e=>e.stopPropagation(),className:"p-0 text-red-500"})})})]}),(0,y.jsx)("div",{title:e.info.description,className:"truncate text-sm mt-2 text-desc h-6 leading-6",children:e.info.description}),(0,y.jsxs)("div",{className:"mt-3 text-desc truncate",title:e.info.tags.join(", "),children:["标签：",(null==(t=e.info.tags)?void 0:t.length)?e.info.tags.join(", "):(0,y.jsx)(y.Fragment,{children:"无"})]})]})},e.id)}):(0,y.jsx)(ol.Z,{className:"mx-auto",description:"暂无应用"})})})}),(0,y.jsx)(J.ZP,{type:"primary",size:"large",block:!0,onClick:()=>{d(""),h("create"),f.resetFields(),m(!0)},children:"添加应用"})]}),(0,y.jsxs)(oa.Z,{width:600,title:`${"create"===g?"添加应用配置":`应用配置详情 - ${null==T?void 0:T.info.name}`}`,open:p,onClose:()=>m(!1),extra:(0,y.jsxs)(oo.Z,{children:[(0,y.jsx)(J.ZP,{onClick:()=>m(!1),children:"取消"}),(0,y.jsx)(J.ZP,{type:"primary",loading:k||A||S,onClick:async()=>{await f.validateFields(),w(!0);try{let e=f.getFieldsValue(),t=null==r?void 0:r.find(e=>e.id===u),n=new r7({user:l,apiBase:e.apiBase,apiKey:e.apiKey}),i={info:await n.getAppInfo(),requestConfig:{apiBase:e.apiBase,apiKey:e.apiKey},answerForm:{enabled:e["answerForm.enabled"],feedbackText:e["answerForm.feedbackText"]}};"edit"===g?await C({id:t.id,...i}):await j({id:Math.random().toString(),...i}),a()}catch(e){console.error("保存应用配置失败",e),z.ZP.error(`保存应用配置失败: ${e}`)}finally{w(!1)}},children:"create"===g?"确定":"更新"})]}),children:["edit"===g?(0,y.jsxs)(on.Z,{labelAlign:"left",labelCol:{span:5},layout:"horizontal",children:[(0,y.jsxs)("div",{className:"text-base mb-3 flex items-center",children:[(0,y.jsx)("div",{className:"h-4 w-1 bg-primary rounded"}),(0,y.jsx)("div",{className:"ml-2 font-semibold",children:"基本信息"})]}),(0,y.jsx)(on.Z.Item,{label:"应用名称",children:(0,y.jsx)(oc.Z,{disabled:!0,value:null==T?void 0:T.info.name})}),(0,y.jsx)(on.Z.Item,{label:"应用描述",children:(0,y.jsx)(oc.Z,{disabled:!0,value:null==T?void 0:T.info.name})}),(0,y.jsx)(on.Z.Item,{label:"应用标签",children:(null==T||null==(t=T.info.tags)?void 0:t.length)?(0,y.jsx)("div",{className:"text-default",children:T.info.tags.join(", ")}):(0,y.jsx)(y.Fragment,{children:"无"})})]}):null,(0,y.jsx)(op,{formInstance:f})]})]})}var of=n(45549),og=n(52503),oh=n(63627),ov=n(81088),oy=n(44416),ob=n(47041),ox=n(79183),oS=n(51850),ow=n(53509),oj=n(8394);let ok=new Map;ok.set("document",["txt","md","mdx","markdown","pdf","html","xlsx","xls","doc","docx","csv","eml","msg","pptx","ppt","xml","epub"]),ok.set("image",["jpg","jpeg","png","gif","webp","svg"]),ok.set("audio",["mp3","m4a","wav","webm","amr"]),ok.set("video",["mp4","mov","mpeg","mpga"]),ok.set("custom",[]);let oC=e=>e.split(".").pop(),oN=e=>{let t=e.split(".").pop(),n=null;return ok.forEach((e,a)=>{e.indexOf(t)>-1&&(n=a)}),n},oA=e=>e>1048576?`${(e/1024/1024).toFixed(2)} MB`:`${(e/1024).toFixed(2)} KB`,oT=e=>{var t;let{content:n,isRequesting:a,onChange:i,onSubmit:r,className:o,onCancel:s,uploadFileApi:l,appParameters:c,onFocus:u}=e,[d,p]=(0,x.useState)(!1),[m,f]=(0,x.useState)([]),[g,h]=(0,x.useState)(new Map),v=(0,x.useMemo)(()=>{if(!(null==c?void 0:c.file_upload.enabled))return[];let e=[];return c.file_upload.allowed_file_types.forEach(t=>{ok.get(t)&&e.push(...ok.get(t)||[])}),e},[null==c?void 0:c.file_upload]),b=async e=>{let t=[...m],n={uid:e.uid,name:e.name,status:"uploading",size:e.size,type:e.type,originFileObj:e},{clear:a}=(()=>{let e=0;f([...t,{...n,percent:e}]);let a=setInterval(()=>{if(e>=99)return void clearInterval(a);e+=1,f([...t,{...n,percent:e}])},100);return{clear:()=>clearInterval(a)}})(),i=await l(e);if((null==i?void 0:i.code)!==0)return void z.ZP.error(null==i?void 0:i.msg);a(),f([...t,{...n,percent:100,status:"done"}]),h(t=>{var n;let a=new Map(t);return a.set(null==e?void 0:e.uid,null==i||null==(n=i.data)?void 0:n.id),a})},S=(0,x.useRef)(null),w=(0,y.jsx)(oS.Z.Header,{title:"上传文件",open:d,onOpenChange:p,styles:{content:{padding:0}},children:(0,y.jsx)(ow.Z,{beforeUpload:async e=>{let t=oC(e.name);return v.length>0&&!v.includes(t)?z.ZP.error(`不支持的文件类型: ${t}`):b(e),!1},items:m,placeholder:e=>"drop"===e?{title:"Drop file here"}:{icon:(0,y.jsx)(ob.Z,{}),title:"点击或拖拽文件到此区域上传",description:(0,y.jsxs)("div",{children:["支持的文件类型：",v.join(", ")]})},getDropContainer:()=>{var e;return null==(e=S.current)?void 0:e.nativeElement},onRemove:e=>{f(t=>t.filter(t=>t.uid!==e.uid))}})});return(0,y.jsx)(oS.Z,{allowSpeech:null==c?void 0:c.speech_to_text.enabled,header:w,value:n,onChange:i,onFocus:u,prefix:(null==c||null==(t=c.file_upload)?void 0:t.enabled)?(0,y.jsx)(oj.Z,{dot:m.length>0&&!d,children:(0,y.jsx)(J.ZP,{onClick:()=>p(!d),icon:(0,y.jsx)(ox.Z,{})})}):null,style:{boxShadow:"0px -2px 12px 4px #efefef"},loading:a,className:o,onSubmit:async e=>{if((null==m?void 0:m.length)&&!m.every(e=>"done"===e.status))return void z.ZP.error("请等待所有文件上传完成");await r(e,{files:(null==m?void 0:m.map(e=>{let t=oN(e.name);return{...e,type:t||"document",transfer_method:"local_file",upload_file_id:g.get(e.uid)}}))||[]}),f([]),p(!1)},onCancel:s})};var o_=n(82120),oE=n(810),oI=n(96002),oO=n(66642),oP=n(83191);n(98647);var oM=n(13224),oL=n(12599),oD=n(6785),oF=n(96858),o$=n(78869),oR=n(30322),oZ=n(92445),oU=(n(27276),n(259)),oz=n(30781),oB=n(45709),oH=n(17118),oG=n(74266),oq=n(73790);s=null,"undefined"!=typeof window&&(s=oz.Z.mermaidAPI);let oK=e=>{let t=new Blob([new TextEncoder().encode(e)],{type:"image/svg+xml;charset=utf-8"});return new Promise((e,n)=>{let a=new FileReader;a.onloadend=()=>e(a.result),a.onerror=n,a.readAsDataURL(t)})},oW=e=>{let{ref:t,...n}=e,[a,i]=(0,x.useState)(null),[r,o]=(0,x.useState)("classic"),l=(0,oB.Z)(n.PrimitiveCode),[c,u]=(0,x.useState)(!0),d=(0,x.useRef)(0),[p,m]=(0,x.useState)(""),[f,g]=(0,x.useState)(""),h=(0,x.useCallback)(async e=>{i(null),u(!0);try{if("undefined"!=typeof window&&s){let t=await s.render("flowchart",e),n=await oK(t.svg.replaceAll("<br>","<br/>"));i(n),u(!1)}}catch(e){l===n.PrimitiveCode&&(u(!1),m(e.message))}},[n.PrimitiveCode]);return(0,x.useEffect)(()=>{"undefined"!=typeof window&&(oz.Z.initialize({startOnLoad:!0,theme:"neutral",look:r,flowchart:{htmlLabels:!0,useMaxWidth:!0}}),h(n.PrimitiveCode))},[r]),(0,x.useEffect)(()=>{d.current&&window.clearTimeout(d.current),d.current=window.setTimeout(()=>{h(n.PrimitiveCode)},300)},[n.PrimitiveCode]),(0,y.jsxs)("div",{ref:t,children:[(0,y.jsx)("div",{className:"msh-segmented msh-segmented-sm css-23bs09 css-var-r1",children:(0,y.jsx)("div",{className:"msh-segmented-group",children:(0,y.jsx)("label",{className:"msh-segmented-item m-2 flex w-[200px] items-center space-x-1",children:(0,y.jsxs)(oq.ZP.Group,{value:r,buttonStyle:"solid",optionType:"button",onChange:e=>{"handDrawn"===e.target.value?o("handDrawn"):o("classic")},children:[(0,y.jsx)(oq.ZP,{value:"classic",children:"经典"}),(0,y.jsx)(oq.ZP,{value:"handDrawn",children:"手绘"})]})})})}),a&&(0,y.jsx)("div",{className:"mermaid object-fit: cover h-auto w-full cursor-pointer",onClick:()=>g(a),children:a&&(0,y.jsx)("img",{src:a,alt:"mermaid_chart"})}),c&&(0,y.jsx)("div",{className:"px-[26px] py-4",children:(0,y.jsx)(oG.Z,{})}),p&&(0,y.jsxs)("div",{className:"px-[26px] py-4",children:[(0,y.jsx)(oH.Z,{className:"h-6 w-6 text-red-500"}),"\xa0",p]})]})};oW.displayName="Flowchart";let oV=e=>{var t;return"string"==typeof e?e.includes("[ENDTHINKFLAG]"):Array.isArray(e)?e.some(e=>oV(e)):null!=e&&null!=(t=e.props)&&!!t.children&&oV(e.props.children)},oX=e=>{var t;return"string"==typeof e?e.replace("[ENDTHINKFLAG]",""):Array.isArray(e)?e.map(e=>oX(e)):(null==e||null==(t=e.props)?void 0:t.children)?x.cloneElement(e,{...e.props,children:oX(e.props.children)}):e},oJ=e=>{let[t]=(0,x.useState)(Date.now()),[n,a]=(0,x.useState)(0),[i,r]=(0,x.useState)(!1),o=(0,x.useRef)();return(0,x.useEffect)(()=>(o.current=setInterval(()=>{i||a(Math.floor((Date.now()-t)/100)/10)},100),()=>{o.current&&clearInterval(o.current)}),[t,i]),(0,x.useEffect)(()=>{oV(e)&&(r(!0),o.current&&clearInterval(o.current))},[e]),{elapsedTime:n,isComplete:i}},oY=e=>{let{children:t,...n}=e,{elapsedTime:a,isComplete:i}=oJ(t),r=oX(t);return n["data-think"]?(0,y.jsxs)("details",{...!i&&{open:!0},className:"group",children:[(0,y.jsx)("summary",{className:"flex cursor-pointer select-none list-none items-center whitespace-nowrap font-bold text-gray-500",children:(0,y.jsxs)("div",{className:"flex shrink-0 items-center",children:[(0,y.jsx)("svg",{className:"mr-2 h-3 w-3 transition-transform duration-500 group-open:rotate-90",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,y.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),i?`已深度思考(${a.toFixed(1)}s)`:`深度思考中...(${a.toFixed(1)}s)`]})}),(0,y.jsx)("div",{className:"border-l mt-1 rounded-lg border-gray-300 text-gray-500 p-3 bg-gray-50",children:r})]}):(0,y.jsx)("details",{...n,children:t})};var oQ=n(69267),o0=n(12737);let o1=e=>{let{content:t}=e,n=(0,x.useRef)(null),[a,i]=(0,x.useState)(""),[r,o]=(0,x.useState)({width:"undefined"!=typeof window?window.innerWidth:0,height:"undefined"!=typeof window?window.innerHeight:0}),s=e=>{let t=new XMLSerializer().serializeToString(e),n=Buffer.from(t).toString("base64");return`data:image/svg+xml;base64,${n}`};return(0,x.useEffect)(()=>{let e=()=>{o({width:window.innerWidth,height:window.innerHeight})};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,x.useEffect)(()=>{if(n.current)try{n.current.innerHTML="";let e=(0,oQ.Wj)().addTo(n.current),a=new DOMParser().parseFromString(t,"image/svg+xml").documentElement;if(!(a instanceof SVGElement))throw Error("Invalid SVG content");let r=Number.parseInt(a.getAttribute("width")||"400",10),o=Number.parseInt(a.getAttribute("height")||"600",10);e.viewbox(0,0,r,o),n.current.style.width=`${Math.min(r,298)}px`,e.svg(o0.Z.sanitize(t)).click(()=>{i(s(a))})}catch(e){n.current&&(n.current.innerHTML='<span style="padding: 1rem;">Error rendering SVG. Wait for the image content to complete.</span>')}},[t,r]),(0,y.jsx)(y.Fragment,{children:(0,y.jsx)("div",{ref:n,style:{maxHeight:"80vh",display:"flex",justifyContent:"center",alignItems:"center",cursor:"pointer",wordBreak:"break-word",whiteSpace:"normal",margin:"0 auto"}})})};var o2=n(5914),o5=n(53075),o4=n(92752),o3=n(1274),o6=n(29981),o8=n.n(o6),o9=n(64919),o7=n(56366),se=n(75510);let st=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,se.m6)(o8()(t))};(0,o9.Z)({html:!0,breaks:!0}).use(o3.Z).use(o7.Z,{delimiters:[{left:"\\[",right:"\\]",display:!0},{left:"\\(",right:"\\)",display:!1},{left:"$$",right:"$$",display:!1}]});let sn=e=>{let{isSVG:t,setIsSVG:n}=e;return(0,y.jsx)(J.ZP,{onClick:()=>{n(e=>!e)},children:(0,y.jsx)("div",{className:st("h-4 w-4"),children:(0,y.jsx)(o4.Z,{})})})};var sa=((f=sa||{}).TEXT="text",f.PASSWORD="password",f.EMAIL="email",f.NUMBER="number",f.DATE="date",f.TIME="time",f.DATETIME="datetime",f.CHECKBOX="checkbox",f.SELECT="select",f);let si=e=>{let{node:t,onSend:n}=e,[a,i]=(0,x.useState)({});(0,x.useEffect)(()=>{let e={};t.children.forEach(t=>{["input","textarea"].includes(t.tagName)&&(e[t.properties.name]=t.properties.value)}),i(e)},[t.children]);let r=e=>{let t={};return e.forEach(e=>{["input","textarea"].includes(e.tagName)&&(t[e.properties.name]=a[e.properties.name])}),t},o=e=>{e.preventDefault();let a=t.properties.dataFormat||"text",i=r(t.children);if("json"===a)console.log("即将发送",a,i),null==n||n(i);else{let e=Object.entries(i).map(e=>{let[t,n]=e;return`${t}: ${n}`}).join("\n");null==n||n(e)}};return(0,y.jsx)("form",{autoComplete:"off",className:"flex flex-col self-stretch pb-3",onSubmit:e=>{e.preventDefault(),e.stopPropagation()},children:t.children.filter(e=>"element"===e.type).map((e,t)=>{var n,r;if("label"===e.tagName)return(0,y.jsx)("label",{htmlFor:e.properties.for,className:"system-md-semibold my-2 text-text-secondary",children:(null==(n=e.children[0])?void 0:n.value)||""},t);if("input"===e.tagName&&Object.values(sa).includes(e.properties.type))return(0,y.jsx)(oc.Z,{type:e.properties.type,name:e.properties.name,placeholder:e.properties.placeholder,value:a[e.properties.name],onChange:t=>{i(n=>({...n,[e.properties.name]:t.target.value}))}},t);if("textarea"===e.tagName)return(0,y.jsx)(oc.Z.TextArea,{name:e.properties.name,placeholder:e.properties.placeholder,value:a[e.properties.name],onChange:t=>{i(n=>({...n,[e.properties.name]:t.target.value}))}},t);if("button"===e.tagName){let n=e.properties.dataVariant;return e.properties.dataSize,(0,y.jsx)(J.ZP,{type:"primary",variant:n,className:"mt-4",onClick:o,children:(null==(r=e.children[0])?void 0:r.value)||""},t)}return(0,y.jsxs)("p",{children:["Unsupported tag: ",e.tagName]},t)})})};si.displayName="MarkdownForm";let sr={sql:"SQL",javascript:"JavaScript",java:"Java",typescript:"TypeScript",vbscript:"VBScript",css:"CSS",html:"HTML",xml:"XML",php:"PHP",python:"Python",yaml:"Yaml",mermaid:"Mermaid",markdown:"MarkDown",makefile:"MakeFile",echarts:"ECharts",shell:"Shell",powershell:"PowerShell",json:"JSON",latex:"Latex",svg:"SVG"},so=e=>e?e in sr?sr[e]:e.charAt(0).toUpperCase()+e.substring(1):"Plain",ss=e=>{if("string"!=typeof e)return e;let t=/```[\s\S]*?```/g,n=e.match(t)||[],a=e.replace(t,"CODE_BLOCK_PLACEHOLDER");return a=(0,oU.Z)([e=>e.replace(/\\\[(.*?)\\\]/g,(e,t)=>`$$${t}$$`),e=>e.replace(/\\\[([\s\S]*?)\\\]/g,(e,t)=>`$$${t}$$`),e=>e.replace(/\\\((.*?)\\\)/g,(e,t)=>`$$${t}$$`),e=>e.replace(/(^|[^\\])\$(.+?)\$/g,(e,t,n)=>`${t}$${n}$`)])(a),n.forEach(e=>{a=a.replace("CODE_BLOCK_PLACEHOLDER",e)}),a},sl=e=>(0,oU.Z)([e=>e.replace("<think>\n","<details data-think=true>\n"),e=>e.replace("\n</think>","\n[ENDTHINKFLAG]</details>")])(e),sc=(0,x.memo)(e=>{let{inline:t,className:n,children:a,...i}=e,[r,o]=(0,x.useState)(!0),s=/language-(\w+)/.exec(n||""),l=null==s?void 0:s[1],c=so(l||""),u=(0,x.useMemo)(()=>{if("echarts"===l)try{return JSON.parse(String(a).replace(/\n$/,""))}catch(e){}return JSON.parse('{"title":{"text":"ECharts error - Wrong JSON format."}}')},[l,a]),d=(0,x.useMemo)(()=>{let e=String(a).replace(/\n$/,"");return"mermaid"===l&&r?(0,y.jsx)(oW,{PrimitiveCode:e}):"echarts"===l?(0,y.jsx)("div",{style:{minHeight:"350px",minWidth:"100%",overflowX:"scroll"},children:(0,y.jsx)(sg,{children:(0,y.jsx)(oP.Z,{option:u,style:{minWidth:"700px"}})})}):"svg"===l&&r?(0,y.jsx)(sg,{children:(0,y.jsx)(o1,{content:e})}):(0,y.jsx)(oR.Z,{...i,style:oZ.Z,customStyle:{paddingLeft:12,borderBottomLeftRadius:"10px",borderBottomRightRadius:"10px",backgroundColor:"var(--color-components-input-bg-normal)"},language:null==s?void 0:s[1],showLineNumbers:!0,PreTag:"div",children:e})},[l,s,i,a,u,r]);return t||!s?(0,y.jsx)("code",{...i,className:n,children:a}):(0,y.jsxs)("div",{className:"relative",children:[(0,y.jsxs)("div",{className:"flex h-8 items-center justify-between rounded-t-[10px] border-b border-divider-subtle bg-components-input-bg-normal p-1 pl-3",children:[(0,y.jsx)("div",{className:"text-gray-700",children:c}),(0,y.jsxs)("div",{className:"flex items-center gap-1",children:[["mermaid","svg"].includes(l)&&(0,y.jsx)(sn,{isSVG:r,setIsSVG:o}),(0,y.jsx)(J.ZP,{children:(0,y.jsx)(o2.Z,{onClick:async()=>{await (0,o5.v)(String(a).replace(/\n$/,"")),z.ZP.success("复制成功")}})})]})]}),d]})});sc.displayName="CodeBlock";let su=(0,x.memo)(e=>{var t;let{node:n}=e,a=(null==(t=n.children[0])?void 0:t.value)||"";return`<script>${a}</script>`});su.displayName="ScriptBlock";let sd=e=>{let{node:t}=e,n=t.children;return n&&n[0]&&"tagName"in n[0]&&"img"===n[0].tagName?(0,y.jsx)(y.Fragment,{children:Array.isArray(e.children)?(0,y.jsx)("p",{children:e.children.slice(1)}):null}):(0,y.jsx)("p",{children:e.children})},sp=e=>{let{src:t}=e;return(0,y.jsx)("img",{src:t})},sm=e=>{var t;let{node:n,...a}=e;return(0,y.jsx)("a",{...a,target:"_blank",className:"cursor-pointer underline !decoration-primary-700 decoration-dashed",children:n.children[0]?null==(t=n.children[0])?void 0:t.value:"Download"})};function sf(e){let{onSubmit:t}=e,n=(0,oU.Z)([sl,ss])(e.markdownText);return(0,y.jsx)("div",{className:"text-default",children:(0,y.jsx)(oO.UG,{remarkPlugins:[oF.Z,[oM.Z,{singleDollarTextMath:!1}],oL.Z],rehypePlugins:[oD.Z,o$.Z,()=>e=>{let t=e=>{var n;"element"===e.type&&(null==(n=e.properties)?void 0:n.ref)&&delete e.properties.ref,"element"!==e.type||/^[a-z][a-z0-9]*$/i.test(e.tagName)||(e.type="text",e.value=`<${e.tagName}`),e.children&&e.children.forEach(t)};e.children.forEach(t)}],disallowedElements:["iframe","head","html","meta","link","style","body",...e.customDisallowedElements||[]],components:{code:sc,img:sp,a:sm,p:sd,form:e=>(0,y.jsx)(si,{...e,onSend:e=>{t(JSON.stringify({...e,isFormSubmit:!0}),{inputs:e})}}),script:su,details:oY},children:n})})}class sg extends x.Component{componentDidCatch(e,t){this.setState({hasError:!0}),console.error(e,t)}render(){return this.state.hasError?(0,y.jsxs)("div",{children:["Oops! An error occurred. This could be due to an ECharts runtime error or invalid SVG content. ",(0,y.jsx)("br",{}),"(see the browser console for more information)"]}):this.props.children}constructor(e){super(e),this.state={hasError:!1}}}var sh=n(34014),sv=n(6997),sy=n(82288);function sb(e){let{text:t}=e;return t?(0,y.jsx)("pre",{className:"!m-0 !p-0 !bg-white !border-none",children:t}):"空"}function sx(e){let{uniqueKey:t,items:n,className:a}=e;if(!(null==n?void 0:n.length))return null;let i=n.map(e=>({title:(0,y.jsx)("div",{className:"text-base",children:e.tool?`已使用 ${e.tool}`:"暂无标题"}),status:"success",icon:(0,y.jsx)(sh.Z,{}),description:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(sy.Z,{className:"mt-3 min-w-chat-card",size:"small",items:[{key:`${t}-tool_input`,label:"输入",children:(0,y.jsx)(sb,{text:e.tool_input})},{key:`${t}-observation`,label:"输出",children:(0,y.jsx)(sb,{text:e.observation})}]}),(0,y.jsx)("pre",{className:"border-none",children:e.thought})]})}));return(null==i?void 0:i.length)?(0,y.jsx)(sv.Z,{className:a,items:i}):null}var sS=n(61124),sw=n(50374),sj=n(42634);function sk(e){let{files:t}=e;return(null==t?void 0:t.length)?t.every(e=>"image"===e.type)?(0,y.jsx)("div",{className:"flex flex-wrap",children:t.map(e=>(0,y.jsx)(sj.TV,{children:(0,y.jsx)(sj.HI,{src:e.url,children:(0,y.jsx)("img",{src:e.url&&(location.origin.includes(".medon.com.cn")||location.origin.includes(".medsci.cn")?location.origin+e.url:"https://ai-base.medon.com.cn"+e.url),alt:e.filename,className:"w-24 h-24 cursor-zoom-in mr-2 rounded-lg",style:{objectFit:"cover"}},e.id)})},e.id))}):(0,y.jsx)(y.Fragment,{children:t.map((e,t)=>{var n;return(0,y.jsxs)("a",{title:"点击下载文件",href:e.url,target:"_blank",className:"p-3 bg-gray-50 rounded-lg w-60 flex items-center cursor-pointer no-underline mb-2",children:["image"===e.type?(0,y.jsx)(sS.Z,{className:"text-3xl text-gray-400 mr-2"}):(0,y.jsx)(sw.Z,{className:"text-3xl text-gray-400 mr-2"}),(0,y.jsxs)("div",{className:"overflow-hidden",children:[(0,y.jsx)("div",{className:"text-default truncate",children:(null==(n=e.filename)?void 0:n.split("_").pop())||e.filename}),e.size?(0,y.jsx)("div",{className:"text-desc truncate",children:oA(e.size)}):null]})]},e.id+t)})}):null}function sC(e){let{items:t}=e;return(null==t?void 0:t.length)?(0,y.jsxs)("div",{className:"pb-3",children:[(0,y.jsxs)("div",{className:"flex items-center text-gray-400",children:[(0,y.jsx)("span",{className:"mr-3 text-sm",children:"引用"}),(0,y.jsx)("div",{className:"flex-1 border-gray-400 border-dashed border-0 border-t h-0"})]}),t.map(e=>(0,y.jsx)("div",{className:"mt-2 truncate",children:(0,y.jsx)("a",{className:"text-gray-600",target:"_blank",href:"javascript:void(0)",title:e.document_name,children:e.document_name})},e.id))]}):null}function sN(e){var t;let{appConfig:n,onSubmit:a,messageItem:{id:i,status:r,error:o,agentThoughts:s,workflows:l,files:c,content:u,retrieverResources:d,role:p}}=e,m=(0,x.useMemo)(()=>{let e=u.startsWith("{")&&u.endsWith("}");if("local"===p||"user"===p&&e){var t,a,i;if((null==(t=n.answerForm)?void 0:t.enabled)&&(null==(a=n.answerForm)?void 0:a.feedbackText))try{return JSON.parse(u).isFormSubmit?null==(i=n.answerForm)?void 0:i.feedbackText:u}catch(e){console.log("computedContent json 解析失败",e)}}return u},[u,null==n?void 0:n.answerForm,p]);return"error"===r?(0,y.jsxs)("p",{className:"text-red-700",children:[(0,y.jsx)(o_.Z,{className:"mr-2"}),(0,y.jsx)("span",{children:o})]}):"success"!==r||u||(null==c?void 0:c.length)||(null==s?void 0:s.length)||(null==l||null==(t=l.nodes)?void 0:t.length)||(null==d?void 0:d.length)?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(sx,{uniqueKey:i,items:s,className:"mt-3"}),(null==c?void 0:c.length)?(0,y.jsx)("div",{className:"mt-3",children:(0,y.jsx)(sk,{files:c})}):null,(0,y.jsx)("div",{className:"local"===p||"user"===p?"":"md:min-w-chat-card",children:(0,y.jsx)(sf,{markdownText:m,onSubmit:a})}),(0,y.jsx)(sC,{items:d})]}):(0,y.jsxs)("p",{className:"text-orange-600",children:[(0,y.jsx)(o_.Z,{className:"mr-2"}),(0,y.jsx)("span",{children:"消息内容为空"}),(0,y.jsx)(oI.Z,{title:"可能是用户在生成内容的过程中点击了停止响应按钮",children:(0,y.jsx)(oE.Z,{className:"ml-2"})})]})}n(70003);var sA=n(53024),sT=n(9702),s_=n(10543),sE=n(89696),sI=n(33331),sO=n(16017);function sP(e){let{icon:t,loading:n=!1,active:a=!1,onClick:i}=e,r=x.cloneElement(t,{className:a?"text-primary":""});return(0,y.jsxs)("div",{className:"relative",children:[(0,y.jsx)(J.ZP,{color:"default",variant:"text",size:"small",icon:r,onClick:i}),(0,y.jsx)(V.Z,{className:"absolute left-0 top-0 w-full h-full",spinning:n})]})}function sM(e){let{messageId:t,messageContent:n,feedback:{rating:a,callback:i},feedbackApi:r,ttsApi:o,ttsConfig:s,onSubmit:l}=e,c="like"===a,u="dislike"===a,[d,p]=(0,sO.Z)({like:!1,dislike:!1}),[m,f]=(0,x.useState)(!1),[g,h]=(0,x.useState)(""),{runAsync:v}=(0,ei.Z)(e=>r({messageId:t.replace("-answer",""),rating:e,content:""}),{manual:!0,onSuccess(){z.ZP.success("操作成功"),null==i||i()},onFinally(){p({like:!1,dislike:!1})}}),b=async e=>{let t=new Audio;t.src=e,t.play(),f(!0),t.addEventListener("ended",()=>{f(!1)})},{runAsync:S,loading:w}=(0,ei.Z)(e=>o({text:e}).then(e=>e.blob()).then(e=>{let t=URL.createObjectURL(e);h(t),b(t)}),{manual:!0}),j=[{icon:(0,y.jsx)(sA.Z,{}),hidden:!1,onClick:()=>{null==l||l(t)}},{icon:(0,y.jsx)(o2.Z,{}),onClick:async()=>{await (0,o5.v)(n),z.ZP.success("复制成功")},active:!1,loading:!1,hidden:!1},{icon:(0,y.jsx)(sT.Z,{}),onClick:()=>{p({like:!0}),v(c?null:"like")},active:c,loading:d.like,hidden:!1},{icon:(0,y.jsx)(s_.Z,{}),onClick:()=>{p({dislike:!0}),v(u?null:"dislike")},active:u,loading:d.dislike,hidden:!1},{icon:m?(0,y.jsx)(sE.Z,{}):(0,y.jsx)(sI.Z,{}),onClick:()=>{g?b(g):S(n)},active:m,loading:w,hidden:!(null==s?void 0:s.enabled)}];return(0,y.jsx)(oo.Z,{children:j.map((e,t)=>!e.hidden&&(0,y.jsx)(sP,{icon:e.icon,onClick:e.onClick,active:e.active,loading:e.loading},t))})}var sL=n(92266),sD=n(21397),sF=n(2016),s$=n(97004),sR=n(48161),sZ=n(71747);let sU=(e,t)=>(0,y.jsxs)(oo.Z,{align:"start",children:[e,(0,y.jsx)("span",{children:t})]}),sz=e=>{var t,n,a,i;let{onPromptItemClick:r,appParameters:o,description:s,appIcon:l,appConfig:c}=e,u=O(),d=(0,x.useMemo)(()=>(sU((0,y.jsx)(sL.Z,{style:{color:"#FF4D4F"}}),"Hot Topics"),sU((0,y.jsx)(sD.Z,{style:{color:"#1890FF"}}),"Design Guide"),sF.Z,s$.Z,sR.Z,null==o?void 0:o.suggested_questions)?[{key:"remote",label:sU((0,y.jsx)(sL.Z,{style:{color:"#FF4D4F"}}),"Hot Topics"),description:"What are you interested in?",children:o.suggested_questions.map((e,t)=>({key:"index-"+t,description:e}))}]:[],[u]);return(0,y.jsx)("div",{className:"flex justify-center w-full px-3 box-border mx-auto",children:(0,y.jsxs)(oo.Z,{direction:"vertical",className:"pt-8 w-full md:!w-3/4",children:[(0,y.jsx)(sZ.Z,{variant:"borderless",icon:l||"https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp",title:(null==(t=c.info)?void 0:t.name)||"Hello, I'm MedSci xAI",description:s||"MedSci xAI"}),(null==(n=d[0].children)?void 0:n.length)||(null==o?void 0:o.opening_statement)?(0,y.jsx)(oy.Z,{title:null==o?void 0:o.opening_statement,vertical:u,items:d,styles:{list:{width:"100%",display:(null==(i=d[0])||null==(a=i.children)?void 0:a.length)?"":"none"},item:u?{width:"100%"}:{flex:1}},onItemClick:r}):null]})})};var sB=n(7712),sH=n(19175),sG=n(23421),sq=e=>{var t;let{messageItems:n,isRequesting:a,nextSuggestions:i,onPromptsItemClick:r,onSubmit:o,onCancel:s,conversationId:l,feedbackCallback:c,difyApi:u,appParameters:d,appConfig:p,onFocus:m,translations:f}=e;console.log("Chatbox",p);let g=(0,R.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),h=g?g[1]:"",[v,b]=(0,x.useState)(""),S=O(),w=B.Z.get("userInfo"),[j,k]=(0,x.useState)(w&&JSON.parse(w).avatar||"https://img.medsci.cn/web/img/user_icon.png"),C={ai:{placement:"start",avatar:S?void 0:{icon:(0,y.jsx)("img",{src:h.includes("novax")||h.includes("elavax")?null==p||null==(t=p.info)?void 0:t.appIcon:"https://static.medsci.cn/ai-write/robot-08128bd4.png",alt:"robot"}),style:{background:"#fde3cf"}},style:S?void 0:{maxWidth:"calc(100% - 44px)"}},user:{placement:"end",avatar:S?void 0:{icon:j?(0,y.jsx)("img",{src:j,alt:"avatar",onError:()=>{k("https://img.medsci.cn/web/img/user_icon.png")}}):(0,y.jsx)(oh.Z,{}),style:{background:j?"":"#87d068",border:"none"}},style:S?void 0:{maxWidth:"calc(100% - 44px)",marginLeft:"44px"}}},N=(0,x.useMemo)(()=>{let e=new Map;return null==n?void 0:n.map(t=>{var n;return"user"===t.role&&e.set(t.id,t),{key:`${t.id}-${t.role}`,content:t.content,messageRender:()=>(0,y.jsx)(sN,{appConfig:p,onSubmit:o,messageItem:t}),role:"local"===t.role?"user":t.role,footer:"ai"===t.role&&(0,y.jsxs)("div",{className:"flex items-center",children:[(0,y.jsx)(sM,{ttsConfig:null==d?void 0:d.text_to_speech,feedbackApi:e=>u.feedbackMessage(e),ttsApi:e=>u.text2Audio(e),messageId:t.id,messageContent:t.content,feedback:{rating:null==(n=t.feedback)?void 0:n.rating,callback:()=>{null==c||c(l)}},onSubmit:n=>{let a=e.get(t.id);n&&a&&o(a.content,{files:(e=>{if(e)return e.map(e=>{var t;return{name:(null==(t=e.filename)?void 0:t.split("_").pop())||e.filename,url:e.url,transfer_method:e.transfer_method,type:e.type,upload_file_id:e.upload_file_id}})})(a.files)})}}),t.created_at&&(0,y.jsxs)("div",{className:"ml-3 text-sm text-desc",children:[(null==f?void 0:f.replyTime)||"回复时间：",t.created_at]})]})}})},[n,l,u,c,p,o]),A=(0,x.useRef)(null),T=(0,x.useDeferredValue)(N);return new URLSearchParams(window.top.location.search),(0,x.useEffect)(()=>{A.current&&A.current.scrollTo({behavior:"smooth",top:A.current.scrollHeight})},[T]),(0,y.jsx)("div",{className:"w-full h-full overflow-hidden my-0 mx-auto box-border flex flex-col gap-4 relative bg-white",children:(0,y.jsxs)("div",{className:"w-full h-full overflow-auto pt-4 pb-48",ref:A,children:[!(null==N?void 0:N.length)&&E(l)&&"medsci-ask"!=h&&(0,y.jsx)(sz,{appParameters:d,onPromptItemClick:r,description:p.info.description,appIcon:p.info.appIcon,appConfig:p}),(0,y.jsx)(ov.Z.List,{items:N,roles:C,className:"flex-1 w-full md:!w-3/4 mx-auto px-3 md:px-0 box-border"}),(0,y.jsxs)("div",{className:"absolute bottom-0 bg-white w-full md:!w-3/4 left-1/2",style:{transform:"translateX(-50%)"},children:[(0,y.jsx)(oy.Z,{className:"text-default p-3 bg-transparent",items:null==i?void 0:i.map((e,t)=>({key:t.toString(),description:e})),onItemClick:r}),(0,y.jsx)("div",{className:"px-3",children:(0,y.jsx)(oT,{appParameters:d,content:v,onChange:e=>b(e),onSubmit:(e,t)=>{e&&(o(e,t),b(""))},isRequesting:a,className:"w-full",uploadFileApi:async function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let a=await u.uploadFile(...t);return 0!=a.code?(z.ZP.error(a.msg),Promise.reject()):u.uploadFile(...t)},onCancel:s,onFocus:m})}),(0,y.jsx)("div",{className:"text-gray-400 text-sm text-center h-8 leading-8",children:(null==f?void 0:f.aiGeneratedContent)||"内容由 AI 生成, 仅供参考"})]})]})})},sK=e=>{let{deleteConversationPromise:t,renameConversationPromise:n,items:a,activeKey:i,onActiveChange:r,onItemsChange:o,refreshItems:s,appConfig:l,onchangeModal2Open:c}=e,u=(0,R.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),d=u?u[1]:"";B.Z.get("userInfo");let[p]=on.Z.useForm(),m=async e=>{if(E(e))null==o||o(a.filter(t=>t.key!==e));else{var n;await t(e,null==l||null==(n=l.info)?void 0:n.appId),s()}z.ZP.success("删除成功"),i===e&&(null==r||r(""))},f=e=>{p.setFieldsValue({name:e.label}),sG.Z.confirm({destroyOnClose:!0,title:"会话重命名",content:(0,y.jsx)(on.Z,{form:p,className:"mt-3",children:(0,y.jsx)(on.Z.Item,{name:"name",children:(0,y.jsx)(oc.Z,{placeholder:"请输入"})})}),onOk:async()=>{await p.validateFields();let t=await p.validateFields();await n(e.key,t.name),z.ZP.success("会话重命名成功"),s()}})};return(0,y.jsx)(sH.Z,{items:a,activeKey:i,onActiveChange:r,menu:e=>({items:[{label:"重命名",key:"rename",icon:(0,y.jsx)(sB.Z,{}),disabled:E(e.key)},d.includes("novax")||d.includes("elavax")?{label:"删除",key:"delete",icon:(0,y.jsx)(eo.Z,{}),danger:!0}:null],onClick:async t=>{switch(t.domEvent.stopPropagation(),t.key){case"delete":await m(e.key);break;case"rename":f(e)}}})})},sW=n(10965),sV=n(95686),sX=n(39016),sJ=n(16483),sY=n.n(sJ),sQ=n(88627);let s0="ai.medsci.cn"===window.location.hostname?"https://ai.medsci.cn":"https://ai.medon.com.cn",s1=new U({baseURL:s0}),s2=class extends A{async getApps(){return await s1.get("/apps")}async getApp(e,t){try{let n=await s1.get("/dev-api/ai-base/index/getAppByUuid",{appUuid:e},t);if(n.data)return{id:n.data.appUuid,info:{name:n.data.appName,description:n.data.appDescription,appUser:n.data.appUser,appId:n.data.dAppUuid,appType:n.data.appType,appIcon:n.data.appIcon,feeTypes:n.data.feeTypes,tags:[],appUuid:n.data.appUuid},requestConfig:{apiBase:`${s0}/dev-api/ai-base/v1`,apiKey:""},answerForm:{enabled:!1}};return void console.warn("No data found for app with id:",e)}catch(e){console.error("Failed to fetch app:",e);return}}async addApp(e){return s1.post("/apps",e)}async updateApp(e){return s1.put(`/apps/${e.id}`,e)}async deleteApp(e){await s1.delete(`/apps/${e}`)}},s5="新对话";function s4(e){let t=(0,x.useRef)(e);return t.current=e,t}var s3=n(80900),s6=n(70699),s8=n(24923);n(77345);let s9=e=>{let{latestProps:t,latestState:n,appParameters:a,getNextSuggestions:i,filesRef:r,abortRef:o,getConversationMessages:s,onConversationIdChange:l,difyApi:c}=e,{user:u}=N(),[d,p]=(0,x.useState)(""),[m]=(0,s3.Z)({request:async(m,f)=>{let{message:g}=m,{onSuccess:h,onUpdate:v,onError:y}=f,b=await c.sendMessage({inputs:(null==g?void 0:g.inputs)||n.current.inputParams,conversation_id:E(t.current.conversationId)?void 0:t.current.conversationId,files:r.current||[],user:u,response_mode:"streaming",query:null==g?void 0:g.content,requestId:"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}),appUuid:e.appUuid}),x="",S=[],w={},j=[];if(200!==b.status){let e=b.statusText||"请求对话接口失败";z.ZP.error(e),o.current=()=>{y({name:b.status.toString(),message:e})},o.current();return}let k=(0,s6.Z)({readableStream:b.body}).getReader();for(o.current=()=>{null==k||k.cancel(),y({name:"abort",message:"用户已取消"})};k;){let{value:e,done:t}=await k.read();if(t){h({content:x,files:S,workflows:w,agentThoughts:j});break}if(e)if(e.data){let t={};try{t=JSON.parse(e.data)}catch(e){console.error("解析 JSON 失败",e)}t.task_id&&t.task_id!==d&&p(t.task_id),console.log("parsedData",t,ot),t.event===ot.MESSAGE_END&&(h({content:x,files:S,workflows:w,agentThoughts:j}),s(t.conversation_id),l(t.conversation_id),(null==a?void 0:a.suggested_questions_after_answer.enabled)&&i(t.message_id));let n=t.data;if(t.event===ot.WORKFLOW_STARTED)w.status="running",w.nodes=[],v({content:x,files:S,workflows:w,agentThoughts:j});else if(t.event===ot.WORKFLOW_FINISHED)console.log("工作流结束",t),w.status="finished",v({content:x,files:S,workflows:w,agentThoughts:j});else if(t.event===ot.WORKFLOW_NODE_STARTED)w.nodes=[...w.nodes||[],{id:n.id,status:"running",type:n.node_type,title:n.title}],v({content:x,files:S,workflows:w,agentThoughts:j});else if(t.event===ot.WORKFLOW_NODE_FINISHED){var C;w.nodes=null==(C=w.nodes)?void 0:C.map(e=>e.id===n.id?{...e,status:"success",inputs:n.inputs,outputs:n.outputs,process_data:n.process_data,elapsed_time:n.elapsed_time,execution_metadata:n.execution_metadata}:e),v({content:x,files:S,workflows:w,agentThoughts:j})}if(t.event===ot.MESSAGE_FILE&&v({content:x+=`<img src=""${t.url} />`,files:S,workflows:w,agentThoughts:j}),t.event===ot.MESSAGE&&v({content:x+=t.answer,files:S,workflows:w,agentThoughts:j}),t.event===ot.ERROR&&(console.log("错误",t),y({name:`${t.status}: ${t.code}`,message:t.message}),z.ZP.error(t.message)),t.event===ot.AGENT_MESSAGE){let e=j[j.length-1];if(!e)continue;{let n=t.answer;e.thought+=n}v({content:x,files:S,workflows:w,agentThoughts:j})}if(t.event===ot.AGENT_THOUGHT){let e=j.findIndex(e=>e.position===t.position),n={conversation_id:t.conversation_id,id:t.id,task_id:t.task_id,position:t.position,tool:t.tool,tool_input:t.tool_input,observation:t.observation,message_files:t.message_files,message_id:t.message_id};-1!==e?j[e]=n:j.push(n),v({content:x,files:S,workflows:w,agentThoughts:j})}}else{console.log("没有数据",e);continue}}}}),{onRequest:f,messages:g,setMessages:h}=(0,s8.Z)({agent:m});return{agent:m,onRequest:f,messages:g,setMessages:h,currentTaskId:d}};var s7=n(31896),le=n(57948),lt=n(33327);function ln(e){let{info:t}=e;return(0,y.jsx)("div",{className:"text-default",children:(0,y.jsxs)("div",{className:"flex items-center justify-center flex-col",children:[(0,y.jsx)(le.Z,{className:"text-2xl text-primary"}),(0,y.jsx)("div",{className:"text-2xl font-bold mt-3",children:t.name}),(0,y.jsx)("div",{className:"text-desc text-base max-w-96 mt-3 text-center",children:t.description}),t.tags?(0,y.jsx)("div",{className:"mt-3 text-center",children:t.tags.map(e=>(0,y.jsx)(lt.Z,{className:"mb-2",children:e},e))}):null]})})}let la=e=>{let{formFilled:t,onStartConversation:n,user_input_form:a,appInfo:i,conversationId:r}=e,o=(0,R.k6)(),{appUuid:s}=(0,R.UO)(),l=new URLSearchParams(window.top.location.search),[c,u]=(0,x.useState)([]),d=(0,x.useRef)(new URLSearchParams(l)),{mode:p}=N(),[m]=on.Z.useForm(),[f,g]=(0,x.useState)({}),h=o.location.pathname.match(/^\/ai-chat\/([^/]+)$/),v=h?h[1]:"";return(0,x.useEffect)(()=>{m.resetFields()},[r]),(0,x.useEffect)(()=>{if(!t&&(null==a?void 0:a.length)||u([]),u((null==a?void 0:a.map(e=>{if(e["text-input"]){var t;let n=e["text-input"],a={type:"input",label:n.label,name:n.variable},i=new URLSearchParams(null==(t=top)?void 0:t.location.search).get(n.variable);return i&&(m.setFieldValue(n.variable,i),f[n.variable]=i,d.current.delete(n.variable)),n.required&&(a.required=!0,a.rules=[{required:!0,message:"请输入"}]),a}return{}}))||[]),l.size!==d.current.size){d.current.has("isNewCvst")&&d.current.delete("isNewCvst");let e=d.current.size?`?${d.current.toString()}`:"";"multiApp"===p?n(f):o.push(`/ai-chat${e}`)}},[a]),(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center -mt-5",children:(0,y.jsx)("div",{className:"max-w-[80vw] w-3/5  px-10 rounded-3xl bg-gray-100 box-border",children:i&&!("medsci-ask"==v||l.get("fromPlatform"))&&(null==a?void 0:a.length)?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(ln,{info:i}),!t&&(null==a?void 0:a.length)?(0,y.jsx)(on.Z,{form:m,className:"mt-6",labelCol:{span:5},children:c.map(e=>(0,y.jsx)(on.Z.Item,{name:e.name,label:e.label,required:e.required,rules:e.rules,children:"input"===e.type?(0,y.jsx)(oc.Z,{placeholder:"请输入"}):"select"===e.type?(0,y.jsx)(od.Z,{placeholder:"请选择"}):"不支持的控件类型"},e.name))}):null,(0,y.jsx)("div",{className:"mt-3 w-full flex justify-center",children:(0,y.jsx)(J.ZP,{type:"primary",icon:(0,y.jsx)(s7.Z,{}),onClick:async()=>{await m.validateFields(),n(m.getFieldsValue())},children:"开始对话"})})]}):!("medsci-ask"==v||l.get("fromPlatform"))&&(null==a?void 0:a.length)?(0,y.jsx)(ol.Z,{description:"请先配置 Dify 应用"}):null})})};function li(e){let t=(0,R.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),n=t?t[1]:"";return(0,y.jsx)("div",{className:"flex h-full items-center flex-[1.5] overflow-hidden justify-center text-primary font-semibold",children:(0,y.jsx)("div",{className:"flex items-center rounded-3xl shadow-md py-1 px-2 text-sm bg-white",children:n.includes("novax")||n.includes("elavax")?(0,y.jsx)("div",{className:"flex items-center",children:e.children}):(0,y.jsx)("a",{href:top.location.href,title:e.children,children:e.children})})})}let lr=e=>{var t,n;let{centerChildren:a,showSubscribe:i,subStatusDetail:r,selectedAppId:o,appList:s,appConfig:l}=e,{mode:c}=N(),u=Q.get("userInfo"),d=u&&(null==(t=JSON.parse(u))?void 0:t.avatar)?JSON.parse(u).avatar:"https://img.medsci.cn/web/img/user_icon.png",p=(0,R.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),m=p?p[1]:"",[f,g]=(0,x.useState)(!1);location.origin.includes("medsci.cn")||location.origin.includes("medon.com.cn");let h=e=>{e.target.src="https://img.medsci.cn/web/img/user_icon.png"},v=()=>Q.get("ai_apps_lang")?Q.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,b=async()=>{g(!1),Q.remove("userInfo",{domain:".medon.com.cn"}),Q.remove("userInfo",{domain:".medsci.cn"}),Q.remove("userInfo",{domain:"localhost"}),Q.remove("yudaoToken",{domain:"ai.medon.com.cn"}),Q.remove("yudaoToken",{domain:"ai.medsci.cn"}),Q.remove("yudaoToken",{domain:".medon.com.cn"}),Q.remove("yudaoToken",{domain:".medsci.cn"}),Q.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("conversation"),window.location.origin.includes("medsci.cn")?window.top.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.top.location.href:window.top.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.top.location.href};return(0,y.jsxs)("div",{className:"h-12 !leading-[3rem] px-4 text-base top-0 z-20 bg-white w-full shadow-sm font-semibold justify-between flex items-center box-border",children:[(0,y.jsx)("div",{className:"flex-1 overflow-hidden flex items-center justify-center",children:a}),"zh-CN"==v()&&!m.includes("novax")&&!m.includes("elavax")&&(0,y.jsx)("div",{onClick:()=>i(),className:"px-[15px] py-[4px] flex items-center h-[28px] rounded border-none mr-[8px] text-xs text-[#614018]",style:{backgroundImage:"linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"},children:"免费"==r.packageType?"升级订阅":"连续包月"==r.packageType||"连续包年"==r.packageType?"修改订阅":"订阅"}),m.includes("novax")||m.includes("elavax")?(0,y.jsx)(li,{children:o?(0,y.jsx)(y.Fragment,{children:(0,y.jsx)(er.Z,{className:"mr-2 ml-2",arrow:!0,placement:"bottom",trigger:["click"],menu:{style:{},selectedKeys:[o],items:[...(null==s?void 0:s.map(e=>{let t=o===e.id;return{key:e.id,label:(0,y.jsx)("div",{className:t?"text-primary":"text-default",children:null==e?void 0:e.info.name}),onClick:()=>{if(m.includes("novax")||m.includes("elavax")){window.top.location.href=location.origin+(window.top.location.href.includes("ai-chat")?"/ai-chat/":"/chat/")+`${null==e?void 0:e.info.appNameEn}`;return}},icon:(0,y.jsx)(en.Z,{})}}))||[]]},children:(0,y.jsxs)("div",{className:"cursor-pointer flex items-center",children:[(0,y.jsx)("span",{className:"cursor-pointer w-[75px] inline-block whitespace-nowrap overflow-hidden text-ellipsis",children:null==l||null==(n=l.info)?void 0:n.name}),(0,y.jsx)(ea.Z,{className:"ml-1"})]})})}):null}):null,(0,y.jsx)("div",{className:"px-[15px] py-[4px] mr-[8px] h-[28px] flex items-center",style:{background:"#f1f5f9"},children:(0,y.jsx)("a",{style:{borderRadius:"4px",fontSize:"12px",color:"#666",lineHeight:"1"},className:"backImg ",href:location.origin.includes(".medon.com.cn")?"https://ai.medon.com.cn/"+v():location.origin.includes(".medsci.cn")?"https://ai.medsci.cn/"+v():"/"+v(),target:"_top",children:"返回首页"})}),!u&&(0,y.jsx)("div",{className:"hover:cursor-pointer",onClick:()=>{let e=v();if(e&&"zh-CN"!==e)window.top.location.href=location.origin+"/"+e+"/login";else{var t,n;null==(t=(n=window).addLoginDom)||t.call(n)}},children:"登录"}),(0,y.jsx)(sW.Z,{placement:"bottomLeft",trigger:"hover",arrow:f,overlayStyle:{width:300,height:163},content:(0,y.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",position:"relative",paddingBottom:"40px"},children:[(0,y.jsx)("a",{className:"exit text-right w-full text-[#333333]",onClick:b,children:"退出"}),(0,y.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},className:"iconHeader bg-write",children:[u&&JSON.parse(u||"").userId&&(0,y.jsx)("img",{src:d,onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),style:{width:"60px",height:"60px"},onError:h,alt:"avatar"}),(0,y.jsx)("span",{className:"account",children:u&&JSON.parse(u||"").userName})]})]}),children:(0,y.jsx)("a",{href:"#",children:(0,y.jsx)("div",{className:"img-area  leading-none",children:u&&JSON.parse(u||"").userId&&(d?(0,y.jsx)("img",{src:d,onError:h,onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),style:{width:"32px",height:"32px"},alt:"avatar"}):(0,y.jsx)(oh.Z,{}))})})})]})};var lo=n(69935);let ls=e=>{var t,n,a;let{userInfo:i,currentItem:r,onClose:o,appConfig:s}=e;i=JSON.parse(i)||{};let[l,c]=(0,x.useState)(!1),[u,d]=(0,x.useState)({}),[p,m]=(0,x.useState)(null),[f,g]=(0,x.useState)(""),[h,v]=(0,x.useState)(i.avatar||"https://img.medsci.cn/web/img/user_icon.png"),[b,S]=(0,x.useState)(window.innerWidth>768),[w,j]=(0,x.useState)(null),[k,C]=(0,x.useState)(null),N=e=>({"tool.Support_Alipay_Payment":"支持支付宝支付","tool.Meisi_Account":"梅斯账号","tool.Please_activate_after_reading_and_agreeing_to_the_agreement":"请阅读并同意协议后激活","tool.Free_Trial":"免费试用","market.subscribe":"订阅","tool.Month":"月","tool.Quarter":"季","tool.Year":"年","tool.sS":"订阅成功"})[e]||e,A=new W;window.location.origin.includes("medon.com.cn")||window.location.origin.includes("medsci.cn");let T=()=>{S(window.innerWidth>768)},_=(e,t)=>{d(e),m(t),(null==e?void 0:e.coinType)=="人民币"&&0!==e.feePrice&&O(e,s.id)},E=e=>{let t=setInterval(async()=>{var n;let a=await A.getSubOrder({piId:`${e}`},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});(null==a||null==(n=a.data)?void 0:n.payStatus)==="PAID"&&(window.location.reload(),clearInterval(t))},2e3);C(t)},I=()=>B.Z.get("ai_apps_lang")?B.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,O=async(e,t)=>{if(!(null==e?void 0:e.coinType))return void z.ZP.warning("请选择订阅服务周期");let n=I();if(i.userId){let n={appUuid:t||"",priceId:e.priceId,monthNum:e.monthNum,packageKey:e.packageKey,packageType:e.type};try{c(!0);let t=await A.createSubscription(n,{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});if((null==t?void 0:t.code)==0)if(c(!1),"人民币"==e.coinType&&0!==e.feePrice){let e=t.data,n=window.location.origin,a=(n.includes(".medsci.cn")||n.includes(".medon.com.cn"),`${n}/payLink/${encodeURIComponent(e)}`);g(a),j(JSON.parse(e).piId),k&&clearInterval(k),E(JSON.parse(e).piId)}else z.ZP.success(N("tool.sS")),setTimeout(()=>{window.top.location.href=t.data},1e3)}catch(e){c(!1),console.error(e)}}else n&&"zh-CN"!==n?window.top.location.href=location.origin+"/"+n+"/login":console.log("Trigger login")};return(0,x.useEffect)(()=>{var e;return"写作"===r.appType&&localStorage.setItem(`appWrite-${s.id}`,JSON.stringify({appUuid:s.id,directoryMd:r.directoryMd})),T(),window.addEventListener("resize",T),b&&(null==(e=r.feeTypes)?void 0:e.length)===1&&_(r.feeTypes[0],0),()=>{window.removeEventListener("resize",T),k&&clearInterval(k)}},[r,b]),(0,y.jsx)("div",{id:"app",children:(0,y.jsxs)("div",{className:"scale",children:[(0,y.jsxs)("div",{className:"micro_header",children:[(0,y.jsx)("div",{className:"micro_left",children:(0,y.jsxs)("div",{className:"avatar",children:[(0,y.jsx)("img",{src:h,onError:()=>{v("https://img.medsci.cn/web/img/user_icon.png")},alt:""}),(0,y.jsx)("span",{className:"t1",children:i.realName||i.userName})]})}),(0,y.jsx)("div",{className:"micro_right",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png",alt:"",onClick:()=>{k&&clearInterval(k),null==o||o(!1)}})})]}),(0,y.jsxs)("div",{className:"micro_main",children:[(0,y.jsx)("div",{className:"micro_main_top",children:(0,y.jsx)("div",{className:"micro_main-sp",children:(0,y.jsx)("div",{className:"micro_main_temp",children:((null==(t=r.feeTypes[0])?void 0:t.coinType)=="美元"||(null==(n=r.feeTypes)?void 0:n.length)>1)&&(0,y.jsx)("div",{className:"swiper-vip",children:null==(a=r.feeTypes)?void 0:a.map((e,t)=>(0,y.jsxs)("div",{className:"swiper-vip-item",onClick:()=>_(e,t),children:[(0,y.jsx)("div",{className:"newer",style:{left:t%4==0&&0!==t?"6px":"-1px"}}),(0,y.jsxs)("div",{className:`swiper-vip-item-child ${p===t?"sactvie":""}`,children:[(0,y.jsx)("div",{className:"title",children:e.type}),(0,y.jsxs)("div",{className:"pricePc",children:[(0,y.jsx)("span",{children:"人民币"===e.coinType?"\xa5":"$"})," ",e.feePrice]})]})]},t))})})})}),(0,y.jsx)("div",{className:"micro_main_middle",children:(0,y.jsxs)("div",{className:"micro_main_middle_banner",children:[(0,y.jsxs)("div",{className:"micro_main_middle_title",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png",alt:""}),r.name]}),(0,y.jsx)("div",{className:"micro_main_middle_content",children:r.description})]})}),u.coinType&&0!==u.feePrice&&(null==u?void 0:u.coinType)=="人民币"&&(0,y.jsx)("div",{className:"micro_main_bottom",children:(0,y.jsx)("div",{className:"micro_pay",children:(0,y.jsxs)("div",{className:"micro_pay_right",children:[l&&(0,y.jsx)("div",{className:"noQrCode"}),!l&&f&&(0,y.jsx)("div",{className:"qr-code",children:(0,y.jsx)(lo.Q,{value:f,size:131,fgColor:"#000",level:"L"})}),(0,y.jsxs)("div",{className:"price",children:[(0,y.jsxs)("div",{className:"micro_way",children:[(0,y.jsx)("div",{className:"box",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})}),(0,y.jsx)("span",{children:N("tool.Support_Alipay_Payment")})]}),(0,y.jsxs)("span",{className:"t1",children:[N("tool.Support_Alipay_Payment"),(0,y.jsx)("span",{className:"bd",children:u.feePrice}),"人民币"===u.coinType?"\xa5":"$","/",3===u.monthNum?N("tool.Quarter"):12===u.monthNum?N("tool.Year"):N("tool.Month")]}),(0,y.jsxs)("span",{className:"t2",children:[N("tool.Meisi_Account"),"：",i.userName]}),(0,y.jsxs)("span",{className:"t3",onClick:()=>{window.open("https://www.medsci.cn/about/index.do?id=27")},children:[N("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"),(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})]})]})]})})}),u.coinType&&0===u.feePrice&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>O(u,s.id),children:N("tool.Free_Trial")})}),u.coinType&&u.feePrice>0&&(null==u?void 0:u.coinType)=="美元"&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>O(u,s.id),children:N("market.subscribe")})})]})]})})};var ll=n(69922);let lc=e=>{var t;let{userInfo:n={},currentItem:a={},onClose:i}=e,[r,o]=(0,x.useState)(!0),[s,l]=(0,x.useState)(!0),[c,u]=(0,x.useState)(!1),[d,p]=(0,x.useState)({}),[m,f]=(0,x.useState)(!1),[g,h]=(0,x.useState)(!1),[v,b]=(0,x.useState)(!1),[S,w]=(0,x.useState)(!1),[j,k]=(0,x.useState)((null==n?void 0:n.avatar)||"https://img.medsci.cn/web/img/user_icon.png"),C=(0,x.useRef)(null),N=new W;window.location.origin.includes("medon.com.cn")||window.location.origin.includes("medsci.cn"),(0,x.useEffect)(()=>{var e,t;let i=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${i}px`),"写作"===a.appType&&localStorage.setItem(`appWrite-${a.appUuid}`,JSON.stringify({appUuid:a.appUuid,directoryMd:a.directoryMd})),(null==(e=a.feeTypes)?void 0:e.length)===1&&a.feeTypes[0].feePrice>=0&&p(a.feeTypes[0]),(null==(t=JSON.parse(n))?void 0:t.userId)&&u(!0),"medsci"===new URLSearchParams(window.location.search).get("source")&&h(!0)},[a,n]);let A=()=>navigator.userAgent.includes("medsci_app"),T=()=>B.Z.get("ai_apps_lang")?B.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,_=()=>{let e=T();if(e&&"zh-CN"!==e)window.top.location.href=location.origin+"/"+e+"/login";else{var t,n;null==(t=(n=window).addLoginDom)||t.call(n)}},E=e=>{p(e)},I=async(e,t,a)=>{var i;if(!v&&a){w(!0),setTimeout(()=>w(!1),500);return}let r=T();if(null==(i=JSON.parse(n))?void 0:i.userId){let n={appUuid:t||"",priceId:e.priceId,monthNum:e.monthNum,packageKey:e.packageKey,packageType:e.type};try{let t=await N.createSubscription(n,{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});if(0==t.code)if("人民币"==e.coinType&&0!==e.feePrice){let e=await N.createAliSub(JSON.parse(t.data));e&&0==e.code&&(window.top.location.href=e.data)}else z.ZP.success("订阅成功"),setTimeout(()=>{window.top.location.href=t.data},1e3)}catch(e){z.ZP.error("订阅失败")}}else r&&"zh-CN"!==r?window.top.location.href=location.origin+"/"+r+"/login":_()};return(0,y.jsxs)("div",{className:`vip ${A()?"sp":""}`,children:[!A()&&(0,y.jsx)("div",{className:"vip-head text-center py-2 text-lg font-medium text-gray-800",children:a.name}),(0,y.jsxs)("div",{className:"vip-introduce bg-gray-900 pt-9 pb-4 relative",children:[(0,y.jsx)("img",{className:"crown absolute right-1 top-0 h-9 object-contain",src:"https://static.medsci.cn/public-image/ms-image/1dcd7d10-58af-11ec-8e2f-1389d01aad85_crown.png",alt:""}),(0,y.jsx)("div",{className:"box mx-3 flex items-center justify-between bg-[url('https://static.medsci.cn/public-image/ms-image/21ad8b80-58b1-11ec-8e2f-1389d01aad85_vip-h5.png')] bg-cover p-3 rounded",children:c?(0,y.jsxs)("div",{className:"box-left flex items-center",children:[(0,y.jsx)("img",{className:"avatar w-8 h-8 rounded-full mr-3",src:j,alt:"",onError:()=>{k("https://img.medsci.cn/web/img/user_icon.png")}}),(0,y.jsx)("div",{className:"box-word",children:(0,y.jsx)("span",{className:"t1 text-lg text-[#58342c]",children:JSON.parse(n).realName||JSON.parse(n).userName})})]}):(0,y.jsxs)("div",{className:"box-left-1 flex items-center",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/web/img/user_icon.png",alt:"",className:"w-8 h-8 rounded-full mr-3"}),(0,y.jsxs)("div",{className:"left2",children:[(0,y.jsx)("span",{className:"t1 text-lg text-[#58342c] cursor-pointer",onClick:_,children:"立即登录"}),(0,y.jsx)("span",{className:"t2 text-sm text-[#7f5947] mt-1",children:"请登录后购买"})]})]})})]}),(0,y.jsxs)("div",{className:"vip-main bg-white rounded-t-3xl -mt-14 z-10 pb-24",children:[(0,y.jsx)("div",{className:"vip-one pl-3",children:(0,y.jsx)("div",{className:"big overflow-auto mr-3",children:(0,y.jsx)("ul",{ref:C,className:"flex mt-2 mb-1",children:null==(t=a.feeTypes)?void 0:t.map((e,t)=>(0,y.jsxs)("li",{className:`min-w-[135px] w-[135px] bg-white border border-gray-300 rounded-lg mr-3 text-center cursor-pointer ${e.type===d.type?"sactvie":""}`,onClick:()=>E(e),children:[(0,y.jsx)("div",{className:"title text-center ellipsis-2-lines mx-auto mt-6 max-w-[102.5px] text-sm font-medium text-gray-800",children:e.type}),(0,y.jsxs)("div",{className:"prices text-2xl font-bold text-orange-400 mt-2 mb-6",children:[(0,y.jsx)("span",{className:"text-lg",children:"人民币"===e.coinType?"\xa5":"$"}),e.feePrice]}),e.originalPrice&&(0,y.jsxs)("div",{className:"isfava relative text-xs text-gray-500 line-through",children:["人民币"===e.coinType?"\xa5":"$",e.feePrice]})]},t))})})}),(0,y.jsx)("div",{className:"vip-two border border-gray-200 rounded m-3 p-2",children:(0,y.jsxs)("div",{className:"vip-two_banner bg-[url('https://img.medsci.cn/202503/48a49f899b4544e39a864d1821b3555a-lRXRIQx3q1VB.png')] bg-cover p-3",children:[(0,y.jsxs)("div",{className:"vip-two_title flex items-center text-sm font-medium text-orange-600",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png",alt:"",className:"w-2 h-2 mx-1"}),a.name]}),(0,y.jsx)("div",{className:"vip-two_content bg-white p-3 text-sm text-gray-700 leading-5 max-h-[calc(var(--vh)*20)] overflow-auto rounded",children:a.description})]})}),d.feePrice>0&&(null==d?void 0:d.coinType)=="人民币"&&(0,y.jsx)("div",{className:"vip-three mt-3",children:(0,y.jsxs)("div",{className:`pay mx-3 p-4 rounded-lg bg-gradient-to-b from-[#fffaf6] to-[#fff8f0] ${m?"h-28":""}`,children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:"",className:"w-15 mb-3"}),(0,y.jsxs)("div",{className:"item flex justify-between items-center bg-white border border-orange-300 rounded p-2 mb-3 cursor-pointer",onClick:()=>{o(!1),l(!0)},children:[(0,y.jsxs)("div",{className:"item-left flex items-center",children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:"",className:"w-5 h-5 mr-2"}),(0,y.jsx)("span",{className:"text-sm text-gray-800",children:"支付宝支付"})]}),(0,y.jsx)("div",{className:"item-right w-5 h-5",children:s&&(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:"",className:"w-full"})})]})]})})]}),d.feePrice>=0&&(0,y.jsx)("div",{className:"vip-pay btns fixed bottom-0 w-full bg-white shadow-lg flex justify-around items-center h-24 z-20",children:0!==d.feePrice&&(null==d?void 0:d.coinType)=="人民币"?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{className:"pay-left ml-2",children:[(0,y.jsx)("div",{className:"t1 text-sm font-medium text-orange-400 mb-1",children:a.name}),(0,y.jsxs)("div",{className:`t2 flex items-start text-xs text-gray-500 ${S?"shake":""}`,children:[(0,y.jsx)(ll.Z,{checked:v,onChange:e=>{b(e.target.checked)},style:{fontSize:"14px"},className:"mr-1"}),(0,y.jsxs)("span",{onClick:()=>{let e="https://www.medsci.cn/about/index.do?id=27";A()?window.top.location.href=e:window.open(e)},className:"cursor-pointer",children:["请在阅读并同意",(0,y.jsx)("span",{className:"text-blue-500",children:"协议"}),"后开通"]})]})]}),(0,y.jsx)("div",{className:"pay-right mr-2 w-48 h-12 bg-gradient-to-r from-[#fde39b] to-[#fbd786] rounded-full flex items-center justify-center cursor-pointer",onClick:()=>I(d,a.appUuid,"ali"),children:(0,y.jsxs)("span",{className:"text-sm text-[#614018]",children:[d.feePrice,"元确认协议并支付"]})})]}):(0,y.jsx)(J.ZP,{type:"primary",className:"w-48 h-12 rounded-full",onClick:()=>I(d,a.appUuid,""),children:0===d.feePrice?"免费试用":"订阅"})})]})},lu=e=>{var t,n,a,i,r,o;let{userInfo:s,currentItem:l,onClose:c,appConfig:u,subStatusDetail:d}=e;s=s&&JSON.parse(s);let[p,m]=(0,x.useState)(!1),[f,g]=(0,x.useState)({}),[h,v]=(0,x.useState)(null),[b,S]=(0,x.useState)(""),[w,j]=(0,x.useState)(0),[k,C]=(0,x.useState)((null==s?void 0:s.avatar)||"https://img.medsci.cn/web/img/user_icon.png"),[N,A]=(0,x.useState)(window.innerWidth>768),[T,_]=(0,x.useState)(null),[E,I]=(0,x.useState)(null);(0,x.useEffect)(()=>{var e;l="zh-CN"==R()?d:l,(null==d||null==(e=d.feeTypes)?void 0:e.length)>0&&"zh-CN"==R()&&(null==d||d.feeTypes.forEach((e,t)=>{d.packageType==e.type&&(v(t),g(e))}))},[]);let O=e=>({"tool.Support_Alipay_Payment":"支持支付宝支付","tool.Meisi_Account":"梅斯账号","tool.Please_activate_after_reading_and_agreeing_to_the_agreement":"请阅读并同意协议后激活","tool.Free_Trial":"免费试用","market.subscribe":"订阅","tool.Month":"月","tool.Quarter":"季","tool.Year":"年","tool.sS":"订阅成功"})[e]||e,P=new W;window.location.origin.includes("medon.com.cn")||window.location.origin.includes("medsci.cn");let M=()=>{window.open("https://www.medsci.cn/about/index.do?id=27")},L=()=>{A(window.innerWidth>768)},D=()=>{E&&clearInterval(E),null==c||c(!1)},F=(e,t)=>{g(e),v(t),(null==e?void 0:e.coinType)=="人民币"&&0!==e.feePrice&&U(e,u.id)},$=e=>{let t=setInterval(async()=>{var n;let a=await P.getSubOrder({piId:`${e}`},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});(null==a||null==(n=a.data)?void 0:n.payStatus)=="PAID"&&(window.location.reload(),clearInterval(t))},2e3);I(t)},R=()=>B.Z.get("ai_apps_lang")?B.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,Z=async()=>{let e=await P.freeLimit({},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});j(null==e?void 0:e.data)},U=async(e,t)=>{if(!(null==e?void 0:e.coinType))return void z.ZP.warning("请选择订阅服务周期");let n=R();if(null==s?void 0:s.userId){let n={appUuid:t,priceId:e.priceId,monthNum:e.monthNum,packageKey:e.packageKey,packageType:e.type};try{m(!0);let t=await P.createSubscription(n,{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});if((null==t?void 0:t.code)==0)if(m(!1),"人民币"==e.coinType&&0!==e.feePrice){let e=t.data,n=window.location.origin,a=(n.includes(".medsci.cn")||n.includes(".medon.com.cn"),`${n}/payLink/${encodeURIComponent(e)}`);S(a),_(JSON.parse(e).piId),$(JSON.parse(e).piId)}else z.ZP.success(O("tool.sS")),setTimeout(()=>{window.top.location.href=t.data},1e3)}catch(e){m(!1),console.error(e)}}else if(n&&"zh-CN"!=n)window.top.location.href=location.origin+"/"+n+"/login";else{var a,i;null==(a=(i=window).addLoginDom)||a.call(i)}};return(0,x.useEffect)(()=>{var e;return Z(),"写作"==l.appType&&localStorage.setItem(`appWrite-${u.id}`,JSON.stringify({appUuid:u.id,directoryMd:l.directoryMd})),L(),window.addEventListener("resize",L),N&&(null==(e=l.feeTypes)?void 0:e.length)==1&&F(l.feeTypes[0],0),()=>{window.removeEventListener("resize",L),E&&clearInterval(E)}},[l,N]),(0,y.jsx)("div",{id:"app",children:(0,y.jsxs)("div",{className:"scale",children:[(0,y.jsxs)("div",{className:"micro_header",children:[(0,y.jsx)("div",{className:"micro_left",children:(0,y.jsxs)("div",{className:"avatar",children:[(0,y.jsx)("img",{src:k,onError:()=>{C("https://img.medsci.cn/web/img/user_icon.png")},alt:""}),(0,y.jsx)("span",{className:"t1",children:(null==s?void 0:s.realName)||(null==s?void 0:s.userName)})]})}),(0,y.jsx)("div",{className:"micro_right",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png",alt:"",onClick:D})})]}),(0,y.jsxs)("div",{className:"micro_main",children:[(0,y.jsx)("div",{className:"micro_main_top",children:(0,y.jsx)("div",{className:"micro_main-sp",children:(0,y.jsxs)("div",{className:"micro_main_temp",children:[((null==(t=l.feeTypes[0])?void 0:t.coinType)=="美元"||(null==(n=l.feeTypes)?void 0:n.length)>1)&&"zh-CN"==R()&&(0,y.jsx)("div",{className:"swiper-vip",children:null==(a=l.feeTypes)?void 0:a.map((e,t)=>(0,y.jsxs)("div",{className:`swiper-vip-item ${(null==d?void 0:d.packageType)=="连续包月"&&"免费"==e.type||(null==d?void 0:d.packageType)=="连续包月"&&"连续包年"==e.type||(null==d?void 0:d.packageType)=="连续包年"&&"免费"==e.type||(null==d?void 0:d.packageType)=="连续包年"&&"连续包月"==e.type?"noClick":""}`,onClick:()=>F(e,t),children:[(0,y.jsx)("div",{className:"newer",style:{left:t%4==0&&0!==t?"6px":"-1px"}}),(0,y.jsxs)("div",{className:`swiper-vip-item-child ${h==t?"sactvie":""}`,children:[(0,y.jsx)("div",{className:"title",children:e.type}),(0,y.jsxs)("div",{className:"pricePc",children:[(0,y.jsx)("span",{children:"人民币"==e.coinType?"\xa5":"$"})," ",e.feePrice]})]})]},t))}),((null==(i=l.feeTypes[0])?void 0:i.coinType)=="美元"||(null==(r=l.feeTypes)?void 0:r.length)>1)&&"zh-CN"!=R()&&(0,y.jsx)("div",{className:"swiper-vip",children:null==(o=l.feeTypes)?void 0:o.map((e,t)=>(0,y.jsxs)("div",{className:"swiper-vip-item",onClick:()=>F(e,t),children:[(0,y.jsx)("div",{className:"newer",style:{left:t%4==0&&0!==t?"6px":"-1px"}}),(0,y.jsxs)("div",{className:`swiper-vip-item-child ${h==t?"sactvie":""}`,children:[(0,y.jsx)("div",{className:"title",children:e.type}),(0,y.jsxs)("div",{className:"pricePc",children:[(0,y.jsx)("span",{children:"人民币"==e.coinType?"\xa5":"$"})," ",e.feePrice]})]})]},t))})]})})}),R()&&"zh-CN"==R()?(0,y.jsx)("div",{className:"micro_main_middle",children:(0,y.jsxs)("div",{className:"micro_main_middle_banner",children:[(0,y.jsxs)("div",{className:"micro_main_middle_title",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"}),"梅斯小智 订阅说明"]}),(0,y.jsxs)("div",{className:"micro_main_middle_content",children:[(0,y.jsxs)("div",{children:["免费：每个自然月内，每个智能体的使用上限",w,"次。次月开始重新计次。"]}),(0,y.jsx)("div",{children:"连续包月：订阅之日起一个月内，每个智能体不限使用次数。"}),(0,y.jsx)("div",{children:"连续包年：订阅之日起一年内，每个智能体不限使用次数"})]})]})}):(0,y.jsx)("div",{className:"micro_main_middle",children:(0,y.jsxs)("div",{className:"micro_main_middle_banner",children:[(0,y.jsxs)("div",{className:"micro_main_middle_title",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png",alt:""}),l.name]}),(0,y.jsx)("div",{className:"micro_main_middle_content",children:l.description})]})}),R()&&"zh-CN"==R()?(0,y.jsxs)("div",{className:"micro_main_bottom onborder",children:[("1"==d.subStatus||"3"==d.subStatus)&&f.type==d.packageType&&(0,y.jsxs)("div",{className:"result",children:[d.subAt," 已订阅"]}),"1"==d.subStatus&&"免费"==d.packageType&&"免费"==f.type&&(0,y.jsx)("div",{className:"result",children:"免费使用中…"}),"3"==d.subStatus&&(0,y.jsxs)("div",{className:"result",children:[d.unSubAt," 取消订阅"]}),"3"==d.subStatus&&(0,y.jsxs)("div",{className:"result",children:["您的订阅可使用至 ",d.expireAt]}),"连续包月"==d.packageType&&"1"==d.subStatus&&(0,y.jsx)("div",{className:"result",children:"连续包月中…"}),"连续包年"==d.packageType&&"1"==d.subStatus&&(0,y.jsx)("div",{className:"result",children:"连续包年中…"}),(0,y.jsxs)("div",{className:"btns",children:["连续包月"==d.packageType&&"1"==d.subStatus?(0,y.jsx)("button",{className:"cursor-pointer",onClick:()=>{sQ.Vq.confirm({title:"提示",message:`取消包月在${d.expireAt}号生效，再次使用需要重新订阅。是否确认取消？`,confirmButtonColor:"#D7813F",onCancel:()=>{},onConfirm:async()=>{await P.cancelSubscription({},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`}),D()}})},children:"取消包月"}):null,"连续包年"==d.packageType&&"1"==d.subStatus?(0,y.jsx)("button",{className:"cursor-pointer",onClick:()=>{sQ.Vq.confirm({title:"提示",message:`取消包月在${d.expireAt}号生次生效，再次使用需要重新订阅。是否确认取消？`,confirmButtonColor:"#D7813F",onCancel:()=>{D()},onConfirm:async()=>{await P.cancelSubscription({},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`}),D()}})},children:"取消包年"}):null]}),(0,y.jsx)("div",{className:"micro_pay",children:f.feePrice>0&&"免费"!=f.type&&"0"==d.subStatus||"免费"!=f.type&&"2"==d.subStatus||"免费"==d.packageType&&"免费"!=f.type?(0,y.jsx)("div",{className:"micro_pay",children:(0,y.jsxs)("div",{className:"micro_pay_right",children:[p&&(0,y.jsx)("div",{className:"noQrCode"}),!p&&b&&(0,y.jsx)("div",{className:"qr-code",children:(0,y.jsx)(lo.Q,{value:b,size:131,fgColor:"#000",level:"L"})}),(0,y.jsxs)("div",{className:"price",children:[(0,y.jsxs)("div",{className:"micro_way",children:[(0,y.jsx)("div",{className:"box",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})}),(0,y.jsx)("span",{children:O("tool.Support_Alipay_Payment")})]}),(0,y.jsxs)("span",{className:"t1",children:[O("tool.Support_Alipay_Payment"),(0,y.jsx)("span",{className:"bd",children:f.feePrice}),"人民币"==f.coinType?"\xa5":"$","/",3==f.monthNum?O("tool.Quarter"):12==f.monthNum?O("tool.Year"):O("tool.Month")]}),(0,y.jsxs)("span",{className:"t2",children:[O("tool.Meisi_Account"),"：",null==s?void 0:s.userName]}),(0,y.jsxs)("span",{className:"t3",onClick:M,children:[O("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"),(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})]})]})]})}):null})]}):null,f.coinType&&0!==f.feePrice&&"zh-CN"!=R()&&(null==f?void 0:f.coinType)=="人民币"&&(0,y.jsx)("div",{className:"micro_main_bottom",children:(0,y.jsx)("div",{className:"micro_pay",children:(0,y.jsxs)("div",{className:"micro_pay_right",children:[p&&(0,y.jsx)("div",{className:"noQrCode"}),!p&&b&&(0,y.jsx)("div",{className:"qr-code",children:(0,y.jsx)(lo.Q,{value:b,size:131,fgColor:"#000",level:"L"})}),(0,y.jsxs)("div",{className:"price",children:[(0,y.jsxs)("div",{className:"micro_way",children:[(0,y.jsx)("div",{className:"box",children:(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:""})}),(0,y.jsx)("span",{children:O("tool.Support_Alipay_Payment")})]}),(0,y.jsxs)("span",{className:"t1",children:[O("tool.Support_Alipay_Payment"),(0,y.jsx)("span",{className:"bd",children:f.feePrice}),"人民币"==f.coinType?"\xa5":"$","/",3==f.monthNum?O("tool.Quarter"):12==f.monthNum?O("tool.Year"):O("tool.Month")]}),(0,y.jsxs)("span",{className:"t2",children:[O("tool.Meisi_Account"),"：",null==s?void 0:s.userName]}),(0,y.jsxs)("span",{className:"t3",onClick:M,children:[O("tool.Please_activate_after_reading_and_agreeing_to_the_agreement"),(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png",alt:""})]})]})]})})}),f.coinType&&"zh-CN"==R()&&0==f.feePrice&&("0"==d.subStatus||"2"==d.subStatus)&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>U(f,u.id),children:O("tool.Free_Trial")})}),f.coinType&&"zh-CN"!=R()&&0==f.feePrice&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>U(f,u.id),children:O("tool.Free_Trial")})}),f.coinType&&f.feePrice>0&&(null==f?void 0:f.coinType)=="美元"&&(0,y.jsx)("div",{className:"btns",children:(0,y.jsx)("button",{className:"subscribe-btn",onClick:()=>U(f,u.id),children:O("market.subscribe")})})]})]})})},ld=e=>{var t,n;let{userInfo:a,currentItem:i={},onClose:r,subStatusDetail:o={}}=e,[s,l]=(0,x.useState)(!0),[c,u]=(0,x.useState)(!0),[d,p]=(0,x.useState)(!1),[m,f]=(0,x.useState)({}),[g,h]=(0,x.useState)(!1),[v,b]=(0,x.useState)(!1),[S,w]=(0,x.useState)(!1),[j,k]=(0,x.useState)(!1),[C,N]=(0,x.useState)(0),[A,T]=(0,x.useState)((null==a?void 0:a.avatar)||"https://img.medsci.cn/web/img/user_icon.png"),_=(0,x.useRef)(null),E=new W;window.location.origin.includes("medon.com.cn")||window.location.origin.includes("medsci.cn"),(0,x.useEffect)(()=>{var e,t;D();let n=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${n}px`),"写作"==i.appType&&localStorage.setItem(`appWrite-${i.appUuid}`,JSON.stringify({appUuid:i.appUuid,directoryMd:i.directoryMd})),(null==(e=i.feeTypes)?void 0:e.length)==1&&i.feeTypes[0].feePrice>=0&&f(i.feeTypes[0]),a&&(null==(t=JSON.parse(a))?void 0:t.userId)&&p(!0),"medsci"==new URLSearchParams(window.top.location.search).get("source")&&b(!0)},[i,a]),(0,x.useEffect)(()=>{var e;i.feeTypes="zh-CN"==O()?null==o?void 0:o.feeTypes:i.feeTypes,(null==o||null==(e=o.feeTypes)?void 0:e.length)>0&&"zh-CN"==O()&&(null==o||o.feeTypes.forEach((e,t)=>{(null==o?void 0:o.packageType)==e.type&&f(e)}))},[]);let I=()=>navigator.userAgent.includes("medsci_app"),O=()=>B.Z.get("ai_apps_lang")?B.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,P=()=>{let e=O();if(e&&"zh-CN"!=e)window.top.location.href=location.origin+"/"+e+"/login";else{var t,n;null==(t=(n=window).addLoginDom)||t.call(n)}},M=()=>{l(!1),u(!0)},L=()=>{let e="https://www.medsci.cn/about/index.do?id=27";I()?window.top.location.href=e:window.open(e)},D=async()=>{let e=await E.freeLimit({},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});N(null==e?void 0:e.data)},F=e=>{f(e)},$=e=>{w(e.target.checked)},R=async(e,t,n)=>{var i;if(!S&&n){k(!0),setTimeout(()=>k(!1),500);return}let r=O();if(a&&(null==(i=JSON.parse(a))?void 0:i.userId)){let n={appUuid:t,priceId:e.priceId,monthNum:e.monthNum,packageKey:e.packageKey,packageType:e.type};try{let t=await E.createSubscription(n,{Authorization:`Bearer ${B.Z.get("yudaoToken")}`});if(0==t.code)if("人民币"==e.coinType&&0!==e.feePrice){let e=await E.createAliSub(JSON.parse(t.data));e&&0==e.code&&(window.top.location.href=e.data)}else setTimeout(()=>{window.top.location.href=t.data},1e3)}catch(e){z.ZP.error("订阅失败")}}else r&&"zh-CN"!=r?window.top.location.href=location.origin+"/"+r+"/login":P()};return(0,y.jsxs)("div",{className:`vip ${I()?"sp":""}`,children:[!I()&&(0,y.jsx)("div",{className:"vip-head text-center py-2 text-lg font-medium text-gray-800",children:i.name}),(0,y.jsxs)("div",{className:"vip-introduce bg-gray-900 pt-9 pb-4 relative",children:[(0,y.jsx)("img",{className:"crown absolute right-1 top-0 h-9 object-contain",src:"https://static.medsci.cn/public-image/ms-image/1dcd7d10-58af-11ec-8e2f-1389d01aad85_crown.png",alt:""}),(0,y.jsx)("div",{className:"box mx-3 flex items-center justify-between bg-[url('https://static.medsci.cn/public-image/ms-image/21ad8b80-58b1-11ec-8e2f-1389d01aad85_vip-h5.png')] bg-cover p-3 rounded",children:d?(0,y.jsxs)("div",{className:"box-left flex items-center",children:[(0,y.jsx)("img",{className:"avatar w-8 h-8 rounded-full mr-3",src:A,alt:"",onError:()=>{T("https://img.medsci.cn/web/img/user_icon.png")}}),(0,y.jsx)("div",{className:"box-word",children:(0,y.jsx)("span",{className:"t1 text-lg text-[#58342c]",children:JSON.parse(a).realName||JSON.parse(a).userName})})]}):(0,y.jsxs)("div",{className:"box-left-1 flex items-center",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/web/img/user_icon.png",alt:"",className:"w-8 h-8 rounded-full mr-3"}),(0,y.jsxs)("div",{className:"left2",children:[(0,y.jsx)("span",{className:"t1 text-lg text-[#58342c] cursor-pointer",onClick:P,children:"立即登录"}),(0,y.jsx)("span",{className:"t2 text-sm text-[#7f5947] mt-1",children:"请登录后购买"})]})]})})]}),(0,y.jsxs)("div",{style:{paddingBottom:"zh-CN"==O()&&("3"==o.subStatus||"1"==o.subStatus&&"免费"==o.packageType)&&m.type==o.packageType?"0":""},className:"vip-main bg-white rounded-t-3xl -mt-14 z-10 ",children:[(0,y.jsx)("div",{className:"vip-one pl-3",children:(0,y.jsx)("div",{className:"big overflow-auto mr-3",children:"zh-CN"==O()?(0,y.jsx)("ul",{ref:_,className:"flex mt-2 mb-1",children:null==(t=i.feeTypes)?void 0:t.map((e,t)=>(0,y.jsxs)("li",{className:`min-w-[135px] w-[135px] bg-white border border-gray-300 rounded-lg mr-3 text-center cursor-pointer ${e.type==m.type?"sactvie":""} ${(null==o?void 0:o.packageType)=="连续包月"&&"免费"==e.type||(null==o?void 0:o.packageType)=="连续包月"&&"连续包年"==e.type||(null==o?void 0:o.packageType)=="连续包年"&&"免费"==e.type||(null==o?void 0:o.packageType)=="连续包年"&&"连续包月"==e.type?"noClick":""}`,onClick:()=>F(e),children:[(0,y.jsx)("div",{className:"title text-center ellipsis-2-lines mx-auto mt-6 max-w-[102.5px] text-sm font-medium text-gray-800",children:e.type}),(0,y.jsxs)("div",{className:"prices text-2xl font-bold text-orange-400 mt-2 mb-6",children:[(0,y.jsx)("span",{className:"text-lg",children:"人民币"==e.coinType?"\xa5":"$"}),e.feePrice]}),e.originalPrice&&(0,y.jsxs)("div",{className:"isfava relative text-xs text-gray-500 line-through",children:["人民币"==e.coinType?"\xa5":"$",e.feePrice]})]},t))}):(0,y.jsx)("ul",{ref:_,className:"flex mt-2 mb-1",children:null==(n=i.feeTypes)?void 0:n.map((e,t)=>(0,y.jsxs)("li",{className:`min-w-[135px] w-[135px] bg-white border border-gray-300 rounded-lg mr-3 text-center cursor-pointer ${e.type==m.type?"sactvie":""}`,onClick:()=>F(e),children:[(0,y.jsx)("div",{className:"title text-center ellipsis-2-lines mx-auto mt-6 max-w-[102.5px] text-sm font-medium text-gray-800",children:e.type}),(0,y.jsxs)("div",{className:"prices text-2xl font-bold text-orange-400 mt-2 mb-6",children:[(0,y.jsx)("span",{className:"text-lg",children:"人民币"==e.coinType?"\xa5":"$"}),e.feePrice]}),e.originalPrice&&(0,y.jsxs)("div",{className:"isfava relative text-xs text-gray-500 line-through",children:["人民币"==e.coinType?"\xa5":"$",e.feePrice]})]},t))})})}),"zh-CN"==O()?(0,y.jsxs)("div",{children:[(0,y.jsx)("div",{className:"vip-two",style:{paddingBottom:"0"},children:(0,y.jsxs)("div",{className:"vip-two_banner",children:[(0,y.jsxs)("div",{className:"vip-two_title",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"}),"梅斯小智 订阅说明"]}),(0,y.jsxs)("div",{className:"vip-two_content",children:[(0,y.jsxs)("div",{children:["免费：每个自然月内，每个智能体的使用上限",C,"次。次月开始重新计次。"]}),(0,y.jsx)("div",{children:"连续包月：订阅之日起一个月内，每个智能体不限使用次数。"}),(0,y.jsx)("div",{children:"连续包年：订阅之日起一年内，每个智能体不限使用次数"})]})]})}),((null==o?void 0:o.subStatus)=="1"||(null==o?void 0:o.subStatus)=="3")&&m.type==(null==o?void 0:o.packageType)&&(0,y.jsxs)("div",{style:{textAlign:"center",fontSize:"14px"},children:[null==o?void 0:o.subAt," 已订阅"]}),(null==o?void 0:o.subStatus)=="1"&&(null==o?void 0:o.packageType)=="免费"&&"免费"==m.type&&(0,y.jsx)("div",{style:{textAlign:"center",fontSize:"14px"},children:"免费使用中…"}),(null==o?void 0:o.subStatus)=="3"&&m.type==o.packageType&&(0,y.jsxs)("div",{style:{textAlign:"center",fontSize:"14px"},children:[null==o?void 0:o.unSubAt," 取消订阅"]}),(null==o?void 0:o.subStatus)=="3"&&m.type==o.packageType&&(0,y.jsxs)("div",{style:{textAlign:"center",fontSize:"14px"},children:["您的订阅可使用至 ",null==o?void 0:o.expireAt]}),(null==o?void 0:o.packageType)=="连续包月"&&(null==o?void 0:o.subStatus)=="1",(null==o?void 0:o.packageType)=="连续包月"&&(null==o?void 0:o.subStatus)=="1"?(0,y.jsx)("div",{style:{textAlign:"center",fontSize:"14px"},children:"连续包月中…"}):null,(null==o?void 0:o.packageType)=="连续包年"&&(null==o?void 0:o.subStatus)=="1"&&(0,y.jsx)("div",{style:{textAlign:"center",fontSize:"14px"},children:"连续包年中…"})]}):(0,y.jsx)("div",{className:"vip-two border border-gray-200 rounded m-3 p-2",children:(0,y.jsxs)("div",{className:"vip-two_banner bg-[url('https://img.medsci.cn/202503/48a49f899b4544e39a864d1821b3555a-lRXRIQx3q1VB.png')] bg-cover p-3",children:[(0,y.jsxs)("div",{className:"vip-two_title flex items-center text-sm font-medium text-orange-600",children:[(0,y.jsx)("img",{src:"https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png",alt:"",className:"w-2 h-2 mx-1"}),i.name]}),(0,y.jsx)("div",{className:"vip-two_content bg-white p-3 text-sm text-gray-700 leading-5 max-h-[calc(var(--vh)*20)] overflow-auto rounded",children:i.description})]})}),"zh-CN"==O()&&(0,y.jsxs)("div",{className:`vip-pay btns ${"连续包月"==o.packageType&&"1"==o.subStatus||"连续包年"==o.packageType&&"1"==o.subStatus?"":"CN_btns"}`,children:[(null==o?void 0:o.packageType)=="连续包月"&&(null==o?void 0:o.subStatus)=="1"&&(0,y.jsx)(J.ZP,{onClick:()=>{sQ.Vq.confirm({title:"提示",message:`取消包月在${null==o?void 0:o.expireAt}号生效，再次使用需要重新订阅。是否确认取消？`,confirmButtonColor:"#D7813F",onCancel:()=>{},onConfirm:async()=>{await E.cancelSubscription({},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`}),r()}})},type:"primary",children:"取消包月"}),(null==o?void 0:o.packageType)=="连续包年"&&(null==o?void 0:o.subStatus)=="1"&&(0,y.jsx)(J.ZP,{onClick:()=>{sQ.Vq.confirm({title:"提示",message:`取消包月在${null==o?void 0:o.expireAt}号生次生效，再次使用需要重新订阅。是否确认取消？`,confirmButtonColor:"#D7813F",onCancel:()=>{},onConfirm:async()=>{await E.cancelSubscription({},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`}),r()}})},type:"primary",children:"取消包年"})]}),m.feePrice>0&&(null==m?void 0:m.coinType)=="人民币"&&"zh-CN"==O()&&"免费"!=m.type&&(null==o?void 0:o.subStatus)=="0"||"免费"!=m.type&&(null==o?void 0:o.subStatus)=="2"||(null==o?void 0:o.packageType)=="免费"&&"免费"!=m.type&&(0,y.jsx)("div",{className:"vip-three mt-3",children:(0,y.jsxs)("div",{className:`pay mx-3 p-4 rounded-lg bg-gradient-to-b from-[#fffaf6] to-[#fff8f0] ${g?"h-28":""}`,children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:"",className:"w-15 mb-3"}),(0,y.jsxs)("div",{className:"item flex justify-between items-center bg-white border border-orange-300 rounded p-2 mb-3 cursor-pointer",onClick:M,children:[(0,y.jsxs)("div",{className:"item-left flex items-center",children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:"",className:"w-5 h-5 mr-2"}),(0,y.jsx)("span",{className:"text-sm text-gray-800",children:"支付宝支付"})]}),(0,y.jsx)("div",{className:"item-right w-5 h-5",children:c&&(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:"",className:"w-full"})})]})]})}),m.feePrice>0&&(null==m?void 0:m.coinType)=="人民币"&&"zh-CN"!=O()&&(0,y.jsx)("div",{className:"vip-three mt-3",children:(0,y.jsxs)("div",{className:`pay mx-3 p-4 rounded-lg bg-gradient-to-b from-[#fffaf6] to-[#fff8f0] ${g?"h-28":""}`,children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png",alt:"",className:"w-15 mb-3"}),(0,y.jsxs)("div",{className:"item flex justify-between items-center bg-white border border-orange-300 rounded p-2 mb-3 cursor-pointer",onClick:M,children:[(0,y.jsxs)("div",{className:"item-left flex items-center",children:[(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png",alt:"",className:"w-5 h-5 mr-2"}),(0,y.jsx)("span",{className:"text-sm text-gray-800",children:"支付宝支付"})]}),(0,y.jsx)("div",{className:"item-right w-5 h-5",children:c&&(0,y.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png",alt:"",className:"w-full"})})]})]})})]}),m.feePrice>=0&&"zh-CN"==O()&&("免费"==m.type&&"0"==o.subStatus||"免费"!=m.type&&"0"==o.subStatus||"免费"!=m.type&&"2"==o.subStatus||"免费"==o.packageType&&"免费"!=m.type)?(0,y.jsx)("div",{className:`vip-pay btns fixed bottom-0 w-full bg-white shadow-lg flex justify-around items-center h-24 z-20  ${((null==o?void 0:o.subStatus)=="1"||(null==o?void 0:o.subStatus)=="3")&&"免费"==m.type?"CN_btns":""}`,children:m.feePrice>0?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{className:"pay-left ml-2",children:[(0,y.jsx)("div",{className:"t1 text-sm font-medium text-orange-400 mb-1",children:i.name}),(0,y.jsxs)("div",{className:`t2 flex items-start text-xs text-gray-500 ${j?"shake":""}`,children:[(0,y.jsx)(ll.Z,{checked:S,onChange:$,style:{fontSize:"14px"},className:"mr-1"}),(0,y.jsxs)("span",{onClick:L,className:"cursor-pointer",children:["请在阅读并同意",(0,y.jsx)("span",{className:"text-blue-500",children:"协议"}),"后开通"]})]})]}),(0,y.jsx)("div",{className:"pay-right mr-2 w-48 h-12 bg-gradient-to-r from-[#fde39b] to-[#fbd786] rounded-full flex items-center justify-center cursor-pointer",onClick:()=>R(m,i.appUuid,"ali"),children:(0,y.jsxs)("span",{className:"text-sm text-[#614018]",children:[m.feePrice,"元确认协议并支付"]})})]}):(0,y.jsx)(J.ZP,{className:"w-48 h-12 rounded-full",onClick:()=>R(m,i.appUuid,""),children:0==m.feePrice?"免费试用":"订阅"})}):null,m.feePrice>=0&&"zh-CN"!=O()?(0,y.jsx)("div",{className:"vip-pay btns fixed bottom-0 w-full bg-white shadow-lg flex justify-around items-center h-24 z-20",children:m.feePrice>0?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{className:"pay-left ml-2",children:[(0,y.jsx)("div",{className:"t1 text-sm font-medium text-orange-400 mb-1",children:i.name}),(0,y.jsxs)("div",{className:`t2 flex items-start text-xs text-gray-500 ${j?"shake":""}`,children:[(0,y.jsx)(ll.Z,{checked:S,onChange:$,style:{fontSize:"14px"},className:"mr-1"}),(0,y.jsxs)("span",{onClick:L,className:"cursor-pointer",children:["请在阅读并同意",(0,y.jsx)("span",{className:"text-blue-500",children:"协议"}),"后开通"]})]})]}),(0,y.jsx)("div",{className:"pay-right mr-2 w-48 h-12 bg-gradient-to-r from-[#fde39b] to-[#fbd786] rounded-full flex items-center justify-center cursor-pointer",onClick:()=>R(m,i.appUuid,"ali"),children:(0,y.jsxs)("span",{className:"text-sm text-[#614018]",children:[m.feePrice,"元确认协议并支付"]})})]}):(0,y.jsx)(J.ZP,{type:"primary",className:"w-48 h-12 rounded-full",onClick:()=>R(m,i.appUuid,""),children:0==m.feePrice?"免费试用":"订阅"})}):null]})};function lp(e){var t,n,a,i,r,o,s,l,c;let{appConfig:u,appInfo:d,appParameters:p,difyApi:m,conversationId:f,conversationItems:g,conversationName:h,onConversationIdChange:v,conversationListLoading:b,onAddConversation:S,onItemsChange:w,conversationItemsChangeCallback:j,appConfigLoading:k,handleStartConfig:C,inputParams:N,setInputParams:A,modal2OpenF:T,onClose:_,onsubmit:I,subStatusDetails:P,changeSubStatusDetails:M,selectedAppId:L,appList:D}=e,F=(0,R.k6)(),{t:$}=(0,ou.$G)(),Z=new s2,U=new W,H=new URLSearchParams(window.top.location.search),G=O(),q=F.location.pathname.match(/^\/ai-chat\/([^/]+)$/),K=q?q[1]:"",X=(0,x.useRef)(()=>{});(0,x.useEffect)(()=>()=>{X.current()},[]),(0,x.useEffect)(()=>{ex(T)},[T]);let[Y,Q]=(0,x.useState)(!1),[ee,et]=(0,x.useState)([]),[en,ea]=(0,x.useState)([]),ei=s4({conversationId:f}),er=s4({inputParams:N}),eo=(0,x.useRef)([]),es=async e=>{ea((await m.getNextSuggestions({message_id:e})).data)},el=async e=>{var t,n,a,i,r;if(!e||E(e))return;let o=await m.getConversationHistory(e);if(!(null==o||null==(t=o.data)?void 0:t.length))return;let s=[];(null==o||null==(n=o.data)?void 0:n.length)&&(null==(a=Object.values(null==(r=o.data)||null==(i=r[0])?void 0:i.inputs))?void 0:a.length)&&A({article_id:H.get("article_id"),qa_id:H.get("qa_id")}),o.data.forEach(e=>{let t=sY()(1e3*e.created_at).format("YYYY-MM-DD HH:mm:ss");s.push({id:e.id,content:e.query,status:"success",isHistory:!0,files:e.message_files,role:"user",created_at:t},{id:e.id,content:e.answer,status:"error"===e.status?e.status:"success",error:e.error||"",isHistory:!0,feedback:e.feedback,agentThoughts:e.agent_thoughts||[],retrieverResources:e.retriever_resources||[],role:"ai",created_at:t})}),ep([]),et(s),(null==s?void 0:s.length)&&(null==p?void 0:p.suggested_questions_after_answer.enabled)&&es(s[s.length-1].id)},{agent:ec,onRequest:eu,messages:ed,setMessages:ep,currentTaskId:em}=s9({latestProps:ei,latestState:er,filesRef:eo,getNextSuggestions:es,appParameters:p,abortRef:X,getConversationMessages:el,onConversationIdChange:v,difyApi:m,appUuid:(null==u?void 0:u.id)||""}),ef=async()=>{f&&!E(f)&&await el(f),Q(!1)};(0,x.useEffect)(()=>{Q(!0),ep([]),ea([]),et([]),ef(),E(f)&&("medsci-ask"!=K&&A({}),H.get("article_id")&&(A({article_id:H.get("article_id"),qa_id:H.get("qa_id")}),U.qaList({articleId:H.get("article_id")||"",encryptionId:H.get("qa_id")||""},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`,Accept:"application/json"}).then(e=>{var t;I(ev((null==e||null==(t=e.data[0])?void 0:t.question)||""))})))},[f]);let eg=async e=>{var t;let n=ey();if("nologin"==m.options.user)return console.log("未登录 onFocus",n),n&&"zh-CN"!=n?window.top.location.href=location.origin+"/"+n+"/login":window.addLoginDom(),!1;if(K.includes("elavax-pro")||K.includes("novax-pro")){let t=await U.bindAppUser({appUuid:(null==u?void 0:u.id)||"",appNameEn:K},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`,Accept:"application/json"});if((null==t?void 0:t.data.remainNum)!=0)return eu({content:e.data.description}),!1}let a=q?q[1]:"",i=await Z.getApp(a,{Authorization:`Bearer ${B.Z.get("yudaoToken")}`,Accept:"application/json"});if(!(null==i?void 0:i.info.appUser)||(null==i||null==(t=i.info.appUser)?void 0:t.status)==2)return document.getElementsByTagName("textarea")[0].blur(),ex(!0),!1;eu({content:e.data.description})},eh=(0,x.useMemo)(()=>{var e,t;return!((null==p||null==(e=p.user_input_form)?void 0:e.length)&&(!f||E(f)))||(null==p||null==(t=p.user_input_form)?void 0:t.every(e=>!!N[e["text-input"].variable]))||!1},[p,N,f]),ev=(e,t)=>{if(document.getElementsByTagName("textarea").length>0&&document.getElementsByTagName("textarea")[0].blur(),!eh){let e=(null==p?void 0:p.user_input_form.filter(e=>{let t=e["text-input"];return!N[t.variable]&&t.required}).map(e=>e["text-input"].label))||[];z.ZP.error(`${e.join("、")}不能为空`);return}eo.current=(null==t?void 0:t.files)||[],eu({content:e,files:null==t?void 0:t.files})},ey=()=>B.Z.get("ai_apps_lang")?B.Z.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,[eb,ex]=(0,x.useState)(!1),eS=e=>{ex(e),_(e)},ew=async()=>{var e;let t=ey();if("nologin"==m.options.user)return console.log("未登录 onFocus",t),t&&"zh-CN"!=t?top.location.href=location.origin+"/"+t+"/login":window.addLoginDom(),!1;if(K.includes("elavax-pro")||K.includes("novax-pro")){let e=await U.bindAppUser({appUuid:(null==u?void 0:u.id)||"",appNameEn:K},{Authorization:`Bearer ${B.Z.get("yudaoToken")}`,Accept:"application/json"});if((null==e?void 0:e.data.remainNum)!=0)return!1}let n=q?q[1]:"",a=await Z.getApp(n,{Authorization:`Bearer ${B.Z.get("yudaoToken")}`,Accept:"application/json"});return!!(null==a?void 0:a.info.appUser)&&(null==a||null==(e=a.info)?void 0:e.appUser.status)!=2||(document.getElementsByTagName("textarea")[0].blur(),ex(!0),!1)},ej=(0,x.useMemo)(()=>ed.map(e=>({id:e.id,status:e.status,error:e.message.error||"",workflows:e.message.workflows,agentThoughts:e.message.agentThoughts,retrieverResources:e.message.retrieverResources,files:e.message.files,content:e.message.content,role:"local"===e.status?"user":"ai"})),[ed]),ek=(0,y.jsx)(sW.Z,{trigger:["click"],content:(0,y.jsxs)("div",{className:"w-60",children:[(0,y.jsx)("div",{className:"text-base font-semibold",children:"对话列表"}),(0,y.jsx)(V.Z,{spinning:b,children:(null==g?void 0:g.length)?(0,y.jsx)(sK,{renameConversationPromise:(e,t)=>null==m?void 0:m.renameConversation({conversation_id:e,name:t}),appConfig:u,deleteConversationPromise:null==m?void 0:m.deleteConversation,items:g,activeKey:f,onActiveChange:v,onItemsChange:w,refreshItems:j,onchangeModal2Open:e=>ex(e)}):(0,y.jsx)(ol.Z,{description:"暂无会话"})}),(0,y.jsx)(J.ZP,{className:"mt-3",onClick:S,block:!0,type:"primary",children:"新增对话"})]}),placement:G?"bottom":"bottomLeft",children:(0,y.jsxs)("div",{className:"flex w-full  items-center justify-start",children:[(0,y.jsx)(sX.Z,{className:"mr-3 cursor-pointer"}),(0,y.jsx)("span",{className:"truncate max-w-[80%]",children:h||s5})]})});return b||k?(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):u?(0,y.jsxs)("div",{className:"flex h-screen flex-col overflow-hidden flex-1",children:[!G&&eb?(0,y.jsx)(sG.Z,{centered:!0,open:eb,onOk:()=>ex(!1),onCancel:()=>ex(!1),children:(null==u?void 0:u.id)&&((null==u||null==(n=u.info)||null==(t=n.appNameEn)?void 0:t.includes("elavax"))||(null==u||null==(i=u.info)||null==(a=i.appNameEn)?void 0:a.includes("novax")))?(0,y.jsx)(ls,{onClose:eS,appConfig:u,currentItem:null==u?void 0:u.info,userInfo:B.Z.get("userInfo"),subStatusDetail:P}):(0,y.jsx)(lu,{onClose:eS,appConfig:u,currentItem:null==u?void 0:u.info,userInfo:B.Z.get("userInfo"),subStatusDetail:P})}):null,G&&eb?(0,y.jsx)(sQ.GI,{visible:eb,style:{width:"100%",height:"90%"},position:"bottom",closeable:!0,round:!0,onClose:()=>eS(!1),children:(null==u?void 0:u.id)&&((null==u||null==(o=u.info)||null==(r=o.appNameEn)?void 0:r.includes("elavax"))||(null==u||null==(l=u.info)||null==(s=l.appNameEn)?void 0:s.includes("novax")))?(0,y.jsx)(lc,{onClose:()=>eS(!1),currentItem:null==u?void 0:u.info,userInfo:B.Z.get("userInfo"),subStatusDetail:P}):(0,y.jsx)(ld,{onClose:()=>eS(!1),currentItem:null==u?void 0:u.info,userInfo:B.Z.get("userInfo"),subStatusDetail:P})}):null,G?(0,y.jsx)(lr,{subStatusDetail:P,appConfig:u,appList:D,selectedAppId:L,showSubscribe:()=>{M(),ex(!0)},centerChildren:ek}):null,(0,y.jsxs)("div",{className:"flex-1 overflow-hidden relative",children:[Y?(0,y.jsx)("div",{className:"absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):null,g.length?f&&eh?(0,y.jsx)(sq,{appConfig:u,conversationId:f,appParameters:p,nextSuggestions:en,messageItems:[...ee,...ej],isRequesting:ec.isRequesting(),onPromptsItemClick:eg,onSubmit:ev,onCancel:async()=>{X.current(),em&&(await m.stopTask(em),el(f))},feedbackApi:m.feedbackMessage,feedbackCallback:e=>{el(e)},uploadFileApi:m.uploadFile,difyApi:m,onFocus:ew,translations:{aiGeneratedContent:$("chat.aiGeneratedContent"),replyTime:$("chat.replyTime")}}):(null==p||null==(c=p.user_input_form)?void 0:c.length)?(0,y.jsx)(la,{conversationId:f,formFilled:eh,onStartConversation:e=>{A(e),f||"medsci-ask"==K||S()},appInfo:d,user_input_form:null==p?void 0:p.user_input_form}):(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):(0,y.jsx)(la,{conversationId:f,formFilled:eh,onStartConversation:e=>{A(e),f||S()},appInfo:d,user_input_form:null==p?void 0:p.user_input_form})]})]}):(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(ol.Z,{description:"请先配置 Dify 应用",children:(0,y.jsx)(J.ZP,{type:"primary",onClick:C,children:"开始配置"})})})}var lm=n(33405);let{Option:lf}=od.Z,lg=[{code:"zh-CN",name:"简体中文",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"zh-TW",name:"繁體中文",flag:"\uD83C\uDDF9\uD83C\uDDFC"},{code:"en",name:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{code:"vi",name:"Tiếng Việt",flag:"\uD83C\uDDFB\uD83C\uDDF3"},{code:"es",name:"Espa\xf1ol",flag:"\uD83C\uDDEA\uD83C\uDDF8"},{code:"ar",name:"العربية",flag:"\uD83C\uDDF8\uD83C\uDDE6"},{code:"id",name:"Bahasa Indonesia",flag:"\uD83C\uDDEE\uD83C\uDDE9"},{code:"pt",name:"Portugu\xeas",flag:"\uD83C\uDDE7\uD83C\uDDF7"},{code:"ja",name:"日本語",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"ko",name:"한국어",flag:"\uD83C\uDDF0\uD83C\uDDF7"},{code:"ms",name:"Bahasa Melayu",flag:"\uD83C\uDDF2\uD83C\uDDFE"}],lh=e=>{let{className:t,size:n="middle",showIcon:a=!0}=e,{i18n:i}=(0,ou.$G)();return(0,y.jsx)(od.Z,{value:i.language,onChange:e=>{i.changeLanguage(e)},className:t,size:n,style:{minWidth:120},suffixIcon:a?(0,y.jsx)(lm.Z,{}):void 0,popupMatchSelectWidth:!1,children:lg.map(e=>(0,y.jsxs)(lf,{value:e.code,children:[(0,y.jsx)("span",{style:{marginRight:8},children:e.flag}),e.name]},e.code))})},lv=(e,t)=>{let[n]=(0,x.useState)(new Map);return(0,x.useEffect)(()=>{let a=new Map(e.map(e=>[e[t],e]));n.clear(),a.forEach((e,t)=>n.set(t,e))},[e,t,n]),n},ly=n.p+"static/image/kefu.1767c230.png",lb=n.p+"static/image/qrcode.74f61641.png",lx=()=>{let[e,t]=(0,x.useState)(!1),[n,a]=(0,x.useState)(!1),[i,r]=(0,x.useState)(!1);return(0,x.useEffect)(()=>{let e=()=>{let e=window.innerWidth<=768;r(e),e?t(!0):t(!1),a(!1)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,y.jsxs)("div",{className:`assistant-container ${e?"is-collapsed":""}`,children:[(0,y.jsx)("div",{className:"assistant-icon",onClick:()=>{let n=!e;t(n),n?setTimeout(()=>{a(!1)},300):a(!0)},children:(0,y.jsx)("img",{src:ly,className:"fas fa-user-astronaut",alt:"客服"})}),(0,y.jsxs)("div",{className:`qr-codes ${n?"is-visible":""}`,children:[(0,y.jsx)("img",{src:lb,alt:"QR Code"}),"扫码添加小助手"]})]})},lS=(0,sV.kc)(e=>{let{token:t,css:n}=e;return{layout:n`
			font-family: AlibabaPuHuiTi, ${t.fontFamily}, sans-serif;
		`,menu:n`
			background: ${t.colorBgLayout}80;
		`}}),lw=e=>{let[t,n]=(0,x.useState)(!1),a=(0,R.k6)(),i=new s2,{extComponents:r,appConfig:o,useAppInit:s,renderCenterTitle:l,handleStartConfig:c,initLoading:u,selectedAppId:d,appList:p}=e,{...m}=N(),{user:f}=m,{t:g}=(0,ou.$G)(),{styles:h}=lS(),[v]=(0,x.useState)(oe({user:f,apiBase:"",apiKey:"",appId:""})),b=new URLSearchParams(window.top.location.search),[S,w]=(0,x.useState)([]),j=lv(S,"key"),[k,C]=(0,x.useState)(!1),[A,T]=(0,x.useState)(),[_,E]=(0,x.useState)(),[I,O]=(0,x.useState)(),[P,M]=(0,x.useState)(!1),[L,D]=(0,x.useState)({}),[F,$]=(0,x.useState)({}),Z=a.location.pathname.match(/^\/ai-chat\/([^/]+)$/),U=Z?Z[1]:"",[B,H]=(0,x.useState)(null),G=()=>{var e;if(null==I||null==(e=I.user_input_form)?void 0:e.length){let e={...L};null==I||I.user_input_form.forEach(t=>{e[t["text-input"].variable]=void 0}),D(e)}},q=async()=>{E(void 0),v&&(M(!0),E({name:o.info.name,description:o.info.description,tags:[]}),O(await v.getAppParameters()),M(!1))};s(v,()=>{q().then(()=>{K().then(()=>{("medsci-ask"==U||b.get("fromPlatform"))&&Y()})}),G(),T(void 0)});let K=async()=>{C(!0);try{var e,t,n;let a=`temp_${Math.random()}`,i=[{key:a,label:s5}];if(en){let n=await (null==v?void 0:v.getConversationList());(null==n?void 0:n.code)==401&&(Q.remove("userInfo",{domain:".medon.com.cn"}),Q.remove("userInfo",{domain:".medsci.cn"}),Q.remove("userInfo",{domain:"localhost"}),Q.remove("yudaoToken",{domain:"ai.medon.com.cn"}),Q.remove("yudaoToken",{domain:"ai.medsci.cn"}),Q.remove("yudaoToken",{domain:".medon.com.cn"}),Q.remove("yudaoToken",{domain:".medsci.cn"}),Q.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("conversation"),window.location.origin.includes("medsci.cn")?window.top.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.top.location.href:window.top.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.top.location.href),i=(null==n||null==(e=n.data)?void 0:e.length)?null==n||null==(t=n.data)?void 0:t.map(e=>({key:e.id,label:e.name})):[{key:a,label:s5}]}w(i),"medsci-ask"==U||b.get("fromPlatform")||T(null==(n=i[0])?void 0:n.key)}catch(e){console.error(e),z.ZP.error(`${g("common.getConversationListFailed")}: ${e}`)}finally{C(!1)}},X=()=>Q.get("ai_apps_lang")?Q.get("ai_apps_lang"):navigator.browserLanguage||navigator.language,Y=async()=>{var e,t;let r=new W,s=a.location.pathname.match(/^\/ai-chat\/([^/]+)$/),l=s?s[1]:"",c=X();if("nologin"==f)return void(c&&"zh-CN"!=c?window.top.location.href=location.origin+"/"+c+"/login":window.addLoginDom());let u=`temp_${Math.random()}`;if(w(e=>[{key:u,label:s5},...e]),l.includes("elavax-pro")||l.includes("novax-pro")){let e=await r.bindAppUser({appUuid:(null==o?void 0:o.id)||"",appNameEn:l},{Authorization:`Bearer ${Q.get("yudaoToken")}`,Accept:"application/json"});if((null==e?void 0:e.data.remainNum)!=0)return T(u),!1}let d=s?s[1]:"",p=await i.getApp(d,{Authorization:`Bearer ${Q.get("yudaoToken")}`,Accept:"application/json"});if(!p.info.appUser||(null==p||null==(e=p.info.appUser)?void 0:e.status)==2){(null==(t=document.getElementsByTagName("textarea"))?void 0:t.length)&&document.getElementsByTagName("textarea")[0].blur(),n(!0);return}T(u)};(0,x.useEffect)(()=>{er()},[]),(0,x.useEffect)(()=>{A&&!j.has(A)&&K()},[A]);let ee=(0,x.useMemo)(()=>{var e;return(null==(e=S.find(e=>e.key===A))?void 0:e.label)||s5},[S,A]);(0,x.useEffect)(()=>{o||(w([]),E(void 0),T(""),G())},[o]);let en=Q.get("userInfo"),ea=en&&JSON.parse(en).avatar?JSON.parse(en).avatar:"https://img.medsci.cn/web/img/user_icon.png",ei=e=>{e.target.src="https://img.medsci.cn/web/img/user_icon.png"},er=async()=>{let e=new W;$((await e.getPackageByKey({},{Authorization:`Bearer ${Q.get("yudaoToken")}`,Accept:"application/json"})).data)},[eo,es]=(0,x.useState)(!1),el=async()=>{es(!1),Q.remove("userInfo",{domain:".medon.com.cn"}),Q.remove("userInfo",{domain:".medsci.cn"}),Q.remove("userInfo",{domain:"localhost"}),localStorage.removeItem("hasuraToken"),Q.remove("yudaoToken",{domain:"ai.medon.com.cn"}),Q.remove("yudaoToken",{domain:"ai.medsci.cn"}),Q.remove("yudaoToken",{domain:".medon.com.cn"}),Q.remove("yudaoToken",{domain:".medsci.cn"}),Q.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("conversation"),window.location.origin.includes("medsci.cn")?window.top.location.href="https://www.medsci.cn/sso_logout?redirectUrl="+window.top.location.href:window.top.location.href="https://portal-test.medon.com.cn/sso_logout?redirectUrl="+window.top.location.href};return(0,y.jsxs)(og.ZP,{theme:{token:{colorPrimary:"#1669ee",colorText:"#333"}},children:[(0,y.jsxs)("div",{className:`w-full h-screen ${h.layout} flex flex-col overflow-hidden bg-[#eff0f5]`,children:[(0,y.jsx)(lx,{}),(0,y.jsxs)("div",{className:"hidden md:!flex items-center justify-between px-6",children:[(0,y.jsx)("div",{className:`flex-1 overflow-hidden ${o?"":"shadow-sm"}`,children:(0,y.jsx)(et,{hideGithubIcon:!0})}),(0,y.jsx)(li,{children:U.includes("novax")||U.includes("elavax")?l&&_?l({name:_.name,description:_.description,tags:_.tags||[],appUser:null,appId:"",appType:"",appIcon:"",feeTypes:[]}):null:o?o.info.name:""}),(0,y.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,y.jsxs)("div",{className:"flex items-center justify-end text-sm",children:["zh-CN"==X()&&!U.includes("novax")&&!U.includes("elavax")&&(0,y.jsx)("div",{className:"cursor-pointer px-[15px] py-[4px] flex items-center h-[28px] rounded border-none text-xs mr-[8px] text-[#614018]",style:{backgroundImage:"linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)"},onClick:()=>{er(),n(!0)},children:F.packageType==g("payment.free")?g("payment.upgradeSubscription"):F.packageType==g("payment.monthlySubscription")||F.packageType==g("payment.yearlySubscription")?g("payment.modifySubscription"):g("payment.subscribe")}),(0,y.jsx)(lh,{size:"small",className:"mr-3"}),(0,y.jsx)("a",{style:{borderRadius:"4px",background:"#f1f5f9",padding:"6px 10px",fontSize:"12px",color:"#666",marginRight:"23px"},className:"backImg",href:location.origin.includes(".medon.com.cn")?"https://ai.medon.com.cn/"+X():location.origin.includes(".medsci.cn")?"https://ai.medsci.cn/"+X():"/"+X(),target:"_top",children:g("common.goHome")}),!en&&(0,y.jsx)("div",{className:"hover:cursor-pointer",onClick:()=>{let e=X();if(e&&"zh-CN"!==e)window.top.location.href=location.origin+"/"+e+"/login";else{var t,n;null==(t=(n=window).addLoginDom)||t.call(n)}},children:g("common.login")}),(0,y.jsx)(sW.Z,{placement:"bottomLeft",trigger:"hover",arrow:eo,overlayStyle:{width:350,paddingBottom:40},content:(0,y.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",position:"relative",paddingBottom:"40px"},children:[(0,y.jsx)("a",{className:"exit text-right w-full text-[#333333]",onClick:el,children:g("common.logout")}),(0,y.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"},className:"iconHeader bg-write",children:[en&&JSON.parse(en||"").userId&&(0,y.jsx)("img",{src:ea,onMouseEnter:()=>es(!0),onMouseLeave:()=>es(!1),style:{width:"60px",height:"60px"},onError:ei,alt:"avatar"}),(0,y.jsx)("span",{className:"account",children:en&&JSON.parse(en||"").userName})]})]}),children:(0,y.jsx)("a",{href:"#",children:(0,y.jsx)("div",{className:"img-area",children:en&&JSON.parse(en||"").userId&&(0,y.jsx)("img",{src:ea,onMouseEnter:()=>es(!0),onMouseLeave:()=>es(!1),style:{width:"30px",height:"32px"},onError:ei,alt:"avatar"})})})})]})})]}),(0,y.jsx)("div",{className:"flex-1 overflow-hidden flex rounded-3xl bg-white",children:P||u?(0,y.jsx)("div",{className:"absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):o?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{className:`${h.menu} hidden md:!flex w-72 h-full flex-col`,children:[o?(0,y.jsx)(J.ZP,{onClick:()=>{G(),Y()},className:"h-10 leading-10 border border-solid border-gray-200 w-[calc(100%-24px)] mt-3 mx-3 text-default",icon:(0,y.jsx)(of.Z,{}),children:g("common.newConversation")}):null,(0,y.jsx)("div",{className:"px-3",children:(0,y.jsx)(V.Z,{spinning:k,children:(null==S?void 0:S.length)?(0,y.jsx)(sK,{renameConversationPromise:(e,t)=>null==v?void 0:v.renameConversation({conversation_id:e,name:t}),deleteConversationPromise:null==v?void 0:v.deleteConversation,items:S,activeKey:A,onActiveChange:e=>{G(),T(e)},onItemsChange:w,refreshItems:K,appConfig:o,onchangeModal2Open:e=>n(e)}):(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(ol.Z,{className:"pt-6",description:g("common.noSessions")})})})})]}),(0,y.jsx)("div",{className:"flex-1 min-w-0 flex flex-col overflow-hidden",children:ee?(0,y.jsx)(lp,{inputParams:L,setInputParams:D,resetFormValues:G,appConfig:o,appConfigLoading:P,appInfo:_,difyApi:v,conversationId:A,conversationName:ee,conversationItems:S,onConversationIdChange:T,appParameters:I,conversationListLoading:k,onAddConversation:Y,onItemsChange:w,conversationItemsChangeCallback:K,modal2OpenF:t,onsubmit:e=>H(e),subStatusDetails:F,changeSubStatusDetails:()=>{er()},onClose:e=>{n(e)},selectedAppId:d,appList:p}):""})]}):(0,y.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,y.jsx)(ol.Z,{description:g("common.noDifyAppConfig"),className:"text-base",children:(0,y.jsx)(J.ZP,{size:"large",type:"primary",onClick:c,children:g("common.startConfig")})})})})]}),r]})},lj=()=>{let{setCurrentAppConfig:e,...t}=N(),{user:n,appService:a,enableSetting:i}=t,r=(0,R.k6)(),o=new W,[s,l]=(0,x.useState)(""),[c,u]=(0,x.useState)(!1),[d,p]=(0,x.useState)(!1),{appId:m}=(0,R.UO)(),f=Q.get("userInfo"),[g,h]=(0,x.useState)(null),v=new URLSearchParams(window.top.location.search),b=r.location.pathname.match(/^\/ai-chat\/([^/]+)$/),S=b?b[1]:"",[w,j]=(0,x.useState)("nologin");(0,x.useEffect)(()=>{l(m)},[m]);let k="ai.medsci.cn"===window.location.hostname?"https://ai.medsci.cn":"https://ai.medon.com.cn",{runAsync:C,data:A,loading:T}=(0,ei.Z)(async()=>{let e=r.location.pathname.match(/^\/ai-chat\/([^/]+)$/),t=e?e[1]:"";if(t)if(t.includes("elavax")||t.includes("novax")){let e=await o.getAppByConfigKey({configKey:t.includes("elavax")?"elavax_apps":"novax_apps"},{Authorization:`Bearer ${Q.get("yudaoToken")}`,Accept:"application/json"});return null==e?void 0:e.data.map(e=>({id:e.appUuid,info:{name:e.appName,description:e.appDescription,appUser:e.appUser,appId:e.dAppUuid,appType:e.appType,appIcon:e.appIcon,feeTypes:e.feeTypes,tags:[],appUuid:e.appUuid,appNameEn:e.appNameEn},requestConfig:{apiKey:Q.get("yudaoToken"),apiBase:`${k}/dev-api/ai-base/v1`},answerForm:{enabled:!1}}))}else{let e=await a.getApp(t,{Authorization:`Bearer ${Q.get("yudaoToken")}`,Accept:"application/json"});if(e.requestConfig.apiKey=Q.get("yudaoToken"),console.log(e),e&&"问答"===e.info.appType)return[e]}return r.replace("/"),[]},{manual:!0,onSuccess:e=>{if(E&&!(null==e?void 0:e.length))return r.replace("/apps"),Promise.resolve([]);if(m)l(m);else if(!s&&(null==e?void 0:e.length)){if(1==e.length&&!S.includes("novax")&&!S.includes("elavax")){var t;l((null==(t=e[0])?void 0:t.id)||"")}(S.includes("novax")||S.includes("elavax"))&&(S.includes("novax")||S.includes("elavax"))&&e.forEach(e=>{e.info.appNameEn==S&&l((null==e?void 0:e.id)||"")})}p(!1)},onError:e=>{z.ZP.error(`获取应用列表失败: ${e}`),console.error(e),p(!1)}}),_=(0,x.useMemo)(()=>null==A?void 0:A.find(e=>e.id===s),[A,s]),E=O();return((0,L.Z)(async()=>{if(f){let e=new W;if(Q.get("yudaoToken")&&"medsci-ask"!=S||v.get("fromPlatform"));else{j(JSON.parse(f).userName);let{data:t}=await e.getAiWriteToken({userId:JSON.parse(f).userId,userName:JSON.parse(f).userName,realName:JSON.parse(f).realName,avatar:JSON.parse(f).avatar,plaintextUserId:JSON.parse(f).plaintextUserId,mobile:JSON.parse(f).mobile,email:JSON.parse(f).email,fromPlatform:"medsci-ask"==S||v.get("fromPlatform")?"medsci":"",appUuid:S})||{};(null==t?void 0:t.token)?(Q.set("yudaoToken",t.token),localStorage.setItem("hasuraToken",t.htoken),localStorage.setItem("openid",t.openid),localStorage.setItem("socialUserId",t.socialUserId),localStorage.setItem("socialType",t.socialType)):console.error("登录失败: 未返回 token")}}C()}),d)?(0,y.jsx)("div",{className:"absolute w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:(0,y.jsx)(V.Z,{spinning:!0})}):_?(0,y.jsx)(lw,{useAppInit:(t,a)=>{(0,x.useEffect)(()=>{let i=null==A?void 0:A.find(e=>e.id===s);i&&(t.updateOptions({appId:i.info.appId,user:n,...i.requestConfig}),e(i),a())},[s])},appConfig:_,initLoading:d,handleStartConfig:()=>{i&&u(!0)},selectedAppId:s,appList:A,extComponents:(0,y.jsx)(y.Fragment,{children:(0,y.jsx)(om,{open:c,onClose:()=>u(!1),activeAppId:s,appList:A,getAppList:C,appListLoading:T,onDeleteSuccess:e=>{e===s&&l("")}})}),renderCenterTitle:()=>{var e;return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(en.Z,{className:"mr-2"}),s?(0,y.jsx)(y.Fragment,{children:(0,y.jsx)(er.Z,{arrow:!0,placement:"bottom",trigger:["click"],menu:{style:{},selectedKeys:[s],items:[...(null==A?void 0:A.map(e=>{let t=s===e.id;return{key:e.id,label:(0,y.jsx)("div",{className:t?"text-primary":"text-default",children:null==e?void 0:e.info.name}),onClick:()=>{if(S.includes("novax")||S.includes("elavax")){window.top.location.href=location.origin+(window.top.location.href.includes("ai-chat")?"/ai-chat/":"/chat/")+`${null==e?void 0:e.info.appNameEn}`;return}},icon:(0,y.jsx)(en.Z,{})}}))||[]]},children:(0,y.jsxs)("div",{className:"cursor-pointer",children:[(0,y.jsx)("span",{className:"cursor-pointer",children:null==_||null==(e=_.info)?void 0:e.name}),(0,y.jsx)(ea.Z,{className:"ml-1"})]})})}):null]})}}):null},lk=()=>{let e=N(),{user:t,appConfig:n}=e;return(0,y.jsx)(lw,{initLoading:!1,handleStartConfig:()=>{},useAppInit:(n,a)=>{let i=async()=>{n.updateOptions({user:t,apiBase:e.appConfig.requestConfig.apiBase,apiKey:e.appConfig.requestConfig.apiKey}),a()};(0,L.Z)(()=>{i()})},appConfig:n,renderCenterTitle:e=>(0,y.jsx)(y.Fragment,{children:null==e?void 0:e.name})})};function lC(){let{user:e,mode:t}=N();return e?"singleApp"===t?(0,y.jsx)(lk,{}):(0,y.jsx)(lj,{}):(0,y.jsx)("div",{className:"w-screen h-screen flex flex-col items-center justify-center",children:(0,y.jsxs)("div",{className:"absolute flex-col w-full h-full left-0 top-0 z-50 flex items-center justify-center",children:[(0,y.jsx)(et,{hideGithubIcon:!0}),(0,y.jsx)("div",{children:"授权登录中..."}),(0,y.jsx)("div",{className:"mt-6",children:(0,y.jsx)(V.Z,{spinning:!0})})]})})}var lN=n(49021),lA=n(94448),lT=JSON.parse('{"common":{"login":"登录","logout":"退出","goHome":"返回首页","newConversation":"新增对话","noSessions":"暂无会话","startConfig":"开始配置","noDifyAppConfig":"暂无 Dify 应用配置","getConversationListFailed":"获取会话列表失败","confirm":"确认","cancel":"取消","save":"保存","delete":"删除","edit":"编辑","add":"添加","update":"更新","close":"关闭","current":"当前","none":"无","tags":"标签","basicInfo":"基本信息","pleaseLoginFirst":"请登录后购买","loginNow":"立即登录","subscriptionFailed":"订阅失败","subscriptionSuccess":"订阅成功","pleaseSelectPeriod":"请选择订阅服务周期","pleaseAgreeToTerms":"请在阅读并同意协议后开通","agreeAndPay":"元确认协议并支付","freeTrial":"免费试用","rmb":"人民币","usd":"美元","writing":"写作"},"app":{"addAppConfigSuccess":"添加应用配置成功","updateAppConfigSuccess":"更新应用配置成功","management":"应用配置管理","appManagement":"应用管理","addApp":"添加应用","addAppConfig":"添加应用配置","appConfigDetail":"应用配置详情","appName":"应用名称","appDescription":"应用描述","appTags":"应用标签","noApps":"暂无应用","confirmDeleteApp":"确定删除应用吗？","deleteAppSuccess":"删除应用成功","saveAppConfigFailed":"保存应用配置失败","currentActiveAppId":"当前激活的应用 ID","appList":"应用列表","getAppList":"获取应用列表","appListLoading":"应用列表加载中","deleteAppSuccessCallback":"删除应用成功回调","getDifyAppInfo":"获取 Dify 应用信息"},"payment":{"subscribe":"订阅","free":"免费","monthlySubscription":"连续包月","yearlySubscription":"连续包年","upgradeSubscription":"升级订阅","modifySubscription":"修改订阅","supportAlipay":"支付宝支付","meisiAccount":"梅斯账号","subscriptionDescription":"梅斯小智 订阅说明","freeDescription":"免费：每个自然月内，每个智能体的使用上限{num}次。次月开始重新计次。","monthlyDescription":"连续包月：订阅之日起一个月内，每个智能体不限使用次数。","yearlyDescription":"连续包年：订阅之日起一年内，每个智能体不限使用次数","subscribed":"已订阅","freeUsing":"免费使用中…","cancelSubscription":"取消订阅","subscriptionValidUntil":"您的订阅可使用至 {date}","monthlySubscribing":"连续包月中…","yearlySubscribing":"连续包年中…","cancelMonthly":"取消包月","cancelYearly":"取消包年","cancelConfirmTitle":"提示","cancelMonthlyConfirm":"取消包月在{date}号生效，再次使用需要重新订阅。是否确认取消？","cancelYearlyConfirm":"取消包年在{date}号生次生效，再次使用需要重新订阅。是否确认取消？","month":"月","quarter":"季","year":"年"},"chat":{"aiGeneratedContent":"内容由 AI 生成, 仅供参考","replyTime":"回复时间：","conversationList":"对话列表","rename":"重命名","conversationRename":"会话重命名","pleaseEnter":"请输入","conversationRenameSuccess":"会话重命名成功","deleteSuccess":"删除成功","uploadFile":"上传文件","unsupportedFileType":"不支持的文件类型: ","clickOrDragToUpload":"点击或拖拽文件到此区域上传","supportedFileTypes":"支持的文件类型：","waitForUploadComplete":"请等待所有文件上传完成"}}'),l_=JSON.parse('{"common":{"login":"登入","logout":"登出","goHome":"返回首頁","newConversation":"新增對話","noSessions":"暫無會話","startConfig":"開始配置","noDifyAppConfig":"暫無 Dify 應用配置","getConversationListFailed":"獲取會話列表失敗","confirm":"確認","cancel":"取消","save":"保存","delete":"刪除","edit":"編輯","add":"添加","update":"更新","close":"關閉","current":"當前","none":"無","tags":"標籤","basicInfo":"基本信息","pleaseLoginFirst":"請登入後購買","loginNow":"立即登入","subscriptionFailed":"訂閱失敗","subscriptionSuccess":"訂閱成功","pleaseSelectPeriod":"請選擇訂閱服務週期","pleaseAgreeToTerms":"請在閱讀並同意協議後開通","agreeAndPay":"元確認協議並支付","freeTrial":"免費試用","rmb":"人民幣","usd":"美元","writing":"寫作"},"app":{"addAppConfigSuccess":"添加應用配置成功","updateAppConfigSuccess":"更新應用配置成功","management":"應用配置管理","appManagement":"應用管理","addApp":"添加應用","addAppConfig":"添加應用配置","appConfigDetail":"應用配置詳情","appName":"應用名稱","appDescription":"應用描述","appTags":"應用標籤","noApps":"暫無應用","confirmDeleteApp":"確定刪除應用嗎？","deleteAppSuccess":"刪除應用成功","saveAppConfigFailed":"保存應用配置失敗","currentActiveAppId":"當前激活的應用 ID","appList":"應用列表","getAppList":"獲取應用列表","appListLoading":"應用列表加載中","deleteAppSuccessCallback":"刪除應用成功回調","getDifyAppInfo":"獲取 Dify 應用信息"},"payment":{"subscribe":"訂閱","free":"免費","monthlySubscription":"連續包月","yearlySubscription":"連續包年","upgradeSubscription":"升級訂閱","modifySubscription":"修改訂閱","supportAlipay":"支付寶支付","meisiAccount":"梅斯賬號","subscriptionDescription":"梅斯小智 訂閱說明","freeDescription":"免費：每個自然月內，每個智能體的使用上限{num}次。次月開始重新計次。","monthlyDescription":"連續包月：訂閱之日起一個月內，每個智能體不限使用次數。","yearlyDescription":"連續包年：訂閱之日起一年內，每個智能體不限使用次數","subscribed":"已訂閱","freeUsing":"免費使用中…","cancelSubscription":"取消訂閱","subscriptionValidUntil":"您的訂閱可使用至 {date}","monthlySubscribing":"連續包月中…","yearlySubscribing":"連續包年中…","cancelMonthly":"取消包月","cancelYearly":"取消包年","cancelConfirmTitle":"提示","cancelMonthlyConfirm":"取消包月在{date}號生效，再次使用需要重新訂閱。是否確認取消？","cancelYearlyConfirm":"取消包年在{date}號生次生效，再次使用需要重新訂閱。是否確認取消？","month":"月","quarter":"季","year":"年"},"chat":{"aiGeneratedContent":"內容由 AI 生成，僅供參考","replyTime":"回覆時間：","conversationList":"對話列表","rename":"重新命名","conversationRename":"會話重新命名","pleaseEnter":"請輸入","conversationRenameSuccess":"會話重新命名成功","deleteSuccess":"刪除成功","uploadFile":"上傳檔案","unsupportedFileType":"不支援的檔案類型：","clickOrDragToUpload":"點擊或拖拽檔案到此區域上傳","supportedFileTypes":"支援的檔案類型：","waitForUploadComplete":"請等待所有檔案上傳完成"}}'),lE=JSON.parse('{"common":{"login":"Login","logout":"Logout","goHome":"Go Home","newConversation":"New Conversation","noSessions":"No Sessions","startConfig":"Start Configuration","noDifyAppConfig":"No Dify App Configuration","getConversationListFailed":"Failed to get conversation list","confirm":"Confirm","cancel":"Cancel","save":"Save","delete":"Delete","edit":"Edit","add":"Add","update":"Update","close":"Close","current":"Current","none":"None","tags":"Tags","basicInfo":"Basic Information","pleaseLoginFirst":"Please login to purchase","loginNow":"Login Now","subscriptionFailed":"Subscription failed","subscriptionSuccess":"Subscription successful","pleaseSelectPeriod":"Please select subscription period","pleaseAgreeToTerms":"Please read and agree to the terms before activation","agreeAndPay":" confirm terms and pay","freeTrial":"Free Trial","rmb":"RMB","usd":"USD","writing":"Writing"},"app":{"addAppConfigSuccess":"App configuration added successfully","updateAppConfigSuccess":"App configuration updated successfully","management":"App Configuration Management","appManagement":"App Management","addApp":"Add App","addAppConfig":"Add App Configuration","appConfigDetail":"App Configuration Details","appName":"App Name","appDescription":"App Description","appTags":"App Tags","noApps":"No Apps","confirmDeleteApp":"Are you sure to delete this app?","deleteAppSuccess":"App deleted successfully","saveAppConfigFailed":"Failed to save app configuration","currentActiveAppId":"Current active app ID","appList":"App List","getAppList":"Get App List","appListLoading":"App list loading","deleteAppSuccessCallback":"Delete app success callback","getDifyAppInfo":"Get Dify app information"},"payment":{"subscribe":"Subscribe","free":"Free","monthlySubscription":"Monthly Subscription","yearlySubscription":"Yearly Subscription","upgradeSubscription":"Upgrade Subscription","modifySubscription":"Modify Subscription","supportAlipay":"Alipay Payment","meisiAccount":"Meisi Account","subscriptionDescription":"Meisi AI Subscription Description","freeDescription":"Free: Each AI agent has a usage limit of {num} times per natural month. Counting resets at the beginning of each month.","monthlyDescription":"Monthly Subscription: Unlimited usage of each AI agent for one month from the subscription date.","yearlyDescription":"Yearly Subscription: Unlimited usage of each AI agent for one year from the subscription date","subscribed":"Subscribed","freeUsing":"Using free version...","cancelSubscription":"Cancel Subscription","subscriptionValidUntil":"Your subscription is valid until {date}","monthlySubscribing":"Monthly subscription active...","yearlySubscribing":"Yearly subscription active...","cancelMonthly":"Cancel Monthly","cancelYearly":"Cancel Yearly","cancelConfirmTitle":"Notice","cancelMonthlyConfirm":"Monthly cancellation will take effect on the {date}th. You need to resubscribe to use again. Are you sure to cancel?","cancelYearlyConfirm":"Yearly cancellation will take effect on the {date}th. You need to resubscribe to use again. Are you sure to cancel?","month":"Month","quarter":"Quarter","year":"Year"},"chat":{"aiGeneratedContent":"Content generated by AI, for reference only","replyTime":"Reply time: ","conversationList":"Conversation List","rename":"Rename","conversationRename":"Conversation Rename","pleaseEnter":"Please enter","conversationRenameSuccess":"Conversation renamed successfully","deleteSuccess":"Deleted successfully","uploadFile":"Upload File","unsupportedFileType":"Unsupported file type: ","clickOrDragToUpload":"Click or drag files to this area to upload","supportedFileTypes":"Supported file types: ","waitForUploadComplete":"Please wait for all files to upload"}}'),lI=JSON.parse('{"common":{"login":"Đăng nhập","logout":"Đăng xuất","goHome":"Về trang chủ","newConversation":"Cuộc tr\xf2 chuyện mới","noSessions":"Kh\xf4ng c\xf3 phi\xean","startConfig":"Bắt đầu cấu h\xecnh","noDifyAppConfig":"Kh\xf4ng c\xf3 cấu h\xecnh ứng dụng Dify","getConversationListFailed":"Kh\xf4ng thể lấy danh s\xe1ch cuộc tr\xf2 chuyện","confirm":"X\xe1c nhận","cancel":"Hủy","save":"Lưu","delete":"X\xf3a","edit":"Chỉnh sửa","add":"Th\xeam","update":"Cập nhật","close":"Đ\xf3ng","current":"Hiện tại","none":"Kh\xf4ng c\xf3","tags":"Thẻ","basicInfo":"Th\xf4ng tin cơ bản","pleaseLoginFirst":"Vui l\xf2ng đăng nhập để mua","loginNow":"Đăng nhập ngay","subscriptionFailed":"Đăng k\xfd thất bại","subscriptionSuccess":"Đăng k\xfd th\xe0nh c\xf4ng","pleaseSelectPeriod":"Vui l\xf2ng chọn chu kỳ đăng k\xfd","pleaseAgreeToTerms":"Vui l\xf2ng đọc v\xe0 đồng \xfd với điều khoản trước khi k\xedch hoạt","agreeAndPay":" x\xe1c nhận điều khoản v\xe0 thanh to\xe1n","freeTrial":"D\xf9ng thử miễn ph\xed","rmb":"Nh\xe2n d\xe2n tệ","usd":"USD","writing":"Viết"},"app":{"addAppConfigSuccess":"Th\xeam cấu h\xecnh ứng dụng th\xe0nh c\xf4ng","updateAppConfigSuccess":"Cập nhật cấu h\xecnh ứng dụng th\xe0nh c\xf4ng","management":"Quản l\xfd cấu h\xecnh ứng dụng","appManagement":"Quản l\xfd ứng dụng","addApp":"Th\xeam ứng dụng","addAppConfig":"Th\xeam cấu h\xecnh ứng dụng","appConfigDetail":"Chi tiết cấu h\xecnh ứng dụng","appName":"T\xean ứng dụng","appDescription":"M\xf4 tả ứng dụng","appTags":"Thẻ ứng dụng","noApps":"Kh\xf4ng c\xf3 ứng dụng","confirmDeleteApp":"Bạn c\xf3 chắc chắn muốn x\xf3a ứng dụng n\xe0y?","deleteAppSuccess":"X\xf3a ứng dụng th\xe0nh c\xf4ng","saveAppConfigFailed":"Lưu cấu h\xecnh ứng dụng thất bại","currentActiveAppId":"ID ứng dụng hiện tại đang hoạt động","appList":"Danh s\xe1ch ứng dụng","getAppList":"Lấy danh s\xe1ch ứng dụng","appListLoading":"Đang tải danh s\xe1ch ứng dụng","deleteAppSuccessCallback":"Callback x\xf3a ứng dụng th\xe0nh c\xf4ng","getDifyAppInfo":"Lấy th\xf4ng tin ứng dụng Dify"},"payment":{"subscribe":"Đăng k\xfd","free":"Miễn ph\xed","monthlySubscription":"Đăng k\xfd h\xe0ng th\xe1ng","yearlySubscription":"Đăng k\xfd h\xe0ng năm","upgradeSubscription":"N\xe2ng cấp đăng k\xfd","modifySubscription":"Sửa đổi đăng k\xfd","supportAlipay":"Thanh to\xe1n Alipay","meisiAccount":"T\xe0i khoản Meisi","subscriptionDescription":"M\xf4 tả đăng k\xfd Meisi AI","freeDescription":"Miễn ph\xed: Mỗi AI agent c\xf3 giới hạn sử dụng {num} lần mỗi th\xe1ng tự nhi\xean. Đếm lại từ đầu mỗi th\xe1ng.","monthlyDescription":"Đăng k\xfd h\xe0ng th\xe1ng: Sử dụng kh\xf4ng giới hạn mỗi AI agent trong một th\xe1ng kể từ ng\xe0y đăng k\xfd.","yearlyDescription":"Đăng k\xfd h\xe0ng năm: Sử dụng kh\xf4ng giới hạn mỗi AI agent trong một năm kể từ ng\xe0y đăng k\xfd","subscribed":"Đ\xe3 đăng k\xfd","freeUsing":"Đang sử dụng phi\xean bản miễn ph\xed...","cancelSubscription":"Hủy đăng k\xfd","subscriptionValidUntil":"Đăng k\xfd của bạn c\xf3 hiệu lực đến {date}","monthlySubscribing":"Đăng k\xfd h\xe0ng th\xe1ng đang hoạt động...","yearlySubscribing":"Đăng k\xfd h\xe0ng năm đang hoạt động...","cancelMonthly":"Hủy h\xe0ng th\xe1ng","cancelYearly":"Hủy h\xe0ng năm","cancelConfirmTitle":"Th\xf4ng b\xe1o","cancelMonthlyConfirm":"Việc hủy h\xe0ng th\xe1ng sẽ c\xf3 hiệu lực v\xe0o ng\xe0y {date}. Bạn cần đăng k\xfd lại để sử dụng. Bạn c\xf3 chắc chắn muốn hủy?","cancelYearlyConfirm":"Việc hủy h\xe0ng năm sẽ c\xf3 hiệu lực v\xe0o ng\xe0y {date}. Bạn cần đăng k\xfd lại để sử dụng. Bạn c\xf3 chắc chắn muốn hủy?","month":"Th\xe1ng","quarter":"Qu\xfd","year":"Năm"},"chat":{"aiGeneratedContent":"Nội dung được tạo bởi AI, chỉ để tham khảo","replyTime":"Thời gian trả lời: ","conversationList":"Danh s\xe1ch cuộc tr\xf2 chuyện","rename":"Đổi t\xean","conversationRename":"Đổi t\xean cuộc tr\xf2 chuyện","pleaseEnter":"Vui l\xf2ng nhập","conversationRenameSuccess":"Đổi t\xean cuộc tr\xf2 chuyện th\xe0nh c\xf4ng","deleteSuccess":"X\xf3a th\xe0nh c\xf4ng","uploadFile":"Tải l\xean tệp","unsupportedFileType":"Loại tệp kh\xf4ng được hỗ trợ: ","clickOrDragToUpload":"Nhấp hoặc k\xe9o tệp v\xe0o khu vực n\xe0y để tải l\xean","supportedFileTypes":"C\xe1c loại tệp được hỗ trợ: ","waitForUploadComplete":"Vui l\xf2ng đợi tất cả tệp tải l\xean ho\xe0n tất"}}'),lO=JSON.parse('{"common":{"login":"Iniciar sesi\xf3n","logout":"Cerrar sesi\xf3n","goHome":"Ir al inicio","newConversation":"Nueva conversaci\xf3n","noSessions":"Sin sesiones","startConfig":"Iniciar configuraci\xf3n","noDifyAppConfig":"Sin configuraci\xf3n de aplicaci\xf3n Dify","getConversationListFailed":"Error al obtener la lista de conversaciones","confirm":"Confirmar","cancel":"Cancelar","save":"Guardar","delete":"Eliminar","edit":"Editar","add":"Agregar","update":"Actualizar","close":"Cerrar","current":"Actual","none":"Ninguno","tags":"Etiquetas","basicInfo":"Informaci\xf3n b\xe1sica","pleaseLoginFirst":"Por favor inicia sesi\xf3n para comprar","loginNow":"Iniciar sesi\xf3n ahora","subscriptionFailed":"Suscripci\xf3n fallida","subscriptionSuccess":"Suscripci\xf3n exitosa","pleaseSelectPeriod":"Por favor selecciona el per\xedodo de suscripci\xf3n","pleaseAgreeToTerms":"Por favor lee y acepta los t\xe9rminos antes de activar","agreeAndPay":" confirmar t\xe9rminos y pagar","freeTrial":"Prueba gratuita","rmb":"RMB","usd":"USD","writing":"Escritura"},"app":{"addAppConfigSuccess":"Configuraci\xf3n de aplicaci\xf3n agregada exitosamente","updateAppConfigSuccess":"Configuraci\xf3n de aplicaci\xf3n actualizada exitosamente","management":"Gesti\xf3n de configuraci\xf3n de aplicaciones","appManagement":"Gesti\xf3n de aplicaciones","addApp":"Agregar aplicaci\xf3n","addAppConfig":"Agregar configuraci\xf3n de aplicaci\xf3n","appConfigDetail":"Detalles de configuraci\xf3n de aplicaci\xf3n","appName":"Nombre de aplicaci\xf3n","appDescription":"Descripci\xf3n de aplicaci\xf3n","appTags":"Etiquetas de aplicaci\xf3n","noApps":"Sin aplicaciones","confirmDeleteApp":"\xbfEst\xe1s seguro de eliminar esta aplicaci\xf3n?","deleteAppSuccess":"Aplicaci\xf3n eliminada exitosamente","saveAppConfigFailed":"Error al guardar configuraci\xf3n de aplicaci\xf3n","currentActiveAppId":"ID de aplicaci\xf3n activa actual","appList":"Lista de aplicaciones","getAppList":"Obtener lista de aplicaciones","appListLoading":"Cargando lista de aplicaciones","deleteAppSuccessCallback":"Callback de eliminaci\xf3n exitosa de aplicaci\xf3n","getDifyAppInfo":"Obtener informaci\xf3n de aplicaci\xf3n Dify"},"payment":{"subscribe":"Suscribirse","free":"Gratis","monthlySubscription":"Suscripci\xf3n mensual","yearlySubscription":"Suscripci\xf3n anual","upgradeSubscription":"Actualizar suscripci\xf3n","modifySubscription":"Modificar suscripci\xf3n","supportAlipay":"Pago con Alipay","meisiAccount":"Cuenta Meisi","subscriptionDescription":"Descripci\xf3n de suscripci\xf3n Meisi AI","freeDescription":"Gratis: Cada agente de IA tiene un l\xedmite de uso de {num} veces por mes natural. El conteo se reinicia al comienzo de cada mes.","monthlyDescription":"Suscripci\xf3n mensual: Uso ilimitado de cada agente de IA durante un mes desde la fecha de suscripci\xf3n.","yearlyDescription":"Suscripci\xf3n anual: Uso ilimitado de cada agente de IA durante un a\xf1o desde la fecha de suscripci\xf3n","subscribed":"Suscrito","freeUsing":"Usando versi\xf3n gratuita...","cancelSubscription":"Cancelar suscripci\xf3n","subscriptionValidUntil":"Tu suscripci\xf3n es v\xe1lida hasta {date}","monthlySubscribing":"Suscripci\xf3n mensual activa...","yearlySubscribing":"Suscripci\xf3n anual activa...","cancelMonthly":"Cancelar mensual","cancelYearly":"Cancelar anual","cancelConfirmTitle":"Aviso","cancelMonthlyConfirm":"La cancelaci\xf3n mensual tendr\xe1 efecto el d\xeda {date}. Necesitas volver a suscribirte para usar nuevamente. \xbfEst\xe1s seguro de cancelar?","cancelYearlyConfirm":"La cancelaci\xf3n anual tendr\xe1 efecto el d\xeda {date}. Necesitas volver a suscribirte para usar nuevamente. \xbfEst\xe1s seguro de cancelar?","month":"Mes","quarter":"Trimestre","year":"A\xf1o"},"chat":{"aiGeneratedContent":"Contenido generado por IA, solo para referencia","replyTime":"Tiempo de respuesta: ","conversationList":"Lista de conversaciones","rename":"Renombrar","conversationRename":"Renombrar conversaci\xf3n","pleaseEnter":"Por favor ingrese","conversationRenameSuccess":"Conversaci\xf3n renombrada exitosamente","deleteSuccess":"Eliminado exitosamente","uploadFile":"Subir archivo","unsupportedFileType":"Tipo de archivo no soportado: ","clickOrDragToUpload":"Haga clic o arrastre archivos a esta \xe1rea para subir","supportedFileTypes":"Tipos de archivo soportados: ","waitForUploadComplete":"Por favor espere a que todos los archivos se suban"}}'),lP=JSON.parse('{"common":{"login":"تسجيل الدخول","logout":"تسجيل الخروج","goHome":"العودة للرئيسية","newConversation":"محادثة جديدة","noSessions":"لا توجد جلسات","startConfig":"بدء التكوين","noDifyAppConfig":"لا يوجد تكوين تطبيق Dify","getConversationListFailed":"فشل في الحصول على قائمة المحادثات","confirm":"تأكيد","cancel":"إلغاء","save":"حفظ","delete":"حذف","edit":"تحرير","add":"إضافة","update":"تحديث","close":"إغلاق","current":"الحالي","none":"لا يوجد","tags":"العلامات","basicInfo":"المعلومات الأساسية","pleaseLoginFirst":"يرجى تسجيل الدخول للشراء","loginNow":"تسجيل الدخول الآن","subscriptionFailed":"فشل الاشتراك","subscriptionSuccess":"نجح الاشتراك","pleaseSelectPeriod":"يرجى اختيار فترة الاشتراك","pleaseAgreeToTerms":"يرجى قراءة الشروط والموافقة عليها قبل التفعيل","agreeAndPay":" تأكيد الشروط والدفع","freeTrial":"تجربة مجانية","rmb":"يوان صيني","usd":"دولار أمريكي","writing":"كتابة"},"app":{"addAppConfigSuccess":"تم إضافة تكوين التطبيق بنجاح","updateAppConfigSuccess":"تم تحديث تكوين التطبيق بنجاح","management":"إدارة تكوين التطبيقات","appManagement":"إدارة التطبيقات","addApp":"إضافة تطبيق","addAppConfig":"إضافة تكوين التطبيق","appConfigDetail":"تفاصيل تكوين التطبيق","appName":"اسم التطبيق","appDescription":"وصف التطبيق","appTags":"علامات التطبيق","noApps":"لا توجد تطبيقات","confirmDeleteApp":"هل أنت متأكد من حذف هذا التطبيق؟","deleteAppSuccess":"تم حذف التطبيق بنجاح","saveAppConfigFailed":"فشل في حفظ تكوين التطبيق","currentActiveAppId":"معرف التطبيق النشط الحالي","appList":"قائمة التطبيقات","getAppList":"الحصول على قائمة التطبيقات","appListLoading":"جاري تحميل قائمة التطبيقات","deleteAppSuccessCallback":"استدعاء نجح حذف التطبيق","getDifyAppInfo":"الحصول على معلومات تطبيق Dify"},"payment":{"subscribe":"اشتراك","free":"مجاني","monthlySubscription":"اشتراك شهري","yearlySubscription":"اشتراك سنوي","upgradeSubscription":"ترقية الاشتراك","modifySubscription":"تعديل الاشتراك","supportAlipay":"دفع عبر Alipay","meisiAccount":"حساب Meisi","subscriptionDescription":"وصف اشتراك Meisi AI","freeDescription":"مجاني: كل وكيل ذكي له حد استخدام {num} مرة شهرياً. يعاد العد في بداية كل شهر.","monthlyDescription":"الاشتراك الشهري: استخدام غير محدود لكل وكيل ذكي لمدة شهر من تاريخ الاشتراك.","yearlyDescription":"الاشتراك السنوي: استخدام غير محدود لكل وكيل ذكي لمدة سنة من تاريخ الاشتراك","subscribed":"مشترك","freeUsing":"يستخدم النسخة المجانية...","cancelSubscription":"إلغاء الاشتراك","subscriptionValidUntil":"اشتراكك صالح حتى {date}","monthlySubscribing":"الاشتراك الشهري نشط...","yearlySubscribing":"الاشتراك السنوي نشط...","cancelMonthly":"إلغاء الشهري","cancelYearly":"إلغاء السنوي","cancelConfirmTitle":"إشعار","cancelMonthlyConfirm":"سيصبح إلغاء الاشتراك الشهري ساري المفعول في اليوم {date}. تحتاج إلى إعادة الاشتراك للاستخدام مرة أخرى. هل أنت متأكد من الإلغاء؟","cancelYearlyConfirm":"سيصبح إلغاء الاشتراك السنوي ساري المفعول في اليوم {date}. تحتاج إلى إعادة الاشتراك للاستخدام مرة أخرى. هل أنت متأكد من الإلغاء؟","month":"شهر","quarter":"ربع سنة","year":"سنة"},"chat":{"aiGeneratedContent":"محتوى مُولد بواسطة الذكاء الاصطناعي، للمرجع فقط","replyTime":"وقت الرد: ","conversationList":"قائمة المحادثات","rename":"إعادة تسمية","conversationRename":"إعادة تسمية المحادثة","pleaseEnter":"يرجى الإدخال","conversationRenameSuccess":"تم إعادة تسمية المحادثة بنجاح","deleteSuccess":"تم الحذف بنجاح","uploadFile":"رفع ملف","unsupportedFileType":"نوع ملف غير مدعوم: ","clickOrDragToUpload":"انقر أو اسحب الملفات إلى هذه المنطقة للرفع","supportedFileTypes":"أنواع الملفات المدعومة: ","waitForUploadComplete":"يرجى انتظار اكتمال رفع جميع الملفات"}}'),lM=JSON.parse('{"common":{"login":"Masuk","logout":"Keluar","goHome":"Ke Beranda","newConversation":"Percakapan Baru","noSessions":"Tidak Ada Sesi","startConfig":"Mulai Konfigurasi","noDifyAppConfig":"Tidak Ada Konfigurasi Aplikasi Dify","getConversationListFailed":"Gagal mendapatkan daftar percakapan","confirm":"Konfirmasi","cancel":"Batal","save":"Simpan","delete":"Hapus","edit":"Edit","add":"Tambah","update":"Perbarui","close":"Tutup","current":"Saat ini","none":"Tidak ada","tags":"Tag","basicInfo":"Informasi Dasar","pleaseLoginFirst":"Silakan login untuk membeli","loginNow":"Login Sekarang","subscriptionFailed":"Langganan gagal","subscriptionSuccess":"Langganan berhasil","pleaseSelectPeriod":"Silakan pilih periode langganan","pleaseAgreeToTerms":"Silakan baca dan setujui syarat sebelum aktivasi","agreeAndPay":" konfirmasi syarat dan bayar","freeTrial":"Uji Coba Gratis","rmb":"RMB","usd":"USD","writing":"Menulis"},"app":{"addAppConfigSuccess":"Konfigurasi aplikasi berhasil ditambahkan","updateAppConfigSuccess":"Konfigurasi aplikasi berhasil diperbarui","management":"Manajemen Konfigurasi Aplikasi","appManagement":"Manajemen Aplikasi","addApp":"Tambah Aplikasi","addAppConfig":"Tambah Konfigurasi Aplikasi","appConfigDetail":"Detail Konfigurasi Aplikasi","appName":"Nama Aplikasi","appDescription":"Deskripsi Aplikasi","appTags":"Tag Aplikasi","noApps":"Tidak Ada Aplikasi","confirmDeleteApp":"Apakah Anda yakin ingin menghapus aplikasi ini?","deleteAppSuccess":"Aplikasi berhasil dihapus","saveAppConfigFailed":"Gagal menyimpan konfigurasi aplikasi","currentActiveAppId":"ID aplikasi aktif saat ini","appList":"Daftar Aplikasi","getAppList":"Dapatkan Daftar Aplikasi","appListLoading":"Memuat daftar aplikasi","deleteAppSuccessCallback":"Callback penghapusan aplikasi berhasil","getDifyAppInfo":"Dapatkan informasi aplikasi Dify"},"payment":{"subscribe":"Berlangganan","free":"Gratis","monthlySubscription":"Langganan Bulanan","yearlySubscription":"Langganan Tahunan","upgradeSubscription":"Upgrade Langganan","modifySubscription":"Ubah Langganan","supportAlipay":"Pembayaran Alipay","meisiAccount":"Akun Meisi","subscriptionDescription":"Deskripsi Langganan Meisi AI","freeDescription":"Gratis: Setiap agen AI memiliki batas penggunaan {num} kali per bulan alami. Penghitungan ulang di awal setiap bulan.","monthlyDescription":"Langganan Bulanan: Penggunaan tak terbatas setiap agen AI selama satu bulan dari tanggal langganan.","yearlyDescription":"Langganan Tahunan: Penggunaan tak terbatas setiap agen AI selama satu tahun dari tanggal langganan","subscribed":"Berlangganan","freeUsing":"Menggunakan versi gratis...","cancelSubscription":"Batalkan Langganan","subscriptionValidUntil":"Langganan Anda berlaku hingga {date}","monthlySubscribing":"Langganan bulanan aktif...","yearlySubscribing":"Langganan tahunan aktif...","cancelMonthly":"Batalkan Bulanan","cancelYearly":"Batalkan Tahunan","cancelConfirmTitle":"Pemberitahuan","cancelMonthlyConfirm":"Pembatalan bulanan akan berlaku pada tanggal {date}. Anda perlu berlangganan lagi untuk menggunakan. Apakah Anda yakin ingin membatalkan?","cancelYearlyConfirm":"Pembatalan tahunan akan berlaku pada tanggal {date}. Anda perlu berlangganan lagi untuk menggunakan. Apakah Anda yakin ingin membatalkan?","month":"Bulan","quarter":"Kuartal","year":"Tahun"},"chat":{"aiGeneratedContent":"Konten yang dihasilkan oleh AI, hanya untuk referensi","replyTime":"Waktu balasan: ","conversationList":"Daftar Percakapan","rename":"Ubah nama","conversationRename":"Ubah nama percakapan","pleaseEnter":"Silakan masukkan","conversationRenameSuccess":"Percakapan berhasil diubah namanya","deleteSuccess":"Berhasil dihapus","uploadFile":"Unggah File","unsupportedFileType":"Jenis file tidak didukung: ","clickOrDragToUpload":"Klik atau seret file ke area ini untuk mengunggah","supportedFileTypes":"Jenis file yang didukung: ","waitForUploadComplete":"Harap tunggu semua file selesai diunggah"}}'),lL=JSON.parse('{"common":{"login":"Entrar","logout":"Sair","goHome":"Ir para In\xedcio","newConversation":"Nova Conversa","noSessions":"Nenhuma Sess\xe3o","startConfig":"Iniciar Configura\xe7\xe3o","noDifyAppConfig":"Nenhuma Configura\xe7\xe3o de Aplicativo Dify","getConversationListFailed":"Falha ao obter lista de conversas","confirm":"Confirmar","cancel":"Cancelar","save":"Salvar","delete":"Excluir","edit":"Editar","add":"Adicionar","update":"Atualizar","close":"Fechar","current":"Atual","none":"Nenhum","tags":"Tags","basicInfo":"Informa\xe7\xf5es B\xe1sicas","pleaseLoginFirst":"Por favor, fa\xe7a login para comprar","loginNow":"Entrar Agora","subscriptionFailed":"Assinatura falhou","subscriptionSuccess":"Assinatura bem-sucedida","pleaseSelectPeriod":"Por favor, selecione o per\xedodo de assinatura","pleaseAgreeToTerms":"Por favor, leia e concorde com os termos antes da ativa\xe7\xe3o","agreeAndPay":" confirmar termos e pagar","freeTrial":"Teste Gratuito","rmb":"RMB","usd":"USD","writing":"Escrita"},"app":{"addAppConfigSuccess":"Configura\xe7\xe3o do aplicativo adicionada com sucesso","updateAppConfigSuccess":"Configura\xe7\xe3o do aplicativo atualizada com sucesso","management":"Gerenciamento de Configura\xe7\xe3o de Aplicativos","appManagement":"Gerenciamento de Aplicativos","addApp":"Adicionar Aplicativo","addAppConfig":"Adicionar Configura\xe7\xe3o de Aplicativo","appConfigDetail":"Detalhes da Configura\xe7\xe3o do Aplicativo","appName":"Nome do Aplicativo","appDescription":"Descri\xe7\xe3o do Aplicativo","appTags":"Tags do Aplicativo","noApps":"Nenhum Aplicativo","confirmDeleteApp":"Tem certeza de que deseja excluir este aplicativo?","deleteAppSuccess":"Aplicativo exclu\xeddo com sucesso","saveAppConfigFailed":"Falha ao salvar configura\xe7\xe3o do aplicativo","currentActiveAppId":"ID do aplicativo ativo atual","appList":"Lista de Aplicativos","getAppList":"Obter Lista de Aplicativos","appListLoading":"Carregando lista de aplicativos","deleteAppSuccessCallback":"Callback de exclus\xe3o de aplicativo bem-sucedida","getDifyAppInfo":"Obter informa\xe7\xf5es do aplicativo Dify"},"payment":{"subscribe":"Assinar","free":"Gr\xe1tis","monthlySubscription":"Assinatura Mensal","yearlySubscription":"Assinatura Anual","upgradeSubscription":"Atualizar Assinatura","modifySubscription":"Modificar Assinatura","supportAlipay":"Pagamento Alipay","meisiAccount":"Conta Meisi","subscriptionDescription":"Descri\xe7\xe3o da Assinatura Meisi AI","freeDescription":"Gr\xe1tis: Cada agente de IA tem um limite de uso de {num} vezes por m\xeas natural. A contagem reinicia no in\xedcio de cada m\xeas.","monthlyDescription":"Assinatura Mensal: Uso ilimitado de cada agente de IA por um m\xeas a partir da data de assinatura.","yearlyDescription":"Assinatura Anual: Uso ilimitado de cada agente de IA por um ano a partir da data de assinatura","subscribed":"Assinado","freeUsing":"Usando vers\xe3o gratuita...","cancelSubscription":"Cancelar Assinatura","subscriptionValidUntil":"Sua assinatura \xe9 v\xe1lida at\xe9 {date}","monthlySubscribing":"Assinatura mensal ativa...","yearlySubscribing":"Assinatura anual ativa...","cancelMonthly":"Cancelar Mensal","cancelYearly":"Cancelar Anual","cancelConfirmTitle":"Aviso","cancelMonthlyConfirm":"O cancelamento mensal entrar\xe1 em vigor no dia {date}. Voc\xea precisa assinar novamente para usar. Tem certeza de que deseja cancelar?","cancelYearlyConfirm":"O cancelamento anual entrar\xe1 em vigor no dia {date}. Voc\xea precisa assinar novamente para usar. Tem certeza de que deseja cancelar?","month":"M\xeas","quarter":"Trimestre","year":"Ano"},"chat":{"aiGeneratedContent":"Conte\xfado gerado por IA, apenas para refer\xeancia","replyTime":"Tempo de resposta: ","conversationList":"Lista de Conversas","rename":"Renomear","conversationRename":"Renomear conversa","pleaseEnter":"Por favor, digite","conversationRenameSuccess":"Conversa renomeada com sucesso","deleteSuccess":"Exclu\xeddo com sucesso","uploadFile":"Enviar Arquivo","unsupportedFileType":"Tipo de arquivo n\xe3o suportado: ","clickOrDragToUpload":"Clique ou arraste arquivos para esta \xe1rea para enviar","supportedFileTypes":"Tipos de arquivo suportados: ","waitForUploadComplete":"Aguarde todos os arquivos serem enviados"}}'),lD=JSON.parse('{"common":{"login":"ログイン","logout":"ログアウト","goHome":"ホームに戻る","newConversation":"新しい会話","noSessions":"セッションなし","startConfig":"設定を開始","noDifyAppConfig":"Difyアプリの設定がありません","getConversationListFailed":"会話リストの取得に失敗しました","confirm":"確認","cancel":"キャンセル","save":"保存","delete":"削除","edit":"編集","add":"追加","update":"更新","close":"閉じる","current":"現在","none":"なし","tags":"タグ","basicInfo":"基本情報","pleaseLoginFirst":"購入するにはログインしてください","loginNow":"今すぐログイン","subscriptionFailed":"購読に失敗しました","subscriptionSuccess":"購読に成功しました","pleaseSelectPeriod":"購読期間を選択してください","pleaseAgreeToTerms":"アクティベーション前に利用規約をお読みになり、同意してください","agreeAndPay":" 利用規約に同意して支払う","freeTrial":"無料トライアル","rmb":"人民元","usd":"米ドル","writing":"ライティング"},"app":{"addAppConfigSuccess":"アプリ設定の追加に成功しました","updateAppConfigSuccess":"アプリ設定の更新に成功しました","management":"アプリ設定管理","appManagement":"アプリ管理","addApp":"アプリを追加","addAppConfig":"アプリ設定を追加","appConfigDetail":"アプリ設定詳細","appName":"アプリ名","appDescription":"アプリの説明","appTags":"アプリタグ","noApps":"アプリがありません","confirmDeleteApp":"このアプリを削除してもよろしいですか？","deleteAppSuccess":"アプリの削除に成功しました","saveAppConfigFailed":"アプリ設定の保存に失敗しました","currentActiveAppId":"現在アクティブなアプリID","appList":"アプリリスト","getAppList":"アプリリストを取得","appListLoading":"アプリリストを読み込み中","deleteAppSuccessCallback":"アプリ削除成功コールバック","getDifyAppInfo":"Difyアプリ情報を取得"},"payment":{"subscribe":"購読","free":"無料","monthlySubscription":"月額購読","yearlySubscription":"年額購読","upgradeSubscription":"購読をアップグレード","modifySubscription":"購読を変更","supportAlipay":"Alipay決済","meisiAccount":"Meisiアカウント","subscriptionDescription":"Meisi AI購読説明","freeDescription":"無料：各AIエージェントは自然月あたり{num}回の使用制限があります。毎月初めにカウントがリセットされます。","monthlyDescription":"月額購読：購読日から1ヶ月間、各AIエージェントを無制限に使用できます。","yearlyDescription":"年額購読：購読日から1年間、各AIエージェントを無制限に使用できます","subscribed":"購読済み","freeUsing":"無料版を使用中...","cancelSubscription":"購読をキャンセル","subscriptionValidUntil":"あなたの購読は{date}まで有効です","monthlySubscribing":"月額購読がアクティブ...","yearlySubscribing":"年額購読がアクティブ...","cancelMonthly":"月額をキャンセル","cancelYearly":"年額をキャンセル","cancelConfirmTitle":"お知らせ","cancelMonthlyConfirm":"月額キャンセルは{date}日に有効になります。再度使用するには再購読が必要です。キャンセルしてもよろしいですか？","cancelYearlyConfirm":"年額キャンセルは{date}日に有効になります。再度使用するには再購読が必要です。キャンセルしてもよろしいですか？","month":"月","quarter":"四半期","year":"年"},"chat":{"aiGeneratedContent":"AIによって生成されたコンテンツ、参考用のみ","replyTime":"返信時間：","conversationList":"会話リスト","rename":"名前を変更","conversationRename":"会話の名前を変更","pleaseEnter":"入力してください","conversationRenameSuccess":"会話の名前変更に成功しました","deleteSuccess":"削除に成功しました","uploadFile":"ファイルをアップロード","unsupportedFileType":"サポートされていないファイルタイプ：","clickOrDragToUpload":"この領域をクリックまたはファイルをドラッグしてアップロード","supportedFileTypes":"サポートされているファイルタイプ：","waitForUploadComplete":"すべてのファイルのアップロードが完了するまでお待ちください"}}'),lF=JSON.parse('{"common":{"login":"로그인","logout":"로그아웃","goHome":"홈으로 가기","newConversation":"새 대화","noSessions":"세션 없음","startConfig":"구성 시작","noDifyAppConfig":"Dify 앱 구성이 없습니다","getConversationListFailed":"대화 목록을 가져오는데 실패했습니다","confirm":"확인","cancel":"취소","save":"저장","delete":"삭제","edit":"편집","add":"추가","update":"업데이트","close":"닫기","current":"현재","none":"없음","tags":"태그","basicInfo":"기본 정보","pleaseLoginFirst":"구매하려면 로그인하세요","loginNow":"지금 로그인","subscriptionFailed":"구독 실패","subscriptionSuccess":"구독 성공","pleaseSelectPeriod":"구독 기간을 선택하세요","pleaseAgreeToTerms":"활성화 전에 약관을 읽고 동의하세요","agreeAndPay":" 약관에 동의하고 결제","freeTrial":"무료 체험","rmb":"위안화","usd":"달러","writing":"글쓰기"},"app":{"addAppConfigSuccess":"앱 구성이 성공적으로 추가되었습니다","updateAppConfigSuccess":"앱 구성이 성공적으로 업데이트되었습니다","management":"앱 구성 관리","appManagement":"앱 관리","addApp":"앱 추가","addAppConfig":"앱 구성 추가","appConfigDetail":"앱 구성 세부사항","appName":"앱 이름","appDescription":"앱 설명","appTags":"앱 태그","noApps":"앱 없음","confirmDeleteApp":"이 앱을 삭제하시겠습니까?","deleteAppSuccess":"앱이 성공적으로 삭제되었습니다","saveAppConfigFailed":"앱 구성 저장 실패","currentActiveAppId":"현재 활성 앱 ID","appList":"앱 목록","getAppList":"앱 목록 가져오기","appListLoading":"앱 목록 로딩 중","deleteAppSuccessCallback":"앱 삭제 성공 콜백","getDifyAppInfo":"Dify 앱 정보 가져오기"},"payment":{"subscribe":"구독","free":"무료","monthlySubscription":"월간 구독","yearlySubscription":"연간 구독","upgradeSubscription":"구독 업그레이드","modifySubscription":"구독 수정","supportAlipay":"Alipay 결제","meisiAccount":"Meisi 계정","subscriptionDescription":"Meisi AI 구독 설명","freeDescription":"무료: 각 AI 에이전트는 자연월당 {num}회 사용 제한이 있습니다. 매월 초에 카운트가 재설정됩니다.","monthlyDescription":"월간 구독: 구독일로부터 한 달 동안 각 AI 에이전트를 무제한 사용할 수 있습니다.","yearlyDescription":"연간 구독: 구독일로부터 일 년 동안 각 AI 에이전트를 무제한 사용할 수 있습니다","subscribed":"구독됨","freeUsing":"무료 버전 사용 중...","cancelSubscription":"구독 취소","subscriptionValidUntil":"구독이 {date}까지 유효합니다","monthlySubscribing":"월간 구독 활성...","yearlySubscribing":"연간 구독 활성...","cancelMonthly":"월간 취소","cancelYearly":"연간 취소","cancelConfirmTitle":"알림","cancelMonthlyConfirm":"월간 취소는 {date}일에 효력이 발생합니다. 다시 사용하려면 재구독이 필요합니다. 취소하시겠습니까?","cancelYearlyConfirm":"연간 취소는 {date}일에 효력이 발생합니다. 다시 사용하려면 재구독이 필요합니다. 취소하시겠습니까?","month":"월","quarter":"분기","year":"년"},"chat":{"aiGeneratedContent":"AI가 생성한 콘텐츠, 참고용입니다","replyTime":"답변 시간: ","conversationList":"대화 목록","rename":"이름 변경","conversationRename":"대화 이름 변경","pleaseEnter":"입력해주세요","conversationRenameSuccess":"대화 이름이 성공적으로 변경되었습니다","deleteSuccess":"성공적으로 삭제되었습니다","uploadFile":"파일 업로드","unsupportedFileType":"지원되지 않는 파일 형식: ","clickOrDragToUpload":"이 영역을 클릭하거나 파일을 드래그하여 업로드","supportedFileTypes":"지원되는 파일 형식: ","waitForUploadComplete":"모든 파일 업로드가 완료될 때까지 기다려주세요"}}'),l$=JSON.parse('{"common":{"login":"Log Masuk","logout":"Log Keluar","goHome":"Pergi ke Laman Utama","newConversation":"Perbualan Baru","noSessions":"Tiada Sesi","startConfig":"Mula Konfigurasi","noDifyAppConfig":"Tiada Konfigurasi Aplikasi Dify","getConversationListFailed":"Gagal mendapatkan senarai perbualan","confirm":"Sahkan","cancel":"Batal","save":"Simpan","delete":"Padam","edit":"Edit","add":"Tambah","update":"Kemaskini","close":"Tutup","current":"Semasa","none":"Tiada","tags":"Tag","basicInfo":"Maklumat Asas","pleaseLoginFirst":"Sila log masuk untuk membeli","loginNow":"Log Masuk Sekarang","subscriptionFailed":"Langganan gagal","subscriptionSuccess":"Langganan berjaya","pleaseSelectPeriod":"Sila pilih tempoh langganan","pleaseAgreeToTerms":"Sila baca dan bersetuju dengan terma sebelum pengaktifan","agreeAndPay":" sahkan terma dan bayar","freeTrial":"Percubaan Percuma","rmb":"RMB","usd":"USD","writing":"Penulisan"},"app":{"addAppConfigSuccess":"Konfigurasi aplikasi berjaya ditambah","updateAppConfigSuccess":"Konfigurasi aplikasi berjaya dikemaskini","management":"Pengurusan Konfigurasi Aplikasi","appManagement":"Pengurusan Aplikasi","addApp":"Tambah Aplikasi","addAppConfig":"Tambah Konfigurasi Aplikasi","appConfigDetail":"Butiran Konfigurasi Aplikasi","appName":"Nama Aplikasi","appDescription":"Penerangan Aplikasi","appTags":"Tag Aplikasi","noApps":"Tiada Aplikasi","confirmDeleteApp":"Adakah anda pasti untuk memadam aplikasi ini?","deleteAppSuccess":"Aplikasi berjaya dipadam","saveAppConfigFailed":"Gagal menyimpan konfigurasi aplikasi","currentActiveAppId":"ID aplikasi aktif semasa","appList":"Senarai Aplikasi","getAppList":"Dapatkan Senarai Aplikasi","appListLoading":"Memuatkan senarai aplikasi","deleteAppSuccessCallback":"Callback pemadaman aplikasi berjaya","getDifyAppInfo":"Dapatkan maklumat aplikasi Dify"},"payment":{"subscribe":"Langgan","free":"Percuma","monthlySubscription":"Langganan Bulanan","yearlySubscription":"Langganan Tahunan","upgradeSubscription":"Naik Taraf Langganan","modifySubscription":"Ubah Langganan","supportAlipay":"Pembayaran Alipay","meisiAccount":"Akaun Meisi","subscriptionDescription":"Penerangan Langganan Meisi AI","freeDescription":"Percuma: Setiap ejen AI mempunyai had penggunaan {num} kali sebulan semula jadi. Pengiraan dimulakan semula pada awal setiap bulan.","monthlyDescription":"Langganan Bulanan: Penggunaan tanpa had setiap ejen AI selama sebulan dari tarikh langganan.","yearlyDescription":"Langganan Tahunan: Penggunaan tanpa had setiap ejen AI selama setahun dari tarikh langganan","subscribed":"Dilanggan","freeUsing":"Menggunakan versi percuma...","cancelSubscription":"Batalkan Langganan","subscriptionValidUntil":"Langganan anda sah sehingga {date}","monthlySubscribing":"Langganan bulanan aktif...","yearlySubscribing":"Langganan tahunan aktif...","cancelMonthly":"Batalkan Bulanan","cancelYearly":"Batalkan Tahunan","cancelConfirmTitle":"Notis","cancelMonthlyConfirm":"Pembatalan bulanan akan berkuat kuasa pada hari {date}. Anda perlu melanggan semula untuk menggunakan. Adakah anda pasti untuk membatalkan?","cancelYearlyConfirm":"Pembatalan tahunan akan berkuat kuasa pada hari {date}. Anda perlu melanggan semula untuk menggunakan. Adakah anda pasti untuk membatalkan?","month":"Bulan","quarter":"Suku Tahun","year":"Tahun"},"chat":{"aiGeneratedContent":"Kandungan yang dijana oleh AI, untuk rujukan sahaja","replyTime":"Masa balasan: ","conversationList":"Senarai Perbualan","rename":"Namakan semula","conversationRename":"Namakan semula perbualan","pleaseEnter":"Sila masukkan","conversationRenameSuccess":"Perbualan berjaya dinamakan semula","deleteSuccess":"Berjaya dipadamkan","uploadFile":"Muat Naik Fail","unsupportedFileType":"Jenis fail tidak disokong: ","clickOrDragToUpload":"Klik atau seret fail ke kawasan ini untuk muat naik","supportedFileTypes":"Jenis fail yang disokong: ","waitForUploadComplete":"Sila tunggu semua fail selesai dimuat naik"}}');lN.ZP.use(lA.Z).use(ou.Db).init({resources:{"zh-CN":{translation:lT},"zh-TW":{translation:l_},en:{translation:lE},vi:{translation:lI},es:{translation:lO},ar:{translation:lP},id:{translation:lM},pt:{translation:lL},ja:{translation:lD},ko:{translation:lF},ms:{translation:l$}},fallbackLng:"zh-CN",debug:!1,interpolation:{escapeValue:!1},detection:{order:["cookie","localStorage","navigator","htmlTag"],caches:["cookie","localStorage"],cookieMinutes:160,cookieDomain:".medsci.cn"}}),function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:I;(0,_.P)(e)}();let lR=[{path:"/:appUuid",component:()=>(0,y.jsx)(lC,{})}],lZ=document.getElementById("root");Q.removeInit("yudaoToken"),lZ&&b.createRoot(lZ).render((0,y.jsx)(function(){let[e,t]=(0,x.useState)("nologin"),[n,a]=(0,x.useState)(null),i=new URLSearchParams(window.top.location.search),r=(0,R.k6)().location.pathname.match(/^\/ai-chat\/([^/]+)$/),o=r?r[1]:"";return(0,L.Z)(()=>{let e=new W,t=$.get("userInfo");if(t)if(a(JSON.parse(t)),$.get("yudaoToken")&&"medsci-ask"!=o||!i.get("fromPlatform"));else{let n=JSON.parse(t);e.getAiWriteToken({userId:n.userId,userName:n.userName,realName:n.realName,avatar:n.avatar,plaintextUserId:n.plaintextUserId,mobile:n.mobile,email:n.email,fromPlatform:"medsci-ask"==o?"medsci":null,appUuid:o}).then(e=>{let{data:t}=e;(null==t?void 0:t.token)?($.set("yudaoToken",t.token),localStorage.setItem("hasuraToken",t.htoken),localStorage.setItem("openid",t.openid),localStorage.setItem("socialUserId",t.socialUserId),localStorage.setItem("socialType",t.socialType)):console.error("登录失败: 未返回 token")})}}),(0,x.useEffect)(()=>{if(n){let e=new W,a=$.get("yudaoToken");t(n.userName),a&&"medsci-ask"!=o||i.get("fromPlatform")||e.getAiWriteToken({userId:n.userId,userName:n.userName,realName:n.realName,avatar:n.avatar,plaintextUserId:n.plaintextUserId,mobile:n.mobile,email:n.email,fromPlatform:"medsci-ask"==o||i.get("fromPlatform")?"medsci":null,appUuid:o}).then(e=>{let{data:t}=e||{};(null==t?void 0:t.token)?($.set("yudaoToken",t.token),localStorage.setItem("hasuraToken",t.htoken),localStorage.setItem("openid",t.openid),localStorage.setItem("socialUserId",t.socialUserId),localStorage.setItem("socialType",t.socialType)):console.error("登录失败: 未返回 token")})}},[n]),(0,x.useEffect)(()=>{e&&console.log("Updated userId====",e)},[e]),(0,x.useEffect)(()=>{$.set("ai_apps_lang",$.get("ai_apps_lang")?$.get("ai_apps_lang"):navigator.browserLanguage||navigator.language)},[]),(0,y.jsx)(R.VK,{basename:"",routes:lR,children:(0,y.jsx)(C,{value:{mode:"multiApp",user:e,appService:new s2,enableSetting:!1},children:(0,y.jsx)(R.AW,{})})})},{}))}},t={};function n(a){var i=t[a];if(void 0!==i)return i.exports;var r=t[a]={exports:{}};return e[a].call(r.exports,r,r.exports,n),r.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(a,i){if(1&i&&(a=this(a)),8&i||"object"==typeof a&&a&&(4&i&&a.__esModule||16&i&&"function"==typeof a.then))return a;var r=Object.create(null);n.r(r);var o={};e=e||[null,t({}),t([]),t(t)];for(var s=2&i&&a;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach(e=>{o[e]=()=>a[e]});return o.default=()=>a,n.d(r,o),r}})(),n.d=(e,t)=>{for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,a)=>(n.f[a](e,t),t),[])),n.u=e=>"static/js/async/"+e+"."+({103:"89ed92cf",113:"e6e53918",139:"fab3c4a9",174:"1a50f52e",18:"cc888385",194:"44058ca1",205:"45736c37",208:"ddb6c055",219:"9951a8b3",254:"fcfa37af",33:"d3140ddd",354:"2ecd0882",360:"f01ca2dd",406:"bf0604ad",469:"fd1e1bbe",476:"198a7d91",503:"d03a97e9",516:"e942ecc7",541:"e01b9e80",584:"7293f8f7",66:"28c91132",672:"696afbca",714:"272bc6bb",733:"0cc83532",751:"d1496734",774:"ca6d64f3",796:"e7c58007",8:"faf715eb",828:"253e6351",836:"41e72eca",854:"f9b7e535",857:"79a042c5",867:"514b7df5",951:"1bfd7079",953:"2ffeeb4d",957:"2743cee0",976:"3df1c38e"})[e]+".js",n.miniCssF=e=>""+e+".css",n.h=()=>"ab6f4f4e02ebe94e",n.g=(()=>{if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}})(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="dify-chat-web:";n.l=function(a,i,r,o){if(e[a])return void e[a].push(i);if(void 0!==r)for(var s,l,c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==a||d.getAttribute("data-webpack")==t+r){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+r),s.src=a),e[a]=[i];var p=function(t,n){s.onerror=s.onload=null,clearTimeout(m);var i=e[a];if(delete e[a],s.parentNode&&s.parentNode.removeChild(s),i&&i.forEach(function(e){return e(n)}),t)return t(n)},m=setTimeout(p.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=p.bind(null,s.onerror),s.onload=p.bind(null,s.onload),l&&document.head.appendChild(s)}})(),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e=[];n.O=(t,a,i,r)=>{if(a){r=r||0;for(var o=e.length;o>0&&e[o-1][2]>r;o--)e[o]=e[o-1];e[o]=[a,i,r];return}for(var s=1/0,o=0;o<e.length;o++){for(var[a,i,r]=e[o],l=!0,c=0;c<a.length;c++)(!1&r||s>=r)&&Object.keys(n.O).every(e=>n.O[e](a[c]))?a.splice(c--,1):(l=!1,r<s&&(s=r));if(l){e.splice(o--,1);var u=i();void 0!==u&&(t=u)}}return t}})(),n.p="/ai-chat/",n.rv=()=>"1.3.5",(()=>{var e={980:0};n.f.j=function(t,a){var i=n.o(e,t)?e[t]:void 0;if(0!==i)if(i)a.push(i[2]);else{var r=new Promise((n,a)=>i=e[t]=[n,a]);a.push(i[2]=r);var o=n.p+n.u(t),s=Error();n.l(o,function(a){if(n.o(e,t)&&(0!==(i=e[t])&&(e[t]=void 0),i)){var r=a&&("load"===a.type?"missing":a.type),o=a&&a.target&&a.target.src;s.message="Loading chunk "+t+" failed.\n("+r+": "+o+")",s.name="ChunkLoadError",s.type=r,s.request=o,i[1](s)}},"chunk-"+t,t)}},n.O.j=t=>0===e[t];var t=(t,a)=>{var i,r,[o,s,l]=a,c=0;if(o.some(t=>0!==e[t])){for(i in s)n.o(s,i)&&(n.m[i]=s[i]);if(l)var u=l(n)}for(t&&t(a);c<o.length;c++)r=o[c],n.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return n.O(u)},a=self.webpackChunkdify_chat_web=self.webpackChunkdify_chat_web||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})(),n.ruid="bundler=rspack@1.3.5";var a=n.O(void 0,["361","749"],function(){return n(58779)});a=n.O(a)})();