(self.webpackChunkdify_chat_web=self.webpackChunkdify_chat_web||[]).push([["854"],{21553:function(t,e,i){var r;r=i(20378),t.exports=(()=>{"use strict";var t={45:(t,e,i)=>{var r={};r.layoutBase=i(551),r.CoSEConstants=i(806),r.CoSEEdge=i(767),r.CoSEGraph=i(880),r.CoSEGraphManager=i(578),r.CoSELayout=i(765),r.CoSENode=i(991),r.ConstraintHandler=i(902),t.exports=r},806:(t,e,i)=>{var r=i(551).FDLayoutConstants;function n(){}for(var o in r)n[o]=r[o];n.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,n.DEFAULT_RADIAL_SEPARATION=r.DEFAULT_EDGE_LENGTH,n.DEFAULT_COMPONENT_SEPERATION=60,n.TILE=!0,n.TILING_PADDING_VERTICAL=10,n.TILING_PADDING_HORIZONTAL=10,n.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,n.ENFORCE_CONSTRAINTS=!0,n.APPLY_LAYOUT=!0,n.RELAX_MOVEMENT_ON_CONSTRAINTS=!0,n.TREE_REDUCTION_ON_INCREMENTAL=!0,n.PURE_INCREMENTAL=n.DEFAULT_INCREMENTAL,t.exports=n},767:(t,e,i)=>{var r=i(551).FDLayoutEdge;function n(t,e,i){r.call(this,t,e,i)}for(var o in n.prototype=Object.create(r.prototype),r)n[o]=r[o];t.exports=n},880:(t,e,i)=>{var r=i(551).LGraph;function n(t,e,i){r.call(this,t,e,i)}for(var o in n.prototype=Object.create(r.prototype),r)n[o]=r[o];t.exports=n},578:(t,e,i)=>{var r=i(551).LGraphManager;function n(t){r.call(this,t)}for(var o in n.prototype=Object.create(r.prototype),r)n[o]=r[o];t.exports=n},765:(t,e,i)=>{var r=i(551).FDLayout,n=i(578),o=i(880),a=i(991),s=i(767),h=i(806),l=i(902),d=i(551).FDLayoutConstants,c=i(551).LayoutConstants,g=i(551).Point,u=i(551).PointD,f=i(551).DimensionD,p=i(551).Layout,v=i(551).Integer,y=i(551).IGeometry,m=i(551).LGraph,E=i(551).Transform,N=i(551).LinkedList;function T(){r.call(this),this.toBeTiled={},this.constraints={}}for(var A in T.prototype=Object.create(r.prototype),r)T[A]=r[A];T.prototype.newGraphManager=function(){var t=new n(this);return this.graphManager=t,t},T.prototype.newGraph=function(t){return new o(null,this.graphManager,t)},T.prototype.newNode=function(t){return new a(this.graphManager,t)},T.prototype.newEdge=function(t){return new s(null,null,t)},T.prototype.initParameters=function(){r.prototype.initParameters.call(this,arguments),this.isSubLayout||(h.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=h.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=h.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=d.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=d.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=d.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=d.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1)},T.prototype.initSpringEmbedder=function(){r.prototype.initSpringEmbedder.call(this),this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/d.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=.04,this.coolingAdjuster=1},T.prototype.layout=function(){return c.DEFAULT_CREATE_BENDS_AS_NEEDED&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},T.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(h.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var t=new Set(this.getAllNodes()),e=this.nodesWithGravity.filter(function(e){return t.has(e)});this.graphManager.setAllNodesToApplyGravitation(e)}}else{var i=this.getFlatForest();if(i.length>0)this.positionNodesRadially(i);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var t=new Set(this.getAllNodes()),e=this.nodesWithGravity.filter(function(e){return t.has(e)});this.graphManager.setAllNodesToApplyGravitation(e),this.positionNodesRandomly()}}return Object.keys(this.constraints).length>0&&(l.handleConstraints(this),this.initConstraintVariables()),this.initSpringEmbedder(),h.APPLY_LAYOUT&&this.runSpringEmbedder(),!0},T.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(!(this.prunedNodesAll.length>0))return!0;else this.isTreeGrowing=!0;if(this.totalIterations%d.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(!(this.prunedNodesAll.length>0))return!0;else this.isTreeGrowing=!0;this.coolingCycle++,0==this.layoutQuality?this.coolingAdjuster=this.coolingCycle:1==this.layoutQuality&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var t=new Set(this.getAllNodes()),e=this.nodesWithGravity.filter(function(e){return t.has(e)});this.graphManager.setAllNodesToApplyGravitation(e),this.graphManager.updateBounds(),this.updateGrid(),h.PURE_INCREMENTAL?this.coolingFactor=d.DEFAULT_COOLING_FACTOR_INCREMENTAL/2:this.coolingFactor=d.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),h.PURE_INCREMENTAL?this.coolingFactor=d.DEFAULT_COOLING_FACTOR_INCREMENTAL/2*((100-this.afterGrowthIterations)/100):this.coolingFactor=d.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var i=!this.isTreeGrowing&&!this.isGrowthFinished,r=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(i,r),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},T.prototype.getPositionsData=function(){for(var t=this.graphManager.getAllNodes(),e={},i=0;i<t.length;i++){var r=t[i].rect,n=t[i].id;e[n]={id:n,x:r.getCenterX(),y:r.getCenterY(),w:r.width,h:r.height}}return e},T.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var t=!1;if("during"===d.ANIMATE)this.emit("layoutstarted");else{for(;!t;)t=this.tick();this.graphManager.updateBounds()}},T.prototype.moveNodes=function(){for(var t,e=this.getAllNodes(),i=0;i<e.length;i++)e[i].calculateDisplacement();Object.keys(this.constraints).length>0&&this.updateDisplacements();for(var i=0;i<e.length;i++)e[i].move()},T.prototype.initConstraintVariables=function(){var t=this;this.idToNodeMap=new Map,this.fixedNodeSet=new Set;for(var e=this.graphManager.getAllNodes(),i=0;i<e.length;i++){var r=e[i];this.idToNodeMap.set(r.id,r)}if(this.constraints.fixedNodeConstraint){this.constraints.fixedNodeConstraint.forEach(function(e){t.fixedNodeSet.add(e.nodeId)});for(var r,e=this.graphManager.getAllNodes(),i=0;i<e.length;i++)if(null!=(r=e[i]).getChild()){var n=function e(i){for(var r,n=i.getChild().getNodes(),o=0,a=0;a<n.length;a++)null==(r=n[a]).getChild()?t.fixedNodeSet.has(r.id)&&(o+=100):o+=e(r);return o}(r);n>0&&(r.fixedNodeWeight=n)}}if(this.constraints.relativePlacementConstraint){var o=new Map,a=new Map;if(this.dummyToNodeForVerticalAlignment=new Map,this.dummyToNodeForHorizontalAlignment=new Map,this.fixedNodesOnHorizontal=new Set,this.fixedNodesOnVertical=new Set,this.fixedNodeSet.forEach(function(e){t.fixedNodesOnHorizontal.add(e),t.fixedNodesOnVertical.add(e)}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var s=this.constraints.alignmentConstraint.vertical,i=0;i<s.length;i++)this.dummyToNodeForVerticalAlignment.set("dummy"+i,[]),s[i].forEach(function(e){o.set(e,"dummy"+i),t.dummyToNodeForVerticalAlignment.get("dummy"+i).push(e),t.fixedNodeSet.has(e)&&t.fixedNodesOnHorizontal.add("dummy"+i)});if(this.constraints.alignmentConstraint.horizontal)for(var l=this.constraints.alignmentConstraint.horizontal,i=0;i<l.length;i++)this.dummyToNodeForHorizontalAlignment.set("dummy"+i,[]),l[i].forEach(function(e){a.set(e,"dummy"+i),t.dummyToNodeForHorizontalAlignment.get("dummy"+i).push(e),t.fixedNodeSet.has(e)&&t.fixedNodesOnVertical.add("dummy"+i)})}if(h.RELAX_MOVEMENT_ON_CONSTRAINTS)this.shuffle=function(t){var e,i,r;for(r=t.length-1;r>=2*t.length/3;r--)e=Math.floor(Math.random()*(r+1)),i=t[r],t[r]=t[e],t[e]=i;return t},this.nodesInRelativeHorizontal=[],this.nodesInRelativeVertical=[],this.nodeToRelativeConstraintMapHorizontal=new Map,this.nodeToRelativeConstraintMapVertical=new Map,this.nodeToTempPositionMapHorizontal=new Map,this.nodeToTempPositionMapVertical=new Map,this.constraints.relativePlacementConstraint.forEach(function(e){if(e.left){var i=o.has(e.left)?o.get(e.left):e.left,r=o.has(e.right)?o.get(e.right):e.right;t.nodesInRelativeHorizontal.includes(i)||(t.nodesInRelativeHorizontal.push(i),t.nodeToRelativeConstraintMapHorizontal.set(i,[]),t.dummyToNodeForVerticalAlignment.has(i)?t.nodeToTempPositionMapHorizontal.set(i,t.idToNodeMap.get(t.dummyToNodeForVerticalAlignment.get(i)[0]).getCenterX()):t.nodeToTempPositionMapHorizontal.set(i,t.idToNodeMap.get(i).getCenterX())),t.nodesInRelativeHorizontal.includes(r)||(t.nodesInRelativeHorizontal.push(r),t.nodeToRelativeConstraintMapHorizontal.set(r,[]),t.dummyToNodeForVerticalAlignment.has(r)?t.nodeToTempPositionMapHorizontal.set(r,t.idToNodeMap.get(t.dummyToNodeForVerticalAlignment.get(r)[0]).getCenterX()):t.nodeToTempPositionMapHorizontal.set(r,t.idToNodeMap.get(r).getCenterX())),t.nodeToRelativeConstraintMapHorizontal.get(i).push({right:r,gap:e.gap}),t.nodeToRelativeConstraintMapHorizontal.get(r).push({left:i,gap:e.gap})}else{var n=a.has(e.top)?a.get(e.top):e.top,s=a.has(e.bottom)?a.get(e.bottom):e.bottom;t.nodesInRelativeVertical.includes(n)||(t.nodesInRelativeVertical.push(n),t.nodeToRelativeConstraintMapVertical.set(n,[]),t.dummyToNodeForHorizontalAlignment.has(n)?t.nodeToTempPositionMapVertical.set(n,t.idToNodeMap.get(t.dummyToNodeForHorizontalAlignment.get(n)[0]).getCenterY()):t.nodeToTempPositionMapVertical.set(n,t.idToNodeMap.get(n).getCenterY())),t.nodesInRelativeVertical.includes(s)||(t.nodesInRelativeVertical.push(s),t.nodeToRelativeConstraintMapVertical.set(s,[]),t.dummyToNodeForHorizontalAlignment.has(s)?t.nodeToTempPositionMapVertical.set(s,t.idToNodeMap.get(t.dummyToNodeForHorizontalAlignment.get(s)[0]).getCenterY()):t.nodeToTempPositionMapVertical.set(s,t.idToNodeMap.get(s).getCenterY())),t.nodeToRelativeConstraintMapVertical.get(n).push({bottom:s,gap:e.gap}),t.nodeToRelativeConstraintMapVertical.get(s).push({top:n,gap:e.gap})}});else{var d=new Map,c=new Map;this.constraints.relativePlacementConstraint.forEach(function(t){if(t.left){var e=o.has(t.left)?o.get(t.left):t.left,i=o.has(t.right)?o.get(t.right):t.right;d.has(e)?d.get(e).push(i):d.set(e,[i]),d.has(i)?d.get(i).push(e):d.set(i,[e])}else{var r=a.has(t.top)?a.get(t.top):t.top,n=a.has(t.bottom)?a.get(t.bottom):t.bottom;c.has(r)?c.get(r).push(n):c.set(r,[n]),c.has(n)?c.get(n).push(r):c.set(n,[r])}});var g=function(t,e){var i=[],r=[],n=new N,o=new Set,a=0;return t.forEach(function(s,h){if(!o.has(h)){i[a]=[],r[a]=!1;var l=h;for(n.push(l),o.add(l),i[a].push(l);0!=n.length;)l=n.shift(),e.has(l)&&(r[a]=!0),t.get(l).forEach(function(t){o.has(t)||(n.push(t),o.add(t),i[a].push(t))});a++}}),{components:i,isFixed:r}},u=g(d,t.fixedNodesOnHorizontal);this.componentsOnHorizontal=u.components,this.fixedComponentsOnHorizontal=u.isFixed;var f=g(c,t.fixedNodesOnVertical);this.componentsOnVertical=f.components,this.fixedComponentsOnVertical=f.isFixed}}},T.prototype.updateDisplacements=function(){var t=this;if(this.constraints.fixedNodeConstraint&&this.constraints.fixedNodeConstraint.forEach(function(e){var i=t.idToNodeMap.get(e.nodeId);i.displacementX=0,i.displacementY=0}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var e=this.constraints.alignmentConstraint.vertical,i=0;i<e.length;i++){for(var r=0,n=0;n<e[i].length;n++){if(this.fixedNodeSet.has(e[i][n])){r=0;break}r+=this.idToNodeMap.get(e[i][n]).displacementX}for(var o=r/e[i].length,n=0;n<e[i].length;n++)this.idToNodeMap.get(e[i][n]).displacementX=o}if(this.constraints.alignmentConstraint.horizontal)for(var a=this.constraints.alignmentConstraint.horizontal,i=0;i<a.length;i++){for(var s=0,n=0;n<a[i].length;n++){if(this.fixedNodeSet.has(a[i][n])){s=0;break}s+=this.idToNodeMap.get(a[i][n]).displacementY}for(var l=s/a[i].length,n=0;n<a[i].length;n++)this.idToNodeMap.get(a[i][n]).displacementY=l}}if(this.constraints.relativePlacementConstraint)if(h.RELAX_MOVEMENT_ON_CONSTRAINTS)this.totalIterations%10==0&&(this.shuffle(this.nodesInRelativeHorizontal),this.shuffle(this.nodesInRelativeVertical)),this.nodesInRelativeHorizontal.forEach(function(e){if(!t.fixedNodesOnHorizontal.has(e)){var i=0;i=t.dummyToNodeForVerticalAlignment.has(e)?t.idToNodeMap.get(t.dummyToNodeForVerticalAlignment.get(e)[0]).displacementX:t.idToNodeMap.get(e).displacementX,t.nodeToRelativeConstraintMapHorizontal.get(e).forEach(function(r){if(r.right){var n=t.nodeToTempPositionMapHorizontal.get(r.right)-t.nodeToTempPositionMapHorizontal.get(e)-i;n<r.gap&&(i-=r.gap-n)}else{var n=t.nodeToTempPositionMapHorizontal.get(e)-t.nodeToTempPositionMapHorizontal.get(r.left)+i;n<r.gap&&(i+=r.gap-n)}}),t.nodeToTempPositionMapHorizontal.set(e,t.nodeToTempPositionMapHorizontal.get(e)+i),t.dummyToNodeForVerticalAlignment.has(e)?t.dummyToNodeForVerticalAlignment.get(e).forEach(function(e){t.idToNodeMap.get(e).displacementX=i}):t.idToNodeMap.get(e).displacementX=i}}),this.nodesInRelativeVertical.forEach(function(e){if(!t.fixedNodesOnHorizontal.has(e)){var i=0;i=t.dummyToNodeForHorizontalAlignment.has(e)?t.idToNodeMap.get(t.dummyToNodeForHorizontalAlignment.get(e)[0]).displacementY:t.idToNodeMap.get(e).displacementY,t.nodeToRelativeConstraintMapVertical.get(e).forEach(function(r){if(r.bottom){var n=t.nodeToTempPositionMapVertical.get(r.bottom)-t.nodeToTempPositionMapVertical.get(e)-i;n<r.gap&&(i-=r.gap-n)}else{var n=t.nodeToTempPositionMapVertical.get(e)-t.nodeToTempPositionMapVertical.get(r.top)+i;n<r.gap&&(i+=r.gap-n)}}),t.nodeToTempPositionMapVertical.set(e,t.nodeToTempPositionMapVertical.get(e)+i),t.dummyToNodeForHorizontalAlignment.has(e)?t.dummyToNodeForHorizontalAlignment.get(e).forEach(function(e){t.idToNodeMap.get(e).displacementY=i}):t.idToNodeMap.get(e).displacementY=i}});else{for(var i=0;i<this.componentsOnHorizontal.length;i++){var d=this.componentsOnHorizontal[i];if(this.fixedComponentsOnHorizontal[i])for(var n=0;n<d.length;n++)this.dummyToNodeForVerticalAlignment.has(d[n])?this.dummyToNodeForVerticalAlignment.get(d[n]).forEach(function(e){t.idToNodeMap.get(e).displacementX=0}):this.idToNodeMap.get(d[n]).displacementX=0;else{for(var c=0,g=0,n=0;n<d.length;n++)if(this.dummyToNodeForVerticalAlignment.has(d[n])){var u=this.dummyToNodeForVerticalAlignment.get(d[n]);c+=u.length*this.idToNodeMap.get(u[0]).displacementX,g+=u.length}else c+=this.idToNodeMap.get(d[n]).displacementX,g++;for(var f=c/g,n=0;n<d.length;n++)this.dummyToNodeForVerticalAlignment.has(d[n])?this.dummyToNodeForVerticalAlignment.get(d[n]).forEach(function(e){t.idToNodeMap.get(e).displacementX=f}):this.idToNodeMap.get(d[n]).displacementX=f}}for(var i=0;i<this.componentsOnVertical.length;i++){var d=this.componentsOnVertical[i];if(this.fixedComponentsOnVertical[i])for(var n=0;n<d.length;n++)this.dummyToNodeForHorizontalAlignment.has(d[n])?this.dummyToNodeForHorizontalAlignment.get(d[n]).forEach(function(e){t.idToNodeMap.get(e).displacementY=0}):this.idToNodeMap.get(d[n]).displacementY=0;else{for(var c=0,g=0,n=0;n<d.length;n++)if(this.dummyToNodeForHorizontalAlignment.has(d[n])){var u=this.dummyToNodeForHorizontalAlignment.get(d[n]);c+=u.length*this.idToNodeMap.get(u[0]).displacementY,g+=u.length}else c+=this.idToNodeMap.get(d[n]).displacementY,g++;for(var f=c/g,n=0;n<d.length;n++)this.dummyToNodeForHorizontalAlignment.has(d[n])?this.dummyToNodeForHorizontalAlignment.get(d[n]).forEach(function(e){t.idToNodeMap.get(e).displacementY=f}):this.idToNodeMap.get(d[n]).displacementY=f}}}},T.prototype.calculateNodesToApplyGravitationTo=function(){var t,e,i=[],r=this.graphManager.getGraphs(),n=r.length;for(e=0;e<n;e++)(t=r[e]).updateConnected(),t.isConnected||(i=i.concat(t.getNodes()));return i},T.prototype.createBendpoints=function(){var t=[];t=t.concat(this.graphManager.getAllEdges());var e=new Set;for(o=0;o<t.length;o++){var i=t[o];if(!e.has(i)){var r=i.getSource(),n=i.getTarget();if(r==n)i.getBendpoints().push(new u),i.getBendpoints().push(new u),this.createDummyNodesForBendpoints(i),e.add(i);else{var o,a,s=[];if(s=(s=s.concat(r.getEdgeListToNode(n))).concat(n.getEdgeListToNode(r)),!e.has(s[0])){if(s.length>1)for(a=0;a<s.length;a++){var h=s[a];h.getBendpoints().push(new u),this.createDummyNodesForBendpoints(h)}s.forEach(function(t){e.add(t)})}}}if(e.size==t.length)break}},T.prototype.positionNodesRadially=function(t){for(var e=new g(0,0),i=Math.ceil(Math.sqrt(t.length)),r=0,n=0,o=0,a=new u(0,0),s=0;s<t.length;s++){s%i==0&&(o=0,n=r,0!=s&&(n+=h.DEFAULT_COMPONENT_SEPERATION),r=0);var l=t[s],d=p.findCenterOfTree(l);e.x=o,e.y=n,(a=T.radialLayout(l,d,e)).y>r&&(r=Math.floor(a.y)),o=Math.floor(a.x+h.DEFAULT_COMPONENT_SEPERATION)}this.transform(new u(c.WORLD_CENTER_X-a.x/2,c.WORLD_CENTER_Y-a.y/2))},T.radialLayout=function(t,e,i){var r=Math.max(this.maxDiagonalInTree(t),h.DEFAULT_RADIAL_SEPARATION);T.branchRadialLayout(e,null,0,359,0,r);var n=m.calculateBounds(t),o=new E;o.setDeviceOrgX(n.getMinX()),o.setDeviceOrgY(n.getMinY()),o.setWorldOrgX(i.x),o.setWorldOrgY(i.y);for(var a=0;a<t.length;a++)t[a].transform(o);var s=new u(n.getMaxX(),n.getMaxY());return o.inverseTransformPoint(s)},T.branchRadialLayout=function(t,e,i,r,n,o){var a,s=(r-i+1)/2;s<0&&(s+=180);var h=(s+i)%360*y.TWO_PI/360,l=n*Math.cos(h),d=n*Math.sin(h);t.setCenter(l,d);var c=[],g=(c=c.concat(t.getEdges())).length;null!=e&&g--;for(var u=0,f=c.length,p=t.getEdgesBetween(e);p.length>1;){var v=p[0];p.splice(0,1);var m=c.indexOf(v);m>=0&&c.splice(m,1),f--,g--}a=null!=e?(c.indexOf(p[0])+1)%f:0;for(var E=Math.abs(r-i)/g,N=a;u!=g;N=++N%f){var A=c[N].getOtherEnd(t);if(A!=e){var L=(i+u*E)%360,w=(L+E)%360;T.branchRadialLayout(A,t,L,w,n+o,o),u++}}},T.maxDiagonalInTree=function(t){for(var e=v.MIN_VALUE,i=0;i<t.length;i++){var r=t[i].getDiagonal();r>e&&(e=r)}return e},T.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},T.prototype.groupZeroDegreeMembers=function(){var t=this,e={};this.memberGroups={},this.idToDummyNode={};for(var i=[],r=this.graphManager.getAllNodes(),n=0;n<r.length;n++){var o=r[n],s=o.getParent();0!==this.getNodeDegreeWithChildren(o)||void 0!=s.id&&this.getToBeTiled(s)||i.push(o)}for(var n=0;n<i.length;n++){var o=i[n],h=o.getParent().id;void 0===e[h]&&(e[h]=[]),e[h]=e[h].concat(o)}Object.keys(e).forEach(function(i){if(e[i].length>1){var r="DummyCompound_"+i;t.memberGroups[r]=e[i];var n=e[i][0].getParent(),o=new a(t.graphManager);o.id=r,o.paddingLeft=n.paddingLeft||0,o.paddingRight=n.paddingRight||0,o.paddingBottom=n.paddingBottom||0,o.paddingTop=n.paddingTop||0,t.idToDummyNode[r]=o;var s=t.getGraphManager().add(t.newGraph(),o),h=n.getChild();h.add(o);for(var l=0;l<e[i].length;l++){var d=e[i][l];h.remove(d),s.add(d)}}})},T.prototype.clearCompounds=function(){var t={},e={};this.performDFSOnCompounds();for(var i=0;i<this.compoundOrder.length;i++)e[this.compoundOrder[i].id]=this.compoundOrder[i],t[this.compoundOrder[i].id]=[].concat(this.compoundOrder[i].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[i].getChild()),this.compoundOrder[i].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(t,e)},T.prototype.clearZeroDegreeMembers=function(){var t=this,e=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(i){var r=t.idToDummyNode[i];if(e[i]=t.tileNodes(t.memberGroups[i],r.paddingLeft+r.paddingRight),r.rect.width=e[i].width,r.rect.height=e[i].height,r.setCenter(e[i].centerX,e[i].centerY),r.labelMarginLeft=0,r.labelMarginTop=0,h.NODE_DIMENSIONS_INCLUDE_LABELS){var n=r.rect.width,o=r.rect.height;r.labelWidth&&("left"==r.labelPosHorizontal?(r.rect.x-=r.labelWidth,r.setWidth(n+r.labelWidth),r.labelMarginLeft=r.labelWidth):"center"==r.labelPosHorizontal&&r.labelWidth>n?(r.rect.x-=(r.labelWidth-n)/2,r.setWidth(r.labelWidth),r.labelMarginLeft=(r.labelWidth-n)/2):"right"==r.labelPosHorizontal&&r.setWidth(n+r.labelWidth)),r.labelHeight&&("top"==r.labelPosVertical?(r.rect.y-=r.labelHeight,r.setHeight(o+r.labelHeight),r.labelMarginTop=r.labelHeight):"center"==r.labelPosVertical&&r.labelHeight>o?(r.rect.y-=(r.labelHeight-o)/2,r.setHeight(r.labelHeight),r.labelMarginTop=(r.labelHeight-o)/2):"bottom"==r.labelPosVertical&&r.setHeight(o+r.labelHeight))}})},T.prototype.repopulateCompounds=function(){for(var t=this.compoundOrder.length-1;t>=0;t--){var e=this.compoundOrder[t],i=e.id,r=e.paddingLeft,n=e.paddingTop,o=e.labelMarginLeft,a=e.labelMarginTop;this.adjustLocations(this.tiledMemberPack[i],e.rect.x,e.rect.y,r,n,o,a)}},T.prototype.repopulateZeroDegreeMembers=function(){var t=this,e=this.tiledZeroDegreePack;Object.keys(e).forEach(function(i){var r=t.idToDummyNode[i],n=r.paddingLeft,o=r.paddingTop,a=r.labelMarginLeft,s=r.labelMarginTop;t.adjustLocations(e[i],r.rect.x,r.rect.y,n,o,a,s)})},T.prototype.getToBeTiled=function(t){var e=t.id;if(null!=this.toBeTiled[e])return this.toBeTiled[e];var i=t.getChild();if(null==i)return this.toBeTiled[e]=!1,!1;for(var r=i.getNodes(),n=0;n<r.length;n++){var o=r[n];if(this.getNodeDegree(o)>0)return this.toBeTiled[e]=!1,!1;if(null==o.getChild()){this.toBeTiled[o.id]=!1;continue}if(!this.getToBeTiled(o))return this.toBeTiled[e]=!1,!1}return this.toBeTiled[e]=!0,!0},T.prototype.getNodeDegree=function(t){t.id;for(var e=t.getEdges(),i=0,r=0;r<e.length;r++){var n=e[r];n.getSource().id!==n.getTarget().id&&(i+=1)}return i},T.prototype.getNodeDegreeWithChildren=function(t){var e=this.getNodeDegree(t);if(null==t.getChild())return e;for(var i=t.getChild().getNodes(),r=0;r<i.length;r++){var n=i[r];e+=this.getNodeDegreeWithChildren(n)}return e},T.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},T.prototype.fillCompexOrderByDFS=function(t){for(var e=0;e<t.length;e++){var i=t[e];null!=i.getChild()&&this.fillCompexOrderByDFS(i.getChild().getNodes()),this.getToBeTiled(i)&&this.compoundOrder.push(i)}},T.prototype.adjustLocations=function(t,e,i,r,n,o,a){e+=r+o,i+=n+a;for(var s=e,h=0;h<t.rows.length;h++){var l=t.rows[h];e=s;for(var d=0,c=0;c<l.length;c++){var g=l[c];g.rect.x=e,g.rect.y=i,e+=g.rect.width+t.horizontalPadding,g.rect.height>d&&(d=g.rect.height)}i+=d+t.verticalPadding}},T.prototype.tileCompoundMembers=function(t,e){var i=this;this.tiledMemberPack=[],Object.keys(t).forEach(function(r){var n=e[r];if(i.tiledMemberPack[r]=i.tileNodes(t[r],n.paddingLeft+n.paddingRight),n.rect.width=i.tiledMemberPack[r].width,n.rect.height=i.tiledMemberPack[r].height,n.setCenter(i.tiledMemberPack[r].centerX,i.tiledMemberPack[r].centerY),n.labelMarginLeft=0,n.labelMarginTop=0,h.NODE_DIMENSIONS_INCLUDE_LABELS){var o=n.rect.width,a=n.rect.height;n.labelWidth&&("left"==n.labelPosHorizontal?(n.rect.x-=n.labelWidth,n.setWidth(o+n.labelWidth),n.labelMarginLeft=n.labelWidth):"center"==n.labelPosHorizontal&&n.labelWidth>o?(n.rect.x-=(n.labelWidth-o)/2,n.setWidth(n.labelWidth),n.labelMarginLeft=(n.labelWidth-o)/2):"right"==n.labelPosHorizontal&&n.setWidth(o+n.labelWidth)),n.labelHeight&&("top"==n.labelPosVertical?(n.rect.y-=n.labelHeight,n.setHeight(a+n.labelHeight),n.labelMarginTop=n.labelHeight):"center"==n.labelPosVertical&&n.labelHeight>a?(n.rect.y-=(n.labelHeight-a)/2,n.setHeight(n.labelHeight),n.labelMarginTop=(n.labelHeight-a)/2):"bottom"==n.labelPosVertical&&n.setHeight(a+n.labelHeight))}})},T.prototype.tileNodes=function(t,e){var i,r=this.tileNodesByFavoringDim(t,e,!0),n=this.tileNodesByFavoringDim(t,e,!1),o=this.getOrgRatio(r);return this.getOrgRatio(n)<o?n:r},T.prototype.getOrgRatio=function(t){var e=t.width/t.height;return e<1&&(e=1/e),e},T.prototype.calcIdealRowWidth=function(t,e){var i,r=h.TILING_PADDING_VERTICAL,n=h.TILING_PADDING_HORIZONTAL,o=t.length,a=0,s=0,l=0;t.forEach(function(t){a+=t.getWidth(),s+=t.getHeight(),t.getWidth()>l&&(l=t.getWidth())});var d=a/o,c=Math.pow(r-n,2)+4*(d+n)*(s/o+r)*o,g=(n-r+Math.sqrt(c))/(2*(d+n));e?(i=Math.ceil(g))==g&&i++:i=Math.floor(g);var u=i*(d+n)-n;return l>u&&(u=l),u+=2*n},T.prototype.tileNodesByFavoringDim=function(t,e,i){var r=h.TILING_PADDING_VERTICAL,n=h.TILING_PADDING_HORIZONTAL,o=h.TILING_COMPARE_BY,a={rows:[],rowWidth:[],rowHeight:[],width:0,height:e,verticalPadding:r,horizontalPadding:n,centerX:0,centerY:0};o&&(a.idealRowWidth=this.calcIdealRowWidth(t,i));var s=function(t){return t.rect.width*t.rect.height},l=function(t,e){return s(e)-s(t)};t.sort(function(t,e){var i=l;return a.idealRowWidth?(i=o)(t.id,e.id):i(t,e)});for(var d=0,c=0,g=0;g<t.length;g++){var u=t[g];d+=u.getCenterX(),c+=u.getCenterY()}a.centerX=d/t.length,a.centerY=c/t.length;for(var g=0;g<t.length;g++){var u=t[g];if(0==a.rows.length)this.insertNodeToRow(a,u,0,e);else if(this.canAddHorizontal(a,u.rect.width,u.rect.height)){var f=a.rows.length-1;a.idealRowWidth||(f=this.getShortestRowIndex(a)),this.insertNodeToRow(a,u,f,e)}else this.insertNodeToRow(a,u,a.rows.length,e);this.shiftToLastRow(a)}return a},T.prototype.insertNodeToRow=function(t,e,i,r){i==t.rows.length&&(t.rows.push([]),t.rowWidth.push(r),t.rowHeight.push(0));var n=t.rowWidth[i]+e.rect.width;t.rows[i].length>0&&(n+=t.horizontalPadding),t.rowWidth[i]=n,t.width<n&&(t.width=n);var o=e.rect.height;i>0&&(o+=t.verticalPadding);var a=0;o>t.rowHeight[i]&&(a=t.rowHeight[i],t.rowHeight[i]=o,a=t.rowHeight[i]-a),t.height+=a,t.rows[i].push(e)},T.prototype.getShortestRowIndex=function(t){for(var e=-1,i=Number.MAX_VALUE,r=0;r<t.rows.length;r++)t.rowWidth[r]<i&&(e=r,i=t.rowWidth[r]);return e},T.prototype.getLongestRowIndex=function(t){for(var e=-1,i=Number.MIN_VALUE,r=0;r<t.rows.length;r++)t.rowWidth[r]>i&&(e=r,i=t.rowWidth[r]);return e},T.prototype.canAddHorizontal=function(t,e,i){if(t.idealRowWidth){var r,n,o=t.rows.length-1;return t.rowWidth[o]+e+t.horizontalPadding<=t.idealRowWidth}var a=this.getShortestRowIndex(t);if(a<0)return!0;var s=t.rowWidth[a];if(s+t.horizontalPadding+e<=t.width)return!0;var h=0;return t.rowHeight[a]<i&&a>0&&(h=i+t.verticalPadding-t.rowHeight[a]),r=t.width-s>=e+t.horizontalPadding?(t.height+h)/(s+e+t.horizontalPadding):(t.height+h)/t.width,h=i+t.verticalPadding,(n=t.width<e?(t.height+h)/e:(t.height+h)/t.width)<1&&(n=1/n),r<1&&(r=1/r),r<n},T.prototype.shiftToLastRow=function(t){var e=this.getLongestRowIndex(t),i=t.rowWidth.length-1,r=t.rows[e],n=r[r.length-1],o=n.width+t.horizontalPadding;if(t.width-t.rowWidth[i]>o&&e!=i){r.splice(-1,1),t.rows[i].push(n),t.rowWidth[e]=t.rowWidth[e]-o,t.rowWidth[i]=t.rowWidth[i]+o,t.width=t.rowWidth[instance.getLongestRowIndex(t)];for(var a=Number.MIN_VALUE,s=0;s<r.length;s++)r[s].height>a&&(a=r[s].height);e>0&&(a+=t.verticalPadding);var h=t.rowHeight[e]+t.rowHeight[i];t.rowHeight[e]=a,t.rowHeight[i]<n.height+t.verticalPadding&&(t.rowHeight[i]=n.height+t.verticalPadding);var l=t.rowHeight[e]+t.rowHeight[i];t.height+=l-h,this.shiftToLastRow(t)}},T.prototype.tilingPreLayout=function(){h.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},T.prototype.tilingPostLayout=function(){h.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},T.prototype.reduceTrees=function(){for(var t,e=[],i=!0;i;){var r=this.graphManager.getAllNodes(),n=[];i=!1;for(var o=0;o<r.length;o++)if(1==(t=r[o]).getEdges().length&&!t.getEdges()[0].isInterGraph&&null==t.getChild()){if(h.PURE_INCREMENTAL){var a=t.getEdges()[0].getOtherEnd(t),s=new f(t.getCenterX()-a.getCenterX(),t.getCenterY()-a.getCenterY());n.push([t,t.getEdges()[0],t.getOwner(),s])}else n.push([t,t.getEdges()[0],t.getOwner()]);i=!0}if(!0==i){for(var l=[],d=0;d<n.length;d++)1==n[d][0].getEdges().length&&(l.push(n[d]),n[d][0].getOwner().remove(n[d][0]));e.push(l),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=e},T.prototype.growTree=function(t){for(var e,i=t.length,r=t[i-1],n=0;n<r.length;n++)e=r[n],this.findPlaceforPrunedNode(e),e[2].add(e[0]),e[2].add(e[1],e[1].source,e[1].target);t.splice(t.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},T.prototype.findPlaceforPrunedNode=function(t){var e=t[0];if(r=e==t[1].source?t[1].target:t[1].source,h.PURE_INCREMENTAL)e.setCenter(r.getCenterX()+t[3].getWidth(),r.getCenterY()+t[3].getHeight());else{var i,r,n,o,a=r.startX,s=r.finishX,l=r.startY,c=r.finishY,g=[0,0,0,0];if(l>0)for(var u=a;u<=s;u++)g[0]+=this.grid[u][l-1].length+this.grid[u][l].length-1;if(s<this.grid.length-1)for(var u=l;u<=c;u++)g[1]+=this.grid[s+1][u].length+this.grid[s][u].length-1;if(c<this.grid[0].length-1)for(var u=a;u<=s;u++)g[2]+=this.grid[u][c+1].length+this.grid[u][c].length-1;if(a>0)for(var u=l;u<=c;u++)g[3]+=this.grid[a-1][u].length+this.grid[a][u].length-1;for(var f=v.MAX_VALUE,p=0;p<g.length;p++)g[p]<f?(f=g[p],n=1,o=p):g[p]==f&&n++;if(3==n&&0==f)0==g[0]&&0==g[1]&&0==g[2]?i=1:0==g[0]&&0==g[1]&&0==g[3]?i=0:0==g[0]&&0==g[2]&&0==g[3]?i=3:0==g[1]&&0==g[2]&&0==g[3]&&(i=2);else if(2==n&&0==f){var y=Math.floor(2*Math.random());i=0==g[0]&&0==g[1]?+(0!=y):0==g[0]&&0==g[2]?2*(0!=y):0==g[0]&&0==g[3]?3*(0!=y):0==g[1]&&0==g[2]?0==y?1:2:0==g[1]&&0==g[3]?0==y?1:3:0==y?2:3}else if(4==n&&0==f){var y=Math.floor(4*Math.random());i=y}else i=o;0==i?e.setCenter(r.getCenterX(),r.getCenterY()-r.getHeight()/2-d.DEFAULT_EDGE_LENGTH-e.getHeight()/2):1==i?e.setCenter(r.getCenterX()+r.getWidth()/2+d.DEFAULT_EDGE_LENGTH+e.getWidth()/2,r.getCenterY()):2==i?e.setCenter(r.getCenterX(),r.getCenterY()+r.getHeight()/2+d.DEFAULT_EDGE_LENGTH+e.getHeight()/2):e.setCenter(r.getCenterX()-r.getWidth()/2-d.DEFAULT_EDGE_LENGTH-e.getWidth()/2,r.getCenterY())}},t.exports=T},991:(t,e,i)=>{var r=i(551).FDLayoutNode,n=i(551).IMath;function o(t,e,i,n){r.call(this,t,e,i,n)}for(var a in o.prototype=Object.create(r.prototype),r)o[a]=r[a];o.prototype.calculateDisplacement=function(){var t=this.graphManager.getLayout();null!=this.getChild()&&this.fixedNodeWeight?(this.displacementX+=t.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.fixedNodeWeight,this.displacementY+=t.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.fixedNodeWeight):(this.displacementX+=t.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY+=t.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren),Math.abs(this.displacementX)>t.coolingFactor*t.maxNodeDisplacement&&(this.displacementX=t.coolingFactor*t.maxNodeDisplacement*n.sign(this.displacementX)),Math.abs(this.displacementY)>t.coolingFactor*t.maxNodeDisplacement&&(this.displacementY=t.coolingFactor*t.maxNodeDisplacement*n.sign(this.displacementY)),this.child&&this.child.getNodes().length>0&&this.propogateDisplacementToChildren(this.displacementX,this.displacementY)},o.prototype.propogateDisplacementToChildren=function(t,e){for(var i,r=this.getChild().getNodes(),n=0;n<r.length;n++)null==(i=r[n]).getChild()?(i.displacementX+=t,i.displacementY+=e):i.propogateDisplacementToChildren(t,e)},o.prototype.move=function(){var t=this.graphManager.getLayout();(null==this.child||0==this.child.getNodes().length)&&(this.moveBy(this.displacementX,this.displacementY),t.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY)),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},o.prototype.setPred1=function(t){this.pred1=t},o.prototype.getPred1=function(){return pred1},o.prototype.getPred2=function(){return pred2},o.prototype.setNext=function(t){this.next=t},o.prototype.getNext=function(){return next},o.prototype.setProcessed=function(t){this.processed=t},o.prototype.isProcessed=function(){return processed},t.exports=o},902:(t,e,i)=>{function r(t){if(!Array.isArray(t))return Array.from(t);for(var e=0,i=Array(t.length);e<t.length;e++)i[e]=t[e];return i}var n=i(806),o=i(551).LinkedList,a=i(551).Matrix,s=i(551).SVD;function h(){}h.handleConstraints=function(t){var e={};e.fixedNodeConstraint=t.constraints.fixedNodeConstraint,e.alignmentConstraint=t.constraints.alignmentConstraint,e.relativePlacementConstraint=t.constraints.relativePlacementConstraint;for(var i=new Map,h=new Map,l=[],d=[],c=t.getAllNodes(),g=0,u=0;u<c.length;u++){var f=c[u];null==f.getChild()&&(h.set(f.id,g++),l.push(f.getCenterX()),d.push(f.getCenterY()),i.set(f.id,f))}e.relativePlacementConstraint&&e.relativePlacementConstraint.forEach(function(t){t.gap||0==t.gap||(t.left?t.gap=n.DEFAULT_EDGE_LENGTH+i.get(t.left).getWidth()/2+i.get(t.right).getWidth()/2:t.gap=n.DEFAULT_EDGE_LENGTH+i.get(t.top).getHeight()/2+i.get(t.bottom).getHeight()/2)});var p=function(t){var e=0,i=0;return t.forEach(function(t){e+=l[h.get(t)],i+=d[h.get(t)]}),{x:e/t.size,y:i/t.size}},v=function(t,e,i,n,a){var s=new Map;t.forEach(function(t,e){s.set(e,0)}),t.forEach(function(t,e){t.forEach(function(t){s.set(t.id,s.get(t.id)+1)})});var c=new Map,g=new Map,u=new o;for(s.forEach(function(t,r){0==t?(u.push(r),i||("horizontal"==e?c.set(r,h.has(r)?l[h.get(r)]:n.get(r)):c.set(r,h.has(r)?d[h.get(r)]:n.get(r)))):c.set(r,Number.NEGATIVE_INFINITY),i&&g.set(r,new Set([r]))}),i&&a.forEach(function(t){var r=[];if(t.forEach(function(t){i.has(t)&&r.push(t)}),r.length>0){var o=0;r.forEach(function(t){"horizontal"==e?c.set(t,h.has(t)?l[h.get(t)]:n.get(t)):c.set(t,h.has(t)?d[h.get(t)]:n.get(t)),o+=c.get(t)}),o/=r.length,t.forEach(function(t){i.has(t)||c.set(t,o)})}else{var a=0;t.forEach(function(t){"horizontal"==e?a+=h.has(t)?l[h.get(t)]:n.get(t):a+=h.has(t)?d[h.get(t)]:n.get(t)}),a/=t.length,t.forEach(function(t){c.set(t,a)})}});0!=u.length;)!function(){var r=u.shift();t.get(r).forEach(function(t){if(c.get(t.id)<c.get(r)+t.gap)if(i&&i.has(t.id)){var o=void 0;if(o="horizontal"==e?h.has(t.id)?l[h.get(t.id)]:n.get(t.id):h.has(t.id)?d[h.get(t.id)]:n.get(t.id),c.set(t.id,o),o<c.get(r)+t.gap){var a=c.get(r)+t.gap-o;g.get(r).forEach(function(t){c.set(t,c.get(t)-a)})}}else c.set(t.id,c.get(r)+t.gap);s.set(t.id,s.get(t.id)-1),0==s.get(t.id)&&u.push(t.id),i&&g.set(t.id,function(t,e){var i=new Set(t),r=!0,n=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(r=(a=s.next()).done);r=!0){var h=a.value;i.add(h)}}catch(t){n=!0,o=t}finally{try{!r&&s.return&&s.return()}finally{if(n)throw o}}return i}(g.get(r),g.get(t.id)))})}();if(i){var f=new Set;t.forEach(function(t,e){0==t.length&&f.add(e)});var p=[];g.forEach(function(t,e){if(f.has(e)){var n=!1,o=!0,a=!1,s=void 0;try{for(var h,l=t[Symbol.iterator]();!(o=(h=l.next()).done);o=!0){var d=h.value;i.has(d)&&(n=!0)}}catch(t){a=!0,s=t}finally{try{!o&&l.return&&l.return()}finally{if(a)throw s}}if(!n){var c=!1,g=void 0;p.forEach(function(e,i){e.has([].concat(r(t))[0])&&(c=!0,g=i)}),c?t.forEach(function(t){p[g].add(t)}):p.push(new Set(t))}}}),p.forEach(function(t,i){var r=Number.POSITIVE_INFINITY,o=Number.POSITIVE_INFINITY,a=Number.NEGATIVE_INFINITY,s=Number.NEGATIVE_INFINITY,g=!0,u=!1,f=void 0;try{for(var p,v=t[Symbol.iterator]();!(g=(p=v.next()).done);g=!0){var y=p.value,m=void 0;m="horizontal"==e?h.has(y)?l[h.get(y)]:n.get(y):h.has(y)?d[h.get(y)]:n.get(y);var E=c.get(y);m<r&&(r=m),m>a&&(a=m),E<o&&(o=E),E>s&&(s=E)}}catch(t){u=!0,f=t}finally{try{!g&&v.return&&v.return()}finally{if(u)throw f}}var N=(r+a)/2-(o+s)/2,T=!0,A=!1,L=void 0;try{for(var w,_=t[Symbol.iterator]();!(T=(w=_.next()).done);T=!0){var I=w.value;c.set(I,c.get(I)+N)}}catch(t){A=!0,L=t}finally{try{!T&&_.return&&_.return()}finally{if(A)throw L}}})}return c},y=function(t){var e=0,i=0,r=0,n=0;if(t.forEach(function(t){t.left?l[h.get(t.left)]-l[h.get(t.right)]>=0?e++:i++:d[h.get(t.top)]-d[h.get(t.bottom)]>=0?r++:n++}),e>i&&r>n)for(var o=0;o<h.size;o++)l[o]=-1*l[o],d[o]=-1*d[o];else if(e>i)for(var a=0;a<h.size;a++)l[a]=-1*l[a];else if(r>n)for(var s=0;s<h.size;s++)d[s]=-1*d[s]},m=function(t){var e=[],i=new o,r=new Set,n=0;return t.forEach(function(o,a){if(!r.has(a)){e[n]=[];var s=a;for(i.push(s),r.add(s),e[n].push(s);0!=i.length;)s=i.shift(),t.get(s).forEach(function(t){r.has(t.id)||(i.push(t.id),r.add(t.id),e[n].push(t.id))});n++}}),e},E=function(t){var e=new Map;return t.forEach(function(t,i){e.set(i,[])}),t.forEach(function(t,i){t.forEach(function(t){e.get(i).push(t),e.get(t.id).push({id:i,gap:t.gap,direction:t.direction})})}),e},N=function(t){var e=new Map;return t.forEach(function(t,i){e.set(i,[])}),t.forEach(function(t,i){t.forEach(function(t){e.get(t.id).push({id:i,gap:t.gap,direction:t.direction})})}),e},T=[],A=[],L=!1,w=!1,_=new Set,I=new Map,C=new Map,M=[];if(e.fixedNodeConstraint&&e.fixedNodeConstraint.forEach(function(t){_.add(t.nodeId)}),e.relativePlacementConstraint&&(e.relativePlacementConstraint.forEach(function(t){t.left?(I.has(t.left)?I.get(t.left).push({id:t.right,gap:t.gap,direction:"horizontal"}):I.set(t.left,[{id:t.right,gap:t.gap,direction:"horizontal"}]),I.has(t.right)||I.set(t.right,[])):(I.has(t.top)?I.get(t.top).push({id:t.bottom,gap:t.gap,direction:"vertical"}):I.set(t.top,[{id:t.bottom,gap:t.gap,direction:"vertical"}]),I.has(t.bottom)||I.set(t.bottom,[]))}),M=m(C=E(I))),n.TRANSFORM_ON_CONSTRAINT_HANDLING){if(e.fixedNodeConstraint&&e.fixedNodeConstraint.length>1)e.fixedNodeConstraint.forEach(function(t,e){T[e]=[t.position.x,t.position.y],A[e]=[l[h.get(t.nodeId)],d[h.get(t.nodeId)]]}),L=!0;else if(e.alignmentConstraint)!function(){var t=0;if(e.alignmentConstraint.vertical){for(var i=e.alignmentConstraint.vertical,n=0;n<i.length;n++)!function(e){var n=new Set;i[e].forEach(function(t){n.add(t)});var o=new Set([].concat(r(n)).filter(function(t){return _.has(t)})),a=void 0;a=o.size>0?l[h.get(o.values().next().value)]:p(n).x,i[e].forEach(function(e){T[t]=[a,d[h.get(e)]],A[t]=[l[h.get(e)],d[h.get(e)]],t++})}(n);L=!0}if(e.alignmentConstraint.horizontal){for(var o=e.alignmentConstraint.horizontal,a=0;a<o.length;a++)!function(e){var i=new Set;o[e].forEach(function(t){i.add(t)});var n=new Set([].concat(r(i)).filter(function(t){return _.has(t)})),a=void 0;a=n.size>0?l[h.get(n.values().next().value)]:p(i).y,o[e].forEach(function(e){T[t]=[l[h.get(e)],a],A[t]=[l[h.get(e)],d[h.get(e)]],t++})}(a);L=!0}e.relativePlacementConstraint&&(w=!0)}();else if(e.relativePlacementConstraint){for(var x=0,O=0,D=0;D<M.length;D++)M[D].length>x&&(x=M[D].length,O=D);if(x<C.size/2)y(e.relativePlacementConstraint),L=!1,w=!1;else{var R=new Map,b=new Map,G=[];M[O].forEach(function(t){I.get(t).forEach(function(e){"horizontal"==e.direction?(R.has(t)?R.get(t).push(e):R.set(t,[e]),R.has(e.id)||R.set(e.id,[]),G.push({left:t,right:e.id})):(b.has(t)?b.get(t).push(e):b.set(t,[e]),b.has(e.id)||b.set(e.id,[]),G.push({top:t,bottom:e.id}))})}),y(G),w=!1;var F=v(R,"horizontal"),S=v(b,"vertical");M[O].forEach(function(t,e){A[e]=[l[h.get(t)],d[h.get(t)]],T[e]=[],F.has(t)?T[e][0]=F.get(t):T[e][0]=l[h.get(t)],S.has(t)?T[e][1]=S.get(t):T[e][1]=d[h.get(t)]}),L=!0}}if(L){for(var P=void 0,U=a.transpose(T),Y=a.transpose(A),k=0;k<U.length;k++)U[k]=a.multGamma(U[k]),Y[k]=a.multGamma(Y[k]);var H=a.multMat(U,a.transpose(Y)),X=s.svd(H);P=a.multMat(X.V,a.transpose(X.U));for(var W=0;W<h.size;W++){var z=[l[W],d[W]],V=[P[0][0],P[1][0]],B=[P[0][1],P[1][1]];l[W]=a.dotProduct(z,V),d[W]=a.dotProduct(z,B)}w&&y(e.relativePlacementConstraint)}}if(n.ENFORCE_CONSTRAINTS){if(e.fixedNodeConstraint&&e.fixedNodeConstraint.length>0){var j={x:0,y:0};e.fixedNodeConstraint.forEach(function(t,e){var i,r={x:l[h.get(t.nodeId)],y:d[h.get(t.nodeId)]},n=(i=t.position,{x:i.x-r.x,y:i.y-r.y});j.x+=n.x,j.y+=n.y}),j.x/=e.fixedNodeConstraint.length,j.y/=e.fixedNodeConstraint.length,l.forEach(function(t,e){l[e]+=j.x}),d.forEach(function(t,e){d[e]+=j.y}),e.fixedNodeConstraint.forEach(function(t){l[h.get(t.nodeId)]=t.position.x,d[h.get(t.nodeId)]=t.position.y})}if(e.alignmentConstraint){if(e.alignmentConstraint.vertical)for(var $=e.alignmentConstraint.vertical,q=0;q<$.length;q++)!function(t){var e=new Set;$[t].forEach(function(t){e.add(t)});var i=new Set([].concat(r(e)).filter(function(t){return _.has(t)})),n=void 0;n=i.size>0?l[h.get(i.values().next().value)]:p(e).x,e.forEach(function(t){_.has(t)||(l[h.get(t)]=n)})}(q);if(e.alignmentConstraint.horizontal)for(var Z=e.alignmentConstraint.horizontal,Q=0;Q<Z.length;Q++)!function(t){var e=new Set;Z[t].forEach(function(t){e.add(t)});var i=new Set([].concat(r(e)).filter(function(t){return _.has(t)})),n=void 0;n=i.size>0?d[h.get(i.values().next().value)]:p(e).y,e.forEach(function(t){_.has(t)||(d[h.get(t)]=n)})}(Q)}e.relativePlacementConstraint&&function(){var t=new Map,i=new Map,r=new Map,n=new Map,o=new Map,a=new Map,s=new Set,c=new Set;if(_.forEach(function(t){s.add(t),c.add(t)}),e.alignmentConstraint){if(e.alignmentConstraint.vertical)for(var g=e.alignmentConstraint.vertical,u=function(e){r.set("dummy"+e,[]),g[e].forEach(function(i){t.set(i,"dummy"+e),r.get("dummy"+e).push(i),_.has(i)&&s.add("dummy"+e)}),o.set("dummy"+e,l[h.get(g[e][0])])},f=0;f<g.length;f++)u(f);if(e.alignmentConstraint.horizontal)for(var p=e.alignmentConstraint.horizontal,y=function(t){n.set("dummy"+t,[]),p[t].forEach(function(e){i.set(e,"dummy"+t),n.get("dummy"+t).push(e),_.has(e)&&c.add("dummy"+t)}),a.set("dummy"+t,d[h.get(p[t][0])])},T=0;T<p.length;T++)y(T)}var A=new Map,L=new Map,w=function(e){I.get(e).forEach(function(r){var n=void 0,o=void 0;"horizontal"==r.direction?(n=t.get(e)?t.get(e):e,o=t.get(r.id)?{id:t.get(r.id),gap:r.gap,direction:r.direction}:r,A.has(n)?A.get(n).push(o):A.set(n,[o]),A.has(o.id)||A.set(o.id,[])):(n=i.get(e)?i.get(e):e,o=i.get(r.id)?{id:i.get(r.id),gap:r.gap,direction:r.direction}:r,L.has(n)?L.get(n).push(o):L.set(n,[o]),L.has(o.id)||L.set(o.id,[]))})},C=!0,M=!1,x=void 0;try{for(var O,D=I.keys()[Symbol.iterator]();!(C=(O=D.next()).done);C=!0){var R=O.value;w(R)}}catch(t){M=!0,x=t}finally{try{!C&&D.return&&D.return()}finally{if(M)throw x}}var b=E(A),G=E(L),F=m(b),S=m(G),P=N(A),U=N(L),Y=[],k=[];F.forEach(function(t,e){Y[e]=[],t.forEach(function(t){0==P.get(t).length&&Y[e].push(t)})}),S.forEach(function(t,e){k[e]=[],t.forEach(function(t){0==U.get(t).length&&k[e].push(t)})});var H=v(A,"horizontal",s,o,Y),X=v(L,"vertical",c,a,k),W=function(t){r.get(t)?r.get(t).forEach(function(e){l[h.get(e)]=H.get(t)}):l[h.get(t)]=H.get(t)},z=!0,V=!1,B=void 0;try{for(var j,$=H.keys()[Symbol.iterator]();!(z=(j=$.next()).done);z=!0){var q=j.value;W(q)}}catch(t){V=!0,B=t}finally{try{!z&&$.return&&$.return()}finally{if(V)throw B}}var Z=function(t){n.get(t)?n.get(t).forEach(function(e){d[h.get(e)]=X.get(t)}):d[h.get(t)]=X.get(t)},Q=!0,J=!1,K=void 0;try{for(var tt,te=X.keys()[Symbol.iterator]();!(Q=(tt=te.next()).done);Q=!0){var q=tt.value;Z(q)}}catch(t){J=!0,K=t}finally{try{!Q&&te.return&&te.return()}finally{if(J)throw K}}}()}for(var J=0;J<c.length;J++){var K=c[J];null==K.getChild()&&K.setCenter(l[h.get(K.id)],d[h.get(K.id)])}},t.exports=h},551:t=>{t.exports=r}},e={};return function i(r){var n=e[r];if(void 0!==n)return n.exports;var o=e[r]={exports:{}};return t[r](o,o.exports,i),o.exports}(45)})()},85858:function(t,e,i){var r;r=i(21553),t.exports=(()=>{"use strict";var t={658:t=>{t.exports=null!=Object.assign?Object.assign.bind(Object):function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return i.forEach(function(e){Object.keys(e).forEach(function(i){return t[i]=e[i]})}),t}},548:(t,e,i)=>{var r=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var i=[],r=!0,n=!1,o=void 0;try{for(var a,s=t[Symbol.iterator]();!(r=(a=s.next()).done)&&(i.push(a.value),!e||i.length!==e);r=!0);}catch(t){n=!0,o=t}finally{try{!r&&s.return&&s.return()}finally{if(n)throw o}}return i}(t,e);throw TypeError("Invalid attempt to destructure non-iterable instance")},n=i(140).layoutBase.LinkedList,o={};o.getTopMostNodes=function(t){for(var e={},i=0;i<t.length;i++)e[t[i].id()]=!0;return t.filter(function(t,i){"number"==typeof t&&(t=i);for(var r=t.parent()[0];null!=r;){if(e[r.id()])return!1;r=r.parent()[0]}return!0})},o.connectComponents=function(t,e,i,r){var o=new n,a=new Set,s=[],h=void 0,l=void 0,d=void 0,c=!1,g=1,u=[],f=[];do!function(){var r=t.collection();f.push(r);var n=i[0],p=t.collection();for(p.merge(n).merge(n.descendants().intersection(e)),s.push(n),p.forEach(function(t){o.push(t),a.add(t),r.merge(t)});0!=o.length;)!function(){n=o.shift();var l=t.collection();n.neighborhood().nodes().forEach(function(t){e.intersection(n.edgesWith(t)).length>0&&l.merge(t)});for(var d=0;d<l.length;d++){var c=l[d];null==(h=i.intersection(c.union(c.ancestors())))||a.has(h[0])||h.union(h.descendants()).forEach(function(t){o.push(t),a.add(t),r.merge(t),i.has(t)&&s.push(t)})}}();if(r.forEach(function(t){e.intersection(t.connectedEdges()).forEach(function(t){r.has(t.source())&&r.has(t.target())&&r.merge(t)})}),s.length==i.length&&(c=!0),!c||c&&g>1){d=(l=s[0]).connectedEdges().length,s.forEach(function(t){t.connectedEdges().length<d&&(d=t.connectedEdges().length,l=t)}),u.push(l.id());var v=t.collection();v.merge(s[0]),s.forEach(function(t){v.merge(t)}),s=[],i=i.difference(v),g++}}();while(!c);return r&&u.length>0&&r.set("dummy"+(r.size+1),u),f},o.relocateComponent=function(t,e,i){if(!i.fixedNodeConstraint){var n=Number.POSITIVE_INFINITY,o=Number.NEGATIVE_INFINITY,a=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;if("draft"==i.quality){var h=!0,l=!1,d=void 0;try{for(var c,g=e.nodeIndexes[Symbol.iterator]();!(h=(c=g.next()).done);h=!0){var u=c.value,f=r(u,2),p=f[0],v=f[1],y=i.cy.getElementById(p);if(y){var m=y.boundingBox(),E=e.xCoords[v]-m.w/2,N=e.xCoords[v]+m.w/2,T=e.yCoords[v]-m.h/2,A=e.yCoords[v]+m.h/2;E<n&&(n=E),N>o&&(o=N),T<a&&(a=T),A>s&&(s=A)}}}catch(t){l=!0,d=t}finally{try{!h&&g.return&&g.return()}finally{if(l)throw d}}var L=t.x-(o+n)/2,w=t.y-(s+a)/2;e.xCoords=e.xCoords.map(function(t){return t+L}),e.yCoords=e.yCoords.map(function(t){return t+w})}else{Object.keys(e).forEach(function(t){var i=e[t],r=i.getRect().x,h=i.getRect().x+i.getRect().width,l=i.getRect().y,d=i.getRect().y+i.getRect().height;r<n&&(n=r),h>o&&(o=h),l<a&&(a=l),d>s&&(s=d)});var _=t.x-(o+n)/2,I=t.y-(s+a)/2;Object.keys(e).forEach(function(t){var i=e[t];i.setCenter(i.getCenterX()+_,i.getCenterY()+I)})}}},o.calcBoundingBox=function(t,e,i,r){for(var n=Number.MAX_SAFE_INTEGER,o=Number.MIN_SAFE_INTEGER,a=Number.MAX_SAFE_INTEGER,s=Number.MIN_SAFE_INTEGER,h=void 0,l=void 0,d=void 0,c=void 0,g=t.descendants().not(":parent"),u=g.length,f=0;f<u;f++){var p=g[f];h=e[r.get(p.id())]-p.width()/2,l=e[r.get(p.id())]+p.width()/2,d=i[r.get(p.id())]-p.height()/2,c=i[r.get(p.id())]+p.height()/2,n>h&&(n=h),o<l&&(o=l),a>d&&(a=d),s<c&&(s=c)}var v={};return v.topLeftX=n,v.topLeftY=a,v.width=o-n,v.height=s-a,v},o.calcParentsWithoutChildren=function(t,e){var i=t.collection();return e.nodes(":parent").forEach(function(t){var e=!1;t.children().forEach(function(t){"none"!=t.css("display")&&(e=!0)}),e||i.merge(t)}),i},t.exports=o},816:(t,e,i)=>{var r=i(548),n=i(140).CoSELayout,o=i(140).CoSENode,a=i(140).layoutBase.PointD,s=i(140).layoutBase.DimensionD,h=i(140).layoutBase.LayoutConstants,l=i(140).layoutBase.FDLayoutConstants,d=i(140).CoSEConstants;t.exports={coseLayout:function(t,e){var i=t.cy,c=t.eles,g=c.nodes(),u=c.edges(),f=void 0,p=void 0,v=void 0,y={};t.randomize&&(f=e.nodeIndexes,p=e.xCoords,v=e.yCoords);var m=function(t){return"function"==typeof t},E=function(t,e){return m(t)?t(e):t},N=r.calcParentsWithoutChildren(i,c);null!=t.nestingFactor&&(d.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=l.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=t.nestingFactor),null!=t.gravity&&(d.DEFAULT_GRAVITY_STRENGTH=l.DEFAULT_GRAVITY_STRENGTH=t.gravity),null!=t.numIter&&(d.MAX_ITERATIONS=l.MAX_ITERATIONS=t.numIter),null!=t.gravityRange&&(d.DEFAULT_GRAVITY_RANGE_FACTOR=l.DEFAULT_GRAVITY_RANGE_FACTOR=t.gravityRange),null!=t.gravityCompound&&(d.DEFAULT_COMPOUND_GRAVITY_STRENGTH=l.DEFAULT_COMPOUND_GRAVITY_STRENGTH=t.gravityCompound),null!=t.gravityRangeCompound&&(d.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=l.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=t.gravityRangeCompound),null!=t.initialEnergyOnIncremental&&(d.DEFAULT_COOLING_FACTOR_INCREMENTAL=l.DEFAULT_COOLING_FACTOR_INCREMENTAL=t.initialEnergyOnIncremental),null!=t.tilingCompareBy&&(d.TILING_COMPARE_BY=t.tilingCompareBy),"proof"==t.quality?h.QUALITY=2:h.QUALITY=0,d.NODE_DIMENSIONS_INCLUDE_LABELS=l.NODE_DIMENSIONS_INCLUDE_LABELS=h.NODE_DIMENSIONS_INCLUDE_LABELS=t.nodeDimensionsIncludeLabels,d.DEFAULT_INCREMENTAL=l.DEFAULT_INCREMENTAL=h.DEFAULT_INCREMENTAL=!t.randomize,d.ANIMATE=l.ANIMATE=h.ANIMATE=t.animate,d.TILE=t.tile,d.TILING_PADDING_VERTICAL="function"==typeof t.tilingPaddingVertical?t.tilingPaddingVertical.call():t.tilingPaddingVertical,d.TILING_PADDING_HORIZONTAL="function"==typeof t.tilingPaddingHorizontal?t.tilingPaddingHorizontal.call():t.tilingPaddingHorizontal,d.DEFAULT_INCREMENTAL=l.DEFAULT_INCREMENTAL=h.DEFAULT_INCREMENTAL=!0,d.PURE_INCREMENTAL=!t.randomize,h.DEFAULT_UNIFORM_LEAF_NODE_SIZES=t.uniformNodeDimensions,"transformed"==t.step&&(d.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,d.ENFORCE_CONSTRAINTS=!1,d.APPLY_LAYOUT=!1),"enforced"==t.step&&(d.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,d.ENFORCE_CONSTRAINTS=!0,d.APPLY_LAYOUT=!1),"cose"==t.step&&(d.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,d.ENFORCE_CONSTRAINTS=!1,d.APPLY_LAYOUT=!0),"all"==t.step&&(t.randomize?d.TRANSFORM_ON_CONSTRAINT_HANDLING=!0:d.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,d.ENFORCE_CONSTRAINTS=!0,d.APPLY_LAYOUT=!0),t.fixedNodeConstraint||t.alignmentConstraint||t.relativePlacementConstraint?d.TREE_REDUCTION_ON_INCREMENTAL=!1:d.TREE_REDUCTION_ON_INCREMENTAL=!0;var T=new n,A=T.newGraphManager();return!function t(e,i,n,h){for(var l=i.length,d=0;d<l;d++){var c=i[d],g=null;0==c.intersection(N).length&&(g=c.children());var u=void 0,m=c.layoutDimensions({nodeDimensionsIncludeLabels:h.nodeDimensionsIncludeLabels});if(null!=c.outerWidth()&&null!=c.outerHeight())if(h.randomize)if(c.isParent()){var T=r.calcBoundingBox(c,p,v,f);u=0==c.intersection(N).length?e.add(new o(n.graphManager,new a(T.topLeftX,T.topLeftY),new s(T.width,T.height))):e.add(new o(n.graphManager,new a(T.topLeftX,T.topLeftY),new s(parseFloat(m.w),parseFloat(m.h))))}else u=e.add(new o(n.graphManager,new a(p[f.get(c.id())]-m.w/2,v[f.get(c.id())]-m.h/2),new s(parseFloat(m.w),parseFloat(m.h))));else u=e.add(new o(n.graphManager,new a(c.position("x")-m.w/2,c.position("y")-m.h/2),new s(parseFloat(m.w),parseFloat(m.h))));else u=e.add(new o(this.graphManager));if(u.id=c.data("id"),u.nodeRepulsion=E(h.nodeRepulsion,c),u.paddingLeft=parseInt(c.css("padding")),u.paddingTop=parseInt(c.css("padding")),u.paddingRight=parseInt(c.css("padding")),u.paddingBottom=parseInt(c.css("padding")),h.nodeDimensionsIncludeLabels&&(u.labelWidth=c.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).w,u.labelHeight=c.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).h,u.labelPosVertical=c.css("text-valign"),u.labelPosHorizontal=c.css("text-halign")),y[c.data("id")]=u,isNaN(u.rect.x)&&(u.rect.x=0),isNaN(u.rect.y)&&(u.rect.y=0),null!=g&&g.length>0){var A=void 0;t(n.getGraphManager().add(n.newGraph(),u),g,n,h)}}}(A.addRoot(),r.getTopMostNodes(g),T,t),!function(e,i,r){for(var n=0,o=0,a=0;a<r.length;a++){var s=r[a],h=y[s.data("source")],c=y[s.data("target")];if(h&&c&&h!==c&&0==h.getEdgesBetween(c).length){var g=i.add(e.newEdge(),h,c);g.id=s.id(),g.idealLength=E(t.idealEdgeLength,s),g.edgeElasticity=E(t.edgeElasticity,s),n+=g.idealLength,o++}}null!=t.idealEdgeLength&&(o>0?d.DEFAULT_EDGE_LENGTH=l.DEFAULT_EDGE_LENGTH=n/o:m(t.idealEdgeLength)?d.DEFAULT_EDGE_LENGTH=l.DEFAULT_EDGE_LENGTH=50:d.DEFAULT_EDGE_LENGTH=l.DEFAULT_EDGE_LENGTH=t.idealEdgeLength,d.MIN_REPULSION_DIST=l.MIN_REPULSION_DIST=l.DEFAULT_EDGE_LENGTH/10,d.DEFAULT_RADIAL_SEPARATION=l.DEFAULT_EDGE_LENGTH)}(T,A,u),t.fixedNodeConstraint&&(T.constraints.fixedNodeConstraint=t.fixedNodeConstraint),t.alignmentConstraint&&(T.constraints.alignmentConstraint=t.alignmentConstraint),t.relativePlacementConstraint&&(T.constraints.relativePlacementConstraint=t.relativePlacementConstraint),T.runLayout(),y}}},212:(t,e,i)=>{var r=function(){function t(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,i,r){return i&&t(e.prototype,i),r&&t(e,r),e}}(),n=i(658),o=i(548),a=i(657).spectralLayout,s=i(816).coseLayout,h=Object.freeze({quality:"default",randomize:!0,animate:!0,animationDuration:1e3,animationEasing:void 0,fit:!0,padding:30,nodeDimensionsIncludeLabels:!1,uniformNodeDimensions:!1,packComponents:!0,step:"all",samplingType:!0,sampleSize:25,nodeSeparation:75,piTol:1e-7,nodeRepulsion:function(t){return 4500},idealEdgeLength:function(t){return 50},edgeElasticity:function(t){return .45},nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,tilingCompareBy:void 0,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.3,fixedNodeConstraint:void 0,alignmentConstraint:void 0,relativePlacementConstraint:void 0,ready:function(){},stop:function(){}});t.exports=function(){function t(e){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");this.options=n({},h,e)}return r(t,[{key:"run",value:function(){var t=this.options,e=t.cy,i=t.eles,r=[],n=[],h=void 0,l=[];t.fixedNodeConstraint&&(!Array.isArray(t.fixedNodeConstraint)||0==t.fixedNodeConstraint.length)&&(t.fixedNodeConstraint=void 0),t.alignmentConstraint&&(t.alignmentConstraint.vertical&&(!Array.isArray(t.alignmentConstraint.vertical)||0==t.alignmentConstraint.vertical.length)&&(t.alignmentConstraint.vertical=void 0),t.alignmentConstraint.horizontal&&(!Array.isArray(t.alignmentConstraint.horizontal)||0==t.alignmentConstraint.horizontal.length)&&(t.alignmentConstraint.horizontal=void 0)),t.relativePlacementConstraint&&(!Array.isArray(t.relativePlacementConstraint)||0==t.relativePlacementConstraint.length)&&(t.relativePlacementConstraint=void 0),(t.fixedNodeConstraint||t.alignmentConstraint||t.relativePlacementConstraint)&&(t.tile=!1,t.packComponents=!1);var d=void 0,c=!1;if(e.layoutUtilities&&t.packComponents&&((d=e.layoutUtilities("get"))||(d=e.layoutUtilities()),c=!0),i.nodes().length>0)if(c){var g=o.getTopMostNodes(t.eles.nodes());if((h=o.connectComponents(e,t.eles,g)).forEach(function(t){var e=t.boundingBox();l.push({x:e.x1+e.w/2,y:e.y1+e.h/2})}),t.randomize&&h.forEach(function(e){t.eles=e,r.push(a(t))}),"default"==t.quality||"proof"==t.quality){var u=e.collection();if(t.tile){var f=new Map,p=0,v={nodeIndexes:f,xCoords:[],yCoords:[]},y=[];if(h.forEach(function(t,e){0==t.edges().length&&(t.nodes().forEach(function(e,i){u.merge(t.nodes()[i]),e.isParent()||(v.nodeIndexes.set(t.nodes()[i].id(),p++),v.xCoords.push(t.nodes()[0].position().x),v.yCoords.push(t.nodes()[0].position().y))}),y.push(e))}),u.length>1){var m=u.boundingBox();l.push({x:m.x1+m.w/2,y:m.y1+m.h/2}),h.push(u),r.push(v);for(var E=y.length-1;E>=0;E--)h.splice(y[E],1),r.splice(y[E],1),l.splice(y[E],1)}}h.forEach(function(e,i){t.eles=e,n.push(s(t,r[i])),o.relocateComponent(l[i],n[i],t)})}else h.forEach(function(e,i){o.relocateComponent(l[i],r[i],t)});var N=new Set;if(h.length>1){var T=[],A=i.filter(function(t){return"none"==t.css("display")});h.forEach(function(e,i){var a=void 0;if("draft"==t.quality&&(a=r[i].nodeIndexes),e.nodes().not(A).length>0){var s={};s.edges=[],s.nodes=[];var h=void 0;e.nodes().not(A).forEach(function(e){if("draft"==t.quality)if(e.isParent()){var l=o.calcBoundingBox(e,r[i].xCoords,r[i].yCoords,a);s.nodes.push({x:l.topLeftX,y:l.topLeftY,width:l.width,height:l.height})}else h=a.get(e.id()),s.nodes.push({x:r[i].xCoords[h]-e.boundingbox().w/2,y:r[i].yCoords[h]-e.boundingbox().h/2,width:e.boundingbox().w,height:e.boundingbox().h});else n[i][e.id()]&&s.nodes.push({x:n[i][e.id()].getLeft(),y:n[i][e.id()].getTop(),width:n[i][e.id()].getWidth(),height:n[i][e.id()].getHeight()})}),e.edges().forEach(function(e){var h=e.source(),l=e.target();if("none"!=h.css("display")&&"none"!=l.css("display"))if("draft"==t.quality){var d=a.get(h.id()),c=a.get(l.id()),g=[],u=[];if(h.isParent()){var f=o.calcBoundingBox(h,r[i].xCoords,r[i].yCoords,a);g.push(f.topLeftX+f.width/2),g.push(f.topLeftY+f.height/2)}else g.push(r[i].xCoords[d]),g.push(r[i].yCoords[d]);if(l.isParent()){var p=o.calcBoundingBox(l,r[i].xCoords,r[i].yCoords,a);u.push(p.topLeftX+p.width/2),u.push(p.topLeftY+p.height/2)}else u.push(r[i].xCoords[c]),u.push(r[i].yCoords[c]);s.edges.push({startX:g[0],startY:g[1],endX:u[0],endY:u[1]})}else n[i][h.id()]&&n[i][l.id()]&&s.edges.push({startX:n[i][h.id()].getCenterX(),startY:n[i][h.id()].getCenterY(),endX:n[i][l.id()].getCenterX(),endY:n[i][l.id()].getCenterY()})}),s.nodes.length>0&&(T.push(s),N.add(i))}});var L=d.packComponents(T,t.randomize).shifts;if("draft"==t.quality)r.forEach(function(t,e){var i=t.xCoords.map(function(t){return t+L[e].dx}),r=t.yCoords.map(function(t){return t+L[e].dy});t.xCoords=i,t.yCoords=r});else{var w=0;N.forEach(function(t){Object.keys(n[t]).forEach(function(e){var i=n[t][e];i.setCenter(i.getCenterX()+L[w].dx,i.getCenterY()+L[w].dy)}),w++})}}}else{var _=t.eles.boundingBox();if(l.push({x:_.x1+_.w/2,y:_.y1+_.h/2}),t.randomize){var I=a(t);r.push(I)}"default"==t.quality||"proof"==t.quality?(n.push(s(t,r[0])),o.relocateComponent(l[0],n[0],t)):o.relocateComponent(l[0],r[0],t)}var C=function(e,i){if("default"==t.quality||"proof"==t.quality){"number"==typeof e&&(e=i);var o=void 0,a=void 0,s=e.data("id");return n.forEach(function(t){s in t&&(o={x:t[s].getRect().getCenterX(),y:t[s].getRect().getCenterY()},a=t[s])}),t.nodeDimensionsIncludeLabels&&(a.labelWidth&&("left"==a.labelPosHorizontal?o.x+=a.labelWidth/2:"right"==a.labelPosHorizontal&&(o.x-=a.labelWidth/2)),a.labelHeight&&("top"==a.labelPosVertical?o.y+=a.labelHeight/2:"bottom"==a.labelPosVertical&&(o.y-=a.labelHeight/2))),void 0==o&&(o={x:e.position("x"),y:e.position("y")}),{x:o.x,y:o.y}}var h=void 0;return r.forEach(function(t){var i=t.nodeIndexes.get(e.id());void 0!=i&&(h={x:t.xCoords[i],y:t.yCoords[i]})}),void 0==h&&(h={x:e.position("x"),y:e.position("y")}),{x:h.x,y:h.y}};if("default"==t.quality||"proof"==t.quality||t.randomize){var M=o.calcParentsWithoutChildren(e,i),x=i.filter(function(t){return"none"==t.css("display")});t.eles=i.not(x),i.nodes().not(":parent").not(x).layoutPositions(this,t,C),M.length>0&&M.forEach(function(t){t.position(C(t))})}else console.log("If randomize option is set to false, then quality option must be 'default' or 'proof'.")}}]),t}()},657:(t,e,i)=>{var r=i(548),n=i(140).layoutBase.Matrix,o=i(140).layoutBase.SVD;t.exports={spectralLayout:function(t){var e=t.cy,i=t.eles,a=i.nodes(),s=i.nodes(":parent"),h=new Map,l=new Map,d=new Map,c=[],g=[],u=[],f=[],p=[],v=[],y=[],m=[],E=void 0,N=t.piTol,T=t.samplingType,A=t.nodeSeparation,L=void 0,w=function(){for(var t=0,e=0,i=!1;e<L;){t=Math.floor(Math.random()*E),i=!1;for(var r=0;r<e;r++)if(f[r]==t){i=!0;break}!i&&(f[e]=t,e++)}},_=function(t,e,i){for(var r=[],n=0,o=0,a=0,s=void 0,h=[],d=0,g=1,u=0;u<E;u++)h[u]=1e8;for(r[o]=t,h[t]=0;o>=n;){for(var f=c[a=r[n++]],y=0;y<f.length;y++)1e8==h[s=l.get(f[y])]&&(h[s]=h[a]+1,r[++o]=s);v[a][e]=h[a]*A}if(i){for(var m=0;m<E;m++)v[m][e]<p[m]&&(p[m]=v[m][e]);for(var N=0;N<E;N++)p[N]>d&&(d=p[N],g=N)}return g};r.connectComponents(e,i,r.getTopMostNodes(a),h),s.forEach(function(t){r.connectComponents(e,i,r.getTopMostNodes(t.descendants().intersection(i)),h)});for(var I=0,C=0;C<a.length;C++)a[C].isParent()||l.set(a[C].id(),I++);var M=!0,x=!1,O=void 0;try{for(var D,R=h.keys()[Symbol.iterator]();!(M=(D=R.next()).done);M=!0){var b=D.value;l.set(b,I++)}}catch(t){x=!0,O=t}finally{try{!M&&R.return&&R.return()}finally{if(x)throw O}}for(var G=0;G<l.size;G++)c[G]=[];s.forEach(function(t){for(var e=t.children().intersection(i);0==e.nodes(":childless").length;)e=e.nodes()[0].children().intersection(i);var r=0,n=e.nodes(":childless")[0].connectedEdges().length;e.nodes(":childless").forEach(function(t,e){t.connectedEdges().length<n&&(n=t.connectedEdges().length,r=e)}),d.set(t.id(),e.nodes(":childless")[r].id())}),a.forEach(function(t){var e=void 0;e=t.isParent()?l.get(d.get(t.id())):l.get(t.id()),t.neighborhood().nodes().forEach(function(r){i.intersection(t.edgesWith(r)).length>0&&(r.isParent()?c[e].push(d.get(r.id())):c[e].push(r.id()))})});var F=function(t){var i=l.get(t),r=void 0;h.get(t).forEach(function(n){r=e.getElementById(n).isParent()?d.get(n):n,c[i].push(r),c[l.get(r)].push(t)})},S=!0,P=!1,U=void 0;try{for(var Y,k=h.keys()[Symbol.iterator]();!(S=(Y=k.next()).done);S=!0){var H=Y.value;F(H)}}catch(t){P=!0,U=t}finally{try{!S&&k.return&&k.return()}finally{if(P)throw U}}E=l.size;var X=void 0;if(E>2){L=E<t.sampleSize?E:t.sampleSize;for(var W=0;W<E;W++)v[W]=[];for(var z=0;z<L;z++)m[z]=[];return"draft"==t.quality||"all"==t.step?(!function(t){var e=void 0;if(t){e=Math.floor(Math.random()*E);for(var i=0;i<E;i++)p[i]=1e8;for(var r=0;r<L;r++)f[r]=e,e=_(e,r,t)}else{w();for(var n=0;n<L;n++)_(f[n],n,t,!1)}for(var o=0;o<E;o++)for(var a=0;a<L;a++)v[o][a]*=v[o][a];for(var s=0;s<L;s++)y[s]=[];for(var h=0;h<L;h++)for(var l=0;l<L;l++)y[h][l]=v[f[l]][h]}(T),!function(){for(var t=o.svd(y),e=t.S,i=t.U,r=t.V,a=e[0]*e[0]*e[0],s=[],h=0;h<L;h++){s[h]=[];for(var l=0;l<L;l++)s[h][l]=0,h==l&&(s[h][l]=e[h]/(e[h]*e[h]+a/(e[h]*e[h])))}m=n.multMat(n.multMat(r,s),n.transpose(i))}(),!function(){for(var t=void 0,e=void 0,i=[],r=[],o=[],a=[],s=0;s<E;s++)i[s]=Math.random(),r[s]=Math.random();i=n.normalize(i),r=n.normalize(r);for(var h=0,l=1e-9,d=1e-9,c=void 0;;){h++;for(var f=0;f<E;f++)o[f]=i[f];if(i=n.multGamma(n.multL(n.multGamma(o),v,m)),t=n.dotProduct(o,i),i=n.normalize(i),(c=Math.abs((l=n.dotProduct(o,i))/d))<=1+N&&c>=1)break;d=l}for(var p=0;p<E;p++)o[p]=i[p];for(h=0,d=1e-9;;){h++;for(var y=0;y<E;y++)a[y]=r[y];if(a=n.minusOp(a,n.multCons(o,n.dotProduct(o,a))),r=n.multGamma(n.multL(n.multGamma(a),v,m)),e=n.dotProduct(a,r),r=n.normalize(r),(c=Math.abs((l=n.dotProduct(a,r))/d))<=1+N&&c>=1)break;d=l}for(var T=0;T<E;T++)a[T]=r[T];g=n.multCons(o,Math.sqrt(Math.abs(t))),u=n.multCons(a,Math.sqrt(Math.abs(e)))}()):l.forEach(function(t,i){g.push(e.getElementById(i).position("x")),u.push(e.getElementById(i).position("y"))}),{nodeIndexes:l,xCoords:g,yCoords:u}}var V=l.keys(),B=e.getElementById(V.next().value),j=B.position(),$=B.outerWidth();if(g.push(j.x),u.push(j.y),2==E){var q=e.getElementById(V.next().value).outerWidth();g.push(j.x+$/2+q/2+t.idealEdgeLength),u.push(j.y)}return{nodeIndexes:l,xCoords:g,yCoords:u}}}},579:(t,e,i)=>{var r=i(212),n=function(t){t&&t("layout","fcose",r)};"undefined"!=typeof cytoscape&&n(cytoscape),t.exports=n},140:t=>{t.exports=r}},e={};return function i(r){var n=e[r];if(void 0!==n)return n.exports;var o=e[r]={exports:{}};return t[r](o,o.exports,i),o.exports}(579)})()},20378:function(t){t.exports=function(){var t=[function(t,e,i){"use strict";function r(){}r.QUALITY=1,r.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,r.DEFAULT_INCREMENTAL=!1,r.DEFAULT_ANIMATION_ON_LAYOUT=!0,r.DEFAULT_ANIMATION_DURING_LAYOUT=!1,r.DEFAULT_ANIMATION_PERIOD=50,r.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,r.DEFAULT_GRAPH_MARGIN=15,r.NODE_DIMENSIONS_INCLUDE_LABELS=!1,r.SIMPLE_NODE_SIZE=40,r.SIMPLE_NODE_HALF_SIZE=r.SIMPLE_NODE_SIZE/2,r.EMPTY_COMPOUND_NODE_SIZE=40,r.MIN_EDGE_LENGTH=1,r.WORLD_BOUNDARY=1e6,r.INITIAL_WORLD_BOUNDARY=r.WORLD_BOUNDARY/1e3,r.WORLD_CENTER_X=1200,r.WORLD_CENTER_Y=900,t.exports=r},function(t,e,i){"use strict";var r=i(2),n=i(8),o=i(9);function a(t,e,i){r.call(this,i),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=i,this.bendpoints=[],this.source=t,this.target=e}for(var s in a.prototype=Object.create(r.prototype),r)a[s]=r[s];a.prototype.getSource=function(){return this.source},a.prototype.getTarget=function(){return this.target},a.prototype.isInterGraph=function(){return this.isInterGraph},a.prototype.getLength=function(){return this.length},a.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},a.prototype.getBendpoints=function(){return this.bendpoints},a.prototype.getLca=function(){return this.lca},a.prototype.getSourceInLca=function(){return this.sourceInLca},a.prototype.getTargetInLca=function(){return this.targetInLca},a.prototype.getOtherEnd=function(t){if(this.source===t)return this.target;if(this.target===t)return this.source;throw"Node is not incident with this edge"},a.prototype.getOtherEndInGraph=function(t,e){for(var i=this.getOtherEnd(t),r=e.getGraphManager().getRoot();;){if(i.getOwner()==e)return i;if(i.getOwner()==r)break;i=i.getOwner().getParent()}return null},a.prototype.updateLength=function(){var t=[,,,,];this.isOverlapingSourceAndTarget=n.getIntersection(this.target.getRect(),this.source.getRect(),t),this.isOverlapingSourceAndTarget||(this.lengthX=t[0]-t[2],this.lengthY=t[1]-t[3],1>Math.abs(this.lengthX)&&(this.lengthX=o.sign(this.lengthX)),1>Math.abs(this.lengthY)&&(this.lengthY=o.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},a.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),1>Math.abs(this.lengthX)&&(this.lengthX=o.sign(this.lengthX)),1>Math.abs(this.lengthY)&&(this.lengthY=o.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},t.exports=a},function(t,e,i){"use strict";t.exports=function(t){this.vGraphObject=t}},function(t,e,i){"use strict";var r=i(2),n=i(10),o=i(13),a=i(0),s=i(16),h=i(5);function l(t,e,i,a){null==i&&null==a&&(a=e),r.call(this,a),null!=t.graphManager&&(t=t.graphManager),this.estimatedSize=n.MIN_VALUE,this.inclusionTreeDepth=n.MAX_VALUE,this.vGraphObject=a,this.edges=[],this.graphManager=t,null!=i&&null!=e?this.rect=new o(e.x,e.y,i.width,i.height):this.rect=new o}for(var d in l.prototype=Object.create(r.prototype),r)l[d]=r[d];l.prototype.getEdges=function(){return this.edges},l.prototype.getChild=function(){return this.child},l.prototype.getOwner=function(){return this.owner},l.prototype.getWidth=function(){return this.rect.width},l.prototype.setWidth=function(t){this.rect.width=t},l.prototype.getHeight=function(){return this.rect.height},l.prototype.setHeight=function(t){this.rect.height=t},l.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},l.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},l.prototype.getCenter=function(){return new h(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},l.prototype.getLocation=function(){return new h(this.rect.x,this.rect.y)},l.prototype.getRect=function(){return this.rect},l.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},l.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},l.prototype.setRect=function(t,e){this.rect.x=t.x,this.rect.y=t.y,this.rect.width=e.width,this.rect.height=e.height},l.prototype.setCenter=function(t,e){this.rect.x=t-this.rect.width/2,this.rect.y=e-this.rect.height/2},l.prototype.setLocation=function(t,e){this.rect.x=t,this.rect.y=e},l.prototype.moveBy=function(t,e){this.rect.x+=t,this.rect.y+=e},l.prototype.getEdgeListToNode=function(t){var e=[],i=this;return i.edges.forEach(function(r){if(r.target==t){if(r.source!=i)throw"Incorrect edge source!";e.push(r)}}),e},l.prototype.getEdgesBetween=function(t){var e=[],i=this;return i.edges.forEach(function(r){if(r.source!=i&&r.target!=i)throw"Incorrect edge source and/or target";(r.target==t||r.source==t)&&e.push(r)}),e},l.prototype.getNeighborsList=function(){var t=new Set,e=this;return e.edges.forEach(function(i){if(i.source==e)t.add(i.target);else{if(i.target!=e)throw"Incorrect incidency!";t.add(i.source)}}),t},l.prototype.withChildren=function(){var t=new Set;if(t.add(this),null!=this.child)for(var e=this.child.getNodes(),i=0;i<e.length;i++)e[i].withChildren().forEach(function(e){t.add(e)});return t},l.prototype.getNoOfChildren=function(){var t=0;if(null==this.child)t=1;else for(var e=this.child.getNodes(),i=0;i<e.length;i++)t+=e[i].getNoOfChildren();return 0==t&&(t=1),t},l.prototype.getEstimatedSize=function(){if(this.estimatedSize==n.MIN_VALUE)throw"assert failed";return this.estimatedSize},l.prototype.calcEstimatedSize=function(){return null==this.child?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},l.prototype.scatter=function(){var t,e,i=-a.INITIAL_WORLD_BOUNDARY,r=a.INITIAL_WORLD_BOUNDARY;t=a.WORLD_CENTER_X+s.nextDouble()*(r-i)+i;var n=-a.INITIAL_WORLD_BOUNDARY,o=a.INITIAL_WORLD_BOUNDARY;e=a.WORLD_CENTER_Y+s.nextDouble()*(o-n)+n,this.rect.x=t,this.rect.y=e},l.prototype.updateBounds=function(){if(null==this.getChild())throw"assert failed";if(0!=this.getChild().getNodes().length){var t=this.getChild();if(t.updateBounds(!0),this.rect.x=t.getLeft(),this.rect.y=t.getTop(),this.setWidth(t.getRight()-t.getLeft()),this.setHeight(t.getBottom()-t.getTop()),a.NODE_DIMENSIONS_INCLUDE_LABELS){var e=t.getRight()-t.getLeft(),i=t.getBottom()-t.getTop();this.labelWidth&&("left"==this.labelPosHorizontal?(this.rect.x-=this.labelWidth,this.setWidth(e+this.labelWidth)):"center"==this.labelPosHorizontal&&this.labelWidth>e?(this.rect.x-=(this.labelWidth-e)/2,this.setWidth(this.labelWidth)):"right"==this.labelPosHorizontal&&this.setWidth(e+this.labelWidth)),this.labelHeight&&("top"==this.labelPosVertical?(this.rect.y-=this.labelHeight,this.setHeight(i+this.labelHeight)):"center"==this.labelPosVertical&&this.labelHeight>i?(this.rect.y-=(this.labelHeight-i)/2,this.setHeight(this.labelHeight)):"bottom"==this.labelPosVertical&&this.setHeight(i+this.labelHeight))}}},l.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==n.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},l.prototype.transform=function(t){var e=this.rect.x;e>a.WORLD_BOUNDARY?e=a.WORLD_BOUNDARY:e<-a.WORLD_BOUNDARY&&(e=-a.WORLD_BOUNDARY);var i=this.rect.y;i>a.WORLD_BOUNDARY?i=a.WORLD_BOUNDARY:i<-a.WORLD_BOUNDARY&&(i=-a.WORLD_BOUNDARY);var r=new h(e,i),n=t.inverseTransformPoint(r);this.setLocation(n.x,n.y)},l.prototype.getLeft=function(){return this.rect.x},l.prototype.getRight=function(){return this.rect.x+this.rect.width},l.prototype.getTop=function(){return this.rect.y},l.prototype.getBottom=function(){return this.rect.y+this.rect.height},l.prototype.getParent=function(){return null==this.owner?null:this.owner.getParent()},t.exports=l},function(t,e,i){"use strict";var r=i(0);function n(){}for(var o in r)n[o]=r[o];n.MAX_ITERATIONS=2500,n.DEFAULT_EDGE_LENGTH=50,n.DEFAULT_SPRING_STRENGTH=.45,n.DEFAULT_REPULSION_STRENGTH=4500,n.DEFAULT_GRAVITY_STRENGTH=.4,n.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,n.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,n.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,n.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,n.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,n.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,n.COOLING_ADAPTATION_FACTOR=.33,n.ADAPTATION_LOWER_NODE_LIMIT=1e3,n.ADAPTATION_UPPER_NODE_LIMIT=5e3,n.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,n.MAX_NODE_DISPLACEMENT=3*n.MAX_NODE_DISPLACEMENT_INCREMENTAL,n.MIN_REPULSION_DIST=n.DEFAULT_EDGE_LENGTH/10,n.CONVERGENCE_CHECK_PERIOD=100,n.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,n.MIN_EDGE_LENGTH=1,n.GRID_CALCULATION_CHECK_PERIOD=10,t.exports=n},function(t,e,i){"use strict";function r(t,e){null==t&&null==e?(this.x=0,this.y=0):(this.x=t,this.y=e)}r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r.prototype.setX=function(t){this.x=t},r.prototype.setY=function(t){this.y=t},r.prototype.getDifference=function(t){return new DimensionD(this.x-t.x,this.y-t.y)},r.prototype.getCopy=function(){return new r(this.x,this.y)},r.prototype.translate=function(t){return this.x+=t.width,this.y+=t.height,this},t.exports=r},function(t,e,i){"use strict";var r=i(2),n=i(10),o=i(0),a=i(7),s=i(3),h=i(1),l=i(13),d=i(12),c=i(11);function g(t,e,i){r.call(this,i),this.estimatedSize=n.MIN_VALUE,this.margin=o.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=t,null!=e&&e instanceof a?this.graphManager=e:null!=e&&e instanceof Layout&&(this.graphManager=e.graphManager)}for(var u in g.prototype=Object.create(r.prototype),r)g[u]=r[u];g.prototype.getNodes=function(){return this.nodes},g.prototype.getEdges=function(){return this.edges},g.prototype.getGraphManager=function(){return this.graphManager},g.prototype.getParent=function(){return this.parent},g.prototype.getLeft=function(){return this.left},g.prototype.getRight=function(){return this.right},g.prototype.getTop=function(){return this.top},g.prototype.getBottom=function(){return this.bottom},g.prototype.isConnected=function(){return this.isConnected},g.prototype.add=function(t,e,i){if(null==e&&null==i){if(null==this.graphManager)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(t)>-1)throw"Node already in graph!";return t.owner=this,this.getNodes().push(t),t}if(!(this.getNodes().indexOf(e)>-1&&this.getNodes().indexOf(i)>-1))throw"Source or target not in graph!";if(e.owner!=i.owner||e.owner!=this)throw"Both owners must be this graph!";return e.owner!=i.owner?null:(t.source=e,t.target=i,t.isInterGraph=!1,this.getEdges().push(t),e.edges.push(t),i!=e&&i.edges.push(t),t)},g.prototype.remove=function(t){if(t instanceof s){if(null==t)throw"Node is null!";if(null==t.owner||t.owner!=this)throw"Owner graph is invalid!";if(null==this.graphManager)throw"Owner graph manager is invalid!";for(var e,i=t.edges.slice(),r=i.length,n=0;n<r;n++)(e=i[n]).isInterGraph?this.graphManager.remove(e):e.source.owner.remove(e);var o=this.nodes.indexOf(t);if(-1==o)throw"Node not in owner node list!";this.nodes.splice(o,1)}else if(t instanceof h){var e=t;if(null==e)throw"Edge is null!";if(null==e.source||null==e.target)throw"Source and/or target is null!";if(null==e.source.owner||null==e.target.owner||e.source.owner!=this||e.target.owner!=this)throw"Source and/or target owner is invalid!";var a=e.source.edges.indexOf(e),l=e.target.edges.indexOf(e);if(!(a>-1&&l>-1))throw"Source and/or target doesn't know this edge!";e.source.edges.splice(a,1),e.target!=e.source&&e.target.edges.splice(l,1);var o=e.source.owner.getEdges().indexOf(e);if(-1==o)throw"Not in owner's edge list!";e.source.owner.getEdges().splice(o,1)}},g.prototype.updateLeftTop=function(){for(var t,e,i,r=n.MAX_VALUE,o=n.MAX_VALUE,a=this.getNodes(),s=a.length,h=0;h<s;h++){var l=a[h];t=l.getTop(),e=l.getLeft(),r>t&&(r=t),o>e&&(o=e)}return r==n.MAX_VALUE?null:(i=void 0!=a[0].getParent().paddingLeft?a[0].getParent().paddingLeft:this.margin,this.left=o-i,this.top=r-i,new d(this.left,this.top))},g.prototype.updateBounds=function(t){for(var e,i,r,o,a,s=n.MAX_VALUE,h=-n.MAX_VALUE,d=n.MAX_VALUE,c=-n.MAX_VALUE,g=this.nodes,u=g.length,f=0;f<u;f++){var p=g[f];t&&null!=p.child&&p.updateBounds(),e=p.getLeft(),i=p.getRight(),r=p.getTop(),o=p.getBottom(),s>e&&(s=e),h<i&&(h=i),d>r&&(d=r),c<o&&(c=o)}var v=new l(s,d,h-s,c-d);s==n.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),a=void 0!=g[0].getParent().paddingLeft?g[0].getParent().paddingLeft:this.margin,this.left=v.x-a,this.right=v.x+v.width+a,this.top=v.y-a,this.bottom=v.y+v.height+a},g.calculateBounds=function(t){for(var e,i,r,o,a=n.MAX_VALUE,s=-n.MAX_VALUE,h=n.MAX_VALUE,d=-n.MAX_VALUE,c=t.length,g=0;g<c;g++){var u=t[g];e=u.getLeft(),i=u.getRight(),r=u.getTop(),o=u.getBottom(),a>e&&(a=e),s<i&&(s=i),h>r&&(h=r),d<o&&(d=o)}return new l(a,h,s-a,d-h)},g.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},g.prototype.getEstimatedSize=function(){if(this.estimatedSize==n.MIN_VALUE)throw"assert failed";return this.estimatedSize},g.prototype.calcEstimatedSize=function(){for(var t=0,e=this.nodes,i=e.length,r=0;r<i;r++)t+=e[r].calcEstimatedSize();return 0==t?this.estimatedSize=o.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=t/Math.sqrt(this.nodes.length),this.estimatedSize},g.prototype.updateConnected=function(){var t,e,i=this;if(0==this.nodes.length){this.isConnected=!0;return}var r=new c,n=new Set,o=this.nodes[0];for(o.withChildren().forEach(function(t){r.push(t),n.add(t)});0!==r.length;)for(var a=(t=(o=r.shift()).getEdges()).length,s=0;s<a;s++)null==(e=t[s].getOtherEndInGraph(o,this))||n.has(e)||e.withChildren().forEach(function(t){r.push(t),n.add(t)});if(this.isConnected=!1,n.size>=this.nodes.length){var h=0;n.forEach(function(t){t.owner==i&&h++}),h==this.nodes.length&&(this.isConnected=!0)}},t.exports=g},function(t,e,i){"use strict";var r,n=i(1);function o(t){r=i(6),this.layout=t,this.graphs=[],this.edges=[]}o.prototype.addRoot=function(){var t=this.layout.newGraph(),e=this.layout.newNode(null),i=this.add(t,e);return this.setRootGraph(i),this.rootGraph},o.prototype.add=function(t,e,i,r,n){if(null==i&&null==r&&null==n){if(null==t)throw"Graph is null!";if(null==e)throw"Parent node is null!";if(this.graphs.indexOf(t)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(t),null!=t.parent)throw"Already has a parent!";if(null!=e.child)throw"Already has a child!";return t.parent=e,e.child=t,t}n=i,r=e,i=t;var o=r.getOwner(),a=n.getOwner();if(null==o||o.getGraphManager()!=this)throw"Source not in this graph mgr!";if(null==a||a.getGraphManager()!=this)throw"Target not in this graph mgr!";if(o==a)return i.isInterGraph=!1,o.add(i,r,n);if(i.isInterGraph=!0,i.source=r,i.target=n,this.edges.indexOf(i)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(i),null==i.source||null==i.target)throw"Edge source and/or target is null!";if(-1!=i.source.edges.indexOf(i)||-1!=i.target.edges.indexOf(i))throw"Edge already in source and/or target incidency list!";return i.source.edges.push(i),i.target.edges.push(i),i},o.prototype.remove=function(t){if(t instanceof r){if(t.getGraphManager()!=this)throw"Graph not in this graph mgr";if(t!=this.rootGraph&&(null==t.parent||t.parent.graphManager!=this))throw"Invalid parent node!";for(var e,i,o=[],a=(o=o.concat(t.getEdges())).length,s=0;s<a;s++)e=o[s],t.remove(e);var h=[];a=(h=h.concat(t.getNodes())).length;for(var s=0;s<a;s++)i=h[s],t.remove(i);t==this.rootGraph&&this.setRootGraph(null);var l=this.graphs.indexOf(t);this.graphs.splice(l,1),t.parent=null}else if(t instanceof n){if(null==(e=t))throw"Edge is null!";if(!e.isInterGraph)throw"Not an inter-graph edge!";if(null==e.source||null==e.target)throw"Source and/or target is null!";if(-1==e.source.edges.indexOf(e)||-1==e.target.edges.indexOf(e))throw"Source and/or target doesn't know this edge!";var l=e.source.edges.indexOf(e);if(e.source.edges.splice(l,1),l=e.target.edges.indexOf(e),e.target.edges.splice(l,1),null==e.source.owner||null==e.source.owner.getGraphManager())throw"Edge owner graph or owner graph manager is null!";if(-1==e.source.owner.getGraphManager().edges.indexOf(e))throw"Not in owner graph manager's edge list!";var l=e.source.owner.getGraphManager().edges.indexOf(e);e.source.owner.getGraphManager().edges.splice(l,1)}},o.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},o.prototype.getGraphs=function(){return this.graphs},o.prototype.getAllNodes=function(){if(null==this.allNodes){for(var t=[],e=this.getGraphs(),i=e.length,r=0;r<i;r++)t=t.concat(e[r].getNodes());this.allNodes=t}return this.allNodes},o.prototype.resetAllNodes=function(){this.allNodes=null},o.prototype.resetAllEdges=function(){this.allEdges=null},o.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},o.prototype.getAllEdges=function(){if(null==this.allEdges){var t=[],e=this.getGraphs();e.length;for(var i=0;i<e.length;i++)t=t.concat(e[i].getEdges());t=t.concat(this.edges),this.allEdges=t}return this.allEdges},o.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},o.prototype.setAllNodesToApplyGravitation=function(t){if(null!=this.allNodesToApplyGravitation)throw"assert failed";this.allNodesToApplyGravitation=t},o.prototype.getRoot=function(){return this.rootGraph},o.prototype.setRootGraph=function(t){if(t.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=t,null==t.parent&&(t.parent=this.layout.newNode("Root node"))},o.prototype.getLayout=function(){return this.layout},o.prototype.isOneAncestorOfOther=function(t,e){if(null==t||null==e)throw"assert failed";if(t==e)return!0;for(var i,r=t.getOwner();null!=(i=r.getParent());){if(i==e)return!0;if(null==(r=i.getOwner()))break}for(r=e.getOwner();null!=(i=r.getParent());){if(i==t)return!0;if(null==(r=i.getOwner()))break}return!1},o.prototype.calcLowestCommonAncestors=function(){for(var t,e,i,r,n,o=this.getAllEdges(),a=o.length,s=0;s<a;s++){if(e=(t=o[s]).source,i=t.target,t.lca=null,t.sourceInLca=e,t.targetInLca=i,e==i){t.lca=e.getOwner();continue}for(r=e.getOwner();null==t.lca;){for(t.targetInLca=i,n=i.getOwner();null==t.lca;){if(n==r){t.lca=n;break}if(n==this.rootGraph)break;if(null!=t.lca)throw"assert failed";t.targetInLca=n.getParent(),n=t.targetInLca.getOwner()}if(r==this.rootGraph)break;null==t.lca&&(t.sourceInLca=r.getParent(),r=t.sourceInLca.getOwner())}if(null==t.lca)throw"assert failed"}},o.prototype.calcLowestCommonAncestor=function(t,e){if(t==e)return t.getOwner();for(var i=t.getOwner();null!=i;){for(var r=e.getOwner();null!=r;){if(r==i)return r;r=r.getParent().getOwner()}i=i.getParent().getOwner()}return i},o.prototype.calcInclusionTreeDepths=function(t,e){null==t&&null==e&&(t=this.rootGraph,e=1);for(var i,r=t.getNodes(),n=r.length,o=0;o<n;o++)(i=r[o]).inclusionTreeDepth=e,null!=i.child&&this.calcInclusionTreeDepths(i.child,e+1)},o.prototype.includesInvalidEdge=function(){for(var t,e=[],i=this.edges.length,r=0;r<i;r++)t=this.edges[r],this.isOneAncestorOfOther(t.source,t.target)&&e.push(t);for(var r=0;r<e.length;r++)this.remove(e[r]);return!1},t.exports=o},function(t,e,i){"use strict";var r=i(12);function n(){}n.calcSeparationAmount=function(t,e,i,r){if(!t.intersects(e))throw"assert failed";var n=[,,];this.decideDirectionsForOverlappingNodes(t,e,n),i[0]=Math.min(t.getRight(),e.getRight())-Math.max(t.x,e.x),i[1]=Math.min(t.getBottom(),e.getBottom())-Math.max(t.y,e.y),t.getX()<=e.getX()&&t.getRight()>=e.getRight()?i[0]+=Math.min(e.getX()-t.getX(),t.getRight()-e.getRight()):e.getX()<=t.getX()&&e.getRight()>=t.getRight()&&(i[0]+=Math.min(t.getX()-e.getX(),e.getRight()-t.getRight())),t.getY()<=e.getY()&&t.getBottom()>=e.getBottom()?i[1]+=Math.min(e.getY()-t.getY(),t.getBottom()-e.getBottom()):e.getY()<=t.getY()&&e.getBottom()>=t.getBottom()&&(i[1]+=Math.min(t.getY()-e.getY(),e.getBottom()-t.getBottom()));var o=Math.abs((e.getCenterY()-t.getCenterY())/(e.getCenterX()-t.getCenterX()));e.getCenterY()===t.getCenterY()&&e.getCenterX()===t.getCenterX()&&(o=1);var a=o*i[0],s=i[1]/o;i[0]<s?s=i[0]:a=i[1],i[0]=-1*n[0]*(s/2+r),i[1]=-1*n[1]*(a/2+r)},n.decideDirectionsForOverlappingNodes=function(t,e,i){t.getCenterX()<e.getCenterX()?i[0]=-1:i[0]=1,t.getCenterY()<e.getCenterY()?i[1]=-1:i[1]=1},n.getIntersection2=function(t,e,i){var r=t.getCenterX(),n=t.getCenterY(),o=e.getCenterX(),a=e.getCenterY();if(t.intersects(e))return i[0]=r,i[1]=n,i[2]=o,i[3]=a,!0;var s=t.getX(),h=t.getY(),l=t.getRight(),d=t.getX(),c=t.getBottom(),g=t.getRight(),u=t.getWidthHalf(),f=t.getHeightHalf(),p=e.getX(),v=e.getY(),y=e.getRight(),m=e.getX(),E=e.getBottom(),N=e.getRight(),T=e.getWidthHalf(),A=e.getHeightHalf(),L=!1,w=!1;if(r===o)n>a?(i[0]=r,i[1]=h,i[2]=o,i[3]=E):n<a&&(i[0]=r,i[1]=c,i[2]=o,i[3]=v);else if(n===a)r>o?(i[0]=s,i[1]=n,i[2]=y,i[3]=a):r<o&&(i[0]=l,i[1]=n,i[2]=p,i[3]=a);else{var _=t.height/t.width,I=e.height/e.width,C=(a-n)/(o-r),M=void 0,x=void 0,O=void 0,D=void 0,R=void 0,b=void 0;if(-_===C?(r>o?(i[0]=d,i[1]=c):(i[0]=l,i[1]=h),L=!0):_===C&&(r>o?(i[0]=s,i[1]=h):(i[0]=g,i[1]=c),L=!0),-I===C?(o>r?(i[2]=m,i[3]=E):(i[2]=y,i[3]=v),w=!0):I===C&&(o>r?(i[2]=p,i[3]=v):(i[2]=N,i[3]=E),w=!0),L&&w)return!1;if(r>o?n>a?(M=this.getCardinalDirection(_,C,4),x=this.getCardinalDirection(I,C,2)):(M=this.getCardinalDirection(-_,C,3),x=this.getCardinalDirection(-I,C,1)):n>a?(M=this.getCardinalDirection(-_,C,1),x=this.getCardinalDirection(-I,C,3)):(M=this.getCardinalDirection(_,C,2),x=this.getCardinalDirection(I,C,4)),!L)switch(M){case 1:D=h,O=r+-f/C,i[0]=O,i[1]=D;break;case 2:O=g,D=n+u*C,i[0]=O,i[1]=D;break;case 3:D=c,O=r+f/C,i[0]=O,i[1]=D;break;case 4:O=d,D=n+-u*C,i[0]=O,i[1]=D}if(!w)switch(x){case 1:b=v,R=o+-A/C,i[2]=R,i[3]=b;break;case 2:R=N,b=a+T*C,i[2]=R,i[3]=b;break;case 3:b=E,R=o+A/C,i[2]=R,i[3]=b;break;case 4:R=m,b=a+-T*C,i[2]=R,i[3]=b}}return!1},n.getCardinalDirection=function(t,e,i){return t>e?i:1+i%4},n.getIntersection=function(t,e,i,n){if(null==n)return this.getIntersection2(t,e,i);var o=t.x,a=t.y,s=e.x,h=e.y,l=i.x,d=i.y,c=n.x,g=n.y,u=void 0,f=void 0,p=void 0,v=void 0,y=void 0,m=void 0,E=void 0,N=void 0,T=void 0;return(p=h-a,y=o-s,E=s*a-o*h,v=g-d,m=l-c,N=c*d-l*g,0==(T=p*m-v*y))?null:new r((y*N-m*E)/T,(v*E-p*N)/T)},n.angleOfVector=function(t,e,i,r){var n=void 0;return t!==i?(n=Math.atan((r-e)/(i-t)),i<t?n+=Math.PI:r<e&&(n+=this.TWO_PI)):n=r<e?this.ONE_AND_HALF_PI:this.HALF_PI,n},n.doIntersect=function(t,e,i,r){var n=t.x,o=t.y,a=e.x,s=e.y,h=i.x,l=i.y,d=r.x,c=r.y,g=(a-n)*(c-l)-(d-h)*(s-o);if(0===g)return!1;var u=((c-l)*(d-n)+(h-d)*(c-o))/g,f=((o-s)*(d-n)+(a-n)*(c-o))/g;return 0<u&&u<1&&0<f&&f<1},n.findCircleLineIntersections=function(t,e,i,r,n,o,a){var s=(i-t)*(i-t)+(r-e)*(r-e),h=2*((t-n)*(i-t)+(e-o)*(r-e)),l=(t-n)*(t-n)+(e-o)*(e-o)-a*a;if(!(h*h-4*s*l>=0))return null;var d=(-h+Math.sqrt(h*h-4*s*l))/(2*s),c=(-h-Math.sqrt(h*h-4*s*l))/(2*s);return d>=0&&d<=1?[d]:c>=0&&c<=1?[c]:null},n.HALF_PI=.5*Math.PI,n.ONE_AND_HALF_PI=1.5*Math.PI,n.TWO_PI=2*Math.PI,n.THREE_PI=3*Math.PI,t.exports=n},function(t,e,i){"use strict";function r(){}r.sign=function(t){return t>0?1:t<0?-1:0},r.floor=function(t){return t<0?Math.ceil(t):Math.floor(t)},r.ceil=function(t){return t<0?Math.floor(t):Math.ceil(t)},t.exports=r},function(t,e,i){"use strict";function r(){}r.MAX_VALUE=0x7fffffff,r.MIN_VALUE=-0x80000000,t.exports=r},function(t,e,i){"use strict";var r=function(){function t(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,i,r){return i&&t(e.prototype,i),r&&t(e,r),e}}(),n=function(t){return{value:t,next:null,prev:null}},o=function(t,e,i,r){return null!==t?t.next=e:r.head=e,null!==i?i.prev=e:r.tail=e,e.prev=t,e.next=i,r.length++,e},a=function(t,e){var i=t.prev,r=t.next;return null!==i?i.next=r:e.head=r,null!==r?r.prev=i:e.tail=i,t.prev=t.next=null,e.length--,t};t.exports=function(){function t(e){var i=this;if(!(this instanceof t))throw TypeError("Cannot call a class as a function");this.length=0,this.head=null,this.tail=null,null!=e&&e.forEach(function(t){return i.push(t)})}return r(t,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(t,e){return o(e.prev,n(t),e,this)}},{key:"insertAfter",value:function(t,e){return o(e,n(t),e.next,this)}},{key:"insertNodeBefore",value:function(t,e){return o(e.prev,t,e,this)}},{key:"insertNodeAfter",value:function(t,e){return o(e,t,e.next,this)}},{key:"push",value:function(t){return o(this.tail,n(t),null,this)}},{key:"unshift",value:function(t){return o(null,n(t),this.head,this)}},{key:"remove",value:function(t){return a(t,this)}},{key:"pop",value:function(){return a(this.tail,this).value}},{key:"popNode",value:function(){return a(this.tail,this)}},{key:"shift",value:function(){return a(this.head,this).value}},{key:"shiftNode",value:function(){return a(this.head,this)}},{key:"get_object_at",value:function(t){if(t<=this.length()){for(var e=1,i=this.head;e<t;)i=i.next,e++;return i.value}}},{key:"set_object_at",value:function(t,e){if(t<=this.length()){for(var i=1,r=this.head;i<t;)r=r.next,i++;r.value=e}}}]),t}()},function(t,e,i){"use strict";function r(t,e,i){this.x=null,this.y=null,null==t&&null==e&&null==i?(this.x=0,this.y=0):"number"==typeof t&&"number"==typeof e&&null==i?(this.x=t,this.y=e):"Point"==t.constructor.name&&null==e&&null==i&&(i=t,this.x=i.x,this.y=i.y)}r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r.prototype.getLocation=function(){return new r(this.x,this.y)},r.prototype.setLocation=function(t,e,i){"Point"==t.constructor.name&&null==e&&null==i?(i=t,this.setLocation(i.x,i.y)):"number"==typeof t&&"number"==typeof e&&null==i&&(parseInt(t)==t&&parseInt(e)==e?this.move(t,e):(this.x=Math.floor(t+.5),this.y=Math.floor(e+.5)))},r.prototype.move=function(t,e){this.x=t,this.y=e},r.prototype.translate=function(t,e){this.x+=t,this.y+=e},r.prototype.equals=function(t){return"Point"==t.constructor.name?this.x==t.x&&this.y==t.y:this==t},r.prototype.toString=function(){return new r().constructor.name+"[x="+this.x+",y="+this.y+"]"},t.exports=r},function(t,e,i){"use strict";function r(t,e,i,r){this.x=0,this.y=0,this.width=0,this.height=0,null!=t&&null!=e&&null!=i&&null!=r&&(this.x=t,this.y=e,this.width=i,this.height=r)}r.prototype.getX=function(){return this.x},r.prototype.setX=function(t){this.x=t},r.prototype.getY=function(){return this.y},r.prototype.setY=function(t){this.y=t},r.prototype.getWidth=function(){return this.width},r.prototype.setWidth=function(t){this.width=t},r.prototype.getHeight=function(){return this.height},r.prototype.setHeight=function(t){this.height=t},r.prototype.getRight=function(){return this.x+this.width},r.prototype.getBottom=function(){return this.y+this.height},r.prototype.intersects=function(t){return!(this.getRight()<t.x||this.getBottom()<t.y||t.getRight()<this.x||t.getBottom()<this.y)},r.prototype.getCenterX=function(){return this.x+this.width/2},r.prototype.getMinX=function(){return this.getX()},r.prototype.getMaxX=function(){return this.getX()+this.width},r.prototype.getCenterY=function(){return this.y+this.height/2},r.prototype.getMinY=function(){return this.getY()},r.prototype.getMaxY=function(){return this.getY()+this.height},r.prototype.getWidthHalf=function(){return this.width/2},r.prototype.getHeightHalf=function(){return this.height/2},t.exports=r},function(t,e,i){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};function n(){}n.lastID=0,n.createID=function(t){return n.isPrimitive(t)?t:(null!=t.uniqueID||(t.uniqueID=n.getString(),n.lastID++),t.uniqueID)},n.getString=function(t){return null==t&&(t=n.lastID),"Object#"+t},n.isPrimitive=function(t){var e=void 0===t?"undefined":r(t);return null==t||"object"!=e&&"function"!=e},t.exports=n},function(t,e,i){"use strict";function r(t){if(!Array.isArray(t))return Array.from(t);for(var e=0,i=Array(t.length);e<t.length;e++)i[e]=t[e];return i}var n=i(0),o=i(7),a=i(3),s=i(1),h=i(6),l=i(5),d=i(17),c=i(29);function g(t){c.call(this),this.layoutQuality=n.QUALITY,this.createBendsAsNeeded=n.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=n.DEFAULT_INCREMENTAL,this.animationOnLayout=n.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=n.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=n.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=n.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new o(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,null!=t&&(this.isRemoteUse=t)}g.RANDOM_SEED=1,g.prototype=Object.create(c.prototype),g.prototype.getGraphManager=function(){return this.graphManager},g.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},g.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},g.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},g.prototype.newGraphManager=function(){var t=new o(this);return this.graphManager=t,t},g.prototype.newGraph=function(t){return new h(null,this.graphManager,t)},g.prototype.newNode=function(t){return new a(this.graphManager,t)},g.prototype.newEdge=function(t){return new s(null,null,t)},g.prototype.checkLayoutSuccess=function(){return null==this.graphManager.getRoot()||0==this.graphManager.getRoot().getNodes().length||this.graphManager.includesInvalidEdge()},g.prototype.runLayout=function(){var t;return this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters(),t=!this.checkLayoutSuccess()&&this.layout(),"during"!==n.ANIMATE&&(t&&!this.isSubLayout&&this.doPostLayout(),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,t)},g.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},g.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var t=this.graphManager.getAllEdges(),e=0;e<t.length;e++)t[e];for(var i=this.graphManager.getRoot().getNodes(),e=0;e<i.length;e++)i[e];this.update(this.graphManager.getRoot())}},g.prototype.update=function(t){if(null==t)this.update2();else if(t instanceof a){if(null!=t.getChild())for(var e=t.getChild().getNodes(),i=0;i<e.length;i++)update(e[i]);null!=t.vGraphObject&&t.vGraphObject.update(t)}else t instanceof s?null!=t.vGraphObject&&t.vGraphObject.update(t):t instanceof h&&null!=t.vGraphObject&&t.vGraphObject.update(t)},g.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=n.QUALITY,this.animationDuringLayout=n.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=n.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=n.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=n.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=n.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=n.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},g.prototype.transform=function(t){if(void 0==t)this.transform(new l(0,0));else{var e=new d,i=this.graphManager.getRoot().updateLeftTop();if(null!=i){e.setWorldOrgX(t.x),e.setWorldOrgY(t.y),e.setDeviceOrgX(i.x),e.setDeviceOrgY(i.y);for(var r=this.getAllNodes(),n=0;n<r.length;n++)r[n].transform(e)}}},g.prototype.positionNodesRandomly=function(t){if(void 0==t)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var e,i,r=t.getNodes(),n=0;n<r.length;n++)null==(i=(e=r[n]).getChild())||0==i.getNodes().length?e.scatter():(this.positionNodesRandomly(i),e.updateBounds())},g.prototype.getFlatForest=function(){for(var t=[],e=!0,i=this.graphManager.getRoot().getNodes(),n=!0,o=0;o<i.length;o++)null!=i[o].getChild()&&(n=!1);if(!n)return t;var a=new Set,s=[],h=new Map,l=[];for(l=l.concat(i);l.length>0&&e;){for(s.push(l[0]);s.length>0&&e;){var d=s[0];s.splice(0,1),a.add(d);for(var c=d.getEdges(),o=0;o<c.length;o++){var g=c[o].getOtherEnd(d);if(h.get(d)!=g)if(a.has(g)){e=!1;break}else s.push(g),h.set(g,d)}}if(e){var u=[].concat(r(a));t.push(u);for(var o=0;o<u.length;o++){var f=u[o],p=l.indexOf(f);p>-1&&l.splice(p,1)}a=new Set,h=new Map}else t=[]}return t},g.prototype.createDummyNodesForBendpoints=function(t){for(var e=[],i=t.source,r=this.graphManager.calcLowestCommonAncestor(t.source,t.target),n=0;n<t.bendpoints.length;n++){var o=this.newNode(null);o.setRect(new Point(0,0),new Dimension(1,1)),r.add(o);var a=this.newEdge(null);this.graphManager.add(a,i,o),e.add(o),i=o}var a=this.newEdge(null);return this.graphManager.add(a,i,t.target),this.edgeToDummyNodes.set(t,e),t.isInterGraph()?this.graphManager.remove(t):r.remove(t),e},g.prototype.createBendpointsFromDummyNodes=function(){var t=[];t=t.concat(this.graphManager.getAllEdges()),t=[].concat(r(this.edgeToDummyNodes.keys())).concat(t);for(var e=0;e<t.length;e++){var i=t[e];if(i.bendpoints.length>0){for(var n=this.edgeToDummyNodes.get(i),o=0;o<n.length;o++){var a=n[o],s=new l(a.getCenterX(),a.getCenterY()),h=i.bendpoints.get(o);h.x=s.x,h.y=s.y,a.getOwner().remove(a)}this.graphManager.add(i,i.source,i.target)}}},g.transform=function(t,e,i,r){if(void 0==i||void 0==r)return t<=50?(n=9*e/500,o=e/10):(n=9*e/50,o=-8*e),n*t+o;var n,o,a=e;return t<=50?a-=(e-e/i)/50*(50-t):a+=(e*r-e)/50*(t-50),a},g.findCenterOfTree=function(t){var e=[];e=e.concat(t);var i=[],r=new Map,n=!1,o=null;(1==e.length||2==e.length)&&(n=!0,o=e[0]);for(var a=0;a<e.length;a++){var s=e[a],h=s.getNeighborsList().size;r.set(s,s.getNeighborsList().size),1==h&&i.push(s)}var l=[];for(l=l.concat(i);!n;){var d=[];d=d.concat(l),l=[];for(var a=0;a<e.length;a++){var s=e[a],c=e.indexOf(s);c>=0&&e.splice(c,1),s.getNeighborsList().forEach(function(t){if(0>i.indexOf(t)){var e=r.get(t)-1;1==e&&l.push(t),r.set(t,e)}})}i=i.concat(l),(1==e.length||2==e.length)&&(n=!0,o=e[0])}return o},g.prototype.setGraphManager=function(t){this.graphManager=t},t.exports=g},function(t,e,i){"use strict";function r(){}r.seed=1,r.x=0,r.nextDouble=function(){return r.x=1e4*Math.sin(r.seed++),r.x-Math.floor(r.x)},t.exports=r},function(t,e,i){"use strict";var r=i(5);function n(t,e){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}n.prototype.getWorldOrgX=function(){return this.lworldOrgX},n.prototype.setWorldOrgX=function(t){this.lworldOrgX=t},n.prototype.getWorldOrgY=function(){return this.lworldOrgY},n.prototype.setWorldOrgY=function(t){this.lworldOrgY=t},n.prototype.getWorldExtX=function(){return this.lworldExtX},n.prototype.setWorldExtX=function(t){this.lworldExtX=t},n.prototype.getWorldExtY=function(){return this.lworldExtY},n.prototype.setWorldExtY=function(t){this.lworldExtY=t},n.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},n.prototype.setDeviceOrgX=function(t){this.ldeviceOrgX=t},n.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},n.prototype.setDeviceOrgY=function(t){this.ldeviceOrgY=t},n.prototype.getDeviceExtX=function(){return this.ldeviceExtX},n.prototype.setDeviceExtX=function(t){this.ldeviceExtX=t},n.prototype.getDeviceExtY=function(){return this.ldeviceExtY},n.prototype.setDeviceExtY=function(t){this.ldeviceExtY=t},n.prototype.transformX=function(t){var e=0,i=this.lworldExtX;return 0!=i&&(e=this.ldeviceOrgX+(t-this.lworldOrgX)*this.ldeviceExtX/i),e},n.prototype.transformY=function(t){var e=0,i=this.lworldExtY;return 0!=i&&(e=this.ldeviceOrgY+(t-this.lworldOrgY)*this.ldeviceExtY/i),e},n.prototype.inverseTransformX=function(t){var e=0,i=this.ldeviceExtX;return 0!=i&&(e=this.lworldOrgX+(t-this.ldeviceOrgX)*this.lworldExtX/i),e},n.prototype.inverseTransformY=function(t){var e=0,i=this.ldeviceExtY;return 0!=i&&(e=this.lworldOrgY+(t-this.ldeviceOrgY)*this.lworldExtY/i),e},n.prototype.inverseTransformPoint=function(t){return new r(this.inverseTransformX(t.x),this.inverseTransformY(t.y))},t.exports=n},function(t,e,i){"use strict";var r=i(15),n=i(4),o=i(0),a=i(8),s=i(9);function h(){r.call(this),this.useSmartIdealEdgeLengthCalculation=n.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=n.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=n.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=n.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=n.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*n.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=n.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=n.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=n.MAX_ITERATIONS}for(var l in h.prototype=Object.create(r.prototype),r)h[l]=r[l];h.prototype.initParameters=function(){r.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=n.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},h.prototype.calcIdealEdgeLengths=function(){for(var t,e,i,r,a,s,h,l=this.getGraphManager().getAllEdges(),d=0;d<l.length;d++)e=(t=l[d]).idealLength,t.isInterGraph&&(r=t.getSource(),a=t.getTarget(),s=t.getSourceInLca().getEstimatedSize(),h=t.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(t.idealLength+=s+h-2*o.SIMPLE_NODE_SIZE),i=t.getLca().getInclusionTreeDepth(),t.idealLength+=e*n.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(r.getInclusionTreeDepth()+a.getInclusionTreeDepth()-2*i))},h.prototype.initSpringEmbedder=function(){var t=this.getAllNodes().length;this.incremental?(t>n.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*n.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(t-n.ADAPTATION_LOWER_NODE_LIMIT)/(n.ADAPTATION_UPPER_NODE_LIMIT-n.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-n.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=n.MAX_NODE_DISPLACEMENT_INCREMENTAL):(t>n.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(n.COOLING_ADAPTATION_FACTOR,1-(t-n.ADAPTATION_LOWER_NODE_LIMIT)/(n.ADAPTATION_UPPER_NODE_LIMIT-n.ADAPTATION_LOWER_NODE_LIMIT)*(1-n.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=n.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(5*this.getAllNodes().length,this.maxIterations),this.displacementThresholdPerNode=3*n.DEFAULT_EDGE_LENGTH/100,this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},h.prototype.calcSpringForces=function(){for(var t,e=this.getAllEdges(),i=0;i<e.length;i++)t=e[i],this.calcSpringForce(t,t.idealLength)},h.prototype.calcRepulsionForces=function(){var t,e,i,r,o,a=!(arguments.length>0)||void 0===arguments[0]||arguments[0],s=arguments.length>1&&void 0!==arguments[1]&&arguments[1],h=this.getAllNodes();if(this.useFRGridVariant)for(this.totalIterations%n.GRID_CALCULATION_CHECK_PERIOD==1&&a&&this.updateGrid(),o=new Set,t=0;t<h.length;t++)i=h[t],this.calculateRepulsionForceOfANode(i,o,a,s),o.add(i);else for(t=0;t<h.length;t++)for(i=h[t],e=t+1;e<h.length;e++)r=h[e],i.getOwner()==r.getOwner()&&this.calcRepulsionForce(i,r)},h.prototype.calcGravitationalForces=function(){for(var t,e=this.getAllNodesToApplyGravitation(),i=0;i<e.length;i++)t=e[i],this.calcGravitationalForce(t)},h.prototype.moveNodes=function(){for(var t=this.getAllNodes(),e=0;e<t.length;e++)t[e].move()},h.prototype.calcSpringForce=function(t,e){var i,r,n,o,a=t.getSource(),s=t.getTarget();if(this.uniformLeafNodeSizes&&null==a.getChild()&&null==s.getChild())t.updateLengthSimple();else if(t.updateLength(),t.isOverlapingSourceAndTarget)return;0!=(i=t.getLength())&&(n=(r=t.edgeElasticity*(i-e))*(t.lengthX/i),o=r*(t.lengthY/i),a.springForceX+=n,a.springForceY+=o,s.springForceX-=n,s.springForceY-=o)},h.prototype.calcRepulsionForce=function(t,e){var i,r,o,h,l,d,c,g=t.getRect(),u=e.getRect(),f=[,,],p=[,,,,];if(g.intersects(u)){a.calcSeparationAmount(g,u,f,n.DEFAULT_EDGE_LENGTH/2),d=2*f[0],c=2*f[1];var v=t.noOfChildren*e.noOfChildren/(t.noOfChildren+e.noOfChildren);t.repulsionForceX-=v*d,t.repulsionForceY-=v*c,e.repulsionForceX+=v*d,e.repulsionForceY+=v*c}else this.uniformLeafNodeSizes&&null==t.getChild()&&null==e.getChild()?(i=u.getCenterX()-g.getCenterX(),r=u.getCenterY()-g.getCenterY()):(a.getIntersection(g,u,p),i=p[2]-p[0],r=p[3]-p[1]),Math.abs(i)<n.MIN_REPULSION_DIST&&(i=s.sign(i)*n.MIN_REPULSION_DIST),Math.abs(r)<n.MIN_REPULSION_DIST&&(r=s.sign(r)*n.MIN_REPULSION_DIST),h=Math.sqrt(o=i*i+r*r),d=(l=(t.nodeRepulsion/2+e.nodeRepulsion/2)*t.noOfChildren*e.noOfChildren/o)*i/h,c=l*r/h,t.repulsionForceX-=d,t.repulsionForceY-=c,e.repulsionForceX+=d,e.repulsionForceY+=c},h.prototype.calcGravitationalForce=function(t){var e,i,r,n,o,a,s,h;i=((e=t.getOwner()).getRight()+e.getLeft())/2,r=(e.getTop()+e.getBottom())/2,n=t.getCenterX()-i,o=t.getCenterY()-r,a=Math.abs(n)+t.getWidth()/2,s=Math.abs(o)+t.getHeight()/2,t.getOwner()==this.graphManager.getRoot()?(a>(h=e.getEstimatedSize()*this.gravityRangeFactor)||s>h)&&(t.gravitationForceX=-this.gravityConstant*n,t.gravitationForceY=-this.gravityConstant*o):(a>(h=e.getEstimatedSize()*this.compoundGravityRangeFactor)||s>h)&&(t.gravitationForceX=-this.gravityConstant*n*this.compoundGravityConstant,t.gravitationForceY=-this.gravityConstant*o*this.compoundGravityConstant)},h.prototype.isConverged=function(){var t,e=!1;return this.totalIterations>this.maxIterations/3&&(e=2>Math.abs(this.totalDisplacement-this.oldTotalDisplacement)),t=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,t||e},h.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},h.prototype.calcNoOfChildrenForAllNodes=function(){for(var t,e=this.graphManager.getAllNodes(),i=0;i<e.length;i++)(t=e[i]).noOfChildren=t.getNoOfChildren()},h.prototype.calcGrid=function(t){var e=0,i=0;e=parseInt(Math.ceil((t.getRight()-t.getLeft())/this.repulsionRange)),i=parseInt(Math.ceil((t.getBottom()-t.getTop())/this.repulsionRange));for(var r=Array(e),n=0;n<e;n++)r[n]=Array(i);for(var n=0;n<e;n++)for(var o=0;o<i;o++)r[n][o]=[];return r},h.prototype.addNodeToGrid=function(t,e,i){var r=0,n=0,o=0,a=0;r=parseInt(Math.floor((t.getRect().x-e)/this.repulsionRange)),n=parseInt(Math.floor((t.getRect().width+t.getRect().x-e)/this.repulsionRange)),o=parseInt(Math.floor((t.getRect().y-i)/this.repulsionRange)),a=parseInt(Math.floor((t.getRect().height+t.getRect().y-i)/this.repulsionRange));for(var s=r;s<=n;s++)for(var h=o;h<=a;h++)this.grid[s][h].push(t),t.setGridCoordinates(r,n,o,a)},h.prototype.updateGrid=function(){var t,e,i=this.getAllNodes();for(t=0,this.grid=this.calcGrid(this.graphManager.getRoot());t<i.length;t++)e=i[t],this.addNodeToGrid(e,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},h.prototype.calculateRepulsionForceOfANode=function(t,e,i,r){if(this.totalIterations%n.GRID_CALCULATION_CHECK_PERIOD==1&&i||r){var o,a=new Set;t.surrounding=[];for(var s=this.grid,h=t.startX-1;h<t.finishX+2;h++)for(var l=t.startY-1;l<t.finishY+2;l++)if(!(h<0||l<0||h>=s.length||l>=s[0].length)){for(var d=0;d<s[h][l].length;d++)if(o=s[h][l][d],t.getOwner()==o.getOwner()&&t!=o&&!e.has(o)&&!a.has(o)){var c=Math.abs(t.getCenterX()-o.getCenterX())-(t.getWidth()/2+o.getWidth()/2),g=Math.abs(t.getCenterY()-o.getCenterY())-(t.getHeight()/2+o.getHeight()/2);c<=this.repulsionRange&&g<=this.repulsionRange&&a.add(o)}}t.surrounding=[].concat(function(t){if(!Array.isArray(t))return Array.from(t);for(var e=0,i=Array(t.length);e<t.length;e++)i[e]=t[e];return i}(a))}for(h=0;h<t.surrounding.length;h++)this.calcRepulsionForce(t,t.surrounding[h])},h.prototype.calcRepulsionRange=function(){return 0},t.exports=h},function(t,e,i){"use strict";var r=i(1),n=i(4);function o(t,e,i){r.call(this,t,e,i),this.idealLength=n.DEFAULT_EDGE_LENGTH,this.edgeElasticity=n.DEFAULT_SPRING_STRENGTH}for(var a in o.prototype=Object.create(r.prototype),r)o[a]=r[a];t.exports=o},function(t,e,i){"use strict";var r=i(3),n=i(4);function o(t,e,i,o){r.call(this,t,e,i,o),this.nodeRepulsion=n.DEFAULT_REPULSION_STRENGTH,this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}for(var a in o.prototype=Object.create(r.prototype),r)o[a]=r[a];o.prototype.setGridCoordinates=function(t,e,i,r){this.startX=t,this.finishX=e,this.startY=i,this.finishY=r},t.exports=o},function(t,e,i){"use strict";function r(t,e){this.width=0,this.height=0,null!==t&&null!==e&&(this.height=e,this.width=t)}r.prototype.getWidth=function(){return this.width},r.prototype.setWidth=function(t){this.width=t},r.prototype.getHeight=function(){return this.height},r.prototype.setHeight=function(t){this.height=t},t.exports=r},function(t,e,i){"use strict";var r=i(14);function n(){this.map={},this.keys=[]}n.prototype.put=function(t,e){var i=r.createID(t);this.contains(i)||(this.map[i]=e,this.keys.push(t))},n.prototype.contains=function(t){return r.createID(t),null!=this.map[t]},n.prototype.get=function(t){var e=r.createID(t);return this.map[e]},n.prototype.keySet=function(){return this.keys},t.exports=n},function(t,e,i){"use strict";var r=i(14);function n(){this.set={}}n.prototype.add=function(t){var e=r.createID(t);this.contains(e)||(this.set[e]=t)},n.prototype.remove=function(t){delete this.set[r.createID(t)]},n.prototype.clear=function(){this.set={}},n.prototype.contains=function(t){return this.set[r.createID(t)]==t},n.prototype.isEmpty=function(){return 0===this.size()},n.prototype.size=function(){return Object.keys(this.set).length},n.prototype.addAllTo=function(t){for(var e=Object.keys(this.set),i=e.length,r=0;r<i;r++)t.push(this.set[e[r]])},n.prototype.size=function(){return Object.keys(this.set).length},n.prototype.addAll=function(t){for(var e=t.length,i=0;i<e;i++){var r=t[i];this.add(r)}},t.exports=n},function(t,e,i){"use strict";function r(){}r.multMat=function(t,e){for(var i=[],r=0;r<t.length;r++){i[r]=[];for(var n=0;n<e[0].length;n++){i[r][n]=0;for(var o=0;o<t[0].length;o++)i[r][n]+=t[r][o]*e[o][n]}}return i},r.transpose=function(t){for(var e=[],i=0;i<t[0].length;i++){e[i]=[];for(var r=0;r<t.length;r++)e[i][r]=t[r][i]}return e},r.multCons=function(t,e){for(var i=[],r=0;r<t.length;r++)i[r]=t[r]*e;return i},r.minusOp=function(t,e){for(var i=[],r=0;r<t.length;r++)i[r]=t[r]-e[r];return i},r.dotProduct=function(t,e){for(var i=0,r=0;r<t.length;r++)i+=t[r]*e[r];return i},r.mag=function(t){return Math.sqrt(this.dotProduct(t,t))},r.normalize=function(t){for(var e=[],i=this.mag(t),r=0;r<t.length;r++)e[r]=t[r]/i;return e},r.multGamma=function(t){for(var e=[],i=0,r=0;r<t.length;r++)i+=t[r];i*=-1/t.length;for(var n=0;n<t.length;n++)e[n]=i+t[n];return e},r.multL=function(t,e,i){for(var r=[],n=[],o=[],a=0;a<e[0].length;a++){for(var s=0,h=0;h<e.length;h++)s+=-.5*e[h][a]*t[h];n[a]=s}for(var l=0;l<i.length;l++){for(var d=0,c=0;c<i.length;c++)d+=i[l][c]*n[c];o[l]=d}for(var g=0;g<e.length;g++){for(var u=0,f=0;f<e[0].length;f++)u+=e[g][f]*o[f];r[g]=u}return r},t.exports=r},function(t,e,i){"use strict";var r=function(){function t(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,i,r){return i&&t(e.prototype,i),r&&t(e,r),e}}(),n=i(11);t.exports=function(){function t(e,i){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");(null!==i||void 0!==i)&&(this.compareFunction=this._defaultCompareFunction);var r=void 0;r=e instanceof n?e.size():e.length,this._quicksort(e,0,r-1)}return r(t,[{key:"_quicksort",value:function(t,e,i){if(e<i){var r=this._partition(t,e,i);this._quicksort(t,e,r),this._quicksort(t,r+1,i)}}},{key:"_partition",value:function(t,e,i){for(var r=this._get(t,e),n=e,o=i;;){for(;this.compareFunction(r,this._get(t,o));)o--;for(;this.compareFunction(this._get(t,n),r);)n++;if(!(n<o))return o;this._swap(t,n,o),n++,o--}}},{key:"_get",value:function(t,e){return t instanceof n?t.get_object_at(e):t[e]}},{key:"_set",value:function(t,e,i){t instanceof n?t.set_object_at(e,i):t[e]=i}},{key:"_swap",value:function(t,e,i){var r=this._get(t,e);this._set(t,e,this._get(t,i)),this._set(t,i,r)}},{key:"_defaultCompareFunction",value:function(t,e){return e>t}}]),t}()},function(t,e,i){"use strict";function r(){}r.svd=function(t){this.U=null,this.V=null,this.s=null,this.m=0,this.n=0,this.m=t.length,this.n=t[0].length;var e=Math.min(this.m,this.n);this.s=function(t){for(var e=[];t-- >0;)e.push(0);return e}(Math.min(this.m+1,this.n)),this.U=function t(e){if(0==e.length)return 0;for(var i=[],r=0;r<e[0];r++)i.push(t(e.slice(1)));return i}([this.m,e]),this.V=function t(e){if(0==e.length)return 0;for(var i=[],r=0;r<e[0];r++)i.push(t(e.slice(1)));return i}([this.n,this.n]);for(var i=function(t){for(var e=[];t-- >0;)e.push(0);return e}(this.n),n=function(t){for(var e=[];t-- >0;)e.push(0);return e}(this.m),o=Math.min(this.m-1,this.n),a=Math.max(0,Math.min(this.n-2,this.m)),s=0;s<Math.max(o,a);s++){if(s<o){this.s[s]=0;for(var h=s;h<this.m;h++)this.s[s]=r.hypot(this.s[s],t[h][s]);if(0!==this.s[s]){t[s][s]<0&&(this.s[s]=-this.s[s]);for(var l=s;l<this.m;l++)t[l][s]/=this.s[s];t[s][s]+=1}this.s[s]=-this.s[s]}for(var d=s+1;d<this.n;d++){if(p=s<o,v=0!==this.s[s],p&&v){for(var c=0,g=s;g<this.m;g++)c+=t[g][s]*t[g][d];c=-c/t[s][s];for(var u=s;u<this.m;u++)t[u][d]+=c*t[u][s]}i[d]=t[s][d]}if(y=s<o)for(var f=s;f<this.m;f++)this.U[f][s]=t[f][s];if(s<a){i[s]=0;for(var p,v,y,m,E,N=s+1;N<this.n;N++)i[s]=r.hypot(i[s],i[N]);if(0!==i[s]){i[s+1]<0&&(i[s]=-i[s]);for(var T=s+1;T<this.n;T++)i[T]/=i[s];i[s+1]+=1}if(i[s]=-i[s],m=s+1<this.m,E=0!==i[s],m&&E){for(var A=s+1;A<this.m;A++)n[A]=0;for(var L=s+1;L<this.n;L++)for(var w=s+1;w<this.m;w++)n[w]+=i[L]*t[w][L];for(var _=s+1;_<this.n;_++)for(var I=-i[_]/i[s+1],C=s+1;C<this.m;C++)t[C][_]+=I*n[C]}for(var M=s+1;M<this.n;M++)this.V[M][s]=i[M]}}var x=Math.min(this.n,this.m+1);o<this.n&&(this.s[o]=t[o][o]),this.m<x&&(this.s[x-1]=0),a+1<x&&(i[a]=t[a][x-1]),i[x-1]=0;for(var O=o;O<e;O++){for(var D=0;D<this.m;D++)this.U[D][O]=0;this.U[O][O]=1}for(var R=o-1;R>=0;R--)if(0!==this.s[R]){for(var b=R+1;b<e;b++){for(var G=0,F=R;F<this.m;F++)G+=this.U[F][R]*this.U[F][b];G=-G/this.U[R][R];for(var S=R;S<this.m;S++)this.U[S][b]+=G*this.U[S][R]}for(var P=R;P<this.m;P++)this.U[P][R]=-this.U[P][R];this.U[R][R]=1+this.U[R][R];for(var U=0;U<R-1;U++)this.U[U][R]=0}else{for(var Y=0;Y<this.m;Y++)this.U[Y][R]=0;this.U[R][R]=1}for(var k=this.n-1;k>=0;k--){if(td=k<a,tc=0!==i[k],td&&tc)for(var H=k+1;H<e;H++){for(var X=0,W=k+1;W<this.n;W++)X+=this.V[W][k]*this.V[W][H];X=-X/this.V[k+1][k];for(var z=k+1;z<this.n;z++)this.V[z][H]+=X*this.V[z][k]}for(var V=0;V<this.n;V++)this.V[V][k]=0;this.V[k][k]=1}for(var B=x-1,j=0;x>0;){var $=void 0,q=void 0;for($=x-2;$>=-1&&-1!==$;$--)if(Math.abs(i[$])<=16033346880071782e-307+2220446049250313e-31*(Math.abs(this.s[$])+Math.abs(this.s[$+1]))){i[$]=0;break}if($===x-2)q=4;else{var Z=void 0;for(Z=x-1;Z>=$&&Z!==$;Z--){var Q=(Z!==x?Math.abs(i[Z]):0)+(Z!==$+1?Math.abs(i[Z-1]):0);if(Math.abs(this.s[Z])<=16033346880071782e-307+2220446049250313e-31*Q){this.s[Z]=0;break}}Z===$?q=3:Z===x-1?q=1:(q=2,$=Z)}switch($++,q){case 1:var J=i[x-2];i[x-2]=0;for(var K=x-2;K>=$;K--){var tt=r.hypot(this.s[K],J),te=this.s[K]/tt,ti=J/tt;this.s[K]=tt,K!==$&&(J=-ti*i[K-1],i[K-1]=te*i[K-1]);for(var tr=0;tr<this.n;tr++)tt=te*this.V[tr][K]+ti*this.V[tr][x-1],this.V[tr][x-1]=-ti*this.V[tr][K]+te*this.V[tr][x-1],this.V[tr][K]=tt}break;case 2:var tn=i[$-1];i[$-1]=0;for(var to=$;to<x;to++){var ta=r.hypot(this.s[to],tn),ts=this.s[to]/ta,th=tn/ta;this.s[to]=ta,tn=-th*i[to],i[to]=ts*i[to];for(var tl=0;tl<this.m;tl++)ta=ts*this.U[tl][to]+th*this.U[tl][$-1],this.U[tl][$-1]=-th*this.U[tl][to]+ts*this.U[tl][$-1],this.U[tl][to]=ta}break;case 3:var td,tc,tg,tu,tf=Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[x-1]),Math.abs(this.s[x-2])),Math.abs(i[x-2])),Math.abs(this.s[$])),Math.abs(i[$])),tp=this.s[x-1]/tf,tv=this.s[x-2]/tf,ty=i[x-2]/tf,tm=this.s[$]/tf,tE=i[$]/tf,tN=((tv+tp)*(tv-tp)+ty*ty)/2,tT=tp*ty*(tp*ty),tA=0;tg=0!==tN,tu=0!==tT,(tg||tu)&&(tA=Math.sqrt(tN*tN+tT),tN<0&&(tA=-tA),tA=tT/(tN+tA));for(var tL=(tm+tp)*(tm-tp)+tA,tw=tm*tE,t_=$;t_<x-1;t_++){var tI=r.hypot(tL,tw),tC=tL/tI,tM=tw/tI;t_!==$&&(i[t_-1]=tI),tL=tC*this.s[t_]+tM*i[t_],i[t_]=tC*i[t_]-tM*this.s[t_],tw=tM*this.s[t_+1],this.s[t_+1]=tC*this.s[t_+1];for(var tx=0;tx<this.n;tx++)tI=tC*this.V[tx][t_]+tM*this.V[tx][t_+1],this.V[tx][t_+1]=-tM*this.V[tx][t_]+tC*this.V[tx][t_+1],this.V[tx][t_]=tI;if(tI=r.hypot(tL,tw),tC=tL/tI,tM=tw/tI,this.s[t_]=tI,tL=tC*i[t_]+tM*this.s[t_+1],this.s[t_+1]=-tM*i[t_]+tC*this.s[t_+1],tw=tM*i[t_+1],i[t_+1]=tC*i[t_+1],t_<this.m-1)for(var tO=0;tO<this.m;tO++)tI=tC*this.U[tO][t_]+tM*this.U[tO][t_+1],this.U[tO][t_+1]=-tM*this.U[tO][t_]+tC*this.U[tO][t_+1],this.U[tO][t_]=tI}i[x-2]=tL;break;case 4:if(this.s[$]<=0&&(this.s[$]=this.s[$]<0?-this.s[$]:0,1))for(var tD=0;tD<=B;tD++)this.V[tD][$]=-this.V[tD][$];for(;$<B&&!(this.s[$]>=this.s[$+1]);){var tR=this.s[$];if(this.s[$]=this.s[$+1],this.s[$+1]=tR,$<this.n-1)for(var tb=0;tb<this.n;tb++)tR=this.V[tb][$+1],this.V[tb][$+1]=this.V[tb][$],this.V[tb][$]=tR;if($<this.m-1)for(var tG=0;tG<this.m;tG++)tR=this.U[tG][$+1],this.U[tG][$+1]=this.U[tG][$],this.U[tG][$]=tR;$++}x--}}return{U:this.U,V:this.V,S:this.s}},r.hypot=function(t,e){var i=void 0;return Math.abs(t)>Math.abs(e)?(i=e/t,i=Math.abs(t)*Math.sqrt(1+i*i)):0!=e?(i=t/e,i=Math.abs(e)*Math.sqrt(1+i*i)):i=0,i},t.exports=r},function(t,e,i){"use strict";var r=function(){function t(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,i,r){return i&&t(e.prototype,i),r&&t(e,r),e}}();t.exports=function(){function t(e,i){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:-1,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1;if(!(this instanceof t))throw TypeError("Cannot call a class as a function");this.sequence1=e,this.sequence2=i,this.match_score=r,this.mismatch_penalty=n,this.gap_penalty=o,this.iMax=e.length+1,this.jMax=i.length+1,this.grid=Array(this.iMax);for(var a=0;a<this.iMax;a++){this.grid[a]=Array(this.jMax);for(var s=0;s<this.jMax;s++)this.grid[a][s]=0}this.tracebackGrid=Array(this.iMax);for(var h=0;h<this.iMax;h++){this.tracebackGrid[h]=Array(this.jMax);for(var l=0;l<this.jMax;l++)this.tracebackGrid[h][l]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return r(t,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var t=1;t<this.jMax;t++)this.grid[0][t]=this.grid[0][t-1]+this.gap_penalty,this.tracebackGrid[0][t]=[!1,!1,!0];for(var e=1;e<this.iMax;e++)this.grid[e][0]=this.grid[e-1][0]+this.gap_penalty,this.tracebackGrid[e][0]=[!1,!0,!1];for(var i=1;i<this.iMax;i++)for(var r=1;r<this.jMax;r++){var n=void 0,o=[this.sequence1[i-1]===this.sequence2[r-1]?this.grid[i-1][r-1]+this.match_score:this.grid[i-1][r-1]+this.mismatch_penalty,this.grid[i-1][r]+this.gap_penalty,this.grid[i][r-1]+this.gap_penalty],a=this.arrayAllMaxIndexes(o);this.grid[i][r]=o[a[0]],this.tracebackGrid[i][r]=[a.includes(0),a.includes(1),a.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var t=[];for(t.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});t[0];){var e=t[0],i=this.tracebackGrid[e.pos[0]][e.pos[1]];i[0]&&t.push({pos:[e.pos[0]-1,e.pos[1]-1],seq1:this.sequence1[e.pos[0]-1]+e.seq1,seq2:this.sequence2[e.pos[1]-1]+e.seq2}),i[1]&&t.push({pos:[e.pos[0]-1,e.pos[1]],seq1:this.sequence1[e.pos[0]-1]+e.seq1,seq2:"-"+e.seq2}),i[2]&&t.push({pos:[e.pos[0],e.pos[1]-1],seq1:"-"+e.seq1,seq2:this.sequence2[e.pos[1]-1]+e.seq2}),0===e.pos[0]&&0===e.pos[1]&&this.alignments.push({sequence1:e.seq1,sequence2:e.seq2}),t.shift()}return this.alignments}},{key:"getAllIndexes",value:function(t,e){for(var i=[],r=-1;-1!==(r=t.indexOf(e,r+1));)i.push(r);return i}},{key:"arrayAllMaxIndexes",value:function(t){return this.getAllIndexes(t,Math.max.apply(null,t))}}]),t}()},function(t,e,i){"use strict";var r=function(){};r.FDLayout=i(18),r.FDLayoutConstants=i(4),r.FDLayoutEdge=i(19),r.FDLayoutNode=i(20),r.DimensionD=i(21),r.HashMap=i(22),r.HashSet=i(23),r.IGeometry=i(8),r.IMath=i(9),r.Integer=i(10),r.Point=i(12),r.PointD=i(5),r.RandomSeed=i(16),r.RectangleD=i(13),r.Transform=i(17),r.UniqueIDGeneretor=i(14),r.Quicksort=i(25),r.LinkedList=i(11),r.LGraphObject=i(2),r.LGraph=i(6),r.LEdge=i(1),r.LGraphManager=i(7),r.LNode=i(3),r.Layout=i(15),r.LayoutConstants=i(0),r.NeedlemanWunsch=i(27),r.Matrix=i(24),r.SVD=i(26),t.exports=r},function(t,e,i){"use strict";function r(){this.listeners=[]}var n=r.prototype;n.addListener=function(t,e){this.listeners.push({event:t,callback:e})},n.removeListener=function(t,e){for(var i=this.listeners.length;i>=0;i--){var r=this.listeners[i];r.event===t&&r.callback===e&&this.listeners.splice(i,1)}},n.emit=function(t,e){for(var i=0;i<this.listeners.length;i++){var r=this.listeners[i];t===r.event&&r.callback(e)}},t.exports=r}],e={};function i(r){if(e[r])return e[r].exports;var n=e[r]={i:r,l:!1,exports:{}};return t[r].call(n.exports,n,n.exports,i),n.l=!0,n.exports}return i.m=t,i.c=e,i.i=function(t){return t},i.d=function(t,e,r){i.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:r})},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=28)}()},22775:function(t,e,i){"use strict";i.d(e,{diagram:()=>tg});var r=i(35289),n=i(36905),o=i(12820),a=i(72002);i(27066);var s=i(43726),h=i(79486),l=i(85653),d=i(36099),c=i(85858),g=i(91007),u={L:"left",R:"right",T:"top",B:"bottom"},f={L:(0,h.eW)(t=>`${t},${t/2} 0,${t} 0,0`,"L"),R:(0,h.eW)(t=>`0,${t/2} ${t},0 ${t},${t}`,"R"),T:(0,h.eW)(t=>`0,0 ${t},0 ${t/2},${t}`,"T"),B:(0,h.eW)(t=>`${t/2},0 ${t},${t} 0,${t}`,"B")},p={L:(0,h.eW)((t,e)=>t-e+2,"L"),R:(0,h.eW)((t,e)=>t-2,"R"),T:(0,h.eW)((t,e)=>t-e+2,"T"),B:(0,h.eW)((t,e)=>t-2,"B")},v=(0,h.eW)(function(t){return m(t)?"L"===t?"R":"L":"T"===t?"B":"T"},"getOppositeArchitectureDirection"),y=(0,h.eW)(function(t){return"L"===t||"R"===t||"T"===t||"B"===t},"isArchitectureDirection"),m=(0,h.eW)(function(t){return"L"===t||"R"===t},"isArchitectureDirectionX"),E=(0,h.eW)(function(t){return"T"===t||"B"===t},"isArchitectureDirectionY"),N=(0,h.eW)(function(t,e){let i=m(t)&&E(e),r=E(t)&&m(e);return i||r},"isArchitectureDirectionXY"),T=(0,h.eW)(function(t){let e=t[0],i=t[1],r=m(e)&&E(i),n=E(e)&&m(i);return r||n},"isArchitecturePairXY"),A=(0,h.eW)(function(t){return"LL"!==t&&"RR"!==t&&"TT"!==t&&"BB"!==t},"isValidArchitectureDirectionPair"),L=(0,h.eW)(function(t,e){let i=`${t}${e}`;return A(i)?i:void 0},"getArchitectureDirectionPair"),w=(0,h.eW)(function([t,e],i){let r=i[0],n=i[1];if(m(r))if(E(n))return[t+("L"===r?-1:1),e+("T"===n?1:-1)];else return[t+("L"===r?-1:1),e];return m(n)?[t+("L"===n?1:-1),e+("T"===r?1:-1)]:[t,e+("T"===r?1:-1)]},"shiftPositionByArchitectureDirectionPair"),_=(0,h.eW)(function(t){return"LT"===t||"TL"===t?[1,1]:"BL"===t||"LB"===t?[1,-1]:"BR"===t||"RB"===t?[-1,-1]:[-1,1]},"getArchitectureDirectionXYFactors"),I=(0,h.eW)(function(t,e){return N(t,e)?"bend":m(t)?"horizontal":"vertical"},"getArchitectureDirectionAlignment"),C=(0,h.eW)(function(t){return"service"===t.type},"isArchitectureService"),M=(0,h.eW)(function(t){return"junction"===t.type},"isArchitectureJunction"),x=(0,h.eW)(t=>t.data(),"edgeData"),O=(0,h.eW)(t=>t.data(),"nodeData"),D=h.vZ.architecture,R=new a.A(()=>({nodes:{},groups:{},edges:[],registeredIds:{},config:D,dataStructures:void 0,elements:{}})),b=(0,h.eW)(()=>{R.reset(),(0,h.ZH)()},"clear"),G=(0,h.eW)(function({id:t,icon:e,in:i,title:r,iconText:n}){if(void 0!==R.records.registeredIds[t])throw Error(`The service id [${t}] is already in use by another ${R.records.registeredIds[t]}`);if(void 0!==i){if(t===i)throw Error(`The service [${t}] cannot be placed within itself`);if(void 0===R.records.registeredIds[i])throw Error(`The service [${t}]'s parent does not exist. Please make sure the parent is created before this service`);if("node"===R.records.registeredIds[i])throw Error(`The service [${t}]'s parent is not a group`)}R.records.registeredIds[t]="node",R.records.nodes[t]={id:t,type:"service",icon:e,iconText:n,title:r,edges:[],in:i}},"addService"),F=(0,h.eW)(()=>Object.values(R.records.nodes).filter(C),"getServices"),S=(0,h.eW)(function({id:t,in:e}){R.records.registeredIds[t]="node",R.records.nodes[t]={id:t,type:"junction",edges:[],in:e}},"addJunction"),P=(0,h.eW)(()=>Object.values(R.records.nodes).filter(M),"getJunctions"),U=(0,h.eW)(()=>Object.values(R.records.nodes),"getNodes"),Y=(0,h.eW)(t=>R.records.nodes[t],"getNode"),k=(0,h.eW)(function({id:t,icon:e,in:i,title:r}){if(void 0!==R.records.registeredIds[t])throw Error(`The group id [${t}] is already in use by another ${R.records.registeredIds[t]}`);if(void 0!==i){if(t===i)throw Error(`The group [${t}] cannot be placed within itself`);if(void 0===R.records.registeredIds[i])throw Error(`The group [${t}]'s parent does not exist. Please make sure the parent is created before this group`);if("node"===R.records.registeredIds[i])throw Error(`The group [${t}]'s parent is not a group`)}R.records.registeredIds[t]="group",R.records.groups[t]={id:t,icon:e,title:r,in:i}},"addGroup"),H=(0,h.eW)(()=>Object.values(R.records.groups),"getGroups"),X=(0,h.eW)(function({lhsId:t,rhsId:e,lhsDir:i,rhsDir:r,lhsInto:n,rhsInto:o,lhsGroup:a,rhsGroup:s,title:h}){if(!y(i))throw Error(`Invalid direction given for left hand side of edge ${t}--${e}. Expected (L,R,T,B) got ${i}`);if(!y(r))throw Error(`Invalid direction given for right hand side of edge ${t}--${e}. Expected (L,R,T,B) got ${r}`);if(void 0===R.records.nodes[t]&&void 0===R.records.groups[t])throw Error(`The left-hand id [${t}] does not yet exist. Please create the service/group before declaring an edge to it.`);if(void 0===R.records.nodes[e]&&void 0===R.records.groups[t])throw Error(`The right-hand id [${e}] does not yet exist. Please create the service/group before declaring an edge to it.`);let l=R.records.nodes[t].in,d=R.records.nodes[e].in;if(a&&l&&d&&l==d)throw Error(`The left-hand id [${t}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);if(s&&l&&d&&l==d)throw Error(`The right-hand id [${e}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);R.records.edges.push({lhsId:t,lhsDir:i,lhsInto:n,lhsGroup:a,rhsId:e,rhsDir:r,rhsInto:o,rhsGroup:s,title:h}),R.records.nodes[t]&&R.records.nodes[e]&&(R.records.nodes[t].edges.push(R.records.edges[R.records.edges.length-1]),R.records.nodes[e].edges.push(R.records.edges[R.records.edges.length-1]))},"addEdge"),W=(0,h.eW)(()=>R.records.edges,"getEdges"),z=(0,h.eW)(()=>{if(void 0===R.records.dataStructures){let t={},e=Object.entries(R.records.nodes).reduce((e,[i,r])=>(e[i]=r.edges.reduce((e,r)=>{let n=Y(r.lhsId)?.in,o=Y(r.rhsId)?.in;if(n&&o&&n!==o){let e=I(r.lhsDir,r.rhsDir);"bend"!==e&&(t[n]??={},t[n][o]=e,t[o]??={},t[o][n]=e)}if(r.lhsId===i){let t=L(r.lhsDir,r.rhsDir);t&&(e[t]=r.rhsId)}else{let t=L(r.rhsDir,r.lhsDir);t&&(e[t]=r.lhsId)}return e},{}),e),{}),i=Object.keys(e)[0],r={[i]:1},n=Object.keys(e).reduce((t,e)=>e===i?t:{...t,[e]:1},{}),o=(0,h.eW)(t=>{let i={[t]:[0,0]},o=[t];for(;o.length>0;){let t=o.shift();if(t){r[t]=1,delete n[t];let a=e[t],[s,h]=i[t];Object.entries(a).forEach(([t,e])=>{r[e]||(i[e]=w([s,h],t),o.push(e))})}}return i},"BFS"),a=[o(i)];for(;Object.keys(n).length>0;)a.push(o(Object.keys(n)[0]));R.records.dataStructures={adjList:e,spatialMaps:a,groupAlignments:t}}return R.records.dataStructures},"getDataStructures"),V=(0,h.eW)((t,e)=>{R.records.elements[t]=e},"setElementForId"),B=(0,h.eW)(t=>R.records.elements[t],"getElementById"),j={clear:b,setDiagramTitle:h.g2,getDiagramTitle:h.Kr,setAccTitle:h.GN,getAccTitle:h.eu,setAccDescription:h.U$,getAccDescription:h.Mx,addService:G,getServices:F,addJunction:S,getJunctions:P,getNodes:U,getNode:Y,addGroup:k,getGroups:H,addEdge:X,getEdges:W,setElementForId:V,getElementById:B,getDataStructures:z};function $(t){let e=(0,h.nV)().architecture;return e?.[t]?e[t]:D[t]}(0,h.eW)($,"getConfigField");var q=(0,h.eW)((t,e)=>{(0,o.A)(t,e),t.groups.map(e.addGroup),t.services.map(t=>e.addService({...t,type:"service"})),t.junctions.map(t=>e.addJunction({...t,type:"junction"})),t.edges.map(e.addEdge)},"populateDb"),Z={parse:(0,h.eW)(async t=>{let e=await (0,l.Qc)("architecture",t);h.cM.debug(e),q(e,j)},"parse")},Q=(0,h.eW)(t=>`
  .edge {
    stroke-width: ${t.archEdgeWidth};
    stroke: ${t.archEdgeColor};
    fill: none;
  }

  .arrow {
    fill: ${t.archEdgeArrowColor};
  }

  .node-bkg {
    fill: none;
    stroke: ${t.archGroupBorderColor};
    stroke-width: ${t.archGroupBorderWidth};
    stroke-dasharray: 8;
  }
  .node-icon-text {
    display: flex; 
    align-items: center;
  }
  
  .node-icon-text > div {
    color: #fff;
    margin: 1px;
    height: fit-content;
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
`,"getStyles"),J=(0,h.eW)(t=>`<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/>${t}</g>`,"wrapIcon"),K={prefix:"mermaid-architecture",height:80,width:80,icons:{database:{body:J('<path id="b" data-name="4" d="m20,57.86c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="c" data-name="3" d="m20,45.95c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="d" data-name="2" d="m20,34.05c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse id="e" data-name="1" cx="40" cy="22.14" rx="20" ry="7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="20" y1="57.86" x2="20" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="60" y1="57.86" x2="60" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},server:{body:J('<rect x="17.5" y="17.5" width="45" height="45" rx="2" ry="2" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="32.5" x2="62.5" y2="32.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="47.5" x2="62.5" y2="47.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><g><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g>')},disk:{body:J('<rect x="20" y="15" width="40" height="50" rx="1" ry="1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="14" ry="14.58" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="4" ry="4.17" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m37.51,42.52l-4.83,13.22c-.26.71-1.1,1.02-1.76.64l-4.18-2.42c-.66-.38-.81-1.26-.33-1.84l9.01-10.8c.88-1.05,2.56-.08,2.09,1.2Z" style="fill: #fff; stroke-width: 0px;"/>')},internet:{body:J('<circle cx="40" cy="40" r="22.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="40" y1="17.5" x2="40" y2="62.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="40" x2="62.5" y2="40" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m39.99,17.51c-15.28,11.1-15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m40.01,17.51c15.28,11.1,15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="30.1" x2="60.25" y2="30.1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="49.9" x2="60.25" y2="49.9" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},cloud:{body:J('<path d="m65,47.5c0,2.76-2.24,5-5,5H20c-2.76,0-5-2.24-5-5,0-1.87,1.03-3.51,2.56-4.36-.04-.21-.06-.42-.06-.64,0-2.6,2.48-4.74,5.65-4.97,1.65-4.51,6.34-7.76,11.85-7.76.86,0,1.69.08,2.5.23,2.09-1.57,4.69-2.5,7.5-2.5,6.1,0,11.19,4.38,12.28,10.17,2.14.56,3.72,2.51,3.72,4.83,0,.03,0,.07-.01.1,2.29.46,4.01,2.48,4.01,4.9Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},unknown:r.cN,blank:{body:J("")}}},tt=(0,h.eW)(async function(t,e){let i=$("padding"),r=$("iconSize"),o=r/2,a=r/6,s=a/2;await Promise.all(e.edges().map(async e=>{let{source:r,sourceDir:l,sourceArrow:d,sourceGroup:c,target:g,targetDir:u,targetArrow:v,targetGroup:y,label:A}=x(e),{x:w,y:I}=e[0].sourceEndpoint(),{x:C,y:M}=e[0].midpoint(),{x:O,y:D}=e[0].targetEndpoint(),R=i+4;if(c&&(m(l)?w+="L"===l?-R:R:I+="T"===l?-R:R+18),y&&(m(u)?O+="L"===u?-R:R:D+="T"===u?-R:R+18),c||j.getNode(r)?.type!=="junction"||(m(l)?w+="L"===l?o:-o:I+="T"===l?o:-o),y||j.getNode(g)?.type!=="junction"||(m(u)?O+="L"===u?o:-o:D+="T"===u?o:-o),e[0]._private.rscratch){let e=t.insert("g");if(e.insert("path").attr("d",`M ${w},${I} L ${C},${M} L${O},${D} `).attr("class","edge"),d){let t=m(l)?p[l](w,a):w-s,i=E(l)?p[l](I,a):I-s;e.insert("polygon").attr("points",f[l](a)).attr("transform",`translate(${t},${i})`).attr("class","arrow")}if(v){let t=m(u)?p[u](O,a):O-s,i=E(u)?p[u](D,a):D-s;e.insert("polygon").attr("points",f[u](a)).attr("transform",`translate(${t},${i})`).attr("class","arrow")}if(A){let t=N(l,u)?"XY":m(l)?"X":"Y",i=0;i="X"===t?Math.abs(w-O):"Y"===t?Math.abs(I-D)/1.5:Math.abs(w-O)/2;let r=e.append("g");if(await (0,n.rw)(r,A,{useHtmlLabels:!1,width:i,classes:"architecture-service-label"},(0,h.nV)()),r.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),"X"===t)r.attr("transform","translate("+C+", "+M+")");else if("Y"===t)r.attr("transform","translate("+C+", "+M+") rotate(-90)");else if("XY"===t){let t=L(l,u);if(t&&T(t)){let e=r.node().getBoundingClientRect(),[i,n]=_(t);r.attr("dominant-baseline","auto").attr("transform",`rotate(${-1*i*n*45})`);let o=r.node().getBoundingClientRect();r.attr("transform",`
                translate(${C}, ${M-e.height/2})
                translate(${i*o.width/2}, ${n*o.height/2})
                rotate(${-1*i*n*45}, 0, ${e.height/2})
              `)}}}}}))},"drawEdges"),te=(0,h.eW)(async function(t,e){let i=.75*$("padding"),o=$("fontSize"),a=$("iconSize")/2;await Promise.all(e.nodes().map(async e=>{let s=O(e);if("group"===s.type){let{h:l,w:d,x1:c,y1:g}=e.boundingBox();t.append("rect").attr("x",c+a).attr("y",g+a).attr("width",d).attr("height",l).attr("class","node-bkg");let u=t.append("g"),f=c,p=g;if(s.icon){let t=u.append("g");t.html(`<g>${await (0,r.s4)(s.icon,{height:i,width:i,fallbackPrefix:K.prefix})}</g>`),t.attr("transform","translate("+(f+a+1)+", "+(p+a+1)+")"),f+=i,p+=o/2-1-2}if(s.label){let t=u.append("g");await (0,n.rw)(t,s.label,{useHtmlLabels:!1,width:d,classes:"architecture-service-label"},(0,h.nV)()),t.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","start").attr("text-anchor","start"),t.attr("transform","translate("+(f+a+4)+", "+(p+a+2)+")")}}}))},"drawGroups"),ti=(0,h.eW)(async function(t,e,i){for(let o of i){let i=e.append("g"),a=$("iconSize");if(o.title){let t=i.append("g");await (0,n.rw)(t,o.title,{useHtmlLabels:!1,width:1.5*a,classes:"architecture-service-label"},(0,h.nV)()),t.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),t.attr("transform","translate("+a/2+", "+a+")")}let s=i.append("g");if(o.icon)s.html(`<g>${await (0,r.s4)(o.icon,{height:a,width:a,fallbackPrefix:K.prefix})}</g>`);else if(o.iconText){s.html(`<g>${await (0,r.s4)("blank",{height:a,width:a,fallbackPrefix:K.prefix})}</g>`);let t=s.append("g").append("foreignObject").attr("width",a).attr("height",a).append("div").attr("class","node-icon-text").attr("style",`height: ${a}px;`).append("div").html(o.iconText),e=parseInt(window.getComputedStyle(t.node(),null).getPropertyValue("font-size").replace(/\D/g,""))??16;t.attr("style",`-webkit-line-clamp: ${Math.floor((a-2)/e)};`)}else s.append("path").attr("class","node-bkg").attr("id","node-"+o.id).attr("d",`M0 ${a} v${-a} q0,-5 5,-5 h${a} q5,0 5,5 v${a} H0 Z`);i.attr("class","architecture-service");let{width:l,height:d}=i._groups[0][0].getBBox();o.width=l,o.height=d,t.setElementForId(o.id,i)}return 0},"drawServices"),tr=(0,h.eW)(function(t,e,i){i.forEach(i=>{let r=e.append("g"),n=$("iconSize");r.append("g").append("rect").attr("id","node-"+i.id).attr("fill-opacity","0").attr("width",n).attr("height",n),r.attr("class","architecture-junction");let{width:o,height:a}=r._groups[0][0].getBBox();r.width=o,r.height=a,t.setElementForId(i.id,r)})},"drawJunctions");function tn(t,e){t.forEach(t=>{e.add({group:"nodes",data:{type:"service",id:t.id,icon:t.icon,label:t.title,parent:t.in,width:$("iconSize"),height:$("iconSize")},classes:"node-service"})})}function to(t,e){t.forEach(t=>{e.add({group:"nodes",data:{type:"junction",id:t.id,parent:t.in,width:$("iconSize"),height:$("iconSize")},classes:"node-junction"})})}function ta(t,e){e.nodes().map(e=>{let i=O(e);"group"!==i.type&&(i.x=e.position().x,i.y=e.position().y,t.getElementById(i.id).attr("transform","translate("+(i.x||0)+","+(i.y||0)+")"))})}function ts(t,e){t.forEach(t=>{e.add({group:"nodes",data:{type:"group",id:t.id,icon:t.icon,label:t.title,parent:t.in},classes:"node-group"})})}function th(t,e){t.forEach(t=>{let{lhsId:i,rhsId:r,lhsInto:n,lhsGroup:o,rhsInto:a,lhsDir:s,rhsDir:h,rhsGroup:l,title:d}=t,c=N(t.lhsDir,t.rhsDir)?"segments":"straight",g={id:`${i}-${r}`,label:d,source:i,sourceDir:s,sourceArrow:n,sourceGroup:o,sourceEndpoint:"L"===s?"0 50%":"R"===s?"100% 50%":"T"===s?"50% 0":"50% 100%",target:r,targetDir:h,targetArrow:a,targetGroup:l,targetEndpoint:"L"===h?"0 50%":"R"===h?"100% 50%":"T"===h?"50% 0":"50% 100%"};e.add({group:"edges",data:g,classes:c})})}function tl(t,e,i){let r=(0,h.eW)((t,e)=>Object.entries(t).reduce((t,[r,n])=>{let o=0,a=Object.entries(n);if(1===a.length)return t[r]=a[0][1],t;for(let n=0;n<a.length-1;n++)for(let s=n+1;s<a.length;s++){let[h,l]=a[n],[d,c]=a[s];i[h]?.[d]===e||"default"===h||"default"===d?(t[r]??=[],t[r]=[...t[r],...l,...c]):(t[`${r}-${o++}`]=l,t[`${r}-${o++}`]=c)}return t},{}),"flattenAlignments"),[n,o]=e.map(e=>{let i={},n={};return Object.entries(e).forEach(([e,[r,o]])=>{let a=t.getNode(e)?.in??"default";i[o]??={},i[o][a]??=[],i[o][a].push(e),n[r]??={},n[r][a]??=[],n[r][a].push(e)}),{horiz:Object.values(r(i,"horizontal")).filter(t=>t.length>1),vert:Object.values(r(n,"vertical")).filter(t=>t.length>1)}}).reduce(([t,e],{horiz:i,vert:r})=>[[...t,...i],[...e,...r]],[[],[]]);return{horizontal:n,vertical:o}}function td(t){let e=[],i=(0,h.eW)(t=>`${t[0]},${t[1]}`,"posToStr"),r=(0,h.eW)(t=>t.split(",").map(t=>parseInt(t)),"strToPos");return t.forEach(t=>{let n=Object.fromEntries(Object.entries(t).map(([t,e])=>[i(e),t])),o=[i([0,0])],a={},s={L:[-1,0],R:[1,0],T:[0,1],B:[0,-1]};for(;o.length>0;){let t=o.shift();if(t){a[t]=1;let h=n[t];if(h){let l=r(t);Object.entries(s).forEach(([t,r])=>{let s=i([l[0]+r[0],l[1]+r[1]]),d=n[s];d&&!a[s]&&(o.push(s),e.push({[u[t]]:d,[u[v(t)]]:h,gap:1.5*$("iconSize")}))})}}}}),e}function tc(t,e,i,r,n,{spatialMaps:o,groupAlignments:a}){return new Promise(s=>{let l=(0,g.Ys)("body").append("div").attr("id","cy").attr("style","display:none"),c=(0,d.Z)({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"straight",label:"data(label)","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"edge.segments",style:{"curve-style":"segments","segment-weights":"0","segment-distances":[.5],"edge-distances":"endpoints","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"node",style:{"compound-sizing-wrt-labels":"include"}},{selector:"node[label]",style:{"text-valign":"bottom","text-halign":"center","font-size":`${$("fontSize")}px`}},{selector:".node-service",style:{label:"data(label)",width:"data(width)",height:"data(height)"}},{selector:".node-junction",style:{width:"data(width)",height:"data(height)"}},{selector:".node-group",style:{padding:`${$("padding")}px`}}]});l.remove(),ts(i,c),tn(t,c),to(e,c),th(r,c);let u=tl(n,o,a),f=td(o),p=c.layout({name:"fcose",quality:"proof",styleEnabled:!1,animate:!1,nodeDimensionsIncludeLabels:!1,idealEdgeLength(t){let[e,i]=t.connectedNodes(),{parent:r}=O(e),{parent:n}=O(i);return r===n?1.5*$("iconSize"):.5*$("iconSize")},edgeElasticity(t){let[e,i]=t.connectedNodes(),{parent:r}=O(e),{parent:n}=O(i);return r===n?.45:.001},alignmentConstraint:u,relativePlacementConstraint:f});p.one("layoutstop",()=>{function t(t,e,i,r){let n,o,{x:a,y:s}=t,{x:h,y:l}=e;o=(r-s+(a-i)*(s-l)/(a-h))/Math.sqrt(1+Math.pow((s-l)/(a-h),2)),n=Math.sqrt(Math.pow(r-s,2)+Math.pow(i-a,2)-Math.pow(o,2))/Math.sqrt(Math.pow(h-a,2)+Math.pow(l-s,2));let d=(h-a)*(r-s)-(l-s)*(i-a);switch(!0){case d>=0:d=1;break;case d<0:d=-1}let c=(h-a)*(i-a)+(l-s)*(r-s);switch(!0){case c>=0:c=1;break;case c<0:c=-1}return{distances:o=Math.abs(o)*d,weights:n*=c}}for(let e of((0,h.eW)(t,"getSegmentWeights"),c.startBatch(),Object.values(c.edges())))if(e.data?.()){let{x:i,y:r}=e.source().position(),{x:n,y:o}=e.target().position();if(i!==n&&r!==o){let i=e.sourceEndpoint(),r=e.targetEndpoint(),{sourceDir:n}=x(e),[o,a]=E(n)?[i.x,r.y]:[r.x,i.y],{weights:s,distances:h}=t(i,r,o,a);e.style("segment-distances",h),e.style("segment-weights",s)}}c.endBatch(),p.run()}),p.run(),c.ready(t=>{h.cM.info("Ready",t),s(c)})})}(0,r.ef)([{name:K.prefix,icons:K}]),d.Z.use(c),(0,h.eW)(tn,"addServices"),(0,h.eW)(to,"addJunctions"),(0,h.eW)(ta,"positionNodes"),(0,h.eW)(ts,"addGroups"),(0,h.eW)(th,"addEdges"),(0,h.eW)(tl,"getAlignments"),(0,h.eW)(td,"getRelativeConstraints"),(0,h.eW)(tc,"layoutArchitecture");var tg={parser:Z,db:j,renderer:{draw:(0,h.eW)(async(t,e,i,r)=>{let n=r.db,o=n.getServices(),a=n.getJunctions(),l=n.getGroups(),d=n.getEdges(),c=n.getDataStructures(),g=(0,s.P)(e),u=g.append("g");u.attr("class","architecture-edges");let f=g.append("g");f.attr("class","architecture-services");let p=g.append("g");p.attr("class","architecture-groups"),await ti(n,f,o),tr(n,f,a);let v=await tc(o,a,l,d,n,c);await tt(u,v),await te(p,v),ta(n,v),(0,h.j7)(void 0,g,$("padding"),$("useMaxWidth"))},"draw")},styles:Q}},12820:function(t,e,i){"use strict";function r(t,e){t.accDescr&&e.setAccDescription?.(t.accDescr),t.accTitle&&e.setAccTitle?.(t.accTitle),t.title&&e.setDiagramTitle?.(t.title)}i.d(e,{A:()=>r}),(0,i(79486).eW)(r,"populateCommonDb")},72002:function(t,e,i){"use strict";i.d(e,{A:()=>n});var r=i(79486),n=class{constructor(t){this.init=t,this.records=this.init()}static{(0,r.eW)(this,"ImperativeState")}reset(){this.records=this.init()}}}}]);