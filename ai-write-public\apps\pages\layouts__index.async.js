"use strict";var G=Object.defineProperty;var s=Object.getOwnPropertySymbols;var p=Object.prototype.hasOwnProperty,Z=Object.prototype.propertyIsEnumerable;var j=(e,n,t)=>n in e?G(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,m=(e,n)=>{for(var t in n||(n={}))p.call(n,t)&&j(e,t,n[t]);if(s)for(var t of s(n))Z.call(n,t)&&j(e,t,n[t]);return e};var C=(e,n)=>{var t={};for(var o in e)p.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&s)for(var o of s(e))n.indexOf(o)<0&&Z.call(e,o)&&(t[o]=e[o]);return t};(self.webpackChunk=self.webpackChunk||[]).push([[717],{70077:function(e,n,t){t.r(n),t.d(n,{default:function(){return z}});var o=t(76492),T=t(85965),E=t(2053),M=t(52460),r=t(75271),X=t(10437),y=t(62900),L=A=>{const g=A,{attachments:a,bubble:d,conversations:u,prompts:v,sender:h,suggestion:i,thoughtChain:l,welcome:f,theme:x}=g,B=C(g,["attachments","bubble","conversations","prompts","sender","suggestion","thoughtChain","welcome","theme"]),{theme:P}=(0,y.Z)(),D=r.useMemo(()=>({attachments:a,bubble:d,conversations:u,prompts:v,sender:h,suggestion:i,thoughtChain:l,welcome:f}),[a,d,u,v,h,i,l,f]),F=r.useMemo(()=>m(m({},P),x),[P,x]);return r.createElement(X.Z.Provider,{value:D},r.createElement(M.ZP,(0,E.Z)({},B,{theme:F})))},c=t(52676);function z(){return(0,c.jsx)(T.Z,{children:(0,c.jsx)(L,{children:(0,c.jsx)(o.j3,{})})})}}}]);
