"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[882],{1095:function(bt,J,t){t.r(J),t.d(J,{default:function(){return st}});var ye=t(26068),K=t.n(ye),je=t(90228),A=t.n(je),Ce=t(335),xe=t.n(Ce),Se=t(87999),Q=t.n(Se),Ze=t(48305),m=t.n(Ze),Ae=t(33950),h=t(75271),Oe=t(44938),Be=t(27425),be=t(35493),Ee=t(35324),De=t(85666),Fe=t(77737),Te=t(85965),Pe=t(71415),y=t(49367),V=t(49801),I=t(66764),R=t(60969),w=t(84447),ke=t(26470),O=t(10880),Ie=t(46632),q=t(1376),Re=t(42930),_=t(40038),Me=t(72966),ee=t(40154),Le=t(41469),M={list:"list___MEVXW",item:"item___oea05",chat:"chat___btcq6",shake:"shake___zq5HW"},te=t(16293),$e=t(17727),e=t(52676),Ne=function(s){var g=s.dataSource,j=s.onCopy,d=s.onChat,c=s.onItemClick;return(0,e.jsx)(te.Z,{className:M.list,grid:{gutter:16,column:4,xs:1,sm:2,md:3,lg:4,xl:4,xxl:4},dataSource:g,renderItem:function(u){return(0,e.jsx)(te.Z.Item,{className:M.item,children:(0,e.jsx)($e.Z,{title:(0,e.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,e.jsx)("span",{children:u.act}),(0,e.jsxs)("span",{children:[(0,e.jsx)(y.ZP,{className:"copy",type:"text",icon:(0,e.jsx)(q.Z,{}),onClick:function(C){j(u.prompt),C.stopPropagation()}}),(0,e.jsx)(y.ZP,{className:M.chat,type:"text",icon:(0,e.jsx)(_.Z,{}),onClick:function(C){d(u.act,u.prompt),C.stopPropagation()}})]})]}),onClick:function(){return c(u.act,u.prompt)},style:{cursor:"pointer",marginBottom:16},children:(0,e.jsx)("div",{style:{display:"-webkit-box",WebkitBoxOrient:"vertical",WebkitLineClamp:3,overflow:"hidden",textOverflow:"ellipsis",lineHeight:"1.5"},children:u.prompt})})})}})},He=t(61308),We=t(41427),Xe=t(68372),Ge=t(11826),Ue=t(1371),ze=t(40173),Ye=t(55287),Je=t(29646),Ke=t(74324),Qe=t(47936),Ve=t(13197),we=t(49030),qe=t(62018),_e="https://ai.medsci.cn/dev-api/ai-base/yps-chat/completion-messages",Et="app-HwXrBLYFYEnpX5M4nTmTf7DC",Dt=function(){return new Promise(function(s){return setTimeout(s,5e3)})},ae=function(s,g){return Math.floor(Math.random()*(g-s+1))+s},et=function(){for(var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:6,g=1,j=3e3,d=[],c=0;c<s;c++){var B=ae(g,j);d.push(B)}return d},Ft=[{label:"Write a report",value:"report"},{label:"\u589E\u52A0\u865A\u62DF\u4E13\u5BB6\u8BC4\u5BA1",value:"review"},{label:"Check some knowledge",value:"knowledge",icon:(0,e.jsx)(Xe.Z,{}),children:[{label:"About React",value:"react"},{label:"About Ant Design",value:"antd"}]}],tt={ai:{placement:"start",avatar:{icon:(0,e.jsx)(Ge.Z,{}),style:{background:"#427da1"}},typing:{step:5,interval:100,suffix:(0,e.jsx)(e.Fragment,{children:" \u270D\uFE0F"})},style:{maxWidth:600},loadingRender:function(){return(0,e.jsxs)(He.Z,{children:[(0,e.jsx)(We.Z,{size:"small"})," \u6B63\u5728\u547C\u53EB\u5C0F\u667A, \u8BF7\u7A0D\u7B49..."]})}},user:{placement:"end",avatar:{icon:(0,e.jsx)(Ue.Z,{}),style:{background:"#87d068"}}}},ne=[{key:"0",label:"\u5168\u90E8",icon:(0,e.jsx)(ze.Z,{}),children:"loading"},{key:"1",label:"\u6559\u80B2\u5DE5\u4F5C\u8005",icon:(0,e.jsx)(Ye.Z,{}),children:"loading"},{key:"2",label:"\u79D1\u6280\u4E0E\u7F16\u7A0B",icon:(0,e.jsx)(Je.Z,{}),children:"loading"},{key:"3",label:"\u8BED\u8A00\u4E0E\u7FFB\u8BD1",icon:(0,e.jsx)(Ke.Z,{}),children:"loading"},{key:"4",label:"\u521B\u610F\u4E0E\u827A\u672F",icon:(0,e.jsx)(Qe.Z,{}),children:"loading"},{key:"5",label:"\u5546\u4E1A\u4E0E\u804C\u4E1A",icon:(0,e.jsx)(Ve.Z,{}),children:"loading"},{key:"6",label:"\u5176\u4ED6",icon:(0,e.jsx)(we.Z,{}),children:"loading"}],at=function(s){return(0,e.jsx)(qe.U,{children:s})},nt=Fe.Z.Search,st=function(){var f=Te.Z.useApp(),s=f.message,g=h.useState(""),j=m()(g,2),d=j[0],c=j[1],B=h.useState(""),u=m()(B,2),x=u[0],C=u[1],rt=h.useState(!1),se=m()(rt,2),ot=se[0],b=se[1],lt=(0,h.useState)([]),re=m()(lt,2),it=re[0],oe=re[1],ut=(0,h.useState)(!1),le=m()(ut,2),dt=le[0],S=le[1],ct=(0,h.useState)(!1),ie=m()(ct,2),vt=ie[0],L=ie[1],mt=(0,h.useState)("\u5168\u90E8"),ue=m()(mt,2),ht=ue[0],pt=ue[1],ft=(0,Oe.Z)({request:function(){var n=Q()(A()().mark(function o(i,p){var v,H,l,E,W,X,D,G,me,F,T,U,he,pe,fe,z,Z,ge,P,Y,k;return A()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(v=i.message,H=p.onUpdate,l=p.onSuccess,E=p.onError,W=localStorage.getItem("yudaoToken"),W){a.next=7;break}return console.error("// TODO \u65E0token, \u8BF7\u5148\u5728\u9996\u9875\u5B8C\u6210\u767B\u5F55, \u6B64\u4EA4\u4E92\u540E\u7EED\u4F1A\u4F18\u5316."),E(Error("// TODO \u65E0token, \u8BF7\u5148\u5728\u9996\u9875\u5B8C\u6210\u767B\u5F55, \u6B64\u4EA4\u4E92\u540E\u7EED\u4F1A\u4F18\u5316.")),a.abrupt("return");case 7:return a.prev=7,b(!0),a.next=11,fetch(_e,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(W)},body:JSON.stringify({inputs:{query:v},response_mode:"streaming",user:"maschen",appId:"e69add1c-c6a1-409d-81ce-eeff39f18196"})});case 11:D=a.sent,D.ok||E(Error("HTTP \u9519\u8BEF! \u72B6\u6001\u7801: ".concat(D.status))),G=(X=D.body)===null||X===void 0?void 0:X.getReader(),me=new TextDecoder("utf-8"),F="",T="";case 17:if(!G){a.next=50;break}return a.next=20,G.read();case 20:if(U=a.sent,he=U.done,pe=U.value,!he){a.next=26;break}return b(!1),a.abrupt("break",50);case 26:fe=me.decode(pe,{stream:!0}),T+=fe,z=T.split(`
`),T=z.pop()||"",Z=xe()(z),a.prev=31,Z.s();case 33:if((ge=Z.n()).done){a.next=40;break}if(P=ge.value,P.trim()!==""){a.next=37;break}return a.abrupt("continue",38);case 37:if(P.startsWith("data:"))try{Y=P.slice(5).trim(),Y&&(k=JSON.parse(Y),k.event==="message"?(F+=k.answer,H(F)):k.event==="message_end"&&l(F))}catch(Bt){console.error("\u89E3\u6790 JSON \u65F6\u53D1\u751F\u9519\u8BEF:",Bt)}case 38:a.next=33;break;case 40:a.next=45;break;case 42:a.prev=42,a.t0=a.catch(31),Z.e(a.t0);case 45:return a.prev=45,Z.f(),a.finish(45);case 48:a.next=17;break;case 50:a.next=56;break;case 52:a.prev=52,a.t1=a.catch(7),console.error("\u8C03\u7528 Dify API \u5931\u8D25:",a.t1),E(Error("\u83B7\u53D6 AI \u54CD\u5E94\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5\uFF01"));case 56:return a.prev=56,b(!1),a.finish(56);case 59:case"end":return a.stop()}},o,null,[[7,52,56,59],[31,42,45,48]])}));function r(o,i){return n.apply(this,arguments)}return r}()}),gt=m()(ft,1),yt=gt[0],$=(0,Be.Z)({agent:yt,requestPlaceholder:"\u{1F517} \u6B63\u5728\u94FE\u63A5\u5C0F\u667A\u8BF7\u7B49\u5F85...",requestFallback:"\u{1F6A7} \u5F53\u524D\u7F51\u7EDC\u7E41\u5FD9, \u8BF7\u7A0D\u540E\u518D\u8BD5~"}),jt=$.onRequest,de=$.messages,Ct=$.setMessages,ce=function(){navigator.clipboard.writeText(d).then(function(){s.success("\u5DF2\u590D\u5236\u5230\u7C98\u8D34\u677F!")}).catch(function(){s.error("\u590D\u5236\u5931\u8D25!")})},ve=function(r,o){L(!0),S(!1),C(r),c(o),de.length===0&&Ct([{id:"hello",message:"\u{1F44B}\u{1F604}\u4F60\u597D\uFF0C\u6211\u662F\u5C0F\u667A\uFF0C\u6709\u4EC0\u4E48\u53EF\u4EE5\u5E2E\u52A9\u4F60~",status:"success"}])},xt=function(r,o){C(r),c(o),S(!0)},N=function(){var n=Q()(A()().mark(function r(){var o,i,p,v=arguments;return A()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return o=v.length>0&&v[0]!==void 0?v[0]:"",i=v.length>1&&v[1]!==void 0?v[1]:"",l.prev=2,l.next=5,Ae.Z.get("https://ai.medsci.cn/openapi/prompt?category=".concat(o,"&keyword=").concat(i));case 5:p=l.sent,oe(p.data.results),l.next=13;break;case 9:l.prev=9,l.t0=l.catch(2),s.error("Failed to fetch prompt items: ".concat(l.t0)),oe([]);case 13:case"end":return l.stop()}},r,null,[[2,9]])}));return function(){return n.apply(this,arguments)}}(),St=function(r){var o,i=((o=ne.find(function(p){return p.key===r}))===null||o===void 0?void 0:o.label)||"";pt(i),N(i)},Zt=function(){s.info("//TODO \u529F\u80FD\u5F00\u53D1\u4E2D\u54E6")},At=ne.map(function(n){return K()(K()({},n),{},{children:(0,e.jsx)(Ne,{dataSource:it,onCopy:ce,onChat:ve,onItemClick:xt})})}),Ot=(0,e.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,e.jsxs)("span",{children:[x,(0,e.jsx)(Pe.Z,{icon:(0,e.jsx)(Ie.Z,{}),color:"#55acee",style:{marginLeft:10},children:" by GitHub "})]}),(0,e.jsxs)("span",{children:[(0,e.jsx)(y.ZP,{type:"text",icon:(0,e.jsx)(q.Z,{}),onClick:ce}),(0,e.jsx)(y.ZP,{type:"text",icon:(0,e.jsx)(Re.Z,{}),onClick:function(){return S(!1)}})]})]});return(0,h.useEffect)(function(){N()},[]),(0,e.jsxs)(e.Fragment,{children:[(0,e.jsxs)(V.Z,{title:"\u4E0E\u5C0F\u667A\u5BF9\u8BDD",width:800,open:vt,onCancel:function(){return L(!1)},onOk:function(){return L(!1)},footer:null,children:[(0,e.jsx)(be.Z.List,{style:{flex:1,maxHeight:"500px",overflowY:"auto"},roles:tt,items:de.map(function(n){var r=n.id,o=n.message,i=n.status;return{key:r,role:i==="local"?"user":"ai",content:i==="local"?o:at(o)}})}),(0,e.jsx)(I.Z,{}),(0,e.jsx)(Ee.Z,{value:d,onChange:function(r){c(r)},submitType:"enter",placeholder:"\u7ED9\u5C0F\u667A\u53D1\u9001\u6D88\u606F",loading:ot,onSubmit:function(r){c(""),jt(r)},onCancel:function(){b(!1)}})]}),(0,e.jsxs)(V.Z,{title:Ot,width:800,open:dt,onCancel:function(){return S(!1)},onOk:function(){return S(!1)},closeIcon:null,footer:null,destroyOnClose:!0,children:[d,(0,e.jsx)(I.Z,{}),(0,e.jsxs)(R.Z,{justify:"space-between",align:"center",children:[(0,e.jsx)(w.C.Group,{size:"small",max:{count:5,style:{color:"#f56a00",backgroundColor:"#fde3cf"}},children:et(ae(1,8)).map(function(n){return(0,e.jsx)(w.C,{src:"https://api.dicebear.com/7.x/bottts/svg?seed=".concat(n)},n)})}),(0,e.jsx)("span",{children:(0,e.jsx)(y.ZP,{type:"primary",icon:(0,e.jsx)(_.Z,{}),onClick:function(){return ve(x,d)},children:"\u4E0E\u5C0F\u667A\u5BF9\u8BDD"})})]})]}),(0,e.jsxs)(R.Z,{vertical:!0,style:{flex:1,height:"100%"},gap:12,children:[(0,e.jsx)(De.Z,{icon:"https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp",title:"\u{1F44B} \u4F60\u597D, \u6B22\u8FCE\u6765\u5230Prompts\u5DE5\u5382",description:"\u{1F680}\u70B9\u51FB\u4E0B\u9762\u7684Prompt\u53EF\u4EE5\u5FEB\u901F\u5F00\u59CB, \u5FEB\u8BD5\u8BD5\u5427~"}),(0,e.jsx)(ke.Z,{defaultActiveKey:"0",type:"line",onChange:St,items:At,tabBarExtraContent:{right:(0,e.jsxs)(R.Z,{justify:"space-between",align:"center",children:[(0,e.jsx)(y.ZP,{icon:(0,e.jsx)(Me.Z,{}),iconPosition:"start",onClick:Zt,children:"\u6211\u8981\u6295\u7A3F"}),(0,e.jsx)(I.Z,{type:"vertical"}),(0,e.jsx)(nt,{placeholder:"\u5FEB\u901F\u641C\u7D22",enterButton:!0,onSearch:function(r){N(ht,r)},style:{minWidth:100,maxWidth:300}})]})}})]}),(0,e.jsxs)(O.Z.Group,{trigger:"hover",type:"primary",style:{insetBlockEnd:98},icon:(0,e.jsx)(ee.Z,{}),children:[(0,e.jsx)(O.Z,{icon:(0,e.jsx)(ee.Z,{}),tooltip:(0,e.jsx)("img",{src:"https://static.medsci.cn/public-image/ms-image/ca822b80-93a8-11ec-bca5-7f892b5df5d6_ms_gzh.png"})}),(0,e.jsx)(O.Z,{icon:(0,e.jsx)(Le.Z,{}),href:"https://www.medsci.cn/message/add_msg.do",target:"_blank",tooltip:"\u7ED9\u6211\u4EEC\u7559\u8A00"})]}),(0,e.jsx)(O.Z.BackTop,{})]})}}}]);
