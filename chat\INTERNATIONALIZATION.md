# 国际化配置完成

## 概述

已成功为 chat 项目添加了完整的国际化支持，包含 11 种语言的翻译文件和语言切换功能。

## 已完成的工作

### 1. 安装依赖
- `react-i18next`: React 国际化库
- `i18next`: 核心国际化库
- `i18next-browser-languagedetector`: 浏览器语言检测

### 2. 创建语言包文件
在 `src/locales/` 目录下创建了以下语言文件：
- `zh-CN.json` - 简体中文 🇨🇳
- `zh-TW.json` - 繁體中文 🇹🇼
- `en.json` - English 🇺🇸
- `vi.json` - Tiếng Việt 🇻🇳
- `es.json` - Español 🇪🇸
- `ar.json` - العربية 🇸🇦
- `id.json` - Bahasa Indonesia 🇮🇩
- `pt.json` - Português 🇧🇷
- `ja.json` - 日本語 🇯🇵
- `ko.json` - 한국어 🇰🇷
- `ms.json` - Bahasa Melayu 🇲🇾

### 3. 配置文件
- `src/i18n/index.ts` - 主配置文件，包含所有语言的导入和配置
- 在 `src/App.tsx` 中导入国际化配置
- 在 `rsbuild.config.ts` 中添加路径别名支持

### 4. 组件和工具
- `src/components/language-switcher.tsx` - 语言切换下拉组件
- `src/hooks/useI18n.ts` - 封装的国际化 Hook
- 在 `src/layout/base-layout.tsx` 中集成语言切换器

### 5. 语言包结构
每个语言文件都包含以下模块：
- `common` - 通用词汇（确认、取消、保存等）
- `chat` - 聊天相关词汇
- `app` - 应用相关词汇
- `error` - 错误信息

## 使用方法

### 在组件中使用翻译
```tsx
import { useTranslation } from 'react-i18next'
// 或者使用封装的 hook
import { useI18n } from '@/hooks/useI18n'

function MyComponent() {
  const { t } = useTranslation()
  // 或者
  const { t } = useI18n()

  return (
    <div>
      <h1>{t('app.title')}</h1>
      <button>{t('common.confirm')}</button>
    </div>
  )
}
```

### 添加语言切换器
```tsx
import LanguageSwitcher from '@/components/language-switcher'

function Header() {
  return (
    <div>
      <LanguageSwitcher size="small" />
    </div>
  )
}
```

### 手动切换语言
```tsx
import { useI18n } from '@/hooks/useI18n'

function MyComponent() {
  const { changeLanguage, getCurrentLanguage } = useI18n()

  const switchToEnglish = () => {
    changeLanguage('en')
  }

  return (
    <div>
      <p>当前语言: {getCurrentLanguage()}</p>
      <button onClick={switchToEnglish}>Switch to English</button>
    </div>
  )
}
```

## 配置特性

### 语言检测
系统会按以下顺序自动检测用户语言：
1. Cookie 中保存的语言设置
2. localStorage 中保存的语言设置
3. 浏览器语言设置
4. HTML 标签的 lang 属性

### Cookie 配置
- 开发环境域名：`localhost`
- 生产环境域名：`.medsci.cn`
- Cookie 有效期：160 分钟
- 自动保存到 localStorage

### 默认设置
- 默认语言：简体中文 (`zh-CN`)
- 调试模式：生产环境关闭
- 转义：关闭（允许 HTML 内容）

## 文件位置

```
chat/src/
├── i18n/
│   ├── index.ts                    # 主配置文件
│   └── README.md                   # 详细使用说明
├── locales/                        # 语言包文件夹
│   ├── zh-CN.json                  # 简体中文
│   ├── zh-TW.json                  # 繁体中文
│   ├── en.json                     # 英文
│   ├── vi.json                     # 越南语
│   ├── es.json                     # 西班牙语
│   ├── ar.json                     # 阿拉伯语
│   ├── id.json                     # 印尼语
│   ├── pt.json                     # 葡萄牙语
│   ├── ja.json                     # 日语
│   ├── ko.json                     # 韩语
│   └── ms.json                     # 马来语
├── components/
│   └── language-switcher.tsx       # 语言切换组件
└── hooks/
    └── useI18n.ts                  # 国际化 Hook
```

## 构建状态

✅ 项目构建成功  
✅ 所有语言文件已创建  
✅ 语言切换器已集成到布局中  
✅ 路径别名配置正确  

## 下一步

1. 根据实际需求调整翻译内容
2. 在具体页面和组件中使用 `t()` 函数替换硬编码文本
3. 测试各种语言的显示效果
4. 根据用户反馈优化翻译质量

## 注意事项

1. 添加新翻译时，请确保所有语言文件都有对应的翻译
2. 翻译 key 使用点号分隔的层级结构，如 `common.confirm`
3. 语言切换会自动保存用户选择
4. 组件会自动响应语言变化，无需手动刷新
