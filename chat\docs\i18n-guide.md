# Chat 项目国际化指南

## 概述

本项目已完成全面的国际化配置，支持 11 种语言，使用 `react-i18next` 作为国际化框架。

## 支持的语言

- 🇨🇳 简体中文 (zh-CN) - 默认语言
- 🇹🇼 繁体中文 (zh-TW)
- 🇺🇸 英语 (en)
- 🇻🇳 越南语 (vi)
- 🇪🇸 西班牙语 (es)
- 🇸🇦 阿拉伯语 (ar)
- 🇮🇩 印尼语 (id)
- 🇵🇹 葡萄牙语 (pt)
- 🇯🇵 日语 (ja)
- 🇰🇷 韩语 (ko)
- 🇲🇾 马来语 (ms)

## 文件结构

```
chat/src/
├── i18n/
│   └── index.ts          # i18n 配置文件
├── locales/              # 语言文件目录
│   ├── zh-CN.json        # 简体中文
│   ├── zh-TW.json        # 繁体中文
│   ├── en.json           # 英语
│   ├── vi.json           # 越南语
│   ├── es.json           # 西班牙语
│   ├── ar.json           # 阿拉伯语
│   ├── id.json           # 印尼语
│   ├── pt.json           # 葡萄牙语
│   ├── ja.json           # 日语
│   ├── ko.json           # 韩语
│   └── ms.json           # 马来语
└── scripts/              # 维护脚本
    ├── test-i18n.js      # 测试脚本
    └── *.js              # 其他更新脚本
```

## 翻译键结构

语言文件采用分模块的结构：

```json
{
  "common": {
    "login": "登录",
    "logout": "退出",
    "newConversation": "新增对话",
    // ... 通用文字
  },
  "app": {
    "addAppConfigSuccess": "添加应用配置成功",
    // ... 应用相关文字
  },
  "payment": {
    "subscribe": "订阅",
    "free": "免费",
    // ... 支付订阅相关文字
  },
  "chat": {
    "conversationList": "对话列表",
    "rename": "重命名",
    // ... 聊天对话相关文字
  }
}
```

## 使用方法

### 1. 在组件中使用

```tsx
import { useTranslation } from 'react-i18next'

function MyComponent() {
  const { t } = useTranslation()
  
  return (
    <div>
      <button>{t('common.login')}</button>
      <span>{t('chat.conversationList')}</span>
    </div>
  )
}
```

### 2. 带参数的翻译

```tsx
// 语言文件中
{
  "payment": {
    "freeDescription": "免费：每个自然月内，每个智能体的使用上限{{num}}次。"
  }
}

// 组件中使用
<div>{t('payment.freeDescription', { num: 10 })}</div>
```

### 3. 条件翻译

```tsx
const subscriptionType = t('payment.free')
if (subStatusDetail.packageType === subscriptionType) {
  // 处理免费用户逻辑
}
```

## 语言切换

语言检测和切换通过以下方式进行：

1. **Cookie**: `ai_apps_lang`
2. **localStorage**: 本地存储
3. **浏览器语言**: navigator.language
4. **HTML 标签**: html lang 属性

优先级：Cookie > localStorage > 浏览器语言 > HTML 标签

## 维护指南

### 添加新的翻译键

1. 在 `zh-CN.json` 中添加新键
2. 运行脚本更新所有语言文件：

```bash
# 创建更新脚本
node scripts/update-new-translations.js
```

### 测试翻译完整性

```bash
node scripts/test-i18n.js
```

### 验证 JSON 格式

```bash
node -e "console.log('JSON valid:', JSON.parse(require('fs').readFileSync('src/locales/zh-CN.json', 'utf8')) ? 'OK' : 'ERROR')"
```

## 最佳实践

1. **键名规范**: 使用 `模块.功能.描述` 的命名方式
2. **避免硬编码**: 所有用户可见的文字都应使用翻译键
3. **保持同步**: 添加新翻译时确保所有语言文件都更新
4. **测试验证**: 定期运行测试脚本验证翻译完整性
5. **语义化**: 翻译键名应该语义化，便于理解和维护

## 已完成的国际化内容

- ✅ 登录/退出相关文字
- ✅ 对话管理相关文字
- ✅ 支付订阅相关文字
- ✅ 应用配置相关文字
- ✅ 错误提示和成功消息
- ✅ 按钮和操作文字
- ✅ 表单验证文字
- ✅ 空状态提示文字

## 注意事项

1. **阿拉伯语**: 从右到左 (RTL) 布局，可能需要额外的 CSS 调整
2. **长文本**: 某些语言的翻译可能比中文更长，需要考虑 UI 布局
3. **文化差异**: 某些概念在不同文化中可能需要不同的表达方式
4. **货币符号**: 已处理人民币和美元的显示逻辑

## 故障排除

如果遇到翻译不显示的问题：

1. 检查 i18n 是否正确导入到 App.tsx
2. 验证翻译键是否存在于语言文件中
3. 确认组件中正确使用了 useTranslation hook
4. 运行测试脚本检查语言文件完整性
