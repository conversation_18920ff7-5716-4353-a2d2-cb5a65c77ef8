{"name": "dify-chat-web", "private": true, "version": "0.0.1", "scripts": {"build:pkgs": "pnpm --filter @dify-chat/* build", "build": "pnpm build:pkgs && rsbuild build", "dev": "rsbuild dev", "format": "prettier --write .", "lint": "eslint .", "preview": "rsbuild preview"}, "packageManager": "pnpm@8.9.2", "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/x": "^1.1.0", "@dify-chat/api": "workspace:^", "@dify-chat/components": "workspace:^", "@dify-chat/core": "workspace:^", "@dify-chat/helpers": "workspace:^", "@toolkit-fe/clipboard": "^0.1.22", "@toolkit-fe/where-am-i": "^0.1.22", "@types/js-cookie": "^3.0.6", "@types/pako": "^2.0.3", "ahooks": "^3.8.4", "antd": "^5.24.7", "antd-style": "^3.7.1", "i18next": "^25.3.1", "i18next-browser-languagedetector": "^8.2.0", "js-cookie": "^3.0.5", "markdown-it": "^14.1.0", "openai": "^4.93.0", "pure-react-router": "^0.2.3", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.6.0", "react-vant": "^3.3.5", "semver": "^7.7.1"}, "devDependencies": {"@eslint/compat": "^1.2.8", "@eslint/js": "^9.24.0", "@fingerprintjs/fingerprintjs": "^4.6.2", "@rsbuild/core": "^1.3.5", "@rsbuild/plugin-less": "^1.2.2", "@rsbuild/plugin-react": "^1.2.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/markdown-it": "^14.1.2", "@types/node": "^22.14.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/semver": "^7.7.0", "dayjs": "^1.11.13", "eslint": "^9.24.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "pako": "^2.1.0", "prettier": "^3.5.3", "tailwindcss": "3", "typescript": "^5.8.3", "typescript-eslint": "^8.29.1"}}