#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/job/main/chat/node_modules/.pnpm/storybook@8.6.12_prettier@3.5.3/node_modules/storybook/bin/node_modules:/mnt/d/job/main/chat/node_modules/.pnpm/storybook@8.6.12_prettier@3.5.3/node_modules/storybook/node_modules:/mnt/d/job/main/chat/node_modules/.pnpm/storybook@8.6.12_prettier@3.5.3/node_modules:/mnt/d/job/main/chat/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/job/main/chat/node_modules/.pnpm/storybook@8.6.12_prettier@3.5.3/node_modules/storybook/bin/node_modules:/mnt/d/job/main/chat/node_modules/.pnpm/storybook@8.6.12_prettier@3.5.3/node_modules/storybook/node_modules:/mnt/d/job/main/chat/node_modules/.pnpm/storybook@8.6.12_prettier@3.5.3/node_modules:/mnt/d/job/main/chat/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../storybook/bin/index.cjs" "$@"
else
  exec node  "$basedir/../storybook/bin/index.cjs" "$@"
fi
