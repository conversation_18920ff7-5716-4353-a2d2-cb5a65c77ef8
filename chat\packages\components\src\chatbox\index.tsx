import { RobotOutlined, UserOutlined } from '@ant-design/icons'
import { Bubble, Prompts } from '@ant-design/x'
import { DifyApi, IFile, IFileType, IGetAppParametersResponse, IMessageFileItem, IMessageItem4Render } from '@dify-chat/api'
import { IDifyAppItem } from '@dify-chat/core'
import { isTempId, useIsMobile } from '@dify-chat/helpers'
import { GetProp, message } from 'antd'
import { useDeferredValue, useEffect, useMemo, useRef, useState } from 'react'

import { MessageSender } from '../message-sender'
import MessageContent from './message/content'
import MessageFooter from './message/footer'
import { WelcomePlaceholder } from './welcome-placeholder'
import Cookies from 'js-cookie'
import { useHistory } from 'pure-react-router'
export interface ChatboxProps {
	/**
	 * 应用参数
	 */
	appParameters?: IGetAppParametersResponse
	/**
	 * 应用配置
	 */
	appConfig: IDifyAppItem
	/**
	 * 消息列表
	 */
	messageItems: IMessageItem4Render[]
	/**
	 * 是否正在请求
	 */
	isRequesting: boolean
	/**
	 * 下一步问题建议
	 */
	nextSuggestions: string[]
	/**
	 * 推荐 Item 点击事件
	 */
	onPromptsItemClick: GetProp<typeof Prompts, 'onItemClick'>
	/**
	 * 内容提交事件
	 * @param value 问题-文本
	 * @param files 问题-文件
	 */
	onSubmit: (
		value: string,
		options?: {
			files?: IFile[]
			inputs?: Record<string, unknown>
		},
	) => void
	/**
	 * 取消读取流
	 */
	onCancel: () => void
	/**
	 * 对话 ID
	 */
	conversationId: string
	/**
	 * 反馈执行成功后的回调
	 */
	feedbackCallback?: (conversationId: string) => void
	/**
	 * Dify API 实例
	 */
	difyApi: DifyApi
	/**
	 * 反馈 API
	 */
	feedbackApi: DifyApi['feedbackMessage']
	/**
	 * 上传文件 API
	 */
	uploadFileApi: DifyApi['uploadFile']
	onFocus: () => void
	/**
	 * 翻译文本
	 */
	translations?: {
		aiGeneratedContent?: string
		replyTime?: string
	}
}

/**
 * 对话内容区
 */
export const Chatbox = (props: ChatboxProps) => {

	const {
		messageItems,
		isRequesting,
		nextSuggestions,
		onPromptsItemClick,
		onSubmit,
		onCancel,
		conversationId,
		feedbackCallback,
		difyApi,
		appParameters,
		appConfig,
		onFocus,
		translations
	} = props
	console.log('Chatbox', appConfig)
	const history = useHistory()
	const match = history.location.pathname.match(/^\/ai-chat\/([^/]+)$/)
	const appNameEn = match ? match[1] : ''
	const [content, setContent] = useState('')
	const isMobile = useIsMobile()
	// 从cookie中获取以后头像
	const userInfoString = Cookies.get('userInfo')
	const [avatar,setAvatar] = useState(userInfoString&&JSON.parse(userInfoString).avatar||'https://img.medsci.cn/web/img/user_icon.png')
	const changeImg = () => {
		setAvatar('https://img.medsci.cn/web/img/user_icon.png');
	  };
	const roles: GetProp<typeof Bubble.List, 'roles'> = {
		ai: {
			placement: 'start',
			avatar: !isMobile ? { icon: <img src={(appNameEn.includes("novax")||appNameEn.includes("elavax"))?appConfig?.info?.appIcon:"https://static.medsci.cn/ai-write/robot-08128bd4.png"} alt="robot" />, style: { background: '#fde3cf' } } : undefined,
			style: isMobile
				? undefined
				: {
						// 减去一个头像的宽度
						maxWidth: 'calc(100% - 44px)',
					},
		},
		user: {
			placement: 'end',
			avatar: !isMobile
				? {
						icon: avatar?(<img src={avatar} alt="avatar"  onError={changeImg}/>):(<UserOutlined />),
						style: {
							background: avatar?'':'#87d068',
							border:"none"
						},
					}
				: undefined,
			style: isMobile
				? undefined
				: {
						// 减去一个头像的宽度
						maxWidth: 'calc(100% - 44px)',
						marginLeft: '44px',
					},
		},
	}

	const items: GetProp<typeof Bubble.List, 'items'> = useMemo(() => {
		const userMessageMap = new Map<string, IMessageItem4Render>();
		return messageItems?.map(messageItem => {
			if (messageItem.role === 'user') {
				userMessageMap.set(messageItem.id, messageItem);
			}
			return {
				key: `${messageItem.id}-${messageItem.role}`,
				// 不要开启 loading 和 typing, 否则流式会无效
				// loading: status === 'loading',
				content: messageItem.content,
				messageRender: () => {
					return (
						<MessageContent
							appConfig={appConfig}
							onSubmit={onSubmit}
							messageItem={messageItem}
						/>
					)
				},
				// 用户发送消息时，status 为 local，需要展示为用户头像
				role: messageItem.role === 'local' ? 'user' : messageItem.role,
				footer: messageItem.role === 'ai' && (
					<div className="flex items-center">
						<MessageFooter
							ttsConfig={appParameters?.text_to_speech}
							feedbackApi={params => difyApi.feedbackMessage(params)}
							ttsApi={params => difyApi.text2Audio(params)}
							messageId={messageItem.id}
							messageContent={messageItem.content}
							feedback={{
								rating: messageItem.feedback?.rating,
								callback: () => {
									feedbackCallback?.(conversationId!)
								},
							}}
							onSubmit={(messageId) => {
								const preMessageItem = userMessageMap.get(messageItem.id);
								if (!messageId || !preMessageItem) {
									return
								}
								const convertFiles = (files: any[] | undefined): IFile[] | undefined => {
									if (!files) return undefined;
									return files.map(file => ({
										name: file.filename?.split('_').pop()||file.filename,
										url: file.url,
										transfer_method: file.transfer_method as 'remote_url', // 类型断言
										type: file.type as IFileType,
										upload_file_id: file.upload_file_id
									}));
								};
								onSubmit(preMessageItem.content, {files: convertFiles(preMessageItem.files)})
							}}
						/>
						{messageItem.created_at && (
							<div className="ml-3 text-sm text-desc">{translations?.replyTime || '回复时间：'}{messageItem.created_at}</div>
						)}
					</div>
				),
			}
		}) as GetProp<typeof Bubble.List, 'items'>
	}, [messageItems, conversationId, difyApi, feedbackCallback, appConfig, onSubmit])

	// 监听 items 更新，滚动到最底部
	const scrollContainerRef = useRef<HTMLDivElement>(null)
	// 延迟更新，优化性能
	const deferredItems = useDeferredValue(items)
	const searchParams = new URLSearchParams((window as any).top.location.search)
	useEffect(() => {
		if (scrollContainerRef.current) {
			scrollContainerRef.current.scrollTo({
				behavior: 'smooth',
				top: scrollContainerRef.current.scrollHeight,
			})
		}
	}, [deferredItems])

	return (
		<div className="w-full h-full overflow-hidden my-0 mx-auto box-border flex flex-col gap-4 relative bg-white">
			<div
				className="w-full h-full overflow-auto pt-4 pb-48"
				ref={scrollContainerRef}
			>
				{/* 🌟 欢迎占位 */}
				{!items?.length && isTempId(conversationId) && appNameEn != 'medsci-ask' &&(
					<WelcomePlaceholder
						appParameters={appParameters}
						onPromptItemClick={onPromptsItemClick}
						description={appConfig.info.description}
						appIcon={appConfig.info.appIcon}
						appConfig={appConfig}
					/>
				)}
				{/* 🌟 消息列表 */}
				<Bubble.List
					items={items}
					roles={roles}
					className="flex-1 w-full md:!w-3/4 mx-auto px-3 md:px-0 box-border"
				/>
				<div
					className="absolute bottom-0 bg-white w-full md:!w-3/4 left-1/2"
					style={{
						transform: 'translateX(-50%)',
					}}
				>
					{/* 🌟 提示词 */}
					<Prompts
						className="text-default p-3 bg-transparent"
						items={nextSuggestions?.map((item, index) => {
							return {
								key: index.toString(),
								description: item,
							}
						})}
						onItemClick={onPromptsItemClick}
					/>
					{/* 🌟 输入框 */}
					<div className="px-3">
						<MessageSender
							appParameters={appParameters}
							content={content}
							onChange={value => setContent(value)}
							onSubmit={(content, options) => {
								if (!content) {
									return
								}
								onSubmit(content, options)
								setContent('')
							}}
							isRequesting={isRequesting}
							className="w-full"
							uploadFileApi={async(...params) => {
								const res:any = await difyApi.uploadFile(...params)
								if(res.code !=0){

									message.error(res.msg)
									return Promise.reject()
								}
								return difyApi.uploadFile(...params)
							}}
							onCancel={onCancel}
							onFocus={onFocus}
						/>
					</div>
					<div className="text-gray-400 text-sm text-center h-8 leading-8">
						{translations?.aiGeneratedContent || '内容由 AI 生成, 仅供参考'}
					</div>
				</div>
			</div>
		</div>
	)
}
