import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { Conversations } from '@ant-design/x'
import { isTempId } from '@dify-chat/helpers'
import { Form, Input, message, Modal } from 'antd'
import Cookies from 'js-cookie'
import { useHistory, useParams } from 'pure-react-router'
import { useState } from 'react'
/**
 * 会话列表项
 */
export interface IConversationItem {
	/**
	 * 会话 ID
	 */
	key: string
	/**
	 * 会话标题
	 */
	label: string
}

interface IConversationListProps {
	/**
	 * 删除会话异步函数
	 */
	deleteConversationPromise: (conversationId: string,appId:string) => Promise<unknown>
	/**
	 * 重命名会话异步函数
	 */
	renameConversationPromise: (conversationId: string, name: string) => Promise<unknown>
	/**
	 * 会话列表
	 */
	items: IConversationItem[]
	/**
	 * 当前激活的会话 ID
	 */
	activeKey?: string
	/**
	 * 会话切换回调
	 */
	onActiveChange?: (key: string) => void
	/**
	 * 会话列表更新回调
	 */
	onItemsChange?: (items: IConversationItem[]) => void
	/**
	 * 刷新会话列表
	 */
	refreshItems: () => void,

	appConfig: any,

	onchangeModal2Open: (value: boolean) => void,
	/**
	 * 翻译文本
	 */
	translations?: {
		rename?: string
		delete?: string
		conversationRename?: string
		pleaseEnter?: string
		conversationRenameSuccess?: string
		deleteSuccess?: string
	}
}

/**
 * 会话列表
 */
export const ConversationList = (props: IConversationListProps) => {
	const {
		deleteConversationPromise,
		renameConversationPromise,
		items,
		activeKey,
		onActiveChange,
		onItemsChange,
		refreshItems,
		appConfig,
		onchangeModal2Open,
		translations
	} = props
	const history = useHistory()
	const match = history.location.pathname.match(/^\/ai-chat\/([^/]+)$/)
	const appNameEn = match ? match[1] : ''
	const userInfoString = Cookies.get('userInfo')
	const [renameForm] = Form.useForm()

	const language = () => {
		return Cookies.get(
			'ai_apps_lang'
		)? Cookies.get('ai_apps_lang')
		: navigator.language
	}
	/**
	 * 删除会话
	 * @param conversationId 会话 ID
	 */
	const deleteConversation = async (conversationId: string) => {
		if (isTempId(conversationId)) {
			// 如果是临时对话，则直接删除
			onItemsChange?.(items.filter(item => item.key !== conversationId))
		} else {
			// 否则调用删除接口
			await deleteConversationPromise(conversationId,appConfig?.info?.appId)
			refreshItems()
		}
		message.success(translations?.deleteSuccess || '删除成功')
		if (activeKey === conversationId) {
			onActiveChange?.('')
		}
	}

	/**
	 * 重命名会话
	 * @param conversation 会话对象
	 */
	const renameConversation = (conversation: IConversationItem) => {
		renameForm.setFieldsValue({
			name: conversation.label,
		})
		Modal.confirm({
			destroyOnClose: true,
			title: translations?.conversationRename || '会话重命名',
			content: (
				<Form
					form={renameForm}
					className="mt-3"
				>
					<Form.Item name="name">
						<Input placeholder={translations?.pleaseEnter || '请输入'} />
					</Form.Item>
				</Form>
			),
			onOk: async () => {
				await renameForm.validateFields()
				const values = await renameForm.validateFields()
				await renameConversationPromise(conversation.key, values.name)
				message.success(translations?.conversationRenameSuccess || '会话重命名成功')
				refreshItems()
			},
		})
	}

	return (
		<Conversations
			items={items}
			activeKey={activeKey}
			onActiveChange={onActiveChange}
			menu={conversation =>  ({
				items: [
					{
						label: translations?.rename || '重命名',
						key: 'rename',
						icon: <EditOutlined />,
						disabled: isTempId(conversation.key),
					},
					((appNameEn.includes("novax")||appNameEn.includes("elavax"))?{
						label: translations?.delete || '删除',
						key: 'delete',
						icon: <DeleteOutlined />,
						danger: true,
					}:null),
				],
				onClick: async menuInfo => {
					// 阻止冒泡 防止点击更多按钮时触发 onActiveChange
					menuInfo.domEvent.stopPropagation()
					switch (menuInfo.key) {
						case 'delete':
							await deleteConversation(conversation.key)
							break
						case 'rename':
							renameConversation(conversation as IConversationItem)
							break
						default:
							break
					}
				},
			})}
		/>
	)
}
