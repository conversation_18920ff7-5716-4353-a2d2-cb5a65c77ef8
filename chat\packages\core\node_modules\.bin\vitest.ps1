#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\job\main\chat\node_modules\.pnpm\vitest@3.1.2_@types+node@22.14.1_jsdom@26.1.0\node_modules\vitest\node_modules;D:\job\main\chat\node_modules\.pnpm\vitest@3.1.2_@types+node@22.14.1_jsdom@26.1.0\node_modules;D:\job\main\chat\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/job/main/chat/node_modules/.pnpm/vitest@3.1.2_@types+node@22.14.1_jsdom@26.1.0/node_modules/vitest/node_modules:/mnt/d/job/main/chat/node_modules/.pnpm/vitest@3.1.2_@types+node@22.14.1_jsdom@26.1.0/node_modules:/mnt/d/job/main/chat/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../vitest/vitest.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../vitest/vitest.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../vitest/vitest.mjs" $args
  } else {
    & "node$exe"  "$basedir/../vitest/vitest.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
