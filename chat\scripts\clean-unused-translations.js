const fs = require('fs');
const path = require('path');

// 需要处理的文件扩展名
const fileExtensions = ['.tsx', '.ts', '.jsx', '.js'];

// 需要忽略的目录
const ignoreDirs = ['node_modules', '.git', 'dist', 'build', 'locales', 'scripts'];

// 递归查找文件
function findFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!ignoreDirs.includes(item)) {
        findFiles(fullPath, files);
      }
    } else if (fileExtensions.includes(path.extname(item))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// 获取对象的所有键路径
function getKeyPaths(obj, prefix = '') {
  const paths = [];
  for (const key in obj) {
    const currentPath = prefix ? `${prefix}.${key}` : key;
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      paths.push(...getKeyPaths(obj[key], currentPath));
    } else {
      paths.push(currentPath);
    }
  }
  return paths;
}

// 从代码中提取使用的翻译键
function extractUsedKeys(files) {
  const usedKeys = new Set();
  
  for (const file of files) {
    const content = fs.readFileSync(file, 'utf8');
    
    // 匹配 t("key") 和 t('key') 模式
    const patterns = [
      /t\(["']([^"']+)["']\)/g,
      /t\(`([^`]+)`\)/g,
    ];
    
    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        usedKeys.add(match[1]);
      }
    }
  }
  
  return usedKeys;
}

// 删除嵌套对象中的键
function deleteNestedKey(obj, path) {
  const keys = path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return false; // 路径不存在
    }
  }
  
  const lastKey = keys[keys.length - 1];
  if (current && typeof current === 'object' && lastKey in current) {
    delete current[lastKey];
    return true;
  }
  
  return false;
}

// 清理空的嵌套对象
function cleanEmptyObjects(obj) {
  for (const key in obj) {
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      cleanEmptyObjects(obj[key]);
      if (Object.keys(obj[key]).length === 0) {
        delete obj[key];
      }
    }
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, '../src');
  const localesDir = path.join(__dirname, '../src/locales');
  
  // 查找所有源代码文件
  const files = findFiles(srcDir);
  console.log(`🔍 扫描 ${files.length} 个源代码文件...`);
  
  // 提取使用的翻译键
  const usedKeys = extractUsedKeys(files);
  console.log(`📝 找到 ${usedKeys.size} 个使用中的翻译键`);
  
  // 处理每个语言文件
  const languageFiles = fs.readdirSync(localesDir).filter(file => file.endsWith('.json'));
  
  for (const langFile of languageFiles) {
    const langPath = path.join(localesDir, langFile);
    const langData = JSON.parse(fs.readFileSync(langPath, 'utf8'));
    
    // 获取所有可用的键
    const allKeys = getKeyPaths(langData);
    console.log(`\n📋 ${langFile}: 包含 ${allKeys.length} 个翻译键`);
    
    // 找出未使用的键
    const unusedKeys = allKeys.filter(key => !usedKeys.has(key));
    
    if (unusedKeys.length > 0) {
      console.log(`🗑️  删除 ${unusedKeys.length} 个未使用的键:`);
      
      // 删除未使用的键
      for (const key of unusedKeys) {
        if (deleteNestedKey(langData, key)) {
          console.log(`   - ${key}`);
        }
      }
      
      // 清理空的嵌套对象
      cleanEmptyObjects(langData);
      
      // 保存文件
      fs.writeFileSync(langPath, JSON.stringify(langData, null, 2), 'utf8');
      console.log(`✅ ${langFile} 已更新`);
    } else {
      console.log(`✨ ${langFile} 没有未使用的键`);
    }
  }
  
  console.log('\n🎉 清理完成！');
  
  // 显示最终统计
  console.log('\n📊 最终统计:');
  console.log(`   使用中的翻译键: ${usedKeys.size}`);
  
  // 重新统计清理后的键数量
  for (const langFile of languageFiles) {
    const langPath = path.join(localesDir, langFile);
    const langData = JSON.parse(fs.readFileSync(langPath, 'utf8'));
    const remainingKeys = getKeyPaths(langData);
    console.log(`   ${langFile}: ${remainingKeys.length} 个键`);
  }
}

// 运行脚本
main();
