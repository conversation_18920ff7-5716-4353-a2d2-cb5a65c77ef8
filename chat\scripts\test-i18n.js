const fs = require('fs');
const path = require('path');

// 测试所有语言文件的完整性
const localesDir = path.join(__dirname, '../src/locales');
const languages = ['zh-CN', 'zh-TW', 'en', 'vi', 'es', 'ar', 'id', 'pt', 'ja', 'ko', 'ms'];

console.log('🔍 开始测试国际化文件完整性...\n');

// 读取基准语言文件 (zh-CN)
const baseFilePath = path.join(localesDir, 'zh-CN.json');
const baseContent = JSON.parse(fs.readFileSync(baseFilePath, 'utf8'));

// 递归获取所有键
function getAllKeys(obj, prefix = '') {
  let keys = [];
  for (const key in obj) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      keys = keys.concat(getAllKeys(obj[key], fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  return keys;
}

const baseKeys = getAllKeys(baseContent);
console.log(`📋 基准语言 (zh-CN) 共有 ${baseKeys.length} 个翻译键\n`);

let allValid = true;

// 检查每个语言文件
languages.forEach(lang => {
  const filePath = path.join(localesDir, `${lang}.json`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ ${lang}.json 文件不存在`);
    allValid = false;
    return;
  }
  
  try {
    const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    const keys = getAllKeys(content);
    
    const missingKeys = baseKeys.filter(key => !keys.includes(key));
    const extraKeys = keys.filter(key => !baseKeys.includes(key));
    
    if (missingKeys.length === 0 && extraKeys.length === 0) {
      console.log(`✅ ${lang}.json - 完整 (${keys.length} 个键)`);
    } else {
      console.log(`⚠️  ${lang}.json - 不完整:`);
      if (missingKeys.length > 0) {
        console.log(`   缺少 ${missingKeys.length} 个键: ${missingKeys.slice(0, 3).join(', ')}${missingKeys.length > 3 ? '...' : ''}`);
      }
      if (extraKeys.length > 0) {
        console.log(`   多余 ${extraKeys.length} 个键: ${extraKeys.slice(0, 3).join(', ')}${extraKeys.length > 3 ? '...' : ''}`);
      }
      allValid = false;
    }
  } catch (error) {
    console.log(`❌ ${lang}.json - JSON 格式错误: ${error.message}`);
    allValid = false;
  }
});

console.log('\n' + '='.repeat(50));

if (allValid) {
  console.log('🎉 所有语言文件都完整且格式正确！');
  console.log('\n📝 主要翻译模块:');
  console.log('   • common - 通用文字 (登录、退出、确认等)');
  console.log('   • app - 应用相关文字');
  console.log('   • payment - 支付订阅相关文字');
  console.log('   • chat - 聊天对话相关文字');
  
  console.log('\n🌍 支持的语言:');
  console.log('   • 简体中文 (zh-CN)');
  console.log('   • 繁体中文 (zh-TW)');
  console.log('   • 英语 (en)');
  console.log('   • 越南语 (vi)');
  console.log('   • 西班牙语 (es)');
  console.log('   • 阿拉伯语 (ar)');
  console.log('   • 印尼语 (id)');
  console.log('   • 葡萄牙语 (pt)');
  console.log('   • 日语 (ja)');
  console.log('   • 韩语 (ko)');
  console.log('   • 马来语 (ms)');
} else {
  console.log('❌ 发现问题，请检查上述错误并修复');
  process.exit(1);
}

console.log('\n✨ 国际化测试完成！');
