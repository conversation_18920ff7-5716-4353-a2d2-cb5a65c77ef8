const fs = require('fs');
const path = require('path');

// 新增的支付相关翻译
const additionalTranslations = {
  'zh-TW': {
    "medSciAccount": "梅斯賬號",
    "pleaseActivateAfterAgreement": "請閱讀並同意協議後激活",
    "freeTrial": "免費試用"
  },
  'en': {
    "medSciAccount": "MedSci Account",
    "pleaseActivateAfterAgreement": "Please activate after reading and agreeing to the agreement",
    "freeTrial": "Free Trial"
  },
  'vi': {
    "medSciAccount": "Tài khoản MedSci",
    "pleaseActivateAfterAgreement": "Vui lòng kích hoạt sau khi đọc và đồng ý với thỏa thuận",
    "freeTrial": "Dùng thử miễn phí"
  },
  'es': {
    "medSciAccount": "Cuenta MedSci",
    "pleaseActivateAfterAgreement": "Por favor active después de leer y aceptar el acuerdo",
    "freeTrial": "Prueba gratuita"
  },
  'ar': {
    "medSciAccount": "حساب MedSci",
    "pleaseActivateAfterAgreement": "يرجى التفعيل بعد قراءة الموافقة على الاتفاقية",
    "freeTrial": "تجربة مجانية"
  },
  'id': {
    "medSciAccount": "Akun MedSci",
    "pleaseActivateAfterAgreement": "Silakan aktifkan setelah membaca dan menyetujui perjanjian",
    "freeTrial": "Uji coba gratis"
  },
  'pt': {
    "medSciAccount": "Conta MedSci",
    "pleaseActivateAfterAgreement": "Por favor, ative após ler e concordar com o acordo",
    "freeTrial": "Teste gratuito"
  },
  'ja': {
    "medSciAccount": "MedSciアカウント",
    "pleaseActivateAfterAgreement": "規約を読んで同意した後に有効化してください",
    "freeTrial": "無料トライアル"
  },
  'ko': {
    "medSciAccount": "MedSci 계정",
    "pleaseActivateAfterAgreement": "약관을 읽고 동의한 후 활성화하세요",
    "freeTrial": "무료 체험"
  },
  'ms': {
    "medSciAccount": "Akaun MedSci",
    "pleaseActivateAfterAgreement": "Sila aktifkan selepas membaca dan bersetuju dengan perjanjian",
    "freeTrial": "Percubaan percuma"
  }
};

// 更新语言文件
const localesDir = path.join(__dirname, '../src/locales');
const languages = ['zh-TW', 'en', 'vi', 'es', 'ar', 'id', 'pt', 'ja', 'ko', 'ms'];

languages.forEach(lang => {
  const filePath = path.join(localesDir, `${lang}.json`);
  
  if (fs.existsSync(filePath)) {
    const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    // 添加新的支付翻译
    if (content.payment && additionalTranslations[lang]) {
      Object.assign(content.payment, additionalTranslations[lang]);
    }
    
    fs.writeFileSync(filePath, JSON.stringify(content, null, 2), 'utf8');
    console.log(`✅ 已更新: ${lang}.json`);
  }
});

console.log('\n🎉 所有语言文件的额外支付翻译更新完成！');
