const fs = require('fs');
const path = require('path');

// 新增的授权翻译
const authorizingTranslations = {
  'zh-TW': {
    "authorizing": "授權登入中..."
  },
  'en': {
    "authorizing": "Authorizing login..."
  },
  'vi': {
    "authorizing": "Đang <PERSON>y quyền đăng nhập..."
  },
  'es': {
    "authorizing": "Autorizando inicio de sesión..."
  },
  'ar': {
    "authorizing": "جاري تسجيل الدخول المصرح به..."
  },
  'id': {
    "authorizing": "Mengotorisasi login..."
  },
  'pt': {
    "authorizing": "Autorizando login..."
  },
  'ja': {
    "authorizing": "ログイン認証中..."
  },
  'ko': {
    "authorizing": "로그인 인증 중..."
  },
  'ms': {
    "authorizing": "Mengizinkan log masuk..."
  }
};

// 更新语言文件
const localesDir = path.join(__dirname, '../src/locales');
const languages = ['zh-TW', 'en', 'vi', 'es', 'ar', 'id', 'pt', 'ja', 'ko', 'ms'];

languages.forEach(lang => {
  const filePath = path.join(localesDir, `${lang}.json`);
  
  if (fs.existsSync(filePath)) {
    const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    // 添加新的授权翻译
    if (content.common && authorizingTranslations[lang]) {
      Object.assign(content.common, authorizingTranslations[lang]);
    }
    
    fs.writeFileSync(filePath, JSON.stringify(content, null, 2), 'utf8');
    console.log(`✅ 已更新: ${lang}.json`);
  }
});

console.log('\n🎉 所有语言文件的授权翻译更新完成！');
