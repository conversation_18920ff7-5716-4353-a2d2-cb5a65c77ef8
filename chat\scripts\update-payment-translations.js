const fs = require('fs');
const path = require('path');

// 新增的支付相关翻译
const paymentTranslations = {
  'zh-TW': {
    "supportAlipayPayment": "支持支付寶支付",
    "month": "月",
    "quarter": "季",
    "year": "年",
    "subscriptionSuccess": "訂閱成功",
    "subscribed": "已訂閱",
    "freeUsing": "免費使用中…",
    "cancelSubscription": "取消訂閱",
    "monthlySubscribing": "連續包月中…",
    "yearlySubscribing": "連續包年中…",
    "cancelMonthly": "取消包月",
    "cancelYearly": "取消包年",
    "cancelConfirmTitle": "提示",
    "cancelMonthlyConfirm": "取消包月在{{date}}號生效，再次使用需要重新訂閱。是否確認取消？",
    "cancelYearlyConfirm": "取消包年在{{date}}號生效，再次使用需要重新訂閱。是否確認取消？",
    "subscriptionExpireAt": "您的訂閱可使用至 {{date}}"
  },
  'en': {
    "supportAlipayPayment": "Supports Alipay Payment",
    "month": "Month",
    "quarter": "Quarter", 
    "year": "Year",
    "subscriptionSuccess": "Subscription Successful",
    "subscribed": "Subscribed",
    "freeUsing": "Free in use...",
    "cancelSubscription": "Cancel Subscription",
    "monthlySubscribing": "Monthly subscription active...",
    "yearlySubscribing": "Yearly subscription active...",
    "cancelMonthly": "Cancel Monthly",
    "cancelYearly": "Cancel Yearly",
    "cancelConfirmTitle": "Confirm",
    "cancelMonthlyConfirm": "Monthly cancellation will take effect on the {{date}}. You need to resubscribe to use again. Confirm cancellation?",
    "cancelYearlyConfirm": "Yearly cancellation will take effect on the {{date}}. You need to resubscribe to use again. Confirm cancellation?",
    "subscriptionExpireAt": "Your subscription is valid until {{date}}"
  },
  'vi': {
    "supportAlipayPayment": "Hỗ trợ thanh toán Alipay",
    "month": "Tháng",
    "quarter": "Quý",
    "year": "Năm", 
    "subscriptionSuccess": "Đăng ký thành công",
    "subscribed": "Đã đăng ký",
    "freeUsing": "Đang sử dụng miễn phí...",
    "cancelSubscription": "Hủy đăng ký",
    "monthlySubscribing": "Đăng ký hàng tháng đang hoạt động...",
    "yearlySubscribing": "Đăng ký hàng năm đang hoạt động...",
    "cancelMonthly": "Hủy hàng tháng",
    "cancelYearly": "Hủy hàng năm",
    "cancelConfirmTitle": "Xác nhận",
    "cancelMonthlyConfirm": "Việc hủy hàng tháng sẽ có hiệu lực vào ngày {{date}}. Bạn cần đăng ký lại để sử dụng. Xác nhận hủy?",
    "cancelYearlyConfirm": "Việc hủy hàng năm sẽ có hiệu lực vào ngày {{date}}. Bạn cần đăng ký lại để sử dụng. Xác nhận hủy?",
    "subscriptionExpireAt": "Đăng ký của bạn có hiệu lực đến {{date}}"
  },
  'es': {
    "supportAlipayPayment": "Admite pago con Alipay",
    "month": "Mes",
    "quarter": "Trimestre",
    "year": "Año",
    "subscriptionSuccess": "Suscripción exitosa",
    "subscribed": "Suscrito",
    "freeUsing": "Uso gratuito...",
    "cancelSubscription": "Cancelar suscripción",
    "monthlySubscribing": "Suscripción mensual activa...",
    "yearlySubscribing": "Suscripción anual activa...",
    "cancelMonthly": "Cancelar mensual",
    "cancelYearly": "Cancelar anual",
    "cancelConfirmTitle": "Confirmar",
    "cancelMonthlyConfirm": "La cancelación mensual entrará en vigor el {{date}}. Necesita volver a suscribirse para usar. ¿Confirmar cancelación?",
    "cancelYearlyConfirm": "La cancelación anual entrará en vigor el {{date}}. Necesita volver a suscribirse para usar. ¿Confirmar cancelación?",
    "subscriptionExpireAt": "Su suscripción es válida hasta {{date}}"
  },
  'ar': {
    "supportAlipayPayment": "يدعم دفع Alipay",
    "month": "شهر",
    "quarter": "ربع سنة",
    "year": "سنة",
    "subscriptionSuccess": "نجح الاشتراك",
    "subscribed": "مشترك",
    "freeUsing": "استخدام مجاني...",
    "cancelSubscription": "إلغاء الاشتراك",
    "monthlySubscribing": "الاشتراك الشهري نشط...",
    "yearlySubscribing": "الاشتراك السنوي نشط...",
    "cancelMonthly": "إلغاء الشهري",
    "cancelYearly": "إلغاء السنوي",
    "cancelConfirmTitle": "تأكيد",
    "cancelMonthlyConfirm": "سيدخل إلغاء الاشتراك الشهري حيز التنفيذ في {{date}}. تحتاج إلى إعادة الاشتراك للاستخدام. تأكيد الإلغاء؟",
    "cancelYearlyConfirm": "سيدخل إلغاء الاشتراك السنوي حيز التنفيذ في {{date}}. تحتاج إلى إعادة الاشتراك للاستخدام. تأكيد الإلغاء؟",
    "subscriptionExpireAt": "اشتراكك صالح حتى {{date}}"
  },
  'id': {
    "supportAlipayPayment": "Mendukung pembayaran Alipay",
    "month": "Bulan",
    "quarter": "Kuartal",
    "year": "Tahun",
    "subscriptionSuccess": "Langganan berhasil",
    "subscribed": "Berlangganan",
    "freeUsing": "Penggunaan gratis...",
    "cancelSubscription": "Batalkan langganan",
    "monthlySubscribing": "Langganan bulanan aktif...",
    "yearlySubscribing": "Langganan tahunan aktif...",
    "cancelMonthly": "Batalkan bulanan",
    "cancelYearly": "Batalkan tahunan",
    "cancelConfirmTitle": "Konfirmasi",
    "cancelMonthlyConfirm": "Pembatalan bulanan akan berlaku pada {{date}}. Anda perlu berlangganan lagi untuk menggunakan. Konfirmasi pembatalan?",
    "cancelYearlyConfirm": "Pembatalan tahunan akan berlaku pada {{date}}. Anda perlu berlangganan lagi untuk menggunakan. Konfirmasi pembatalan?",
    "subscriptionExpireAt": "Langganan Anda berlaku hingga {{date}}"
  },
  'pt': {
    "supportAlipayPayment": "Suporta pagamento Alipay",
    "month": "Mês",
    "quarter": "Trimestre",
    "year": "Ano",
    "subscriptionSuccess": "Assinatura bem-sucedida",
    "subscribed": "Assinado",
    "freeUsing": "Uso gratuito...",
    "cancelSubscription": "Cancelar assinatura",
    "monthlySubscribing": "Assinatura mensal ativa...",
    "yearlySubscribing": "Assinatura anual ativa...",
    "cancelMonthly": "Cancelar mensal",
    "cancelYearly": "Cancelar anual",
    "cancelConfirmTitle": "Confirmar",
    "cancelMonthlyConfirm": "O cancelamento mensal entrará em vigor no dia {{date}}. Você precisa assinar novamente para usar. Confirmar cancelamento?",
    "cancelYearlyConfirm": "O cancelamento anual entrará em vigor no dia {{date}}. Você precisa assinar novamente para usar. Confirmar cancelamento?",
    "subscriptionExpireAt": "Sua assinatura é válida até {{date}}"
  },
  'ja': {
    "supportAlipayPayment": "Alipay決済をサポート",
    "month": "月",
    "quarter": "四半期",
    "year": "年",
    "subscriptionSuccess": "サブスクリプション成功",
    "subscribed": "サブスクライブ済み",
    "freeUsing": "無料使用中...",
    "cancelSubscription": "サブスクリプションをキャンセル",
    "monthlySubscribing": "月額サブスクリプション有効中...",
    "yearlySubscribing": "年額サブスクリプション有効中...",
    "cancelMonthly": "月額をキャンセル",
    "cancelYearly": "年額をキャンセル",
    "cancelConfirmTitle": "確認",
    "cancelMonthlyConfirm": "月額のキャンセルは{{date}}日に有効になります。再度使用するには再サブスクライブが必要です。キャンセルを確認しますか？",
    "cancelYearlyConfirm": "年額のキャンセルは{{date}}日に有効になります。再度使用するには再サブスクライブが必要です。キャンセルを確認しますか？",
    "subscriptionExpireAt": "あなたのサブスクリプションは{{date}}まで有効です"
  },
  'ko': {
    "supportAlipayPayment": "Alipay 결제 지원",
    "month": "월",
    "quarter": "분기",
    "year": "년",
    "subscriptionSuccess": "구독 성공",
    "subscribed": "구독됨",
    "freeUsing": "무료 사용 중...",
    "cancelSubscription": "구독 취소",
    "monthlySubscribing": "월간 구독 활성화 중...",
    "yearlySubscribing": "연간 구독 활성화 중...",
    "cancelMonthly": "월간 취소",
    "cancelYearly": "연간 취소",
    "cancelConfirmTitle": "확인",
    "cancelMonthlyConfirm": "월간 취소는 {{date}}일에 적용됩니다. 다시 사용하려면 재구독이 필요합니다. 취소를 확인하시겠습니까?",
    "cancelYearlyConfirm": "연간 취소는 {{date}}일에 적용됩니다. 다시 사용하려면 재구독이 필요합니다. 취소를 확인하시겠습니까?",
    "subscriptionExpireAt": "귀하의 구독은 {{date}}까지 유효합니다"
  },
  'ms': {
    "supportAlipayPayment": "Menyokong pembayaran Alipay",
    "month": "Bulan",
    "quarter": "Suku tahun",
    "year": "Tahun",
    "subscriptionSuccess": "Langganan berjaya",
    "subscribed": "Dilanggan",
    "freeUsing": "Penggunaan percuma...",
    "cancelSubscription": "Batalkan langganan",
    "monthlySubscribing": "Langganan bulanan aktif...",
    "yearlySubscribing": "Langganan tahunan aktif...",
    "cancelMonthly": "Batalkan bulanan",
    "cancelYearly": "Batalkan tahunan",
    "cancelConfirmTitle": "Sahkan",
    "cancelMonthlyConfirm": "Pembatalan bulanan akan berkuat kuasa pada {{date}}. Anda perlu melanggan semula untuk menggunakan. Sahkan pembatalan?",
    "cancelYearlyConfirm": "Pembatalan tahunan akan berkuat kuasa pada {{date}}. Anda perlu melanggan semula untuk menggunakan. Sahkan pembatalan?",
    "subscriptionExpireAt": "Langganan anda sah sehingga {{date}}"
  }
};

// 更新语言文件
const localesDir = path.join(__dirname, '../src/locales');
const languages = ['zh-TW', 'en', 'vi', 'es', 'ar', 'id', 'pt', 'ja', 'ko', 'ms'];

languages.forEach(lang => {
  const filePath = path.join(localesDir, `${lang}.json`);
  
  if (fs.existsSync(filePath)) {
    const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    // 添加新的支付翻译
    if (content.payment && paymentTranslations[lang]) {
      Object.assign(content.payment, paymentTranslations[lang]);
    }
    
    fs.writeFileSync(filePath, JSON.stringify(content, null, 2), 'utf8');
    console.log(`✅ 已更新: ${lang}.json`);
  }
});

console.log('\n🎉 所有语言文件的支付模块更新完成！');
