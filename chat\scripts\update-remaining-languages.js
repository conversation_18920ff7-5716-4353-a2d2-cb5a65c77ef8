const fs = require('fs');
const path = require('path');

// 剩余语言的翻译
const remainingLanguages = {
  'ar': {
    "common": {
      "login": "تسجيل الدخول",
      "logout": "تسجيل الخروج",
      "goHome": "العودة للرئيسية",
      "newConversation": "محادثة جديدة",
      "noSessions": "لا توجد جلسات",
      "startConfig": "بدء التكوين",
      "noDifyAppConfig": "لا يوجد تكوين تطبيق Dify",
      "getConversationListFailed": "فشل في الحصول على قائمة المحادثات",
      "confirm": "تأكيد",
      "cancel": "إلغاء",
      "save": "حفظ",
      "delete": "حذف",
      "edit": "تحرير",
      "add": "إضافة",
      "update": "تحديث",
      "close": "إغلاق",
      "current": "الحالي",
      "none": "لا يوجد",
      "tags": "العلامات",
      "basicInfo": "المعلومات الأساسية",
      "pleaseLoginFirst": "يرجى تسجيل الدخول للشراء",
      "loginNow": "تسجيل الدخول الآن",
      "subscriptionFailed": "فشل الاشتراك",
      "subscriptionSuccess": "نجح الاشتراك",
      "pleaseSelectPeriod": "يرجى اختيار فترة الاشتراك",
      "pleaseAgreeToTerms": "يرجى قراءة الشروط والموافقة عليها قبل التفعيل",
      "agreeAndPay": " تأكيد الشروط والدفع",
      "freeTrial": "تجربة مجانية",
      "rmb": "يوان صيني",
      "usd": "دولار أمريكي",
      "writing": "كتابة"
    },
    "app": {
      "addAppConfigSuccess": "تم إضافة تكوين التطبيق بنجاح",
      "updateAppConfigSuccess": "تم تحديث تكوين التطبيق بنجاح",
      "management": "إدارة تكوين التطبيقات",
      "appManagement": "إدارة التطبيقات",
      "addApp": "إضافة تطبيق",
      "addAppConfig": "إضافة تكوين التطبيق",
      "appConfigDetail": "تفاصيل تكوين التطبيق",
      "appName": "اسم التطبيق",
      "appDescription": "وصف التطبيق",
      "appTags": "علامات التطبيق",
      "noApps": "لا توجد تطبيقات",
      "confirmDeleteApp": "هل أنت متأكد من حذف هذا التطبيق؟",
      "deleteAppSuccess": "تم حذف التطبيق بنجاح",
      "saveAppConfigFailed": "فشل في حفظ تكوين التطبيق",
      "currentActiveAppId": "معرف التطبيق النشط الحالي",
      "appList": "قائمة التطبيقات",
      "getAppList": "الحصول على قائمة التطبيقات",
      "appListLoading": "جاري تحميل قائمة التطبيقات",
      "deleteAppSuccessCallback": "استدعاء نجح حذف التطبيق",
      "getDifyAppInfo": "الحصول على معلومات تطبيق Dify"
    },
    "payment": {
      "subscribe": "اشتراك",
      "free": "مجاني",
      "monthlySubscription": "اشتراك شهري",
      "yearlySubscription": "اشتراك سنوي",
      "upgradeSubscription": "ترقية الاشتراك",
      "modifySubscription": "تعديل الاشتراك",
      "supportAlipay": "دفع عبر Alipay",
      "meisiAccount": "حساب Meisi",
      "subscriptionDescription": "وصف اشتراك Meisi AI",
      "freeDescription": "مجاني: كل وكيل ذكي له حد استخدام {num} مرة شهرياً. يعاد العد في بداية كل شهر.",
      "monthlyDescription": "الاشتراك الشهري: استخدام غير محدود لكل وكيل ذكي لمدة شهر من تاريخ الاشتراك.",
      "yearlyDescription": "الاشتراك السنوي: استخدام غير محدود لكل وكيل ذكي لمدة سنة من تاريخ الاشتراك",
      "subscribed": "مشترك",
      "freeUsing": "يستخدم النسخة المجانية...",
      "cancelSubscription": "إلغاء الاشتراك",
      "subscriptionValidUntil": "اشتراكك صالح حتى {date}",
      "monthlySubscribing": "الاشتراك الشهري نشط...",
      "yearlySubscribing": "الاشتراك السنوي نشط...",
      "cancelMonthly": "إلغاء الشهري",
      "cancelYearly": "إلغاء السنوي",
      "cancelConfirmTitle": "إشعار",
      "cancelMonthlyConfirm": "سيصبح إلغاء الاشتراك الشهري ساري المفعول في اليوم {date}. تحتاج إلى إعادة الاشتراك للاستخدام مرة أخرى. هل أنت متأكد من الإلغاء؟",
      "cancelYearlyConfirm": "سيصبح إلغاء الاشتراك السنوي ساري المفعول في اليوم {date}. تحتاج إلى إعادة الاشتراك للاستخدام مرة أخرى. هل أنت متأكد من الإلغاء؟",
      "month": "شهر",
      "quarter": "ربع سنة",
      "year": "سنة"
    }
  },
  'id': {
    "common": {
      "login": "Masuk",
      "logout": "Keluar",
      "goHome": "Ke Beranda",
      "newConversation": "Percakapan Baru",
      "noSessions": "Tidak Ada Sesi",
      "startConfig": "Mulai Konfigurasi",
      "noDifyAppConfig": "Tidak Ada Konfigurasi Aplikasi Dify",
      "getConversationListFailed": "Gagal mendapatkan daftar percakapan",
      "confirm": "Konfirmasi",
      "cancel": "Batal",
      "save": "Simpan",
      "delete": "Hapus",
      "edit": "Edit",
      "add": "Tambah",
      "update": "Perbarui",
      "close": "Tutup",
      "current": "Saat ini",
      "none": "Tidak ada",
      "tags": "Tag",
      "basicInfo": "Informasi Dasar",
      "pleaseLoginFirst": "Silakan login untuk membeli",
      "loginNow": "Login Sekarang",
      "subscriptionFailed": "Langganan gagal",
      "subscriptionSuccess": "Langganan berhasil",
      "pleaseSelectPeriod": "Silakan pilih periode langganan",
      "pleaseAgreeToTerms": "Silakan baca dan setujui syarat sebelum aktivasi",
      "agreeAndPay": " konfirmasi syarat dan bayar",
      "freeTrial": "Uji Coba Gratis",
      "rmb": "RMB",
      "usd": "USD",
      "writing": "Menulis"
    },
    "app": {
      "addAppConfigSuccess": "Konfigurasi aplikasi berhasil ditambahkan",
      "updateAppConfigSuccess": "Konfigurasi aplikasi berhasil diperbarui",
      "management": "Manajemen Konfigurasi Aplikasi",
      "appManagement": "Manajemen Aplikasi",
      "addApp": "Tambah Aplikasi",
      "addAppConfig": "Tambah Konfigurasi Aplikasi",
      "appConfigDetail": "Detail Konfigurasi Aplikasi",
      "appName": "Nama Aplikasi",
      "appDescription": "Deskripsi Aplikasi",
      "appTags": "Tag Aplikasi",
      "noApps": "Tidak Ada Aplikasi",
      "confirmDeleteApp": "Apakah Anda yakin ingin menghapus aplikasi ini?",
      "deleteAppSuccess": "Aplikasi berhasil dihapus",
      "saveAppConfigFailed": "Gagal menyimpan konfigurasi aplikasi",
      "currentActiveAppId": "ID aplikasi aktif saat ini",
      "appList": "Daftar Aplikasi",
      "getAppList": "Dapatkan Daftar Aplikasi",
      "appListLoading": "Memuat daftar aplikasi",
      "deleteAppSuccessCallback": "Callback penghapusan aplikasi berhasil",
      "getDifyAppInfo": "Dapatkan informasi aplikasi Dify"
    },
    "payment": {
      "subscribe": "Berlangganan",
      "free": "Gratis",
      "monthlySubscription": "Langganan Bulanan",
      "yearlySubscription": "Langganan Tahunan",
      "upgradeSubscription": "Upgrade Langganan",
      "modifySubscription": "Ubah Langganan",
      "supportAlipay": "Pembayaran Alipay",
      "meisiAccount": "Akun Meisi",
      "subscriptionDescription": "Deskripsi Langganan Meisi AI",
      "freeDescription": "Gratis: Setiap agen AI memiliki batas penggunaan {num} kali per bulan alami. Penghitungan ulang di awal setiap bulan.",
      "monthlyDescription": "Langganan Bulanan: Penggunaan tak terbatas setiap agen AI selama satu bulan dari tanggal langganan.",
      "yearlyDescription": "Langganan Tahunan: Penggunaan tak terbatas setiap agen AI selama satu tahun dari tanggal langganan",
      "subscribed": "Berlangganan",
      "freeUsing": "Menggunakan versi gratis...",
      "cancelSubscription": "Batalkan Langganan",
      "subscriptionValidUntil": "Langganan Anda berlaku hingga {date}",
      "monthlySubscribing": "Langganan bulanan aktif...",
      "yearlySubscribing": "Langganan tahunan aktif...",
      "cancelMonthly": "Batalkan Bulanan",
      "cancelYearly": "Batalkan Tahunan",
      "cancelConfirmTitle": "Pemberitahuan",
      "cancelMonthlyConfirm": "Pembatalan bulanan akan berlaku pada tanggal {date}. Anda perlu berlangganan lagi untuk menggunakan. Apakah Anda yakin ingin membatalkan?",
      "cancelYearlyConfirm": "Pembatalan tahunan akan berlaku pada tanggal {date}. Anda perlu berlangganan lagi untuk menggunakan. Apakah Anda yakin ingin membatalkan?",
      "month": "Bulan",
      "quarter": "Kuartal",
      "year": "Tahun"
    }
  }
};

// 更新语言文件
function updateLanguageFile(langCode, translations) {
  const filePath = path.join(__dirname, `../src/locales/${langCode}.json`);
  fs.writeFileSync(filePath, JSON.stringify(translations, null, 2), 'utf8');
  console.log(`✅ 已更新: ${langCode}.json`);
}

// 更新所有剩余语言文件
Object.entries(remainingLanguages).forEach(([langCode, translations]) => {
  updateLanguageFile(langCode, translations);
});

console.log('\n🎉 剩余语言文件更新完成！');
