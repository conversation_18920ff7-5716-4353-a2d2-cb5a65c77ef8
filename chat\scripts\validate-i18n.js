const fs = require('fs');
const path = require('path');

// 语言文件列表
const languages = [
  'zh-CN', 'zh-TW', 'en', 'vi', 'es', 'ar', 'id', 'pt', 'ja', 'ko', 'ms'
];

const localesDir = path.join(__dirname, '../src/locales');

// 获取对象的所有键路径
function getKeyPaths(obj, prefix = '') {
  const paths = [];
  for (const key in obj) {
    const currentPath = prefix ? `${prefix}.${key}` : key;
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      paths.push(...getKeyPaths(obj[key], currentPath));
    } else {
      paths.push(currentPath);
    }
  }
  return paths.sort();
}

// 验证语言文件
function validateLanguageFiles() {
  const results = {
    files: {},
    missingKeys: {},
    extraKeys: {},
    errors: []
  };

  // 读取所有语言文件
  for (const lang of languages) {
    const filePath = path.join(localesDir, `${lang}.json`);
    try {
      if (!fs.existsSync(filePath)) {
        results.errors.push(`Missing file: ${lang}.json`);
        continue;
      }
      
      const content = fs.readFileSync(filePath, 'utf8');
      const data = JSON.parse(content);
      const keyPaths = getKeyPaths(data);
      
      results.files[lang] = {
        path: filePath,
        keyCount: keyPaths.length,
        keys: keyPaths
      };
    } catch (error) {
      results.errors.push(`Error reading ${lang}.json: ${error.message}`);
    }
  }

  // 以中文为基准检查一致性
  if (results.files['zh-CN']) {
    const baseKeys = results.files['zh-CN'].keys;
    
    for (const lang of languages) {
      if (lang === 'zh-CN' || !results.files[lang]) continue;
      
      const currentKeys = results.files[lang].keys;
      const missing = baseKeys.filter(key => !currentKeys.includes(key));
      const extra = currentKeys.filter(key => !baseKeys.includes(key));
      
      if (missing.length > 0) {
        results.missingKeys[lang] = missing;
      }
      if (extra.length > 0) {
        results.extraKeys[lang] = extra;
      }
    }
  }

  return results;
}

// 运行验证
const results = validateLanguageFiles();

console.log('=== 国际化文件验证报告 ===\n');

// 文件统计
console.log('📁 文件统计:');
for (const [lang, info] of Object.entries(results.files)) {
  console.log(`  ${lang}: ${info.keyCount} 个翻译键`);
}
console.log();

// 错误报告
if (results.errors.length > 0) {
  console.log('❌ 错误:');
  results.errors.forEach(error => console.log(`  ${error}`));
  console.log();
}

// 缺失键报告
const missingCount = Object.keys(results.missingKeys).length;
if (missingCount > 0) {
  console.log('⚠️  缺失翻译键:');
  for (const [lang, keys] of Object.entries(results.missingKeys)) {
    console.log(`  ${lang}: ${keys.length} 个缺失`);
    keys.forEach(key => console.log(`    - ${key}`));
  }
  console.log();
}

// 额外键报告
const extraCount = Object.keys(results.extraKeys).length;
if (extraCount > 0) {
  console.log('🔍 额外翻译键:');
  for (const [lang, keys] of Object.entries(results.extraKeys)) {
    console.log(`  ${lang}: ${keys.length} 个额外`);
    keys.forEach(key => console.log(`    - ${key}`));
  }
  console.log();
}

// 总结
if (results.errors.length === 0 && missingCount === 0 && extraCount === 0) {
  console.log('✅ 所有语言文件结构一致，验证通过！');
} else {
  console.log('❌ 发现问题，需要修复。');
}

// 导出结果供其他脚本使用
module.exports = results;
