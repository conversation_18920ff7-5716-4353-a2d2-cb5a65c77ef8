import { DifyChatProvider } from '@dify-chat/core'
import { initResponsiveConfig } from '@dify-chat/helpers'
// import FingerPrintJS from '@fingerprintjs/fingerprintjs'
import { useMount } from 'ahooks'
import {Cookies} from './utils/cookieHandler'
import { BrowserRouter, type IRoute, Route, useHistory } from 'pure-react-router'
import { useEffect, useState } from 'react'
import AppService from './services/app/request'
import ChatPage from './pages/chat'
import DifyAppService from './services/app/restful'
// 导入国际化配置
import './i18n'
// 初始化响应式配置
initResponsiveConfig()

const routes: IRoute[] = [
	{ path: '/:appUuid', component: () => <ChatPage /> },
	// { path: '/app/:appId', component: () => <ChatPage /> },
	// { path: '/apps', component: () => <AppListPage /> },
	// { path: '/', component: () => <IndexPage /> },
]

/**
 * Dify Chat 的最小应用实例
 */
export default function App() {
	const [userId, setUserId] = useState<string>('nologin')
	const [userInfo, setUserInfo]:any = useState(null)
	const searchParams = new URLSearchParams((window as any).top.location.search)
	const history = useHistory()
	const match = history.location.pathname.match(/^\/ai-chat\/([^/]+)$/)
	const appUuid = match ? match[1] : ''	
	useMount(() => {
		const appservice = new  AppService()

		// // 模拟登录过程获取用户唯一标识
		// const loadFP = async () => {
		// 	const fp = await FingerPrintJS.load()
		// 	const result = await fp.get()
		// 	setUserId(result.visitorId)
		// }
		// loadFP()

		// 从cookie中获取userInfo
		const userInfoString = Cookies.get('userInfo')
		if (userInfoString) {
			setUserInfo(JSON.parse(userInfoString))
			const yudaoToken = Cookies.get('yudaoToken')
			if (yudaoToken&&!(appUuid == 'medsci-ask')||!(searchParams.get('fromPlatform'))) {
			} else {
				const userInfos = JSON.parse(userInfoString)
				appservice.getAiWriteToken({
						userId: userInfos.userId,
						userName: userInfos.userName,
						realName: userInfos.realName,
						avatar: userInfos.avatar,
						plaintextUserId: userInfos.plaintextUserId,
						mobile: userInfos.mobile,
						email: userInfos.email,
						fromPlatform: (appUuid == 'medsci-ask')?'medsci':null,
						appUuid: appUuid
				}).then(({data}:any) => {
					if (data?.token) {
						Cookies.set("yudaoToken", data.token);
						localStorage.setItem("hasuraToken", data.htoken);
						localStorage.setItem("openid", data.openid);
						localStorage.setItem("socialUserId", data.socialUserId);
						localStorage.setItem("socialType", data.socialType);
					  } else {
						console.error("登录失败: 未返回 token");
					  }
				})
			}
		}
	})

	useEffect(() => {
		if (userInfo) {
			const appservice = new  AppService()
			const yudaoToken = Cookies.get('yudaoToken')
			setUserId(userInfo.userName) // 更新 userId
			if (yudaoToken&&!(appUuid == 'medsci-ask')||(searchParams.get('fromPlatform'))) {
			} else {
				appservice.getAiWriteToken({
						userId: userInfo.userId,
						userName: userInfo.userName,
						realName: userInfo.realName,
						avatar: userInfo.avatar,
						plaintextUserId: userInfo.plaintextUserId,
						mobile: userInfo.mobile,
						email: userInfo.email,
						fromPlatform: ((appUuid == 'medsci-ask')||(searchParams.get('fromPlatform')))?'medsci':null,
						appUuid: appUuid
				}).then((response) => {
					const { data } = response || {};
					if (data?.token) {
						Cookies.set("yudaoToken", data.token);
						localStorage.setItem("hasuraToken", data.htoken);
						localStorage.setItem("openid", data.openid);
						localStorage.setItem("socialUserId", data.socialUserId);
						localStorage.setItem("socialType", data.socialType);
					  } else {
						console.error("登录失败: 未返回 token");
					  }
				})
			}
		}
	}, [userInfo]) // 依赖 userInfo

	useEffect(() => {
		if (userId) {
			console.log('Updated userId====', userId) // 在这里处理更新后的 userId
		}
	}, [userId]) // 依赖 userId
	useEffect(() => {
		Cookies.set(
			"ai_apps_lang",
			Cookies.get("ai_apps_lang")
			  ? Cookies.get("ai_apps_lang")
			  : (navigator as any).browserLanguage || navigator.language
		  );
	}, []) // 依赖 userId
	return (
		<BrowserRouter
			basename=""
			routes={routes}
		>
			<DifyChatProvider
				value={{
					mode: 'multiApp', //  singleApp
					user: userId,
					// 默认使用 localstorage, 如果需要使用其他存储方式，可以实现 DifyAppStore 接口后传入，异步接口实现参考 src/services/app/restful.ts
					appService: new DifyAppService(),
					enableSetting: false,
					// appConfig: {
					// 	requestConfig: {
					// 		apiBase: 'https://api.dify.ai',
					// 		apiKey: '/v1',
					// 	},
					// },
				}}
			>
				<Route />
			</DifyChatProvider>
		</BrowserRouter>
	)
}
