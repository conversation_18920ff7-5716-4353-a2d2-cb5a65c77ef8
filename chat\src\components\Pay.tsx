import { IDifyChatContextSingleApp, useDifyChat } from '@dify-chat/core'
// 假设 CSS 文件单独存放
import { useRequest } from 'ahooks'
import { message } from 'antd'
import { useTranslation } from 'react-i18next'
import Cookies from 'js-cookie'
import { QRCodeCanvas } from 'qrcode.react'
import React, { useEffect, useState } from 'react'
import { Dialog } from 'react-vant';
import '../assets/css/pay.css'
import AppService from '../services/app/request'

const Pay = ({
	userInfo,
	currentItem,
	onClose,
	appConfig,
	subStatusDetail,
}: {
	userInfo: any
	currentItem: any
	onClose?: (val: boolean) => void
	appConfig: any
	subStatusDetail: any
}) => {
	const { t } = useTranslation()

	userInfo = userInfo&&JSON.parse(userInfo)
	const [loading, setLoading] = useState(false)
	const [activeItem, setActiveItem]: any = useState({})
	const [active, setActive] = useState(null)
	const [payUrl, setPayUrl] = useState('')
	const [num, setNum] = useState(0)
	const [avatar, setAvatar] = useState(
		userInfo?.avatar || 'https://img.medsci.cn/web/img/user_icon.png',
	)
	const [isPc, setIsPc] = useState(window.innerWidth > 768)
	const [piId, setPiId] = useState(null)
	const [timer, setTimer] = useState(null)
  useEffect(() => {
		currentItem = language() == 'zh-CN' ? subStatusDetail : currentItem
    if(subStatusDetail?.feeTypes?.length >0 && language() == 'zh-CN'){
      subStatusDetail?.feeTypes.forEach((element,index) => {
      if(subStatusDetail.packageType == element.type){
        setActive(index)
        setActiveItem(element) 
      }
    });
    }
  
  }, [])
	const appserver = new AppService()

	const getDefaultLanguageCode = async () => {
		return 'zh-CN' // 模拟语言
	}

	const isUp =
		window.location.origin.includes('medon.com.cn') || window.location.origin.includes('medsci.cn')

	const changeImg = () => {
		setAvatar('https://img.medsci.cn/web/img/user_icon.png')
	}

	const toAgreement = () => {
		window.open('https://www.medsci.cn/about/index.do?id=27')
	}

	const checkScreenWidth = () => {
		setIsPc(window.innerWidth > 768)
	}

	const close = () => {
		if (timer) clearInterval(timer)
		onClose?.(false)
	}

	const CheckItem = (item: any, index: number) => {
		setActiveItem(item)
		setActive(index)
		if (item?.coinType == t('common.rmb') && item.feePrice !== 0) {
			subscribe(item, appConfig.id)
		}
	}

	const getStatus = (piId: any) => {
		const interval = setInterval(async () => {
			const res = await appserver.getSubOrder(
				{ piId: `${piId}` },
				{
					Authorization: `Bearer ${Cookies.get('yudaoToken')}`,
				},
			)
			if (res?.data?.payStatus == 'PAID') {
				window.location.reload()
				clearInterval(interval)
			}
		}, 2000)
		// 将 NodeJS.Timeout 类型转换为 React state 可接受的类型
		setTimer(interval as unknown as null)
	}
	const language = () => {
		return Cookies.get('ai_apps_lang')
			? Cookies.get('ai_apps_lang')
			: navigator.browserLanguage || navigator.language
	}
	const getNum = async () => {
		const res = await appserver.freeLimit(
			{},
			{
				Authorization: `Bearer ${Cookies.get('yudaoToken')}`,
			},
		)
		setNum(res?.data)
	}
	const subscribe = async (item: any, appUuid: string) => {
		if (!item?.coinType) {
			message.warning(t('common.pleaseSelectPeriod'))
			return
		}
		const languages = language()
		if (!userInfo?.userId) {
			if (!languages || languages == 'zh-CN') {
				(window as any).addLoginDom?.()
			} else {
				window.top.location.href = location.origin + '/' + languages + '/login'
			}
		} else {
			const subscriptionParams = {
				appUuid,
				priceId: item.priceId,
				monthNum: item.monthNum,
        packageKey:item.packageKey,
        packageType:item.type
			}
			try {
				setLoading(true)
				const res = await appserver.createSubscription(subscriptionParams, {
					Authorization: `Bearer ${Cookies.get('yudaoToken')}`,
				})
				if (res?.code == 0) {
					setLoading(false)
					if (item.coinType == t('common.rmb') && item.feePrice !== 0) {
						const payInfo = res.data
						const origin = window.location.origin
						const payLink =
							origin.includes('.medsci.cn') || origin.includes('.medon.com.cn')
								? `${origin}/payLink/${encodeURIComponent(payInfo)}`
								: `${origin}/payLink/${encodeURIComponent(payInfo)}`
						setPayUrl(payLink)
						setPiId(JSON.parse(payInfo).piId)
						getStatus(JSON.parse(payInfo).piId)
					} else {
						message.success(t('payment.subscriptionSuccess'))
						setTimeout(() => {
							window.top.location.href = res.data
						}, 1000)
					}
				}
			} catch (error) {
				setLoading(false)
				console.error(error)
			}
		}
	}
	useEffect(() => {
		getNum()
		if (currentItem.appType == 'writing') {
			localStorage.setItem(
				`appWrite-${appConfig.id}`,
				JSON.stringify({
					appUuid: appConfig.id,
					directoryMd: currentItem.directoryMd,
				}),
			)
		}
		checkScreenWidth()
		window.addEventListener('resize', checkScreenWidth)

		if (isPc && currentItem.feeTypes?.length == 1) {
			const feeType = currentItem.feeTypes[0]
			CheckItem(feeType, 0)
		}

		return () => {
			window.removeEventListener('resize', checkScreenWidth)
			if (timer) clearInterval(timer)
		}
	}, [currentItem, isPc])

	return (
		<div id="app">
			<div className="scale">
				<div className="micro_header">
					<div className="micro_left">
						<div className="avatar">
							<img
								src={avatar}
								onError={changeImg}
								alt=""
							/>
							<span className="t1">{userInfo?.realName || userInfo?.userName}</span>
						</div>
					</div>
					<div className="micro_right">
						<img
							src="https://static.medsci.cn/public-image/ms-image/78bf61f0-208d-11ee-ae3d-b3e9904fad92_关闭@2x.png"
							alt=""
							onClick={close}
						/>
					</div>
				</div>
				<div className="micro_main">
					<div className="micro_main_top">
						<div className="micro_main-sp">
							<div className="micro_main_temp">
								{(currentItem.feeTypes[0]?.coinType == 'USD' ||
									currentItem.feeTypes?.length > 1)&&language() == 'zh-CN' && (
									<div className="swiper-vip">
										{currentItem.feeTypes?.map(
											(
												item: { type: string; coinType: string; feePrice: number },
												index: number,
											) => (
												<div
													key={index}
													className={`swiper-vip-item ${(subStatusDetail?.packageType==t('payment.monthlySubscription')&&item.type==t('payment.free'))||(subStatusDetail?.packageType==t('payment.monthlySubscription')&&item.type==t('payment.yearlySubscription'))||((subStatusDetail?.packageType==t('payment.yearlySubscription')&&item.type==t('payment.free'))||(subStatusDetail?.packageType==t('payment.yearlySubscription')&&item.type==t('payment.monthlySubscription')))?'noClick':''}`}
													onClick={() => CheckItem(item, index)}
												>
													<div
														className="newer"
														style={{
															left: index % 4 == 0 && index !== 0 ? '6px' : '-1px',
														}}
													></div>
													<div
														className={`swiper-vip-item-child ${active == index ? 'sactvie' : ''}`}
													>
														<div className="title">{item.type}</div>
														<div className="pricePc">
															<span>{item.coinType == t('common.rmb') ? '¥' : '$'}</span> {item.feePrice}
														</div>
													</div>
												</div>
											),
										)}
									</div>
								)}
								                    {(currentItem.feeTypes[0]?.coinType == 'USD' ||
									currentItem.feeTypes?.length > 1)&&language() != 'zh-CN' && (
									<div className="swiper-vip">
										{currentItem.feeTypes?.map(
											(
												item: { type: string; coinType: string; feePrice: number },
												index: number,
											) => (
												<div
													key={index}
													className="swiper-vip-item"
													onClick={() => CheckItem(item, index)}
												>
													<div
														className="newer"
														style={{
															left: index % 4 == 0 && index !== 0 ? '6px' : '-1px',
														}}
													></div>
													<div
														className={`swiper-vip-item-child ${active == index ? 'sactvie' : ''}`}
													>
														<div className="title">{item.type}</div>
														<div className="pricePc">
															<span>{item.coinType == t('common.rmb') ? '¥' : '$'}</span> {item.feePrice}
														</div>
													</div>
												</div>
											),
										)}
									</div>
								)}
							</div>
						</div>
					</div>
					{language() && language() == 'zh-CN' ? (
						<div className="micro_main_middle">
							<div className="micro_main_middle_banner">
								<div className="micro_main_middle_title">
									<img src="https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png" />
									{t('payment.subscriptionDescription')}
								</div>
								<div className="micro_main_middle_content">
									<div>{t('payment.freeDescription', { num })}</div>
									<div>{t('payment.monthlyDescription')}</div>
									<div>{t('payment.yearlyDescription')}</div>
								</div>
							</div>
						</div>
					) : (
						<div className="micro_main_middle">
							<div className="micro_main_middle_banner">
								<div className="micro_main_middle_title">
									<img
										src="https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"
										alt=""
									/>
									{currentItem.name}
								</div>
								<div className="micro_main_middle_content">{currentItem.description}</div>
							</div>
						</div>
					)}
					{language() && language() == 'zh-CN' ? (
						<div className="micro_main_bottom onborder">
							{(subStatusDetail.subStatus == '1' || subStatusDetail.subStatus == '3') &&
								activeItem.type == subStatusDetail.packageType && (
									<div className="result">{subStatusDetail.subAt} {t('payment.subscribed')}</div>
								)}

							{subStatusDetail.subStatus == '1' &&
								subStatusDetail.packageType == t('payment.free') &&
								activeItem.type == t('payment.free') && <div className="result">{t('payment.freeUsing')}</div>}

							{subStatusDetail.subStatus == '3' && (
								<div className="result">{subStatusDetail.unSubAt} {t('payment.cancelSubscription')}</div>
							)}

							{subStatusDetail.subStatus == '3' && (
								<div className="result">{t('payment.subscriptionExpireAt', { date: subStatusDetail.expireAt })}</div>
							)}

							{subStatusDetail.packageType == t('payment.monthlySubscription') && subStatusDetail.subStatus == '1' && (
								<div className="result">{t('payment.monthlySubscribing')}</div>
							)}

							{subStatusDetail.packageType == t('payment.yearlySubscription') && subStatusDetail.subStatus == '1' && (
								<div className="result">{t('payment.yearlySubscribing')}</div>
							)}
							<div className="btns">
								{subStatusDetail.packageType == t('payment.monthlySubscription') && subStatusDetail.subStatus == '1' ? (
									<button className='cursor-pointer' onClick={() => {  Dialog.confirm({
                    title: t('payment.cancelConfirmTitle'),
                    message: t('payment.cancelMonthlyConfirm', { date: subStatusDetail.expireAt }),
                    confirmButtonColor: '#D7813F',
                    onCancel: () => {},
                    onConfirm:async () => {
                      const res = await appserver.cancelSubscription({},{
                        Authorization: `Bearer ${Cookies.get('yudaoToken')}`,
                      })
                      close()
                    }
                  }})}>{t('payment.cancelMonthly')}</button>
								) : null}
								{subStatusDetail.packageType == t('payment.yearlySubscription') && subStatusDetail.subStatus == '1' ? (
									<button className='cursor-pointer' onClick={ () => {  Dialog.confirm({
                    title: t('payment.cancelConfirmTitle'),
                    message: t('payment.cancelYearlyConfirm', { date: subStatusDetail.expireAt }),
                    confirmButtonColor: '#D7813F',
                    onCancel: () => {
                      close()
                    },
                    onConfirm:async () => {
                      const res = await appserver.cancelSubscription({},{
                      Authorization: `Bearer ${Cookies.get('yudaoToken')}`,
                    })
                    close()
                    }
                  }})}>{t('payment.cancelYearly')}</button>
								) : null}
							</div>
							<div className="micro_pay">
								{(activeItem.feePrice > 0 &&
									activeItem.type != t('payment.free') &&
									subStatusDetail.subStatus == '0') ||
								(activeItem.type != t('payment.free') && subStatusDetail.subStatus == '2') ||
								(subStatusDetail.packageType == t('payment.free') && activeItem.type != t('payment.free')) ? (
									<div className="micro_pay">
										<div className="micro_pay_right">
											{loading && <div className="noQrCode"></div>}
											{!loading && payUrl && (
												<div className="qr-code">
													{/* 替换为 qrcode.react 或其他二维码库 */}
													<QRCodeCanvas
														value={payUrl}
														size={131}
														fgColor="#000"
														level="L" // 纠错等级高
													/>
												</div>
											)}
											<div className="price">
												<div className="micro_way">
													<div className="box">
														<img
															src="https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png"
															alt=""
														/>
													</div>
													<span>{t('payment.supportAlipayPayment')}</span>
												</div>
												<span className="t1">
													{t('payment.supportAlipayPayment')}
													<span className="bd">{activeItem.feePrice}</span>
													{activeItem.coinType == t('common.rmb') ? '¥' : '$'}/
													{activeItem.monthNum == 3
														? t('payment.quarter')
														: activeItem.monthNum == 12
															? t('payment.year')
															: t('payment.month')}
												</span>
												<span className="t2">
													{t('payment.medSciAccount')}：{userInfo?.userName}
												</span>
												<span
													className="t3"
													onClick={toAgreement}
												>
													{t('payment.pleaseActivateAfterAgreement')}
													<img
														src="https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png"
														alt=""
													/>
												</span>
											</div>
										</div>
									</div>
								) : null}
							</div>
						</div>
					) : null}

					{activeItem.coinType && activeItem.feePrice !== 0&&language() != 'zh-CN' && activeItem?.coinType == t('common.rmb') && (
						<div className="micro_main_bottom">
							<div className="micro_pay">
								<div className="micro_pay_right">
									{loading && <div className="noQrCode"></div>}
									{!loading && payUrl && (
										<div className="qr-code">
											{/* 替换为 qrcode.react 或其他二维码库 */}
											<QRCodeCanvas
												value={payUrl}
												size={131}
												fgColor="#000"
												level="L" // 纠错等级高
											/>
										</div>
									)}
									<div className="price">
										<div className="micro_way">
											<div className="box">
												<img
													src="https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png"
													alt=""
												/>
											</div>
											<span>{t('payment.supportAlipayPayment')}</span>
										</div>
										<span className="t1">
											{t('payment.supportAlipayPayment')}
											<span className="bd">{activeItem.feePrice}</span>
											{activeItem.coinType == t('common.rmb') ? '¥' : '$'}/
											{activeItem.monthNum == 3
												? t('payment.quarter')
												: activeItem.monthNum == 12
													? t('payment.year')
													: t('payment.month')}
										</span>
										<span className="t2">
											{t('payment.medSciAccount')}：{userInfo?.userName}
										</span>
										<span
											className="t3"
											onClick={toAgreement}
										>
											{t('payment.pleaseActivateAfterAgreement')}
											<img
												src="https://static.medsci.cn/public-image/ms-image/17ae6920-5be4-11ec-8e2f-1389d01aad85_right.png"
												alt=""
											/>
										</span>
									</div>
								</div>
							</div>
						</div>
					)}
					{activeItem.coinType&&language() == 'zh-CN' && activeItem.feePrice == 0&&(subStatusDetail.subStatus=='0'||subStatusDetail.subStatus=='2') && (
						<div className="btns">
							<button
								className="subscribe-btn"
								onClick={() => subscribe(activeItem, appConfig.id)}
							>
								{t('payment.freeTrial')}
							</button>
						</div>
					)}
					{activeItem.coinType &&language() != 'zh-CN' && activeItem.feePrice == 0&& (
						<div className="btns">
							<button
								className="subscribe-btn"
								onClick={() => subscribe(activeItem, appConfig.id)}
							>
								{t('payment.freeTrial')}
							</button>
						</div>
					)}
					{activeItem.coinType && activeItem.feePrice > 0 && activeItem?.coinType == 'USD' && (
						<div className="btns">
							<button
								className="subscribe-btn"
								onClick={() => subscribe(activeItem, appConfig.id)}
							>
								{t('market.subscribe')}
							</button>
						</div>
					)}
				</div>
			</div>
		</div>
	)
}

export default Pay
