import { Button, Checkbox, message } from 'antd'
// Assuming CSS is extracted to a separate file
import Cookies from 'js-cookie'
import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Dialog } from 'react-vant'

import '../assets/css/vip.css'
import AppService from '../services/app/request'

const PayMobile = ({
	userInfo,
	currentItem = {},
	onClose,
	subStatusDetail = {},
}: {
	userInfo: any
	currentItem: any
	onClose: () => void
	subStatusDetail: any
}) => {
	const { t } = useTranslation()
	const [isCheckW, setIsCheckW] = useState(true)
	const [isCheckZ, setIsCheckZ] = useState(true)
	const [isLogin, setIsLogin] = useState(false)
	const [activeItem, setActiveItem]: any = useState({})
	const [isWx, setIsWx] = useState(false)
	const [isFromMedsci, setIsFromMedsci] = useState(false)
	const [radio, setRadio] = useState(false)
	const [isShaking, setIsShaking] = useState(false)
	const [num, setNum] = useState(0)
	const [avatar, setAvatar] = useState(
		userInfo?.avatar || 'https://img.medsci.cn/web/img/user_icon.png',
	)
	const scrollRef = useRef(null)
	const appserver = new AppService()
	const isUp =
		window.location.origin.includes('medon.com.cn') || window.location.origin.includes('medsci.cn')
	useEffect(() => {
		getNum()
		// Set viewport height
		const windowVH = window.innerHeight / 100
		document.documentElement.style.setProperty('--vh', `${windowVH}px`)

		// Initialize app writing data
		if (currentItem.appType == 'writing') {
			localStorage.setItem(
				`appWrite-${currentItem.appUuid}`,
				JSON.stringify({
					appUuid: currentItem.appUuid,
					directoryMd: currentItem.directoryMd,
				}),
			)
		}

		// Auto-select fee type if only one exists
		if (currentItem.feeTypes?.length == 1) {
			if (currentItem.feeTypes[0].feePrice >= 0) {
				setActiveItem(currentItem.feeTypes[0])
			}
		}
		// Check login status
		if (userInfo&&JSON.parse(userInfo)?.userId) {
			setIsLogin(true)
		}

		// Check if from Medsci
		const params = new URLSearchParams(window.top.location.search)
		if (params.get('source') == 'medsci') {
			setIsFromMedsci(true)
		}
	}, [currentItem, userInfo])
	useEffect(() => {
		currentItem.feeTypes = language() == 'zh-CN' ? subStatusDetail?.feeTypes : currentItem.feeTypes
		if (subStatusDetail?.feeTypes?.length > 0 && language() == 'zh-CN') {
			subStatusDetail?.feeTypes.forEach((element, index) => {
				if (subStatusDetail?.packageType == element.type) {
					setActiveItem(element)
				}
			})
		}
	}, [])
	const isMedSci = () => {
		return navigator.userAgent.includes('medsci_app')
	}

	const changeImg = () => {
		setAvatar('https://img.medsci.cn/web/img/user_icon.png')
	}
	const language = () => {
		return Cookies.get('ai_apps_lang')
			? Cookies.get('ai_apps_lang')
			: navigator.browserLanguage || navigator.language
	}
	const login = () => {
		const languages = language()
		if (!languages || languages == 'zh-CN') {
			// 为了解决 TypeScript 类型错误，需要先声明 window.addLoginDom 方法
			;(window as any).addLoginDom?.()
		} else {
			window.top.location.href = location.origin + '/' + languages + '/login'
		}
		// Replace with actual login logic
	}

	const checkFn1 = () => {
		setIsCheckW(true)
		setIsCheckZ(false)
	}

	const checkFn2 = () => {
		setIsCheckW(false)
		setIsCheckZ(true)
	}

	const goAgreent = () => {
		const url =
			process.env.NODE_ENV == 'production'
				? 'https://www.medsci.cn/about/index.do?id=27'
				: 'https://portal-test.medon.com.cn/agreement/27'
		if (isMedSci()) {
			window.top.location.href = url
		} else {
			window.open(url)
		}
	}
	const getNum = async () => {
		const res = await appserver.freeLimit(
			{},
			{
				Authorization: `Bearer ${Cookies.get('yudaoToken')}`,
			},
		)
		setNum(res?.data)
	}
	const CheckItem = (item: any) => {
		setActiveItem(item)
	}
	//  选择支付方式
	const changeRadio = (e: any) => {
		setRadio(e.target.checked)
	}
	const subscribe = async (item: any, appUuid: any, type: any) => {
		if (!radio && type) {
			setIsShaking(true)
			setTimeout(() => setIsShaking(false), 500)
			return
		}

		const languages = language()
		if (!userInfo||!JSON.parse(userInfo)?.userId) {
			if (!languages || languages == 'zh-CN') {
				login()
			} else {
				window.top.location.href = location.origin + '/' + languages + '/login'
			}
		} else {
			const subscriptionParams = {
				appUuid,
				priceId: item.priceId,
				monthNum: item.monthNum,
				packageKey: item.packageKey,
				packageType: item.type,
			}
			try {
				const res = await appserver.createSubscription(subscriptionParams, {
					Authorization: `Bearer ${Cookies.get('yudaoToken')}`,
				})
				if (res.code == 0) {
					if (item.coinType == t('common.rmb') && item.feePrice !== 0) {
						const resUrl = await appserver.createAliSub(JSON.parse(res.data))
						if (resUrl && resUrl.code == 0) {
							window.top.location.href = resUrl.data
						}
					} else {
						setTimeout(() => {
							window.top.location.href = res.data
						}, 1000)
					}
				}
			} catch (error) {
				message.error(t('common.subscriptionFailed'))
			}
		}
	}

	return (
		<div className={`vip ${isMedSci() ? 'sp' : ''}`}>
			{!isMedSci() && (
				<div className="vip-head text-center py-2 text-lg font-medium text-gray-800">
					{currentItem.name}
				</div>
			)}
			<div className="vip-introduce bg-gray-900 pt-9 pb-4 relative">
				<img
					className="crown absolute right-1 top-0 h-9 object-contain"
					src="https://static.medsci.cn/public-image/ms-image/1dcd7d10-58af-11ec-8e2f-1389d01aad85_crown.png"
					alt=""
				/>
				<div className="box mx-3 flex items-center justify-between bg-[url('https://static.medsci.cn/public-image/ms-image/21ad8b80-58b1-11ec-8e2f-1389d01aad85_vip-h5.png')] bg-cover p-3 rounded">
					{!isLogin ? (
						<div className="box-left-1 flex items-center">
							<img
								src="https://img.medsci.cn/web/img/user_icon.png"
								alt=""
								className="w-8 h-8 rounded-full mr-3"
							/>
							<div className="left2">
								<span
									className="t1 text-lg text-[#58342c] cursor-pointer"
									onClick={login}
								>
									{t('common.loginNow')}
								</span>
								<span className="t2 text-sm text-[#7f5947] mt-1">{t('common.pleaseLoginFirst')}</span>
							</div>
						</div>
					) : (
						<div className="box-left flex items-center">
							<img
								className="avatar w-8 h-8 rounded-full mr-3"
								src={avatar}
								alt=""
								onError={changeImg}
							/>
							<div className="box-word">
								<span className="t1 text-lg text-[#58342c]">
									{JSON.parse(userInfo).realName || JSON.parse(userInfo).userName}
								</span>
							</div>
						</div>
					)}
				</div>
			</div>
			<div style={{ paddingBottom: (language() == 'zh-CN' &&(subStatusDetail.subStatus == '3'||(subStatusDetail.subStatus == '1'&&subStatusDetail.packageType=='免费'))&&activeItem.type == subStatusDetail.packageType)? '0':'' }} className="vip-main bg-white rounded-t-3xl -mt-14 z-10 ">
				<div className="vip-one pl-3">
					<div className="big overflow-auto mr-3">
						{language() == 'zh-CN' ? (
							<ul
								ref={scrollRef}
								className="flex mt-2 mb-1"
							>
								{currentItem.feeTypes?.map((item: any, index: any) => (
									<li
										key={index}
										className={`min-w-[135px] w-[135px] bg-white border border-gray-300 rounded-lg mr-3 text-center cursor-pointer ${
											item.type == activeItem.type ? 'sactvie' : ''
										} ${(subStatusDetail?.packageType==t('payment.monthlySubscription')&&item.type==t('payment.free'))||(subStatusDetail?.packageType==t('payment.monthlySubscription')&&item.type==t('payment.yearlySubscription'))||((subStatusDetail?.packageType==t('payment.yearlySubscription')&&item.type==t('payment.free'))||(subStatusDetail?.packageType==t('payment.yearlySubscription')&&item.type==t('payment.monthlySubscription')))?'noClick':''}`}
										onClick={() => CheckItem(item)}
									>
										<div className="title text-center ellipsis-2-lines mx-auto mt-6 max-w-[102.5px] text-sm font-medium text-gray-800">
											{item.type}
										</div>
										<div className="prices text-2xl font-bold text-orange-400 mt-2 mb-6">
											<span className="text-lg">{item.coinType == t('common.rmb') ? '¥' : '$'}</span>
											{item.feePrice}
										</div>
										{item.originalPrice && (
											<div className="isfava relative text-xs text-gray-500 line-through">
												{item.coinType == t('common.rmb') ? '¥' : '$'}
												{item.feePrice}
											</div>
										)}
									</li>
								))}
							</ul>
						) : (
							<ul
								ref={scrollRef}
								className="flex mt-2 mb-1"
							>
								{currentItem.feeTypes?.map((item: any, index: any) => (
									<li
										key={index}
										className={`min-w-[135px] w-[135px] bg-white border border-gray-300 rounded-lg mr-3 text-center cursor-pointer ${
											item.type == activeItem.type ? 'sactvie' : ''
										}`}
										onClick={() => CheckItem(item)}
									>
										<div className="title text-center ellipsis-2-lines mx-auto mt-6 max-w-[102.5px] text-sm font-medium text-gray-800">
											{item.type}
										</div>
										<div className="prices text-2xl font-bold text-orange-400 mt-2 mb-6">
											<span className="text-lg">{item.coinType == t('common.rmb') ? '¥' : '$'}</span>
											{item.feePrice}
										</div>
										{item.originalPrice && (
											<div className="isfava relative text-xs text-gray-500 line-through">
												{item.coinType == t('common.rmb') ? '¥' : '$'}
												{item.feePrice}
											</div>
										)}
									</li>
								))}
							</ul>
						)}
					</div>
				</div>
				{language() == 'zh-CN' ? (
					<div>
						<div
							className="vip-two"
							style={{ paddingBottom: '0' }}
						>
							<div className="vip-two_banner">
								<div className="vip-two_title">
									<img src="https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png" />
									{t('payment.subscriptionDescription')}
								</div>
								<div className="vip-two_content">
									<div>{t('payment.freeDescription', { num })}</div>
									<div>{t('payment.monthlyDescription')}</div>
									<div>{t('payment.yearlyDescription')}</div>
								</div>
							</div>
						</div>
						{(subStatusDetail?.subStatus == '1' || subStatusDetail?.subStatus == '3') &&
							activeItem.type == subStatusDetail?.packageType && (
								<div style={{ textAlign: 'center', fontSize: '14px' }}>
									{subStatusDetail?.subAt} {t('payment.subscribed')}
								</div>
							)}
						{subStatusDetail?.subStatus == '1' &&
							subStatusDetail?.packageType == t('payment.free') &&
							activeItem.type == t('payment.free') && (
								<div style={{ textAlign: 'center', fontSize: '14px' }}>{t('payment.freeUsing')}</div>
							)}

						{subStatusDetail?.subStatus == '3'&&activeItem.type==subStatusDetail.packageType && (
							<div style={{ textAlign: 'center', fontSize: '14px' }}>
								{subStatusDetail?.unSubAt} {t('payment.cancelSubscription')}
							</div>
						)}

						{subStatusDetail?.subStatus == '3'&&activeItem.type==subStatusDetail.packageType && (
							<div style={{ textAlign: 'center', fontSize: '14px' }}>
								{t('payment.subscriptionExpireAt', { date: subStatusDetail?.expireAt })}
							</div>
						)}
						{subStatusDetail?.packageType == t('payment.monthlySubscription') && subStatusDetail?.subStatus == '1'}
						{subStatusDetail?.packageType == t('payment.monthlySubscription') && subStatusDetail?.subStatus == '1' ? (
							<div style={{ textAlign: 'center', fontSize: '14px' }}>{t('payment.monthlySubscribing')}</div>
						) : null}

						{subStatusDetail?.packageType == t('payment.yearlySubscription') && subStatusDetail?.subStatus == '1' && (
							<div style={{ textAlign: 'center', fontSize: '14px' }}>{t('payment.yearlySubscribing')}</div>
						)}
					</div>
				) : (
					<div className="vip-two border border-gray-200 rounded m-3 p-2">
						<div className="vip-two_banner bg-[url('https://img.medsci.cn/202503/48a49f899b4544e39a864d1821b3555a-lRXRIQx3q1VB.png')] bg-cover p-3">
							<div className="vip-two_title flex items-center text-sm font-medium text-orange-600">
								<img
									src="https://img.medsci.cn/202503/36f8fcc00cb841eaaa9ff81b3b225285-Ar3LIaiI1GQo.png"
									alt=""
									className="w-2 h-2 mx-1"
								/>
								{currentItem.name}
							</div>
							<div className="vip-two_content bg-white p-3 text-sm text-gray-700 leading-5 max-h-[calc(var(--vh)*20)] overflow-auto rounded">
								{currentItem.description}
							</div>
						</div>
					</div>
				)}
				{language() == 'zh-CN' && (
					<div
						className={`vip-pay btns ${(subStatusDetail.packageType == t('payment.monthlySubscription') && subStatusDetail.subStatus == '1') || (subStatusDetail.packageType == t('payment.yearlySubscription') && subStatusDetail.subStatus == '1') ? '' : 'CN_btns'}`}
					>
						
						{subStatusDetail?.packageType == t('payment.monthlySubscription') && subStatusDetail?.subStatus == '1' && (
							<Button
								onClick={() => {
									Dialog.confirm({
										title: t('payment.cancelConfirmTitle'),
										message: t('payment.cancelMonthlyConfirm', { date: subStatusDetail?.expireAt }),
										confirmButtonColor: '#D7813F',
										onCancel: () => {},
										onConfirm: async () => {
											const res = await appserver.cancelSubscription(
												{},
												{
													Authorization: `Bearer ${Cookies.get('yudaoToken')}`,
												},
											)
											onClose()
										},
									})
								}}
								type="primary"
							>
								{t('payment.cancelMonthly')}
							</Button>
						)}
						{subStatusDetail?.packageType == t('payment.yearlySubscription') && subStatusDetail?.subStatus == '1' && (
							<Button
								onClick={() => {
									Dialog.confirm({
										title: t('payment.cancelConfirmTitle'),
										message: t('payment.cancelYearlyConfirm', { date: subStatusDetail?.expireAt }),
										confirmButtonColor: '#D7813F',
										onCancel: () => {},
										onConfirm: async () => {
											const res = await appserver.cancelSubscription(
												{},
												{
													Authorization: `Bearer ${Cookies.get('yudaoToken')}`,
												},
											)
											onClose()
										},
									})
								}}
								type="primary"
							>
								{t('payment.cancelYearly')}
							</Button>
						)}
					</div>
				)}
				{(activeItem.feePrice > 0 &&
					activeItem?.coinType == t('common.rmb') &&
					language() == 'zh-CN' &&
					activeItem.type != t('payment.free') &&
					subStatusDetail?.subStatus == '0') ||
					(activeItem.type != t('payment.free') && subStatusDetail?.subStatus == '2') ||
					(subStatusDetail?.packageType == t('payment.free') && activeItem.type != t('payment.free') && (
						<div className="vip-three mt-3">
							<div
								className={`pay mx-3 p-4 rounded-lg bg-gradient-to-b from-[#fffaf6] to-[#fff8f0] ${isWx ? 'h-28' : ''}`}
							>
								<img
									src="https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png"
									alt=""
									className="w-15 mb-3"
								/>
								<div
									className="item flex justify-between items-center bg-white border border-orange-300 rounded p-2 mb-3 cursor-pointer"
									onClick={checkFn2}
								>
									<div className="item-left flex items-center">
										<img
											src="https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png"
											alt=""
											className="w-5 h-5 mr-2"
										/>
										<span className="text-sm text-gray-800">{t('payment.supportAlipayPayment')}</span>
									</div>
									<div className="item-right w-5 h-5">
										{isCheckZ && (
											<img
												src="https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png"
												alt=""
												className="w-full"
											/>
										)}
									</div>
								</div>
							</div>
						</div>
					))}
				{activeItem.feePrice > 0 && activeItem?.coinType == t('common.rmb') && language() != 'zh-CN' && (
					<div className="vip-three mt-3">
						<div
							className={`pay mx-3 p-4 rounded-lg bg-gradient-to-b from-[#fffaf6] to-[#fff8f0] ${isWx ? 'h-28' : ''}`}
						>
							<img
								src="https://static.medsci.cn/public-image/ms-image/d465fcf0-5c9c-11ec-8e2f-1389d01aad85_payway.png"
								alt=""
								className="w-15 mb-3"
							/>
							<div
								className="item flex justify-between items-center bg-white border border-orange-300 rounded p-2 mb-3 cursor-pointer"
								onClick={checkFn2}
							>
								<div className="item-left flex items-center">
									<img
										src="https://static.medsci.cn/public-image/ms-image/19eef410-5893-11ec-8e2f-1389d01aad85_zfb.png"
										alt=""
										className="w-5 h-5 mr-2"
									/>
									<span className="text-sm text-gray-800">{t('payment.supportAlipayPayment')}</span>
								</div>
								<div className="item-right w-5 h-5">
									{isCheckZ && (
										<img
											src="https://static.medsci.cn/public-image/ms-image/e1bd35d0-5c9c-11ec-8e2f-1389d01aad85_checked.png"
											alt=""
											className="w-full"
										/>
									)}
								</div>
							</div>
						</div>
					</div>
				)}
			</div>
			{activeItem.feePrice >= 0 &&
			language() == 'zh-CN' &&
			((activeItem.type== t('payment.free')&&subStatusDetail.subStatus=='0')||(activeItem.type!= t('payment.free')&&subStatusDetail.subStatus=='0')||(activeItem.type!= t('payment.free')&&subStatusDetail.subStatus=='2')||(subStatusDetail.packageType== t('payment.free')&&activeItem.type!=t('payment.free'))) ? (
				<div
					className={`vip-pay btns fixed bottom-0 w-full bg-white shadow-lg flex justify-around items-center h-24 z-20  ${(subStatusDetail?.subStatus == '1' || subStatusDetail?.subStatus == '3') && activeItem.type == t('payment.free') ? 'CN_btns' : ''}`}
				>
					{activeItem.feePrice > 0 ? (
						<>
							<div className="pay-left ml-2">
								<div className="t1 text-sm font-medium text-orange-400 mb-1">
									{currentItem.name}
								</div>
								<div
									className={`t2 flex items-start text-xs text-gray-500 ${isShaking ? 'shake' : ''}`}
								>
									<Checkbox
										checked={radio}
										onChange={changeRadio}
										style={{ fontSize: '14px' }}
										className="mr-1"
									/>
									<span
										onClick={goAgreent}
										className="cursor-pointer"
									>
										{t('payment.pleaseActivateAfterAgreement')}
									</span>
								</div>
							</div>
							<div
								className="pay-right mr-2 w-48 h-12 bg-gradient-to-r from-[#fde39b] to-[#fbd786] rounded-full flex items-center justify-center cursor-pointer"
								onClick={() => subscribe(activeItem, currentItem.appUuid, 'ali')}
							>
								<span className="text-sm text-[#614018]">
									{activeItem.feePrice}元{t('payment.subscribe')}
								</span>
							</div>
						</>
					) : (
						<Button
							className="w-48 h-12 rounded-full"
							onClick={() => subscribe(activeItem, currentItem.appUuid, '')}
						>
							{activeItem.feePrice == 0 ? t('payment.freeTrial') : t('payment.subscribe')}
						</Button>
					)}
				</div>
			) : null}
			{activeItem.feePrice >= 0 && language() != 'zh-CN' ? (
				<div className="vip-pay btns fixed bottom-0 w-full bg-white shadow-lg flex justify-around items-center h-24 z-20">
					{activeItem.feePrice > 0 ? (
						<>
							<div className="pay-left ml-2">
								<div className="t1 text-sm font-medium text-orange-400 mb-1">
									{currentItem.name}
								</div>
								<div
									className={`t2 flex items-start text-xs text-gray-500 ${isShaking ? 'shake' : ''}`}
								>
									<Checkbox
										checked={radio}
										onChange={changeRadio}
										style={{ fontSize: '14px' }}
										className="mr-1"
									/>
									<span
										onClick={goAgreent}
										className="cursor-pointer"
									>
										{t('payment.pleaseActivateAfterAgreement')}
									</span>
								</div>
							</div>
							<div
								className="pay-right mr-2 w-48 h-12 bg-gradient-to-r from-[#fde39b] to-[#fbd786] rounded-full flex items-center justify-center cursor-pointer"
								onClick={() => subscribe(activeItem, currentItem.appUuid, 'ali')}
							>
								<span className="text-sm text-[#614018]">
									{activeItem.feePrice}元{t('payment.subscribe')}
								</span>
							</div>
						</>
					) : (
						<Button
							type="primary"
							className="w-48 h-12 rounded-full"
							onClick={() => subscribe(activeItem, currentItem.appUuid, '')}
						>
							{activeItem.feePrice == 0 ? t('payment.freeTrial') : t('payment.subscribe')}
						</Button>
					)}
				</div>
			) : null}
		</div>
	)
}

export default PayMobile
