import React from 'react'
import { Card, Space, Button, Typography, Divider } from 'antd'
import { useI18n } from '../hooks/useI18n'
import LanguageSwitcher from './language-switcher'

const { Title, Paragraph, Text } = Typography

export const I18nDemo: React.FC = () => {
  const { t } = useI18n()

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={2}>{t('app.title')}</Title>
            <LanguageSwitcher />
          </div>
          
          <Paragraph>{t('app.description')}</Paragraph>
          
          <Divider>{t('common.common')}</Divider>
          
          <Space wrap>
            <Button type="primary">{t('common.confirm')}</Button>
            <Button>{t('common.cancel')}</Button>
            <Button>{t('common.save')}</Button>
            <Button danger>{t('common.delete')}</Button>
            <Button>{t('common.edit')}</Button>
            <Button>{t('common.add')}</Button>
          </Space>
          
          <Divider>{t('chat.title')}</Divider>
          
          <Space wrap>
            <Button>{t('chat.newChat')}</Button>
            <Button>{t('chat.send')}</Button>
            <Button>{t('chat.clearHistory')}</Button>
            <Button>{t('chat.settings')}</Button>
            <Button>{t('chat.export')}</Button>
          </Space>
          
          <Divider>示例文本 / Example Text</Divider>
          
          <Space direction="vertical">
            <Text strong>{t('chat.placeholder')}</Text>
            <Text type="secondary">{t('chat.thinking')}</Text>
            <Text type="success">{t('common.success')}</Text>
            <Text type="warning">{t('common.warning')}</Text>
            <Text type="danger">{t('common.error')}</Text>
          </Space>
        </Space>
      </Card>
    </div>
  )
}

export default I18nDemo
