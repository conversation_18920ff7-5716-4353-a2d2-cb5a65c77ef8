import React from 'react'
import { Select } from 'antd'
import { GlobalOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'

const { Option } = Select

interface LanguageSwitcherProps {
  className?: string
  size?: 'small' | 'middle' | 'large'
  showIcon?: boolean
}

const languages = [
  { code: 'zh-CN', name: '简体中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
]

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  className,
  size = 'middle',
  showIcon = true,
}) => {
  const { i18n } = useTranslation()

  const handleLanguageChange = (value: string) => {
    i18n.changeLanguage(value)
  }

  return (
    <Select
      value={i18n.language}
      onChange={handleLanguageChange}
      className={className}
      size={size}
      style={{ minWidth: 120 }}
      suffixIcon={showIcon ? <GlobalOutlined /> : undefined}
      popupMatchSelectWidth={false}
    >
      {languages.map((lang) => (
        <Option key={lang.code} value={lang.code}>
          <span style={{ marginRight: 8 }}>{lang.flag}</span>
          {lang.name}
        </Option>
      ))}
    </Select>
  )
}

export default LanguageSwitcher
