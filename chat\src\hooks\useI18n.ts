import { useTranslation } from 'react-i18next'

export const useI18n = () => {
  const { t, i18n } = useTranslation()

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng)
  }

  const getCurrentLanguage = () => {
    return i18n.language
  }

  const isLanguage = (lng: string) => {
    return i18n.language === lng
  }

  const isChinese = () => {
    return i18n.language.startsWith('zh')
  }

  const isEnglish = () => {
    return i18n.language === 'en'
  }

  return {
    t,
    i18n,
    changeLanguage,
    getCurrentLanguage,
    isLanguage,
    isChinese,
    isEnglish,
  }
}

export default useI18n
