# 国际化配置使用说明

## 概述

本项目使用 `react-i18next` 实现国际化功能，支持中文和英文两种语言。

## 文件结构

```
src/
├── i18n/
│   ├── index.ts         # i18n 配置文件
│   └── README.md        # 使用说明
├── locales/             # 语言包文件夹
│   ├── zh-CN.json       # 简体中文
│   ├── zh-TW.json       # 繁体中文
│   ├── en.json          # 英文
│   ├── vi.json          # 越南语
│   ├── es.json          # 西班牙语
│   ├── ar.json          # 阿拉伯语
│   ├── id.json          # 印尼语
│   ├── pt.json          # 葡萄牙语
│   ├── ja.json          # 日语
│   ├── ko.json          # 韩语
│   └── ms.json          # 马来语
├── components/
│   └── language-switcher.tsx  # 语言切换组件
└── hooks/
    └── useI18n.ts       # 国际化 Hook
```

## 使用方法

### 1. 在组件中使用翻译

```tsx
import { useTranslation } from 'react-i18next'
// 或者使用封装的 hook
import { useI18n } from '@/hooks/useI18n'

function MyComponent() {
  const { t } = useTranslation()
  // 或者
  const { t } = useI18n()

  return (
    <div>
      <h1>{t('app.title')}</h1>
      <p>{t('app.description')}</p>
      <button>{t('common.confirm')}</button>
    </div>
  )
}
```

### 2. 语言切换

```tsx
import LanguageSwitcher from '@/components/language-switcher'

function Header() {
  return (
    <div>
      <LanguageSwitcher />
    </div>
  )
}
```

### 3. 手动切换语言

```tsx
import { useI18n } from '@/hooks/useI18n'

function MyComponent() {
  const { changeLanguage, getCurrentLanguage, isChinese } = useI18n()

  const handleSwitchToChinese = () => {
    changeLanguage('zh-CN')
  }

  const handleSwitchToEnglish = () => {
    changeLanguage('en')
  }

  return (
    <div>
      <p>当前语言: {getCurrentLanguage()}</p>
      <p>是否中文: {isChinese() ? '是' : '否'}</p>
      <button onClick={handleSwitchToChinese}>切换到中文</button>
      <button onClick={handleSwitchToEnglish}>Switch to English</button>
    </div>
  )
}
```

## 语言包结构

语言包采用嵌套的 JSON 结构，便于组织和管理：

```json
{
  "common": {
    "confirm": "确认",
    "cancel": "取消"
  },
  "chat": {
    "title": "智能对话",
    "send": "发送"
  },
  "app": {
    "title": "AI智能助手"
  }
}
```

## 添加新的翻译

1. 在 `src/locales/zh-CN.json` 中添加中文翻译
2. 在对应的语言文件中添加翻译（如 `en.json`、`ja.json` 等）
3. 确保所有语言文件的 key 结构保持一致
4. 如果需要添加新语言：
   - 在 `src/locales/` 目录下创建新的语言文件
   - 在 `src/i18n/index.ts` 中导入并添加到 resources 对象
   - 在 `src/components/language-switcher.tsx` 中添加到 languages 数组

## 支持的语言

- `zh-CN`: 简体中文 🇨🇳
- `zh-TW`: 繁體中文 🇹🇼
- `en`: English 🇺🇸
- `vi`: Tiếng Việt 🇻🇳
- `es`: Español 🇪🇸
- `ar`: العربية 🇸🇦
- `id`: Bahasa Indonesia 🇮🇩
- `pt`: Português 🇧🇷
- `ja`: 日本語 🇯🇵
- `ko`: 한국어 🇰🇷
- `ms`: Bahasa Melayu 🇲🇾

## 语言检测

系统会按以下顺序检测用户语言：
1. Cookie 中保存的语言设置
2. localStorage 中保存的语言设置
3. 浏览器语言设置
4. HTML 标签的 lang 属性

## 配置说明

- **fallbackLng**: 默认语言为中文 (`zh-CN`)
- **Cookie 域名**: 开发环境为 `localhost`，生产环境为 `.medsci.cn`
- **Cookie 有效期**: 160 分钟
- **调试模式**: 生产环境关闭

## 注意事项

1. 翻译 key 使用点号分隔的层级结构，如 `common.confirm`
2. 添加新翻译时，请确保中英文都有对应的翻译
3. 语言切换会自动保存到 Cookie 和 localStorage
4. 组件会自动响应语言变化，无需手动刷新
