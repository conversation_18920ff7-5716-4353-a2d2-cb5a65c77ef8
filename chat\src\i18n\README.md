# 国际化配置使用说明

## 概述

本项目使用 `react-i18next` 实现国际化功能，支持中文和英文两种语言。

## 文件结构

```
src/i18n/
├── index.ts              # i18n 配置文件
├── locales/
│   ├── zh-CN.json       # 中文语言包
│   └── en.json          # 英文语言包
└── README.md            # 使用说明
```

## 使用方法

### 1. 在组件中使用翻译

```tsx
import { useTranslation } from 'react-i18next'
// 或者使用封装的 hook
import { useI18n } from '@/hooks/useI18n'

function MyComponent() {
  const { t } = useTranslation()
  // 或者
  const { t } = useI18n()

  return (
    <div>
      <h1>{t('app.title')}</h1>
      <p>{t('app.description')}</p>
      <button>{t('common.confirm')}</button>
    </div>
  )
}
```

### 2. 语言切换

```tsx
import LanguageSwitcher from '@/components/language-switcher'

function Header() {
  return (
    <div>
      <LanguageSwitcher />
    </div>
  )
}
```

### 3. 手动切换语言

```tsx
import { useI18n } from '@/hooks/useI18n'

function MyComponent() {
  const { changeLanguage, getCurrentLanguage, isChinese } = useI18n()

  const handleSwitchToChinese = () => {
    changeLanguage('zh-CN')
  }

  const handleSwitchToEnglish = () => {
    changeLanguage('en')
  }

  return (
    <div>
      <p>当前语言: {getCurrentLanguage()}</p>
      <p>是否中文: {isChinese() ? '是' : '否'}</p>
      <button onClick={handleSwitchToChinese}>切换到中文</button>
      <button onClick={handleSwitchToEnglish}>Switch to English</button>
    </div>
  )
}
```

## 语言包结构

语言包采用嵌套的 JSON 结构，便于组织和管理：

```json
{
  "common": {
    "confirm": "确认",
    "cancel": "取消"
  },
  "chat": {
    "title": "智能对话",
    "send": "发送"
  },
  "app": {
    "title": "AI智能助手"
  }
}
```

## 添加新的翻译

1. 在 `zh-CN.json` 中添加中文翻译
2. 在 `en.json` 中添加对应的英文翻译
3. 确保两个文件的 key 结构保持一致

## 支持的语言

- `zh-CN`: 简体中文
- `en`: 英文

## 语言检测

系统会按以下顺序检测用户语言：
1. Cookie 中保存的语言设置
2. localStorage 中保存的语言设置
3. 浏览器语言设置
4. HTML 标签的 lang 属性

## 配置说明

- **fallbackLng**: 默认语言为中文 (`zh-CN`)
- **Cookie 域名**: 开发环境为 `localhost`，生产环境为 `.medsci.cn`
- **Cookie 有效期**: 160 分钟
- **调试模式**: 生产环境关闭

## 注意事项

1. 翻译 key 使用点号分隔的层级结构，如 `common.confirm`
2. 添加新翻译时，请确保中英文都有对应的翻译
3. 语言切换会自动保存到 Cookie 和 localStorage
4. 组件会自动响应语言变化，无需手动刷新
