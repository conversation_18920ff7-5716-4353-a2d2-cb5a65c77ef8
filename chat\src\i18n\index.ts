import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

// 导入语言包
import zhCN from '../locales/zh-CN.json'
import zhTW from '../locales/zh-TW.json'
import en from '../locales/en.json'
import vi from '../locales/vi.json'
import es from '../locales/es.json'
import ar from '../locales/ar.json'
import id from '../locales/id.json'
import pt from '../locales/pt.json'
import ja from '../locales/ja.json'
import ko from '../locales/ko.json'
import ms from '../locales/ms.json'

const resources = {
  'zh-CN': {
    translation: zhCN,
  },
  'zh-TW': {
    translation: zhTW,
  },
  en: {
    translation: en,
  },
  vi: {
    translation: vi,
  },
  es: {
    translation: es,
  },
  ar: {
    translation: ar,
  },
  id: {
    translation: id,
  },
  pt: {
    translation: pt,
  },
  ja: {
    translation: ja,
  },
  ko: {
    translation: ko,
  },
  ms: {
    translation: ms,
  },
}

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'zh-CN',
    debug: false,
    
    interpolation: {
      escapeValue: false,
    },
    
    detection: {
      order: ['cookie', 'localStorage', 'navigator', 'htmlTag'],
      caches: ['cookie', 'localStorage'],
      cookieMinutes: 160,
      cookieDomain: process.env.NODE_ENV === 'development' ? 'localhost' : '.medsci.cn',
    },
  })

export default i18n
