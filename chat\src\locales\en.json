{"common": {"login": "<PERSON><PERSON>", "logout": "Logout", "goHome": "Go Home", "newConversation": "New Conversation", "newDialog": "New Dialog", "noSessions": "No Sessions", "startConfig": "Start Configuration", "noDifyAppConfig": "No Dify App Configuration", "getConversationListFailed": "Failed to get conversation list", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "update": "Update", "close": "Close", "current": "Current", "none": "None", "tags": "Tags", "basicInfo": "Basic Information", "pleaseLoginFirst": "Please login to purchase", "loginNow": "Login Now", "subscriptionFailed": "Subscription failed", "subscriptionSuccess": "Subscription successful", "pleaseSelectPeriod": "Please select subscription period", "pleaseAgreeToTerms": "Please read and agree to the terms before activation", "agreeAndPay": " confirm terms and pay", "freeTrial": "Free Trial", "rmb": "RMB", "usd": "USD", "writing": "Writing"}, "app": {"addAppConfigSuccess": "App configuration added successfully", "updateAppConfigSuccess": "App configuration updated successfully", "management": "App Configuration Management", "appManagement": "App Management", "addApp": "Add App", "addAppConfig": "Add App Configuration", "appConfigDetail": "App Configuration Details", "appName": "App Name", "appDescription": "App Description", "appTags": "App Tags", "noApps": "No Apps", "confirmDeleteApp": "Are you sure to delete this app?", "deleteAppSuccess": "App deleted successfully", "saveAppConfigFailed": "Failed to save app configuration", "currentActiveAppId": "Current active app ID", "appList": "App List", "getAppList": "Get App List", "appListLoading": "App list loading", "deleteAppSuccessCallback": "Delete app success callback", "getDifyAppInfo": "Get Dify app information"}, "payment": {"subscribe": "Subscribe", "free": "Free", "monthlySubscription": "Monthly Subscription", "yearlySubscription": "Yearly Subscription", "upgradeSubscription": "Upgrade Subscription", "modifySubscription": "Modify Subscription", "supportAlipay": "Alipay Payment", "meisiAccount": "<PERSON><PERSON> Account", "subscriptionDescription": "Meisi AI Subscription Description", "freeDescription": "Free: Each AI agent has a usage limit of {num} times per natural month. Counting resets at the beginning of each month.", "monthlyDescription": "Monthly Subscription: Unlimited usage of each AI agent for one month from the subscription date.", "yearlyDescription": "Yearly Subscription: Unlimited usage of each AI agent for one year from the subscription date", "subscribed": "Subscribed", "freeUsing": "Using free version...", "cancelSubscription": "Cancel Subscription", "subscriptionValidUntil": "Your subscription is valid until {date}", "monthlySubscribing": "Monthly subscription active...", "yearlySubscribing": "Yearly subscription active...", "cancelMonthly": "Cancel Monthly", "cancelYearly": "Cancel Yearly", "cancelConfirmTitle": "Notice", "cancelMonthlyConfirm": "Monthly cancellation will take effect on the {date}th. You need to resubscribe to use again. Are you sure to cancel?", "cancelYearlyConfirm": "Yearly cancellation will take effect on the {date}th. You need to resubscribe to use again. Are you sure to cancel?", "month": "Month", "quarter": "Quarter", "year": "Year"}, "chat": {"aiGeneratedContent": "Content generated by AI, for reference only", "replyTime": "Reply time: ", "conversationList": "Conversation List", "rename": "<PERSON><PERSON>", "conversationRename": "Conversation Rename", "pleaseEnter": "Please enter", "conversationRenameSuccess": "Conversation renamed successfully", "deleteSuccess": "Deleted successfully", "uploadFile": "Upload File", "unsupportedFileType": "Unsupported file type: ", "clickOrDragToUpload": "Click or drag files to this area to upload", "supportedFileTypes": "Supported file types: ", "waitForUploadComplete": "Please wait for all files to upload"}}