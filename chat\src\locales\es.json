{"common": {"confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "add": "Agregar", "search": "Buscar", "loading": "Cargando...", "submit": "Enviar", "reset": "Restablecer", "back": "Atrás", "next": "Siguient<PERSON>", "previous": "Anterior", "close": "<PERSON><PERSON><PERSON>", "open": "Abrir", "copy": "Copiar", "paste": "<PERSON><PERSON><PERSON>", "cut": "Cortar", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "clear": "Limpiar", "refresh": "Actualizar", "retry": "Reintentar", "success": "Éxito", "error": "Error", "warning": "Advertencia", "info": "Información"}, "chat": {"title": "Chat IA", "placeholder": "Por favor ingrese su pregunta...", "send": "Enviar", "newChat": "Nueva conversación", "chatHistory": "Historial de chat", "clearHistory": "Limpiar historial", "typing": "Escribiendo...", "thinking": "Pensando...", "regenerate": "<PERSON><PERSON><PERSON>", "copy": "Copiar", "like": "Me gusta", "dislike": "No me gusta", "share": "Compartir", "export": "Exportar", "settings": "Configuración", "model": "<PERSON><PERSON>", "temperature": "Creatividad", "maxTokens": "<PERSON><PERSON><PERSON>", "systemPrompt": "Prompt del sistema", "userPrompt": "Prompt del usuario", "assistant": "<PERSON><PERSON><PERSON>", "user": "Usuario", "system": "Sistema", "startConversation": "Iniciar conversación", "conversationList": "Lista de conversaciones", "noConversations": "Sin conversaciones", "deleteConversation": "Eliminar conversación", "renameConversation": "Renombrar conversación", "conversationTitle": "Título de conversación", "enterTitle": "Por favor ingrese el título", "confirmDelete": "¿Está seguro de eliminar esta conversación?", "deleteSuccess": "Eliminación exitosa", "renameSuccess": "Renombrado exitoso", "copySuccess": "<PERSON><PERSON><PERSON>", "exportSuccess": "Exportación exitosa", "networkError": "<PERSON><PERSON>r de red, por favor intente más tarde", "apiError": "E<PERSON>r de API, por favor verifique la configuración", "inputTooLong": "El contenido de entrada es demasiado largo", "inputEmpty": "Por favor ingrese contenido", "modelNotSelected": "Por favor seleccione un modelo", "connecting": "Conectando...", "connected": "Conectado", "disconnected": "Desconectado", "reconnecting": "Reconectando...", "sendMessage": "<PERSON><PERSON><PERSON> men<PERSON>", "messageHistory": "Historial de mensajes", "clearMessages": "Lim<PERSON><PERSON>", "exportMessages": "Exportar mensajes", "importMessages": "I<PERSON>rtar men<PERSON>s", "messageCount": "Cantidad de mensajes", "characterCount": "Cantidad de caracteres", "wordCount": "Cantidad de palabras", "readingTime": "Tiempo de lectura", "lastUpdated": "Última actualización", "createdAt": "Creado en", "updatedAt": "Actualizado en"}, "app": {"title": "Asistente IA", "description": "Asistente de conversación inteligente impulsado por IA", "version": "Versión", "author": "Autor", "contact": "Contáctanos", "feedback": "Comentarios", "help": "<PERSON><PERSON><PERSON>", "about": "Acerca de", "privacy": "Política de privacidad", "terms": "Términos de servicio", "language": "Idioma", "theme": "<PERSON><PERSON>", "lightMode": "<PERSON>do claro", "darkMode": "<PERSON><PERSON> oscuro", "autoMode": "<PERSON><PERSON><PERSON>", "fontSize": "Tamaño de fuente", "fontFamily": "Familia de fuente", "layout": "Diseño", "sidebar": "Barra lateral", "header": "Encabezado", "footer": "Pie de página", "fullscreen": "Pantalla completa", "exitFullscreen": "Salir de pantalla completa", "minimize": "<PERSON><PERSON><PERSON>", "maximize": "Maximizar", "restore": "Restaurar", "pin": "<PERSON><PERSON>", "unpin": "<PERSON><PERSON><PERSON>"}, "error": {"404": "Página no encontrada", "500": "Error del servidor", "403": "Acceso denegado", "401": "Acceso no autorizado", "400": "Solicitud incorrecta", "timeout": "Tiempo de espera agotado", "networkError": "Error de conexión de red", "unknownError": "Error descon<PERSON>", "tryAgain": "Por favor intente de nuevo", "goHome": "Ir al inicio", "contactSupport": "<PERSON>ar so<PERSON>e", "errorCode": "<PERSON><PERSON><PERSON>", "errorMessage": "<PERSON><PERSON><PERSON>", "errorDetails": "Detalles del error", "reportError": "Reportar error", "errorReported": "Error reportado", "thankYou": "<PERSON><PERSON><PERSON> por sus comentarios"}}