{"common": {"confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "delete": "Excluir", "edit": "<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON><PERSON>", "loading": "Carregando...", "submit": "Enviar", "reset": "Redefinir", "back": "Voltar", "next": "Próximo", "previous": "Anterior", "close": "<PERSON><PERSON><PERSON>", "open": "Abrir", "copy": "Copiar", "paste": "Colar", "cut": "Cortar", "select": "Selecionar", "selectAll": "Selecionar Tudo", "clear": "Limpar", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "retry": "Tentar Novamente", "success": "Sucesso", "error": "Erro", "warning": "Aviso", "info": "Informação"}, "chat": {"title": "Chat IA", "placeholder": "Por favor, digite sua pergunta...", "send": "Enviar", "newChat": "Nova Conversa", "chatHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearHistory": "<PERSON><PERSON>", "typing": "Digitando...", "thinking": "Pensando...", "regenerate": "<PERSON><PERSON><PERSON>", "copy": "Copiar", "like": "Curtir", "dislike": "Não Curtir", "share": "Compartilhar", "export": "Exportar", "settings": "Configurações", "model": "<PERSON><PERSON>", "temperature": "Criatividade", "maxTokens": "Comprimento Máximo", "systemPrompt": "Prompt do Sistema", "userPrompt": "Prompt do Usuário", "assistant": "<PERSON><PERSON><PERSON>", "user": "<PERSON><PERSON><PERSON><PERSON>", "system": "Sistema", "startConversation": "Iniciar <PERSON>", "conversationList": "Lista de Conversas", "noConversations": "<PERSON><PERSON><PERSON><PERSON>", "deleteConversation": "Excluir Conversa", "renameConversation": "Renomear Conversa", "conversationTitle": "<PERSON><PERSON><PERSON><PERSON> da Conversa", "enterTitle": "Por favor, digite o título", "confirmDelete": "Tem certeza de que deseja excluir esta conversa?", "deleteSuccess": "Exclusão bem-sucedida", "renameSuccess": "Renomeação bem-sucedida", "copySuccess": "Cópia be<PERSON>-sucedida", "exportSuccess": "Exportação bem-sucedida", "networkError": "<PERSON>rro de rede, tente novamente mais tarde", "apiError": "Erro de API, verifique a configuração", "inputTooLong": "Conteúdo de entrada muito longo", "inputEmpty": "Por favor, digite o conteúdo", "modelNotSelected": "Por favor, selecione um modelo", "connecting": "Conectando...", "connected": "Conectado", "disconnected": "Desconectado", "reconnecting": "Reconectando...", "sendMessage": "Enviar Mensagem", "messageHistory": "Histórico de Mensagens", "clearMessages": "Limpar <PERSON>", "exportMessages": "Exportar Mensagens", "importMessages": "Importar Mensagens", "messageCount": "Contagem de Mensagens", "characterCount": "Contagem de Caracteres", "wordCount": "Contagem de Palavras", "readingTime": "Tempo de Leitura", "lastUpdated": "Última Atualização", "createdAt": "C<PERSON><PERSON> em", "updatedAt": "Atualizado em"}, "app": {"title": "Assistente IA", "description": "Assistente de conversa inteligente alimentado por IA", "version": "Vers<PERSON>", "author": "Autor", "contact": "Entre em Contato", "feedback": "<PERSON><PERSON><PERSON>", "help": "<PERSON><PERSON><PERSON>", "about": "Sobre", "privacy": "Política de Privacidade", "terms": "Termos de Serviço", "language": "Idioma", "theme": "<PERSON><PERSON>", "lightMode": "<PERSON><PERSON>", "darkMode": "<PERSON><PERSON>", "autoMode": "<PERSON><PERSON><PERSON>", "fontSize": "<PERSON><PERSON><PERSON>", "fontFamily": "Família <PERSON>", "layout": "Layout", "sidebar": "Barra Lateral", "header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "footer": "Rodapé", "fullscreen": "Tela Cheia", "exitFullscreen": "<PERSON><PERSON> <PERSON>", "minimize": "<PERSON><PERSON><PERSON>", "maximize": "Maximizar", "restore": "Restaurar", "pin": "Fixar", "unpin": "Desfixar"}, "error": {"404": "Página Não Encontrada", "500": "<PERSON>rro do Servidor", "403": "<PERSON><PERSON>", "401": "Acesso Não Autorizado", "400": "Solicitação Inválida", "timeout": "Timeout da Solicitação", "networkError": "Erro de Conexão de Rede", "unknownError": "<PERSON><PERSON>", "tryAgain": "Tente Novamente", "goHome": "Ir para Início", "contactSupport": "Con<PERSON>r <PERSON>", "errorCode": "<PERSON>ó<PERSON>", "errorMessage": "Mensagem de Erro", "errorDetails": "Detalhes do Erro", "reportError": "<PERSON><PERSON><PERSON>", "errorReported": "<PERSON><PERSON>", "thankYou": "<PERSON><PERSON><PERSON> pelo seu feedback"}}