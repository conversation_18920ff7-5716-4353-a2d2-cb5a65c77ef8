{"common": {"confirm": "确认", "cancel": "取消", "save": "保存", "delete": "删除", "edit": "编辑", "add": "添加", "search": "搜索", "loading": "加载中...", "submit": "提交", "reset": "重置", "back": "返回", "next": "下一步", "previous": "上一步", "close": "关闭", "open": "打开", "copy": "复制", "paste": "粘贴", "cut": "剪切", "select": "选择", "selectAll": "全选", "clear": "清空", "refresh": "刷新", "retry": "重试", "success": "成功", "error": "错误", "warning": "警告", "info": "信息", "login": "登录", "logout": "退出", "goHome": "返回首页", "current": "当前", "none": "无", "tags": "标签", "update": "更新", "determine": "确定", "newConversation": "新增对话", "noSessions": "暂无会话", "startConfig": "开始配置", "noDifyAppConfig": "暂无 Dify 应用配置", "getConversationListFailed": "获取会话列表失败", "createNewConversation": "创建新对话", "jumpToAuthPage": "跳转到获取授权页", "notSubscribed": "没有订阅", "alreadySubscribed": "已订阅", "getSubscriptionInfo": "获取订阅信息", "popupLoginBox": "弹出登录框", "checkIfConversationExists": "如果对话 ID 不在当前列表中，则刷新一下", "createDifyApiInstance": "创建 Dify API 实例", "optimizeConversationListSearch": "优化会话列表查找逻辑（高频操作）", "resetInputFormValues": "重置输入表单值", "traverseInputParamsSetUndefined": "遍历 inputParams 置为 undefined", "getAppInfo": "获取应用信息", "getConversationList": "获取对话列表", "addTempNewConversation": "添加临时新对话(要到第一次服务器响应有效的对话 ID 时才真正地创建完成)", "usePreviousMatchVariable": "使用之前获取的 match 变量", "header": "头部", "rightIcons": "右侧图标", "leftConversationList": "左侧对话列表", "rightChatWindow": "右侧聊天窗口 - 移动端全屏", "conversationManagement": "对话管理", "addSession": "添加会话"}, "app": {"title": "AI智能助手", "description": "基于人工智能的智能对话助手", "management": "应用配置管理", "addApp": "添加应用", "addAppConfig": "添加应用配置", "appConfigDetail": "应用配置详情", "basicInfo": "基本信息", "appName": "应用名称", "appDescription": "应用描述", "appTags": "应用标签", "noApps": "暂无应用", "confirmDeleteApp": "确定删除应用吗？", "deleteAppSuccess": "删除应用成功", "addAppConfigSuccess": "添加应用配置成功", "updateAppConfigSuccess": "更新应用配置成功", "saveAppConfigFailed": "保存应用配置失败", "saveAppConfigConfirmLoading": "保存应用配置确认按钮的 loading", "getDifyAppInfo": "获取 Dify 应用信息", "currentActiveAppId": "当前激活的应用 ID", "appList": "应用列表", "getAppList": "获取应用列表", "appListLoading": "应用列表加载中", "deleteAppSuccessCallback": "删除应用成功回调", "appManagement": "应用管理"}, "payment": {"supportAlipay": "支持支付宝支付", "meisiAccount": "梅斯账号", "pleaseAgreeToTerms": "请阅读并同意协议后激活", "freeTrial": "免费试用", "subscribe": "订阅", "month": "月", "quarter": "季", "year": "年", "subscribeSuccess": "订阅成功", "pleaseSelectPeriod": "请选择订阅服务周期", "rmb": "人民币", "usd": "美元", "free": "免费", "monthlySubscription": "连续包月", "yearlySubscription": "连续包年", "subscriptionDescription": "梅斯小智 订阅说明", "freeDescription": "免费：每个自然月内，每个智能体的使用上限{num}次。次月开始重新计次。", "monthlyDescription": "连续包月：订阅之日起一个月内，每个智能体不限使用次数。", "yearlyDescription": "连续包年：订阅之日起一年内，每个智能体不限使用次数", "subscribed": "已订阅", "freeUsing": "免费使用中…", "cancelSubscription": "取消订阅", "subscriptionValidUntil": "您的订阅可使用至 {date}", "monthlySubscribing": "连续包月中…", "yearlySubscribing": "连续包年中…", "cancelMonthly": "取消包月", "cancelYearly": "取消包年", "cancelConfirmTitle": "提示", "cancelMonthlyConfirm": "取消包月在{date}号生效，再次使用需要重新订阅。是否确认取消？", "cancelYearlyConfirm": "取消包年在{date}号生效，再次使用需要重新订阅。是否确认取消？", "qrCodeLevel": "纠错等级高", "upgradeSubscription": "升级订阅", "modifySubscription": "修改订阅", "simulateTranslationFunction": "模拟翻译函数，实际项目中替换为 i18n 库", "simulateLanguage": "模拟语言", "convertNodeJSTimeoutType": "将 NodeJS.Timeout 类型转换为 React state 可接受的类型", "replaceWithUIPrompt": "替换为 UI 提示", "writingAppType": "写作", "closeIcon": "关闭", "replaceWithQRCodeLibrary": "替换为 qrcode.react 或其他二维码库", "highErrorCorrectionLevel": "纠错等级高"}}