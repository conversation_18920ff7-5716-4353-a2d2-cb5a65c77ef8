<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国际化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .language-switcher {
            margin-bottom: 20px;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #ccc;
            border-radius: 4px;
            cursor: pointer;
        }
        button.active {
            background-color: #007bff;
            color: white;
        }
        .translation-key {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>国际化功能测试</h1>
    
    <div class="language-switcher">
        <h3>语言切换：</h3>
        <button onclick="setLanguage('zh-CN')" id="btn-zh-CN" class="active">简体中文</button>
        <button onclick="setLanguage('zh-TW')" id="btn-zh-TW">繁體中文</button>
        <button onclick="setLanguage('en')" id="btn-en">English</button>
        <button onclick="setLanguage('vi')" id="btn-vi">Tiếng Việt</button>
        <button onclick="setLanguage('es')" id="btn-es">Español</button>
        <button onclick="setLanguage('ar')" id="btn-ar">العربية</button>
        <button onclick="setLanguage('id')" id="btn-id">Bahasa Indonesia</button>
        <button onclick="setLanguage('pt')" id="btn-pt">Português</button>
        <button onclick="setLanguage('ja')" id="btn-ja">日本語</button>
        <button onclick="setLanguage('ko')" id="btn-ko">한국어</button>
        <button onclick="setLanguage('ms')" id="btn-ms">Bahasa Melayu</button>
    </div>

    <div class="test-section">
        <h3>通用翻译测试</h3>
        <p><span class="translation-key">common.login</span>: <span id="test-login"></span></p>
        <p><span class="translation-key">common.logout</span>: <span id="test-logout"></span></p>
        <p><span class="translation-key">common.newConversation</span>: <span id="test-newConversation"></span></p>
        <p><span class="translation-key">common.confirm</span>: <span id="test-confirm"></span></p>
        <p><span class="translation-key">common.cancel</span>: <span id="test-cancel"></span></p>
    </div>

    <div class="test-section">
        <h3>支付相关翻译测试</h3>
        <p><span class="translation-key">payment.subscriptionDescription</span>: <span id="test-subscriptionDescription"></span></p>
        <p><span class="translation-key">payment.monthlySubscription</span>: <span id="test-monthlySubscription"></span></p>
        <p><span class="translation-key">payment.yearlySubscription</span>: <span id="test-yearlySubscription"></span></p>
        <p><span class="translation-key">payment.freeDescription</span>: <span id="test-freeDescription"></span></p>
    </div>

    <div class="test-section">
        <h3>聊天相关翻译测试</h3>
        <p><span class="translation-key">chat.aiGeneratedContent</span>: <span id="test-aiGeneratedContent"></span></p>
        <p><span class="translation-key">chat.conversationList</span>: <span id="test-conversationList"></span></p>
        <p><span class="translation-key">chat.rename</span>: <span id="test-rename"></span></p>
        <p><span class="translation-key">chat.deleteSuccess</span>: <span id="test-deleteSuccess"></span></p>
    </div>

    <script>
        // 模拟翻译数据
        const translations = {
            'zh-CN': {
                'common.login': '登录',
                'common.logout': '退出',
                'common.newConversation': '新增对话',
                'common.confirm': '确认',
                'common.cancel': '取消',
                'payment.subscriptionDescription': '梅斯小智 订阅说明',
                'payment.monthlySubscription': '连续包月',
                'payment.yearlySubscription': '连续包年',
                'payment.freeDescription': '免费：每个自然月内，每个智能体的使用上限{num}次。次月开始重新计次。',
                'chat.aiGeneratedContent': '内容由 AI 生成, 仅供参考',
                'chat.conversationList': '对话列表',
                'chat.rename': '重命名',
                'chat.deleteSuccess': '删除成功'
            },
            'en': {
                'common.login': 'Login',
                'common.logout': 'Logout',
                'common.newConversation': 'New Conversation',
                'common.confirm': 'Confirm',
                'common.cancel': 'Cancel',
                'payment.subscriptionDescription': 'Meisi AI Subscription Description',
                'payment.monthlySubscription': 'Monthly Subscription',
                'payment.yearlySubscription': 'Yearly Subscription',
                'payment.freeDescription': 'Free: Each AI agent has a usage limit of {num} times per natural month. Counting resets at the beginning of each month.',
                'chat.aiGeneratedContent': 'Content generated by AI, for reference only',
                'chat.conversationList': 'Conversation List',
                'chat.rename': 'Rename',
                'chat.deleteSuccess': 'Deleted successfully'
            },
            'ja': {
                'common.login': 'ログイン',
                'common.logout': 'ログアウト',
                'common.newConversation': '新しい会話',
                'common.confirm': '確認',
                'common.cancel': 'キャンセル',
                'payment.subscriptionDescription': 'Meisi AIサブスクリプション説明',
                'payment.monthlySubscription': '月額サブスクリプション',
                'payment.yearlySubscription': '年額サブスクリプション',
                'payment.freeDescription': '無料：各AIエージェントは自然月あたり{num}回の使用制限があります。毎月初めにカウントがリセットされます。',
                'chat.aiGeneratedContent': 'AIによって生成されたコンテンツ、参考用のみ',
                'chat.conversationList': '会話リスト',
                'chat.rename': '名前を変更',
                'chat.deleteSuccess': '削除に成功しました'
            }
        };

        let currentLanguage = 'zh-CN';

        function setLanguage(lang) {
            currentLanguage = lang;
            
            // 更新按钮状态
            document.querySelectorAll('.language-switcher button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(`btn-${lang}`).classList.add('active');
            
            // 更新翻译文本
            updateTranslations();
        }

        function updateTranslations() {
            const langData = translations[currentLanguage] || translations['zh-CN'];
            
            // 更新各个测试项
            const testKeys = [
                'login', 'logout', 'newConversation', 'confirm', 'cancel',
                'subscriptionDescription', 'monthlySubscription', 'yearlySubscription', 'freeDescription',
                'aiGeneratedContent', 'conversationList', 'rename', 'deleteSuccess'
            ];
            
            testKeys.forEach(key => {
                const element = document.getElementById(`test-${key}`);
                if (element) {
                    let translationKey;
                    if (['login', 'logout', 'newConversation', 'confirm', 'cancel'].includes(key)) {
                        translationKey = `common.${key}`;
                    } else if (['subscriptionDescription', 'monthlySubscription', 'yearlySubscription', 'freeDescription'].includes(key)) {
                        translationKey = `payment.${key}`;
                    } else {
                        translationKey = `chat.${key}`;
                    }
                    
                    let text = langData[translationKey] || `[${translationKey}]`;
                    if (key === 'freeDescription') {
                        text = text.replace('{num}', '10');
                    }
                    element.textContent = text;
                }
            });
        }

        // 初始化
        updateTranslations();
    </script>
</body>
</html>
